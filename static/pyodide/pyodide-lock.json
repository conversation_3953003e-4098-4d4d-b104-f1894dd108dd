{"info": {"abi_version": "2024_0", "arch": "wasm32", "platform": "emscripten_3_1_58", "python": "3.12.7", "version": "0.27.3"}, "packages": {"affine": {"depends": [], "file_name": "affine-2.4.0-py3-none-any.whl", "imports": ["affine"], "install_dir": "site", "name": "affine", "package_type": "package", "sha256": "708aa092f3ee514f45afa4674d1385203789128406d2c30490a2e6eeef9d4f59", "unvendored_tests": true, "version": "2.4.0"}, "affine-tests": {"depends": ["affine"], "file_name": "affine-tests.tar", "imports": [], "install_dir": "site", "name": "affine-tests", "package_type": "package", "sha256": "f1c41942e8b59516a463918a2ff2ad9a1e4462dfca4b58d035b42592a0ab5b8b", "unvendored_tests": false, "version": "2.4.0"}, "aiohttp": {"depends": ["aiosignal", "async-timeout", "attrs", "charset-normalizer", "frozenlist", "multidict", "yarl"], "file_name": "aiohttp-3.9.5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["aiohttp"], "install_dir": "site", "name": "aiohttp", "package_type": "package", "sha256": "af776312f5564740795a34fbc51986d81e94ba9644fedcb79f70bae3ec9edd50", "unvendored_tests": true, "version": "3.9.5"}, "aiohttp-tests": {"depends": ["aiohttp"], "file_name": "aiohttp-tests.tar", "imports": [], "install_dir": "site", "name": "aiohttp-tests", "package_type": "package", "sha256": "5363e839575bb91e7d9101569228dc7a569eaa02e1c3cf63c7adbfa63e193208", "unvendored_tests": false, "version": "3.9.5"}, "aiosignal": {"depends": ["frozenlist"], "file_name": "aiosignal-1.3.1-py3-none-any.whl", "imports": ["aiosignal"], "install_dir": "site", "name": "aiosignal", "package_type": "package", "sha256": "9c910d6fb9571df9aa1aedff70ea1e9e788af2635794e16c51c555685078e3b2", "unvendored_tests": false, "version": "1.3.1"}, "altair": {"depends": ["typing-extensions", "jinja2", "jsonschema", "packaging", "narwhals"], "file_name": "altair-5.4.1-py3-none-any.whl", "imports": ["altair"], "install_dir": "site", "name": "altair", "package_type": "package", "sha256": "aae79ee70f20ba6178cde69504269e3c3b345cbd78065ccbc97d711160e4f953", "unvendored_tests": false, "version": "5.4.1"}, "annotated-types": {"depends": [], "file_name": "annotated_types-0.6.0-py3-none-any.whl", "imports": ["annotated_types"], "install_dir": "site", "name": "annotated-types", "package_type": "package", "sha256": "319ca0e1f8f91acc59a94b5cae70394447ea4cf572d62ba839185a1cea8c9521", "unvendored_tests": true, "version": "0.6.0"}, "annotated-types-tests": {"depends": ["annotated-types"], "file_name": "annotated-types-tests.tar", "imports": [], "install_dir": "site", "name": "annotated-types-tests", "package_type": "package", "sha256": "448ec5b62cc938836c4064fcfcb97efe87c0f5dbcec4f90743e0699402f7aa92", "unvendored_tests": false, "version": "0.6.0"}, "apsw": {"depends": [], "file_name": "apsw-3.47.2.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["apsw"], "install_dir": "site", "name": "apsw", "package_type": "package", "sha256": "3b54f41232d1a0decce7789163ca5aa877028d931961b14896754d02f8b1bd15", "unvendored_tests": false, "version": "3.47.2.0"}, "argon2-cffi": {"depends": ["argon2-cffi-bindings"], "file_name": "argon2_cffi-23.1.0-py3-none-any.whl", "imports": ["argon2"], "install_dir": "site", "name": "argon2-cffi", "package_type": "package", "sha256": "3e91a1e6aebbe7f6f36c5c539bb107094d73503ab2b32b8e60f14b986a88586e", "unvendored_tests": false, "version": "23.1.0"}, "argon2-cffi-bindings": {"depends": ["cffi"], "file_name": "argon2_cffi_bindings-21.2.0-cp312-abi3-pyodide_2024_0_wasm32.whl", "imports": ["_argon2_cffi_bindings"], "install_dir": "site", "name": "argon2-cffi-bindings", "package_type": "package", "sha256": "18d3561ac548ad94dceac468635f8d5da8c360b8419cb24895832772a0136d77", "unvendored_tests": false, "version": "21.2.0"}, "arro3-compute": {"depends": ["arro3-core"], "file_name": "arro3_compute-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["arro3.compute"], "install_dir": "site", "name": "arro3-compute", "package_type": "package", "sha256": "fce98777f49dc1b4d2e3b4a27a8fd1e870256b22ad080fa2f6a7bc47a6f33fe5", "unvendored_tests": false, "version": "0.4.1"}, "arro3-core": {"depends": [], "file_name": "arro3_core-0.4.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["arro3.core"], "install_dir": "site", "name": "arro3-core", "package_type": "package", "sha256": "d83bcd1ea07845d2d6c030a58abf075bb93325959b731184f34dc53bc23e71c5", "unvendored_tests": false, "version": "0.4.1"}, "arro3-io": {"depends": ["arro3-core"], "file_name": "arro3_io-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["arro3.io"], "install_dir": "site", "name": "arro3-io", "package_type": "package", "sha256": "f2a40329df5f9e736e84e8497b8b3086e622298478084f96672e60fe263fdae1", "unvendored_tests": false, "version": "0.4.1"}, "asciitree": {"depends": [], "file_name": "asciitree-0.3.3-py3-none-any.whl", "imports": ["as<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "as<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "8da38f5882aa27a3bc77d769158f541b63e7457d2adccb53dc60c2f7a2e64318", "unvendored_tests": false, "version": "0.3.3"}, "astropy": {"depends": ["packaging", "numpy", "pyerfa", "pyyaml", "astropy_iers_data"], "file_name": "astropy-7.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["astropy"], "install_dir": "site", "name": "astropy", "package_type": "package", "sha256": "0b8fc7e0a244914bb3b3c38cc6f6383273588241175ba1bc8be52f62e07cc06c", "unvendored_tests": false, "version": "7.0.0"}, "astropy-iers-data": {"depends": [], "file_name": "astropy_iers_data-0.2024.4.22.0.29.50-py3-none-any.whl", "imports": ["astropy_iers_data"], "install_dir": "site", "name": "astropy_iers_data", "package_type": "package", "sha256": "1d90a7d39ba36c74b90e95ca9c9fdcd27a32a90ca907ce8b5c56e6f1dcee1ad4", "unvendored_tests": true, "version": "0.2024.4.22.0.29.50"}, "astropy-iers-data-tests": {"depends": ["astropy_iers_data"], "file_name": "astropy_iers_data-tests.tar", "imports": [], "install_dir": "site", "name": "astropy_iers_data-tests", "package_type": "package", "sha256": "c4fe6e79fde625d35ef5dec8e3cb95f6a9e0d10fe16dfc3c2e566c3d50291940", "unvendored_tests": false, "version": "0.2024.4.22.0.29.50"}, "asttokens": {"depends": ["six"], "file_name": "asttokens-2.4.1-py2.py3-none-any.whl", "imports": ["asttokens"], "install_dir": "site", "name": "asttokens", "package_type": "package", "sha256": "844642d2a15b3f9ab537d948f137800ea52ac61452f87d3ac6f7b95b205bc951", "unvendored_tests": false, "version": "2.4.1"}, "async-timeout": {"depends": [], "file_name": "async_timeout-4.0.3-py3-none-any.whl", "imports": ["async_timeout"], "install_dir": "site", "name": "async-timeout", "package_type": "package", "sha256": "a773b7556c1074042ce7414a4c73477bb51a4684b797ef878be7e619363af126", "unvendored_tests": false, "version": "4.0.3"}, "atomicwrites": {"depends": [], "file_name": "atomicwrites-1.4.1-py2.py3-none-any.whl", "imports": ["atomicwrites"], "install_dir": "site", "name": "atomicwrites", "package_type": "package", "sha256": "ced4f094b377f5d52557a4921908b05a79709b802c36b7c9ed35122f75a806b5", "unvendored_tests": false, "version": "1.4.1"}, "attrs": {"depends": ["six"], "file_name": "attrs-23.2.0-py3-none-any.whl", "imports": ["attr", "attrs"], "install_dir": "site", "name": "attrs", "package_type": "package", "sha256": "55dec7af2bfa3de8e4b3f5dd8546a79caddee71b6397f8c2715e09a9d80f1475", "unvendored_tests": false, "version": "23.2.0"}, "autograd": {"depends": ["numpy", "future"], "file_name": "autograd-1.7.0-py3-none-any.whl", "imports": ["autograd"], "install_dir": "site", "name": "autograd", "package_type": "package", "sha256": "233586e21097d58f536b93553e78e47f5ed3ed3c17fd9e43d4b9eb2529009abf", "unvendored_tests": true, "version": "1.7.0"}, "autograd-tests": {"depends": ["autograd"], "file_name": "autograd-tests.tar", "imports": [], "install_dir": "site", "name": "autograd-tests", "package_type": "package", "sha256": "2f52ed9cee3c7712aa374ebecff9dda677b4e296ddbb06260ede4e9d3730c02a", "unvendored_tests": false, "version": "1.7.0"}, "awkward-cpp": {"depends": ["numpy"], "file_name": "awkward_cpp-44-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["awkward_cpp"], "install_dir": "site", "name": "awkward-cpp", "package_type": "package", "sha256": "b0859ccac9d1b09249fc9acab0dec8374e446f3e6feba39e3dc0855ab65f3cdf", "unvendored_tests": false, "version": "44"}, "b2d": {"depends": ["numpy", "pydantic", "setuptools", "annotated-types"], "file_name": "b2d-0.7.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["b2d"], "install_dir": "site", "name": "b2d", "package_type": "package", "sha256": "d4b9c09c571dde870f2d3d6bcecf6e9c8b5b78404d14083c08fe3f1b9f21230a", "unvendored_tests": false, "version": "0.7.4"}, "bcrypt": {"depends": [], "file_name": "bcrypt-4.1.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["bcrypt"], "install_dir": "site", "name": "bcrypt", "package_type": "package", "sha256": "cce6a07ff88f52ff6c2f3fabefcb6558c635c9127336fe1a1f5c3d0777ff1d06", "unvendored_tests": false, "version": "4.1.2"}, "beautifulsoup4": {"depends": ["soupsieve"], "file_name": "<PERSON><PERSON>up4-4.12.3-py3-none-any.whl", "imports": ["bs4"], "install_dir": "site", "name": "beautifulsoup4", "package_type": "package", "sha256": "2a93ba66221af7b9be375e4fc6cb541b477e41bab6564458f262cb74659e12ac", "unvendored_tests": true, "version": "4.12.3"}, "beautifulsoup4-tests": {"depends": ["beautifulsoup4"], "file_name": "beautifulsoup4-tests.tar", "imports": [], "install_dir": "site", "name": "beautifulsoup4-tests", "package_type": "package", "sha256": "b3efc48965e56d811ceaaf0d1b66579f20e80ef34b4e94f51454d2db6cae926e", "unvendored_tests": false, "version": "4.12.3"}, "biopython": {"depends": ["numpy"], "file_name": "biopython-1.84-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["Bio", "BioSQL"], "install_dir": "site", "name": "biopython", "package_type": "package", "sha256": "7cd317eb6466b4f9d68d76d1f8bcadf64fff5ec3104ee7687bed1ea67b3c1d1e", "unvendored_tests": false, "version": "1.84"}, "bitarray": {"depends": [], "file_name": "bitarray-2.9.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["bitarray"], "install_dir": "site", "name": "bitarray", "package_type": "package", "sha256": "f726cb3c473793b1e3aea59ed1d8783d969992f8e6f702b09a63f5a5edbebbda", "unvendored_tests": true, "version": "2.9.2"}, "bitarray-tests": {"depends": ["bitarray"], "file_name": "bitarray-tests.tar", "imports": [], "install_dir": "site", "name": "bitarray-tests", "package_type": "package", "sha256": "628e6e87a87c7ad690a63fec992d2120e442b74bef03a5abd79192f47c3f1ac1", "unvendored_tests": false, "version": "2.9.2"}, "bitstring": {"depends": ["bitarray"], "file_name": "bitstring-4.1.4-py3-none-any.whl", "imports": ["bitstring"], "install_dir": "site", "name": "bitstring", "package_type": "package", "sha256": "d1cb7fc1dd1ca54b6b111764ba8e08976fb426f24ba3e822d024783d4f33f54f", "unvendored_tests": false, "version": "4.1.4"}, "bleach": {"depends": ["webencodings", "packaging", "six"], "file_name": "bleach-6.1.0-py3-none-any.whl", "imports": ["bleach"], "install_dir": "site", "name": "bleach", "package_type": "package", "sha256": "5a833fddfc68229640eef8725602e60f9d47410975f0c2e165768e3c73630da2", "unvendored_tests": false, "version": "6.1.0"}, "bokeh": {"depends": ["contourpy", "numpy", "jinja2", "pandas", "pillow", "python-dateutil", "six", "typing-extensions", "pyyaml", "xyzservices"], "file_name": "bokeh-3.6.0-py3-none-any.whl", "imports": ["bokeh"], "install_dir": "site", "name": "bokeh", "package_type": "package", "sha256": "d822cf47a8392ab050085bc63f3580a96822227d733f9d18b994d1051e81f95f", "unvendored_tests": false, "version": "3.6.0"}, "boost-histogram": {"depends": ["numpy"], "file_name": "boost_histogram-1.5.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["boost_histogram"], "install_dir": "site", "name": "boost-histogram", "package_type": "package", "sha256": "176aea4c207395906ff81eaf68cd3143f545c1671a071812aa01c6f96e9d4552", "unvendored_tests": false, "version": "1.5.0"}, "brotli": {"depends": [], "file_name": "brotli-1.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["brotli"], "install_dir": "site", "name": "brotli", "package_type": "package", "sha256": "dbb967104fe1d0edd882ab6d161b63431077db6c1c8bebfbb44a7166a36086c6", "unvendored_tests": false, "version": "1.1.0"}, "buffer-test": {"depends": [], "file_name": "buffer_test-0.1.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["buffer_test"], "install_dir": "site", "name": "buffer-test", "package_type": "package", "sha256": "145a9496f6ce68c18d5d08adc95f842af21e7dc55dc3f9748976a2029209e44b", "unvendored_tests": false, "version": "0.1.1"}, "cachetools": {"depends": [], "file_name": "cachetools-5.3.3-py3-none-any.whl", "imports": ["cachetools"], "install_dir": "site", "name": "cachetools", "package_type": "package", "sha256": "4daf081ceab63bec98196dffd49ee4d3f3a87174339a77cfe1165521ac1ec772", "unvendored_tests": false, "version": "5.3.3"}, "cartopy": {"depends": ["shapely", "pyshp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy"], "file_name": "cartopy-0.24.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cartopy"], "install_dir": "site", "name": "Cartopy", "package_type": "package", "sha256": "8cf183ce8b16ad12427f059b686656822571c79a282c7ba37651d7013c5529d5", "unvendored_tests": true, "version": "0.24.1"}, "cartopy-tests": {"depends": ["cartopy"], "file_name": "Cartopy-tests.tar", "imports": [], "install_dir": "site", "name": "Cartopy-tests", "package_type": "package", "sha256": "7160f4bae1d5f39fdaa5998b05a9dcb24cce1ee385c4e5b1ed4010cac3378f1a", "unvendored_tests": false, "version": "0.24.1"}, "casadi": {"depends": ["numpy"], "file_name": "casadi-3.6.7-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["casadi"], "install_dir": "site", "name": "casadi", "package_type": "package", "sha256": "720787088d9988365bf89fd639498e806f3f1ff2182276a6c75254bcc908d646", "unvendored_tests": false, "version": "3.6.7"}, "cbor-diag": {"depends": [], "file_name": "cbor_diag-1.0.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cbor_diag"], "install_dir": "site", "name": "cbor-diag", "package_type": "package", "sha256": "bf86a4ed2f0b36592b9b982d13da0ec20117def26bd4a31946771afab94281b4", "unvendored_tests": false, "version": "1.0.1"}, "certifi": {"depends": [], "file_name": "certifi-2024.12.14-py3-none-any.whl", "imports": ["certifi"], "install_dir": "site", "name": "certifi", "package_type": "package", "sha256": "3448463e6fa75a4c58b95cf2545c4f2c716a12681004529c8eee33007de70428", "unvendored_tests": false, "version": "2024.12.14"}, "cffi": {"depends": ["pyc<PERSON><PERSON>"], "file_name": "cffi-1.17.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cffi"], "install_dir": "site", "name": "cffi", "package_type": "package", "sha256": "e43942f0d3e23df028a0fe7687239dde57ad1316054e7ab7e7b7c070cb2e2330", "unvendored_tests": false, "version": "1.17.1"}, "cffi-example": {"depends": ["cffi"], "file_name": "cffi_example-0.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cffi_example"], "install_dir": "site", "name": "cffi_example", "package_type": "package", "sha256": "8136bb5daa9731f4f7add5cac7277496bcc8d53bf44e3cb01b4fec84080dbbb3", "unvendored_tests": false, "version": "0.1"}, "cftime": {"depends": ["numpy"], "file_name": "cftime-1.6.4.post1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cftime"], "install_dir": "site", "name": "cftime", "package_type": "package", "sha256": "d64d2afd88efe2dcc66aed654ffdf70ae29dd67d663882b0347feaf6790a06b3", "unvendored_tests": false, "version": "1.6.4.post1"}, "charset-normalizer": {"depends": [], "file_name": "charset_normalizer-3.3.2-py3-none-any.whl", "imports": ["charset_normalizer"], "install_dir": "site", "name": "charset-normalizer", "package_type": "package", "sha256": "6571ca3c6e54c6bcb330c1f1cfa53da6f94dcca26697e4579c0d921e9dd1456d", "unvendored_tests": false, "version": "3.3.2"}, "clarabel": {"depends": ["numpy", "scipy"], "file_name": "clarabel-0.9.0-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["clarabel"], "install_dir": "site", "name": "clarabel", "package_type": "package", "sha256": "3e529ca17e25de3aa86503940b59ba2d217a6842c7316717201f94628b401e2d", "unvendored_tests": false, "version": "0.9.0"}, "click": {"depends": [], "file_name": "click-8.1.7-py3-none-any.whl", "imports": ["click"], "install_dir": "site", "name": "click", "package_type": "package", "sha256": "57cb35e834bc3925fac45a550ee1e5453b9441573fd95738e17422a0107c6a90", "unvendored_tests": false, "version": "8.1.7"}, "cligj": {"depends": ["click"], "file_name": "cligj-0.7.2-py3-none-any.whl", "imports": ["cligj"], "install_dir": "site", "name": "cligj", "package_type": "package", "sha256": "cf217debf4ba0a1cfd1f7d106521390d25147bb95ee4faa5cce07c21f9e15f81", "unvendored_tests": false, "version": "0.7.2"}, "clingo": {"depends": ["cffi"], "file_name": "clingo-5.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["clingo"], "install_dir": "site", "name": "clingo", "package_type": "package", "sha256": "76493cc69e94d85702a8b886e7521003f9d5a1056cb4660065dc14f25a83a415", "unvendored_tests": false, "version": "5.7.1"}, "cloudpickle": {"depends": [], "file_name": "cloudpickle-3.0.0-py3-none-any.whl", "imports": ["cloudpickle"], "install_dir": "site", "name": "cloudpickle", "package_type": "package", "sha256": "9c9bc3706fd958dbc23c042be5c283f03d0d3c2d2895337bed5ddc75f2c77270", "unvendored_tests": false, "version": "3.0.0"}, "cmyt": {"depends": ["colorspacious", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more-itertools", "numpy"], "file_name": "cmyt-2.0.0-py3-none-any.whl", "imports": ["cmyt"], "install_dir": "site", "name": "cmyt", "package_type": "package", "sha256": "ef747577e13bb7b60649cf7d0e730b2b88f674bdec4a7e2bf23d3c7fa2774efa", "unvendored_tests": true, "version": "2.0.0"}, "cmyt-tests": {"depends": ["cmyt"], "file_name": "cmyt-tests.tar", "imports": [], "install_dir": "site", "name": "cmyt-tests", "package_type": "package", "sha256": "33ae22d9fab3647be281c33dc7ac3c36f7d197009c4ea9ba5ad2d86073ad9445", "unvendored_tests": false, "version": "2.0.0"}, "colorspacious": {"depends": ["numpy"], "file_name": "colorspacious-1.1.2-py2.py3-none-any.whl", "imports": ["colorspacious"], "install_dir": "site", "name": "colorspacious", "package_type": "package", "sha256": "4051611378a13fc21e96aa31339ea3191f219c47a8370327ebab72e82f748df4", "unvendored_tests": false, "version": "1.1.2"}, "contourpy": {"depends": ["numpy"], "file_name": "contourpy-1.3.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["contourpy"], "install_dir": "site", "name": "contourpy", "package_type": "package", "sha256": "4d63578413c058f3f3bb75467181fcb6d7e1d5d1255375c50d9bfb97230fb02e", "unvendored_tests": false, "version": "1.3.0"}, "coolprop": {"depends": ["numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "coolprop-6.6.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["CoolProp"], "install_dir": "site", "name": "coolprop", "package_type": "package", "sha256": "b50c17d027e6cc2556f7baa66705c62e10b70be209f6ba031bb8d54839a037da", "unvendored_tests": true, "version": "6.6.0"}, "coolprop-tests": {"depends": ["coolprop"], "file_name": "coolprop-tests.tar", "imports": [], "install_dir": "site", "name": "coolprop-tests", "package_type": "package", "sha256": "2cda8d5d87d37e6bec7a69c70494276715454b7170b283cfaf70e315bd8064c9", "unvendored_tests": false, "version": "6.6.0"}, "coverage": {"depends": ["sqlite3"], "file_name": "coverage-7.4.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["coverage"], "install_dir": "site", "name": "coverage", "package_type": "package", "sha256": "0206e604a6003ddfaf540f3764d6264d48356a09a26f6186e2358f818acb0a1f", "unvendored_tests": false, "version": "7.4.4"}, "cpp-exceptions-test": {"depends": [], "file_name": "cpp-exceptions-test-0.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "cpp-exceptions-test", "package_type": "shared_library", "sha256": "f557abd6f7a809737eddc836e71e2cc3044f8049208cc53defeb0c4a9fd2b6cb", "unvendored_tests": false, "version": "0.1"}, "cpp-exceptions-test2": {"depends": [], "file_name": "cpp_exceptions_test2-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cpp-exceptions-test2"], "install_dir": "site", "name": "cpp-exceptions-test2", "package_type": "package", "sha256": "0ca1854ad233ce73ed879600fb832af8c5a2408876a0eb0426c9519b1ea6a1e3", "unvendored_tests": false, "version": "1.0"}, "cramjam": {"depends": [], "file_name": "cramjam-2.8.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cramjam"], "install_dir": "site", "name": "cramjam", "package_type": "package", "sha256": "6fc9349ec06064498f240e8c1731cebdb1be6830a3113a73e1b46d3e11b2fb9b", "unvendored_tests": false, "version": "2.8.3"}, "crc32c": {"depends": [], "file_name": "crc32c-2.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["crc32c"], "install_dir": "site", "name": "crc32c", "package_type": "package", "sha256": "d1337835f329aae0e995ce21037ffc22f4ffdfb6b1fb714d492f307b51949b69", "unvendored_tests": false, "version": "2.7.1"}, "cryptography": {"depends": ["openssl", "six", "cffi"], "file_name": "cryptography-42.0.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cryptography"], "install_dir": "site", "name": "cryptography", "package_type": "package", "sha256": "8a28d9a446dd9289733f0e2a93a72979fe39e7fe5e7eb924e794931ad207d16a", "unvendored_tests": false, "version": "42.0.5"}, "css-inline": {"depends": [], "file_name": "css_inline-0.14.6-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["css_inline"], "install_dir": "site", "name": "css-inline", "package_type": "package", "sha256": "1a8e0753bc91583bc738ffc326b08d2bb2066cae3d3b0fac3bfe69b4d923a55a", "unvendored_tests": false, "version": "0.14.6"}, "cssselect": {"depends": [], "file_name": "cssselect-1.2.0-py2.py3-none-any.whl", "imports": ["cssselect"], "install_dir": "site", "name": "cssselect", "package_type": "package", "sha256": "89db9cd274c4740898d2d39e54645a75b7e995b1c8853201f10e8e20cdfb9743", "unvendored_tests": false, "version": "1.2.0"}, "cvxpy-base": {"depends": ["numpy", "scipy", "clarabel"], "file_name": "cvxpy_base-1.5.1-py3-none-any.whl", "imports": ["cvxpy"], "install_dir": "site", "name": "cvxpy-base", "package_type": "package", "sha256": "5d57c3123b80e215cddf460762a68a427baebc0fae71eff3089809f374c9298c", "unvendored_tests": true, "version": "1.5.1"}, "cvxpy-base-tests": {"depends": ["cvxpy-base"], "file_name": "cvxpy-base-tests.tar", "imports": [], "install_dir": "site", "name": "cvxpy-base-tests", "package_type": "package", "sha256": "70157f5f7ce833811cbf0abc9e82e7626d97df584073db9509603359a7f21cec", "unvendored_tests": false, "version": "1.5.1"}, "cycler": {"depends": ["six"], "file_name": "cycler-0.12.1-py3-none-any.whl", "imports": ["cycler"], "install_dir": "site", "name": "cycler", "package_type": "package", "sha256": "ed9d04e8faa600862cf7f09726c95d98c6e7c5b5198a6c7ea812cfcf37f45621", "unvendored_tests": false, "version": "0.12.1"}, "cysignals": {"depends": [], "file_name": "cysignals-1.12.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cysignals"], "install_dir": "site", "name": "cysignals", "package_type": "package", "sha256": "15ca48d7503020d547a4ff7463deab233a629db553bd2630920f8753cc79e879", "unvendored_tests": false, "version": "1.12.2"}, "cytoolz": {"depends": ["toolz"], "file_name": "cytoolz-0.12.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cytoolz"], "install_dir": "site", "name": "cytoolz", "package_type": "package", "sha256": "00f38b1af954bdf3f03b3f263415e2432291159f47b92a43e8cb6352a35f8c59", "unvendored_tests": true, "version": "0.12.3"}, "cytoolz-tests": {"depends": ["cytoolz"], "file_name": "cytoolz-tests.tar", "imports": [], "install_dir": "site", "name": "cytoolz-tests", "package_type": "package", "sha256": "600369507845a75524d7a779529412676a3daf630207c50063809f26aa18ef81", "unvendored_tests": false, "version": "0.12.3"}, "decorator": {"depends": [], "file_name": "decorator-5.1.1-py3-none-any.whl", "imports": ["decorator"], "install_dir": "site", "name": "decorator", "package_type": "package", "sha256": "2713ce2df2d2010526de4a4ccb9661a0ae0f443bf95001f74a7ecb69f98b3efc", "unvendored_tests": false, "version": "5.1.1"}, "demes": {"depends": ["attrs", "ruamel.yaml"], "file_name": "demes-0.2.3-py3-none-any.whl", "imports": ["demes"], "install_dir": "site", "name": "demes", "package_type": "package", "sha256": "73beb7b42270de08d795cce9ba44ffd6adc661d8a4b5a1f7902f8c14b20f13c5", "unvendored_tests": false, "version": "0.2.3"}, "deprecation": {"depends": ["packaging"], "file_name": "deprecation-2.1.0-py2.py3-none-any.whl", "imports": ["deprecation"], "install_dir": "site", "name": "deprecation", "package_type": "package", "sha256": "3fe66593ef9b7c59973797c3cc3720b814b191fcf906a52d388beb2527605534", "unvendored_tests": false, "version": "2.1.0"}, "distlib": {"depends": [], "file_name": "distlib-0.3.8-py2.py3-none-any.whl", "imports": ["distlib"], "install_dir": "site", "name": "distlib", "package_type": "package", "sha256": "4329a583594bc02711149f9334c8a5c6803bb79aca3a387c2a3e92ea6aa70b90", "unvendored_tests": false, "version": "0.3.8"}, "docutils": {"depends": [], "file_name": "docutils-0.21.1-py3-none-any.whl", "imports": ["docutils"], "install_dir": "site", "name": "docutils", "package_type": "package", "sha256": "3b96290f73f837936195c48f5c0f6593a279b27b40ef6a395bb9b486b2609d86", "unvendored_tests": false, "version": "0.21.1"}, "duckdb": {"depends": [], "file_name": "duckdb-1.1.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["duckdb"], "install_dir": "site", "name": "duckdb", "package_type": "package", "sha256": "fb6e26ae8c6dabdb565f8b64926177c75982a7b20324cd7bce99c8fb4a0ab95c", "unvendored_tests": false, "version": "1.1.2"}, "ewah-bool-utils": {"depends": [], "file_name": "ewah_bool_utils-1.2.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["ewah_bool_utils"], "install_dir": "site", "name": "ewah_bool_utils", "package_type": "package", "sha256": "851c03fc12c573b32808d44974da0c3ab18319707c3e7a035401c2cef6b1e8fd", "unvendored_tests": true, "version": "1.2.2"}, "ewah-bool-utils-tests": {"depends": ["ewah_bool_utils"], "file_name": "ewah_bool_utils-tests.tar", "imports": [], "install_dir": "site", "name": "ewah_bool_utils-tests", "package_type": "package", "sha256": "ef8b669cf225d089eb1fba5bafe2a8137ca84f0be2d4793c48663016a754cd0c", "unvendored_tests": false, "version": "1.2.2"}, "exceptiongroup": {"depends": [], "file_name": "exceptiongroup-1.2.1-py3-none-any.whl", "imports": ["exceptiongroup"], "install_dir": "site", "name": "exceptiongroup", "package_type": "package", "sha256": "986fb320ff86a00e78ec5dd3826cb503d6dec51e7adfccf576aa403dd75ef55c", "unvendored_tests": false, "version": "1.2.1"}, "executing": {"depends": [], "file_name": "executing-2.0.1-py2.py3-none-any.whl", "imports": ["executing"], "install_dir": "site", "name": "executing", "package_type": "package", "sha256": "dc0205e27ff57198b88920272785be932be8aa12d52dfe06c2fb1c07a752d374", "unvendored_tests": false, "version": "2.0.1"}, "fastparquet": {"depends": ["cramjam", "numpy", "pandas", "fsspec", "packaging"], "file_name": "fastparquet-2024.5.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["fastparquet"], "install_dir": "site", "name": "fastparquet", "package_type": "package", "sha256": "76c01f54e35457faeb47ad51d5550c9064772ff1e34878e5bfd126d3b46a60b6", "unvendored_tests": false, "version": "2024.5.0"}, "fiona": {"depends": ["attrs", "certifi", "setuptools", "six", "click", "cligj"], "file_name": "fiona-1.9.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["fiona"], "install_dir": "site", "name": "fiona", "package_type": "package", "sha256": "8d3686076e7ee584b95dda122b2e302cc5f5978ab93aacad4f0d3bd4b7f4c317", "unvendored_tests": true, "version": "1.9.5"}, "fiona-tests": {"depends": ["fiona"], "file_name": "fiona-tests.tar", "imports": [], "install_dir": "site", "name": "fiona-tests", "package_type": "package", "sha256": "c7d399239822b916bfdbe05240e9827ab4c5fedafb698f129dd3cffe823cb736", "unvendored_tests": false, "version": "1.9.5"}, "fonttools": {"depends": [], "file_name": "fonttools-4.51.0-py3-none-any.whl", "imports": ["fontTools"], "install_dir": "site", "name": "fonttools", "package_type": "package", "sha256": "4962ced67c8c316b1019288a77632749f57f8882d9544800090f542b6ea679ee", "unvendored_tests": false, "version": "4.51.0"}, "fpcast-test": {"depends": [], "file_name": "fpcast_test-0.1.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["fpcast_test"], "install_dir": "site", "name": "fpcast-test", "package_type": "package", "sha256": "3eca4cd6dec7c5f96f59d8af3282cf62be7325bc90a9f01a502541471c8dbeb4", "unvendored_tests": false, "version": "0.1.1"}, "freesasa": {"depends": [], "file_name": "freesasa-2.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["freesasa"], "install_dir": "site", "name": "freesasa", "package_type": "package", "sha256": "df82474f48962f6f0200fa4142c2cf812715c6ca0aa76240a51dbf39636efeb2", "unvendored_tests": false, "version": "2.2.1"}, "frozenlist": {"depends": [], "file_name": "frozenlist-1.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["frozenlist"], "install_dir": "site", "name": "frozenlist", "package_type": "package", "sha256": "54c1083ed6b796be50f11798e15c7fef6fd2ae64c38c730623e17b44cf8d69ff", "unvendored_tests": false, "version": "1.4.1"}, "fsspec": {"depends": [], "file_name": "fsspec-2024.3.1-py3-none-any.whl", "imports": ["fsspec"], "install_dir": "site", "name": "fsspec", "package_type": "package", "sha256": "d5bfff20c7b8c1baacb6ff1935f1be0bb434f8c7ae6b8695dfd92bdfeb3f50c4", "unvendored_tests": true, "version": "2024.3.1"}, "fsspec-tests": {"depends": ["fsspec"], "file_name": "fsspec-tests.tar", "imports": [], "install_dir": "site", "name": "fsspec-tests", "package_type": "package", "sha256": "280d939f4792368713020024166d002f85feeeac884aeca0959c709d90205043", "unvendored_tests": false, "version": "2024.3.1"}, "future": {"depends": [], "file_name": "future-1.0.0-py3-none-any.whl", "imports": ["future"], "install_dir": "site", "name": "future", "package_type": "package", "sha256": "a14827b91ce5a078c9cd5e2bb9dcd169664b8e6d33f6e6ef3f22588ed41131c9", "unvendored_tests": true, "version": "1.0.0"}, "future-tests": {"depends": ["future"], "file_name": "future-tests.tar", "imports": [], "install_dir": "site", "name": "future-tests", "package_type": "package", "sha256": "b04381175c00f67cca159bb15fcb9393c2bfa9b7046b2f3488be06fed2d56877", "unvendored_tests": false, "version": "1.0.0"}, "galpy": {"depends": ["numpy", "scipy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "astropy", "future", "setuptools"], "file_name": "galpy-1.10.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["galpy"], "install_dir": "site", "name": "galpy", "package_type": "package", "sha256": "f58da4be568ace925c932cba597eeed7d316e5930dec03eda15f55d5c4028790", "unvendored_tests": false, "version": "1.10.1"}, "gdal": {"depends": ["geos"], "file_name": "gdal-3.8.3.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "gdal", "package_type": "shared_library", "sha256": "9284fc5d25d13be5cd4359d7c0211dcf3de571065e6624e3665bc5c0a6c47bd7", "unvendored_tests": false, "version": "3.8.3"}, "gensim": {"depends": ["numpy", "scipy", "six", "smart-open", "wrapt"], "file_name": "gensim-4.3.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gensim"], "install_dir": "site", "name": "gensim", "package_type": "package", "sha256": "b0e031fc53fc2b51854e2713b772f2b25ed3912e9a339414676b817de09671e5", "unvendored_tests": true, "version": "4.3.3"}, "gensim-tests": {"depends": ["gensim"], "file_name": "gensim-tests.tar", "imports": [], "install_dir": "site", "name": "gensim-tests", "package_type": "package", "sha256": "04d6ac8ec163bea286c6aef2a2be2831bf38aa6def3d9f3acebbd65719f7b146", "unvendored_tests": false, "version": "4.3.3"}, "geopandas": {"depends": ["shapely", "fiona", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "packaging", "pandas"], "file_name": "geopandas-1.0.1-py3-none-any.whl", "imports": ["geopandas"], "install_dir": "site", "name": "geopandas", "package_type": "package", "sha256": "4c407dd57407945b1cf707c4859b5e9c95b01750c23d6d4b522c8532bfdc4225", "unvendored_tests": true, "version": "1.0.1"}, "geopandas-tests": {"depends": ["geopandas"], "file_name": "geopandas-tests.tar", "imports": [], "install_dir": "site", "name": "geopandas-tests", "package_type": "package", "sha256": "60e64f2f5ed1e8410b8657656eb8b53cf203b7cb3f9ef4da49cb311197cb1c74", "unvendored_tests": false, "version": "1.0.1"}, "geos": {"depends": [], "file_name": "geos-3.12.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "geos", "package_type": "shared_library", "sha256": "a4739f36fd3f3cdf4752491174cec4992b10ba2d68a208b50fdee214df6fb09c", "unvendored_tests": false, "version": "3.12.1"}, "gmpy2": {"depends": [], "file_name": "gmpy2-2.1.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gmpy2"], "install_dir": "site", "name": "gmpy2", "package_type": "package", "sha256": "7f7503d2d0fed10ccc8cbe1041ebb989ee8964ed73756f2f484197cf7aa5e9eb", "unvendored_tests": false, "version": "2.1.5"}, "gsw": {"depends": ["numpy"], "file_name": "gsw-3.6.19-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gsw"], "install_dir": "site", "name": "gsw", "package_type": "package", "sha256": "d4c26fae4169c776e87a871b7de8d52ff261948bac392a51e39c0b17d90981fe", "unvendored_tests": true, "version": "3.6.19"}, "gsw-tests": {"depends": ["gsw"], "file_name": "gsw-tests.tar", "imports": [], "install_dir": "site", "name": "gsw-tests", "package_type": "package", "sha256": "1c72b5b507ce62bf6d4ee54413193416c22337fd33a7f8b7f08c9d5157fa90cf", "unvendored_tests": false, "version": "3.6.19"}, "h3": {"depends": [], "file_name": "h3-4.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["h3"], "install_dir": "site", "name": "h3", "package_type": "package", "sha256": "66964c37d5345596055dccb695f1c605115a8477ec9564558476c163c6935320", "unvendored_tests": false, "version": "4.2.1"}, "h5py": {"depends": ["numpy", "pkgconfig"], "file_name": "h5py-3.12.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["h5py"], "install_dir": "site", "name": "h5py", "package_type": "package", "sha256": "23e87001669286627f695c197ce58883f5dc3f28e7c096c98f8022a84c0a8ce8", "unvendored_tests": true, "version": "3.12.1"}, "h5py-tests": {"depends": ["h5py"], "file_name": "h5py-tests.tar", "imports": [], "install_dir": "site", "name": "h5py-tests", "package_type": "package", "sha256": "2b347f778f8f913ba5772b7bb6be2a9a598b95d70b01dbdee102e5a8cf3390a8", "unvendored_tests": false, "version": "3.12.1"}, "hashlib": {"depends": ["openssl"], "file_name": "hashlib-1.0.0-py2.py3-none-any.whl", "imports": ["_hashlib"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "cpython_module", "sha256": "c8df775520c0ee30586d43cb0a6b7afd5deef76bae989c232a8b826458cbe8c5", "unvendored_tests": false, "version": "1.0.0"}, "html5lib": {"depends": ["webencodings", "six"], "file_name": "html5lib-1.1-py2.py3-none-any.whl", "imports": ["html5lib"], "install_dir": "site", "name": "html5lib", "package_type": "package", "sha256": "25522420fe5b27785b96b29409b413213359658a51a1b0d4e83b93d5991066fb", "unvendored_tests": false, "version": "1.1"}, "httpx": {"depends": [], "file_name": "httpx-0.28.1-py3-none-any.whl", "imports": ["httpx"], "install_dir": "site", "name": "httpx", "package_type": "package", "sha256": "d6b7dd241503095aac1700bad4315c6853df055b406db2bd4d0d0a642b5b1459", "unvendored_tests": false, "version": "0.28.1"}, "idna": {"depends": [], "file_name": "idna-3.7-py3-none-any.whl", "imports": ["idna"], "install_dir": "site", "name": "idna", "package_type": "package", "sha256": "43217cd9c46e0429a452d8e3274ad02a06bbbc10690bac5db672253cee12f816", "unvendored_tests": false, "version": "3.7"}, "igraph": {"depends": ["texttable"], "file_name": "igraph-0.11.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["igraph"], "install_dir": "site", "name": "igraph", "package_type": "package", "sha256": "a1424470f5e3daf36b50ffe71ed476a06d04ce8a0ad6336f52ce3f97048e10b8", "unvendored_tests": false, "version": "0.11.4"}, "imageio": {"depends": ["numpy", "pillow"], "file_name": "imageio-2.36.0-py3-none-any.whl", "imports": ["imageio"], "install_dir": "site", "name": "imageio", "package_type": "package", "sha256": "5ea5c1383dfc9386c8f77e55b36bd70c04429880c9a93722b8386d8986fa67c8", "unvendored_tests": false, "version": "2.36.0"}, "iminuit": {"depends": ["numpy"], "file_name": "iminuit-2.30.1-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["iminuit"], "install_dir": "site", "name": "iminuit", "package_type": "package", "sha256": "7933eef72cdf5f6e08bb30ee4b47ac290677c1bcc7fbe03f5c360663da055364", "unvendored_tests": false, "version": "2.30.1"}, "iniconfig": {"depends": [], "file_name": "iniconfig-2.0.0-py3-none-any.whl", "imports": ["iniconfig"], "install_dir": "site", "name": "iniconfig", "package_type": "package", "sha256": "e07c5569e61117748282d14ec50fe64a756b70e86150b2b8ef7ddb05160adfc6", "unvendored_tests": false, "version": "2.0.0"}, "ipython": {"depends": ["asttokens", "decorator", "executing", "matplotlib-inline", "prompt_toolkit", "pure-eval", "pygments", "six", "stack-data", "traitlets", "sqlite3", "wcwidth"], "file_name": "ipython-8.23.0-py3-none-any.whl", "imports": ["IPython"], "install_dir": "site", "name": "ipython", "package_type": "package", "sha256": "33bdd836866caacfc217480df23ba17a3789a7703134e359262b5af7d313288b", "unvendored_tests": true, "version": "8.23.0"}, "ipython-tests": {"depends": ["ipython"], "file_name": "ipython-tests.tar", "imports": [], "install_dir": "site", "name": "ipython-tests", "package_type": "package", "sha256": "16ca546a851d65d8d087ee931a87b0aa98302945de96d2cb6e307bd2cf294bb6", "unvendored_tests": false, "version": "8.23.0"}, "jedi": {"depends": ["parso"], "file_name": "jedi-0.19.1-py2.py3-none-any.whl", "imports": ["jedi"], "install_dir": "site", "name": "jedi", "package_type": "package", "sha256": "4d4cc8e57eea4a9c9e00ba47877d6e5555fed19aac6ea3c3424542c471de6065", "unvendored_tests": true, "version": "0.19.1"}, "jedi-tests": {"depends": ["jedi"], "file_name": "jedi-tests.tar", "imports": [], "install_dir": "site", "name": "jedi-tests", "package_type": "package", "sha256": "499c00bec73ba48ac7ebfb6a51054756920734994c68769ca6695749e7a6aa52", "unvendored_tests": false, "version": "0.19.1"}, "jinja2": {"depends": ["markupsafe"], "file_name": "Jinja2-3.1.3-py3-none-any.whl", "imports": ["jinja2"], "install_dir": "site", "name": "Jinja2", "package_type": "package", "sha256": "4937c31f081841ec980080bd7f2b0a63d33f89ecb404ebeb49a6b5a29360210c", "unvendored_tests": false, "version": "3.1.3"}, "joblib": {"depends": [], "file_name": "joblib-1.4.0-py3-none-any.whl", "imports": ["joblib"], "install_dir": "site", "name": "joblib", "package_type": "package", "sha256": "0497212a60f4a1c5bba3a3a80c6f78e4167f49fe8659fba1bd82b5676be32ef4", "unvendored_tests": true, "version": "1.4.0"}, "joblib-tests": {"depends": ["joblib"], "file_name": "joblib-tests.tar", "imports": [], "install_dir": "site", "name": "joblib-tests", "package_type": "package", "sha256": "976d2629e2dab67630216db4649f6b36291dbc04165af94493ba2e9c9a215d88", "unvendored_tests": false, "version": "1.4.0"}, "jsonschema": {"depends": ["attrs", "pyrsistent", "referencing", "jsonschema_specifications"], "file_name": "jsonschema-4.21.1-py3-none-any.whl", "imports": ["jsonschema"], "install_dir": "site", "name": "jsonschema", "package_type": "package", "sha256": "e79fdc785c37d45b080d7471c377a677c6ac8d34eeda2dc810dde67ff23d66b7", "unvendored_tests": true, "version": "4.21.1"}, "jsonschema-specifications": {"depends": [], "file_name": "jsonschema_specifications-2023.12.1-py3-none-any.whl", "imports": ["jsonschema_specifications"], "install_dir": "site", "name": "jsonschema_specifications", "package_type": "package", "sha256": "342d6df7da853b9c782bfdd282e91d6669b1a3e0c71837035beef68a6ead6e37", "unvendored_tests": true, "version": "2023.12.1"}, "jsonschema-specifications-tests": {"depends": ["jsonschema_specifications"], "file_name": "jsonschema_specifications-tests.tar", "imports": [], "install_dir": "site", "name": "jsonschema_specifications-tests", "package_type": "package", "sha256": "5f4a2bd403cbba9f23d0aab25b141d5d705bda388d4693e0ade582e4cdb268cd", "unvendored_tests": false, "version": "2023.12.1"}, "jsonschema-tests": {"depends": ["jsonschema"], "file_name": "jsonschema-tests.tar", "imports": [], "install_dir": "site", "name": "jsonschema-tests", "package_type": "package", "sha256": "ca973adef632ce0381c075e97d0e125abceaa708a43d41d9b3b55869faaa3887", "unvendored_tests": false, "version": "4.21.1"}, "kiwisolver": {"depends": [], "file_name": "kiwisolver-1.4.5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["kiwisolver"], "install_dir": "site", "name": "kiwisolver", "package_type": "package", "sha256": "4d23b603d826710ab2dae35b5b032048eec1b1166c00167d4dd422b3521d5c00", "unvendored_tests": false, "version": "1.4.5"}, "lakers-python": {"depends": [], "file_name": "lakers_python-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["lakers"], "install_dir": "site", "name": "lakers-python", "package_type": "package", "sha256": "93562798b3f87c56654f19cd67a2a672cf3b8088e5814dca8f33d76de343eca1", "unvendored_tests": false, "version": "0.4.1"}, "lazy-loader": {"depends": [], "file_name": "lazy_loader-0.4-py3-none-any.whl", "imports": ["lazy_loader"], "install_dir": "site", "name": "lazy_loader", "package_type": "package", "sha256": "aa6fe0b907a67a3a27e95e47312bd27f6f3c73124249e986f88a5ec1d6515bb9", "unvendored_tests": true, "version": "0.4"}, "lazy-loader-tests": {"depends": ["lazy_loader"], "file_name": "lazy_loader-tests.tar", "imports": [], "install_dir": "site", "name": "lazy_loader-tests", "package_type": "package", "sha256": "c23c9288fa311491c59b7947852fd0a97caa8bc8c179dec47290de1f8a4d7f6c", "unvendored_tests": false, "version": "0.4"}, "lazy-object-proxy": {"depends": [], "file_name": "lazy_object_proxy-1.10.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["lazy_object_proxy"], "install_dir": "site", "name": "lazy-object-proxy", "package_type": "package", "sha256": "8d76dd57ac34c9b9219311c5e5d2b7262c8ad0b036eedd1e7d17675e3360314c", "unvendored_tests": false, "version": "1.10.0"}, "libcst": {"depends": ["pyyaml"], "file_name": "libcst-1.4.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["libcst"], "install_dir": "site", "name": "libcst", "package_type": "package", "sha256": "83982421e0c1bb1c0e440eb96d941cca705f6765dd420194c59078cc6b7da3a7", "unvendored_tests": true, "version": "1.4.0"}, "libcst-tests": {"depends": ["libcst"], "file_name": "libcst-tests.tar", "imports": [], "install_dir": "site", "name": "libcst-tests", "package_type": "package", "sha256": "cb2e07c16ff67d68ec6a89b31c3c7a6a6f27f619f9f5fdc028bd603462561035", "unvendored_tests": false, "version": "1.4.0"}, "libhdf5": {"depends": [], "file_name": "libhdf5-1.12.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libhdf5", "package_type": "shared_library", "sha256": "7e8b80afa835e35eb40ad16295c3924ea7421065b97b421f577015e738a6923b", "unvendored_tests": false, "version": "1.12.1"}, "libheif": {"depends": [], "file_name": "libheif-1.12.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "lib<PERSON><PERSON>", "package_type": "shared_library", "sha256": "5ef3adaba102ec1c272c169f764cb4e37e1bcb7ebace947548d0d84b5abfe0b0", "unvendored_tests": false, "version": "1.12.0"}, "libmagic": {"depends": [], "file_name": "libmagic-5.42.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libmagic", "package_type": "shared_library", "sha256": "1944f9748fc241d306a84037040a221a45360f26cfcd50171a17f7d827f0a2c8", "unvendored_tests": false, "version": "5.42"}, "lightgbm": {"depends": ["numpy", "scipy", "scikit-learn"], "file_name": "lightgbm-4.5.0-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["lightgbm"], "install_dir": "site", "name": "lightgbm", "package_type": "package", "sha256": "4eac2aa10d75f712e33ee9f24809e22c3450b4b67c32d18044eb544bd7fbc060", "unvendored_tests": false, "version": "4.5.0"}, "logbook": {"depends": ["ssl"], "file_name": "logbook-1.7.0.post0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["logbook"], "install_dir": "site", "name": "logbook", "package_type": "package", "sha256": "971c41c6e6a28392c16d7b6eb8962ec092c9ebfac4da4c2708acd73e452fa429", "unvendored_tests": false, "version": "1.7.0.post0"}, "lxml": {"depends": [], "file_name": "lxml-5.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["lxml"], "install_dir": "site", "name": "lxml", "package_type": "package", "sha256": "def6a621c67000a4b848557d3e8a318af372f383b0b29077a36363914ad33413", "unvendored_tests": false, "version": "5.2.1"}, "lzma": {"depends": [], "file_name": "lzma-1.0.0-py2.py3-none-any.whl", "imports": ["lzma", "_lzma"], "install_dir": "site", "name": "lzma", "package_type": "cpython_module", "sha256": "ea4f252f9bd37444f0b2e943c9173e802c40689fdcd2ae1defbd4b67b46eef09", "unvendored_tests": false, "version": "1.0.0"}, "markupsafe": {"depends": [], "file_name": "markupsafe-2.1.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["markupsafe"], "install_dir": "site", "name": "MarkupSafe", "package_type": "package", "sha256": "b3cc3adbe38156060b2f08ba276385c01fa70f487738e7a4eaecf9da03d79fae", "unvendored_tests": false, "version": "2.1.5"}, "matplotlib": {"depends": ["contourpy", "cycler", "fonttools", "kiwisolver", "numpy", "packaging", "pillow", "pyparsing", "python-dateutil", "pytz", "matplotlib-pyodide"], "file_name": "matplotlib-3.8.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pylab", "mpl_toolkits", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "8aec22ec8860e41b1a99afbb850b1c687d6f849c83aeb662245dd330d7cbe6c0", "unvendored_tests": true, "version": "3.8.4"}, "matplotlib-inline": {"depends": ["traitlets"], "file_name": "matplotlib_inline-0.1.7-py3-none-any.whl", "imports": ["matplotlib-inline"], "install_dir": "site", "name": "matplotlib-inline", "package_type": "package", "sha256": "a18efdbe93dd5881b6efaf7df067c5ac69cdd84e3942d438a46416dc59f53c06", "unvendored_tests": false, "version": "0.1.7"}, "matplotlib-pyodide": {"depends": [], "file_name": "matplotlib_pyodide-0.2.3-py3-none-any.whl", "imports": ["matplotlib_pyodide"], "install_dir": "site", "name": "matplotlib-pyodide", "package_type": "package", "sha256": "96fcc00c76a3c6b61e53a8a4a2373c18387c750bd8b53b9ee728e1e57235c7e2", "unvendored_tests": false, "version": "0.2.3"}, "matplotlib-tests": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "matplotlib-tests.tar", "imports": [], "install_dir": "site", "name": "matplotlib-tests", "package_type": "package", "sha256": "87938ee5594247643ea589075d518f44dcf682a26ff5dd3f9b380b29aec4f455", "unvendored_tests": false, "version": "3.8.4"}, "memory-allocator": {"depends": [], "file_name": "memory_allocator-0.1.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["memory_allocator"], "install_dir": "site", "name": "memory-allocator", "package_type": "package", "sha256": "7ce75f825f439d2ad8a3e643ed15d82fa4a69b5f3aa76cd5e91ae84367aeea0d", "unvendored_tests": false, "version": "0.1.4"}, "micropip": {"depends": ["packaging"], "file_name": "micropip-0.8.0-py3-none-any.whl", "imports": ["micropip"], "install_dir": "site", "name": "micropip", "package_type": "package", "sha256": "8be261d01ec63c0c2e8855a61f57eef506430ca564b94238b06fdf20cdaee7ac", "unvendored_tests": false, "version": "0.8.0"}, "mmh3": {"depends": [], "file_name": "mmh3-4.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["mmh3"], "install_dir": "site", "name": "mmh3", "package_type": "package", "sha256": "d7ebbbeedb892dedd2516267fe7366e770f9bf534b8df265f095dce91e12fc23", "unvendored_tests": false, "version": "4.1.0"}, "mne": {"depends": ["numpy", "scipy", "setuptools", "decorator", "lazy_loader", "packaging"], "file_name": "mne-1.8.0-py3-none-any.whl", "imports": ["mne"], "install_dir": "site", "name": "mne", "package_type": "package", "sha256": "584a85c781ddf4dde066996d8d3e52b9828928646383e8fb34de2c2163f9af7e", "unvendored_tests": true, "version": "1.8.0"}, "mne-tests": {"depends": ["mne"], "file_name": "mne-tests.tar", "imports": [], "install_dir": "site", "name": "mne-tests", "package_type": "package", "sha256": "04f0a12e69064b8fb37860381b517c1efa7dd74379cb7cd3cb38bcf00d138053", "unvendored_tests": false, "version": "1.8.0"}, "more-itertools": {"depends": [], "file_name": "more_itertools-10.2.0-py3-none-any.whl", "imports": ["more_itertools"], "install_dir": "site", "name": "more-itertools", "package_type": "package", "sha256": "dc9ebaffd67b42b3919c84bc31727f15a6260dfda9271eec8867385be23a3740", "unvendored_tests": false, "version": "10.2.0"}, "mpmath": {"depends": [], "file_name": "mpmath-1.3.0-py3-none-any.whl", "imports": ["mpmath"], "install_dir": "site", "name": "mpmath", "package_type": "package", "sha256": "36a05016ff46ab04eb5bcb9eedbaddd6088809afc401a8b7dd6dfc5e382665f3", "unvendored_tests": true, "version": "1.3.0"}, "mpmath-tests": {"depends": ["mpmath"], "file_name": "mpmath-tests.tar", "imports": [], "install_dir": "site", "name": "mpmath-tests", "package_type": "package", "sha256": "05b80145a0315e52fce0bd3b46210ebebf2f9d3ab8c0f358e1369b06183bc787", "unvendored_tests": false, "version": "1.3.0"}, "msgpack": {"depends": [], "file_name": "msgpack-1.1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msgpack"], "install_dir": "site", "name": "msgpack", "package_type": "package", "sha256": "754a1bac2d191a6dc1509313675a11d27023a8ddb5e325041a72534ebff49603", "unvendored_tests": false, "version": "1.1.0"}, "msgspec": {"depends": [], "file_name": "msgspec-0.18.6-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msgspec"], "install_dir": "site", "name": "msgspec", "package_type": "package", "sha256": "ea3c3ec66e8e2847bda5560e4fb0581c8cc576e2df884cc4e31ff41f1e489e3b", "unvendored_tests": false, "version": "0.18.6"}, "msprime": {"depends": ["numpy", "newick", "tskit", "demes", "rpds-py"], "file_name": "msprime-1.3.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msprime"], "install_dir": "site", "name": "msprime", "package_type": "package", "sha256": "61a8c2c4707134cb8f082bdd804504e3a30f51f09d5b9687e3fd511129e97f2f", "unvendored_tests": false, "version": "1.3.3"}, "multidict": {"depends": [], "file_name": "multidict-6.0.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["multidict"], "install_dir": "site", "name": "multidict", "package_type": "package", "sha256": "957ed0fe177c00b24a3f7ce08a5167e5cb39fd2860106d0a604246b67eb52dad", "unvendored_tests": false, "version": "6.0.5"}, "munch": {"depends": ["setuptools", "six"], "file_name": "munch-4.0.0-py2.py3-none-any.whl", "imports": ["munch"], "install_dir": "site", "name": "munch", "package_type": "package", "sha256": "126f98c117aeb339e7d329b3d593d0c5dab6490818628de741520bec8736e4bb", "unvendored_tests": false, "version": "4.0.0"}, "mypy": {"depends": [], "file_name": "mypy-1.9.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["mypyc", "mypy"], "install_dir": "site", "name": "mypy", "package_type": "package", "sha256": "08cb9b50756a54993a699c8c50d48298a4d86bdd80886755fc6e000b8bc9bb9c", "unvendored_tests": true, "version": "1.9.0"}, "mypy-tests": {"depends": ["mypy"], "file_name": "mypy-tests.tar", "imports": [], "install_dir": "site", "name": "mypy-tests", "package_type": "package", "sha256": "1cb4248fc39416e68979c7ebe4382657d9d0b884044b101f3acc36341d634c95", "unvendored_tests": false, "version": "1.9.0"}, "narwhals": {"depends": [], "file_name": "narwhals-1.24.1-py3-none-any.whl", "imports": ["narwhals"], "install_dir": "site", "name": "narwhals", "package_type": "package", "sha256": "67ff956580eea788c8ca9d842365e29d88097b655f05ae0ecc0a543d0f6ca270", "unvendored_tests": false, "version": "1.24.1"}, "netcdf4": {"depends": ["numpy", "packaging", "h5py", "cftime", "certifi"], "file_name": "netcdf4-1.7.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["netCDF4"], "install_dir": "site", "name": "netcdf4", "package_type": "package", "sha256": "8c7c6477b3126872154d4110c01b9a70cd6376433c731f655bac827d205e048b", "unvendored_tests": false, "version": "1.7.2"}, "networkx": {"depends": ["decorator", "setuptools", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy"], "file_name": "networkx-3.4.2-py3-none-any.whl", "imports": ["networkx"], "install_dir": "site", "name": "networkx", "package_type": "package", "sha256": "718a2d63c99b636db73415e09d34841a4e7723940899348473f167f72ed6af78", "unvendored_tests": true, "version": "3.4.2"}, "networkx-tests": {"depends": ["networkx"], "file_name": "networkx-tests.tar", "imports": [], "install_dir": "site", "name": "networkx-tests", "package_type": "package", "sha256": "00256f8ae1fb66e5e292894d2fee36371fae70acccb27f3ac9c245a93374d515", "unvendored_tests": false, "version": "3.4.2"}, "newick": {"depends": [], "file_name": "newick-1.9.0-py2.py3-none-any.whl", "imports": ["newick"], "install_dir": "site", "name": "newick", "package_type": "package", "sha256": "bed2a3dbd426fe80e9a54296464d34548b146da1dcb872b6d50f48dac2c75a40", "unvendored_tests": false, "version": "1.9.0"}, "nh3": {"depends": [], "file_name": "nh3-0.2.17-cp37-abi3-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["nh3"], "install_dir": "site", "name": "nh3", "package_type": "package", "sha256": "949b6dde3ae2629a30f99f82a14b7a4c22da6049ca8307270b4423dc7f18961e", "unvendored_tests": false, "version": "0.2.17"}, "nlopt": {"depends": ["numpy"], "file_name": "nlopt-2.9.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["nlopt"], "install_dir": "site", "name": "nlopt", "package_type": "package", "sha256": "3a99a4e2d7d85b4f84a853c8666c2f6b1dfb5e3dd109f70aaab4ed2eec1c22f5", "unvendored_tests": false, "version": "2.9.1"}, "nltk": {"depends": ["regex", "sqlite3"], "file_name": "nltk-3.8.1-py3-none-any.whl", "imports": ["nltk"], "install_dir": "site", "name": "nltk", "package_type": "package", "sha256": "cea288db5c20b32138958f4210c9b62171cdf14534538736a6397e782454dafe", "unvendored_tests": true, "version": "3.8.1"}, "nltk-tests": {"depends": ["nltk"], "file_name": "nltk-tests.tar", "imports": [], "install_dir": "site", "name": "nltk-tests", "package_type": "package", "sha256": "dbc03250fdd8be30154b0dfded39cc7c060ab4e9da3c60067debc08782d2f50c", "unvendored_tests": false, "version": "3.8.1"}, "numcodecs": {"depends": ["numpy", "msgpack"], "file_name": "numcodecs-0.13.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["numcodecs"], "install_dir": "site", "name": "numcodecs", "package_type": "package", "sha256": "c897da253598475f4eb729bc5675f7537fd9e90a15691f6853d649370604e857", "unvendored_tests": true, "version": "0.13.1"}, "numcodecs-tests": {"depends": ["numcodecs"], "file_name": "numcodecs-tests.tar", "imports": [], "install_dir": "site", "name": "numcodecs-tests", "package_type": "package", "sha256": "7d86f1eab3c93e2fe112c925ae34c9e8cd3b982408d432bda9ee31c69ee9393e", "unvendored_tests": false, "version": "0.13.1"}, "numpy": {"depends": [], "file_name": "numpy-2.0.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["numpy"], "install_dir": "site", "name": "numpy", "package_type": "package", "sha256": "a5e133aa791e2eeb31d3d074e78633f106561e41ffac10545080b2ca76415a44", "unvendored_tests": true, "version": "2.0.2"}, "numpy-tests": {"depends": ["numpy"], "file_name": "numpy-tests.tar", "imports": [], "install_dir": "site", "name": "numpy-tests", "package_type": "package", "sha256": "d76afab4a38f7afee7362d68e31c92f62ca76e44042c58827b0de6312b44ae98", "unvendored_tests": false, "version": "2.0.2"}, "openblas": {"depends": [], "file_name": "openblas-0.3.26.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openblas", "package_type": "shared_library", "sha256": "b75fb737daf5a062381a140ac83e26b9749bc7b775c5c2f96ac9fc428278503d", "unvendored_tests": false, "version": "0.3.26"}, "opencv-python": {"depends": ["numpy"], "file_name": "opencv_python-*********-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cv2"], "install_dir": "site", "name": "opencv-python", "package_type": "package", "sha256": "a359671defaafa478ab2bbc401c843832233fc388cd3bf6fd8b6246a47b4df8a", "unvendored_tests": false, "version": "*********"}, "openssl": {"depends": [], "file_name": "openssl-1.1.1w.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openssl", "package_type": "shared_library", "sha256": "3a9b7ca64491569efc9b27cc49480bc6291cdcb4024b5b5a82e1a94078b571d7", "unvendored_tests": false, "version": "1.1.1w"}, "optlang": {"depends": ["sympy", "six", "swiglpk"], "file_name": "optlang-1.8.1-py2.py3-none-any.whl", "imports": ["optlang"], "install_dir": "site", "name": "optlang", "package_type": "package", "sha256": "8920d390b26aefaaaee7b5d6686f666c5d1a260a6968c85464034744b5cfe6cf", "unvendored_tests": true, "version": "1.8.1"}, "optlang-tests": {"depends": ["optlang"], "file_name": "optlang-tests.tar", "imports": [], "install_dir": "site", "name": "optlang-tests", "package_type": "package", "sha256": "58b43d8a239951c1ca8ad26d396466b93145aff83bd82523e42496ff70b7d6bb", "unvendored_tests": false, "version": "1.8.1"}, "orjson": {"depends": [], "file_name": "orjson-3.10.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "304fe252e6ef5dcd4b803f3a6f40e139716df6d997211475d32e97dc70a6906f", "unvendored_tests": false, "version": "3.10.1"}, "packaging": {"depends": [], "file_name": "packaging-24.2-py3-none-any.whl", "imports": ["packaging"], "install_dir": "site", "name": "packaging", "package_type": "package", "sha256": "70b0c82fd5049fbc38b97710e329324871f7f75979cd4bdab16b4688f172879f", "unvendored_tests": false, "version": "24.2"}, "pandas": {"depends": ["numpy", "python-dateutil", "pytz"], "file_name": "pandas-2.2.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pandas"], "install_dir": "site", "name": "pandas", "package_type": "package", "sha256": "db2ba32534eb2f0633bcddae052a8919f847e51c6b62d247bb389be552799c20", "unvendored_tests": true, "version": "2.2.3"}, "pandas-tests": {"depends": ["pandas"], "file_name": "pandas-tests.tar", "imports": [], "install_dir": "site", "name": "pandas-tests", "package_type": "package", "sha256": "65a6b6e60d29649b141021792551cada63ccc33c6739d9d4be5ed2f9deb8e485", "unvendored_tests": false, "version": "2.2.3"}, "parso": {"depends": [], "file_name": "parso-0.8.4-py2.py3-none-any.whl", "imports": ["parso"], "install_dir": "site", "name": "parso", "package_type": "package", "sha256": "870de6ea19456d89025a4a84f26cea1cdd9254a95cfe3c4a0b71c06364dac50f", "unvendored_tests": false, "version": "0.8.4"}, "patsy": {"depends": ["numpy", "six"], "file_name": "patsy-0.5.6-py2.py3-none-any.whl", "imports": ["patsy"], "install_dir": "site", "name": "patsy", "package_type": "package", "sha256": "2021ed334578ff8d4b842cae18927a24cc5f8fcaa3d0ee9bc94bc036e70dfa82", "unvendored_tests": true, "version": "0.5.6"}, "patsy-tests": {"depends": ["patsy"], "file_name": "patsy-tests.tar", "imports": [], "install_dir": "site", "name": "patsy-tests", "package_type": "package", "sha256": "4e0db966e4b5c8afb9b1a086d25d54df97129114fce679c23c4c50ab294ab0c7", "unvendored_tests": false, "version": "0.5.6"}, "pcodec": {"depends": ["numpy"], "file_name": "pcodec-0.3.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pcodec"], "install_dir": "site", "name": "pcodec", "package_type": "package", "sha256": "fe2c12c98c2c59fed5c0e97de0854b7120adbb9ff639f63a50e757d9d29e339e", "unvendored_tests": false, "version": "0.3.3"}, "peewee": {"depends": ["sqlite3", "cffi"], "file_name": "peewee-3.17.3-py3-none-any.whl", "imports": ["peewee"], "install_dir": "site", "name": "peewee", "package_type": "package", "sha256": "3cc62f4a7e09dfe1f6837b77024c26a53e0d45080ddee5132b33c8cf44602b78", "unvendored_tests": true, "version": "3.17.3"}, "peewee-tests": {"depends": ["peewee"], "file_name": "peewee-tests.tar", "imports": [], "install_dir": "site", "name": "peewee-tests", "package_type": "package", "sha256": "d4018f2902856222647ba9ea827dacbcba48c20b8e783a77affcf441a9f09391", "unvendored_tests": false, "version": "3.17.3"}, "pi-heif": {"depends": ["cffi", "pillow", "lib<PERSON><PERSON>"], "file_name": "pi_heif-0.21.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pi_heif"], "install_dir": "site", "name": "pi-heif", "package_type": "package", "sha256": "8b592086e9f46238e89ba16ab1821033c3f10ac3b41b04263828ef1793d6b24d", "unvendored_tests": false, "version": "0.21.0"}, "pillow": {"depends": [], "file_name": "pillow-10.2.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["PIL"], "install_dir": "site", "name": "Pillow", "package_type": "package", "sha256": "532d485088291a079f82951eefdcd2aab051af03ec6ed194d91b2804c1ed8e05", "unvendored_tests": false, "version": "10.2.0"}, "pillow-heif": {"depends": ["cffi", "pillow", "lib<PERSON><PERSON>"], "file_name": "pillow_heif-0.20.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pillow_heif"], "install_dir": "site", "name": "pillow-heif", "package_type": "package", "sha256": "50048b8bc9964456fb4adff7bc7828233bc53bb64bed60e62d28b3d006f7b936", "unvendored_tests": false, "version": "0.20.0"}, "pkgconfig": {"depends": [], "file_name": "pkgconfig-1.5.5-py3-none-any.whl", "imports": ["pkgconfig"], "install_dir": "site", "name": "pkgconfig", "package_type": "package", "sha256": "2293743453584ad79117f9d9e854563a27f4e2c166bd8767d75ffd54bf7e7e4f", "unvendored_tests": false, "version": "1.5.5"}, "pluggy": {"depends": [], "file_name": "pluggy-1.5.0-py3-none-any.whl", "imports": ["pluggy"], "install_dir": "site", "name": "pluggy", "package_type": "package", "sha256": "196b44e3e2fe6d93d7ae3bdcba421cd624c74f0ec1bff82bf71dc585e3981c7e", "unvendored_tests": false, "version": "1.5.0"}, "polars": {"depends": [], "file_name": "polars-1.18.0-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["polars"], "install_dir": "site", "name": "polars", "package_type": "package", "sha256": "3b6cec1a3dca40b07791f5cbd270ef5f67921843d73bfd8f12176ea8d7b362f2", "unvendored_tests": false, "version": "1.18.0"}, "pplpy": {"depends": ["gmpy2", "cysignals"], "file_name": "pplpy-0.8.10-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["ppl"], "install_dir": "site", "name": "pplpy", "package_type": "package", "sha256": "887250d5cb8d4960081eff75600c08a3ce9c0f8fbff605a953488254dd03ca5e", "unvendored_tests": false, "version": "0.8.10"}, "primecountpy": {"depends": ["cysignals"], "file_name": "primecountpy-0.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["primecountpy"], "install_dir": "site", "name": "primecountpy", "package_type": "package", "sha256": "32be2069c23a8a704ef07e21c463cb80dd0f75926282e5e616525fad996ae865", "unvendored_tests": false, "version": "0.1.0"}, "prompt-toolkit": {"depends": [], "file_name": "prompt_toolkit-3.0.43-py3-none-any.whl", "imports": ["prompt_toolkit"], "install_dir": "site", "name": "prompt_toolkit", "package_type": "package", "sha256": "ccdda309e6ab33ccdf6c9c1232d42cd703ac205c716773ab2031047c226ca9a5", "unvendored_tests": false, "version": "3.0.43"}, "protobuf": {"depends": [], "file_name": "protobuf-5.29.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["google"], "install_dir": "site", "name": "protobuf", "package_type": "package", "sha256": "d7e43227732b3c483b5a1cf0ec32df9a1aa5297ea32c00532a53df3a93dbf507", "unvendored_tests": false, "version": "5.29.2"}, "pure-eval": {"depends": [], "file_name": "pure_eval-0.2.3-py3-none-any.whl", "imports": ["pure_eval"], "install_dir": "site", "name": "pure-eval", "package_type": "package", "sha256": "5d1d3da6e9ae2cbe04d8d69e966578caeedcd071250b35115af4e0ccf8ab5355", "unvendored_tests": false, "version": "0.2.3"}, "py": {"depends": [], "file_name": "py-1.11.0-py2.py3-none-any.whl", "imports": ["py"], "install_dir": "site", "name": "py", "package_type": "package", "sha256": "02389b31f588b6505f4933385043c24cbaff1865119e3b401f9915fec551aded", "unvendored_tests": false, "version": "1.11.0"}, "pyarrow": {"depends": ["numpy", "pandas", "pyodide-unix-timezones"], "file_name": "pyarrow-18.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["p<PERSON><PERSON>"], "install_dir": "site", "name": "p<PERSON><PERSON>", "package_type": "package", "sha256": "4513c92c069106bdc4c44be8e74ba67dd022151cd790cecc46f2a28b3e24c8b4", "unvendored_tests": false, "version": "18.1.0"}, "pyclipper": {"depends": [], "file_name": "pyclipper-1.3.0.post5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pyclipper"], "install_dir": "site", "name": "pyclipper", "package_type": "package", "sha256": "d2f64f90ee3f33773d20db737396b03bfebfe0b04dac46f232ac69569c9eff2f", "unvendored_tests": false, "version": "1.3.0.post5"}, "pycparser": {"depends": [], "file_name": "pycparser-2.22-py3-none-any.whl", "imports": ["pyc<PERSON><PERSON>"], "install_dir": "site", "name": "pyc<PERSON><PERSON>", "package_type": "package", "sha256": "fbcb60d5ae36a6401b89b703ee562d6707d62101b83cdd23196439f1ce32185b", "unvendored_tests": false, "version": "2.22"}, "pycryptodome": {"depends": [], "file_name": "pycryptodome-3.20.0-cp35-abi3-pyodide_2024_0_wasm32.whl", "imports": ["Crypto"], "install_dir": "site", "name": "pycryptodome", "package_type": "package", "sha256": "d86c833ed2898735ee52d6cecdc5078419ba04344fecf92a9d1c7551fe682417", "unvendored_tests": true, "version": "3.20.0"}, "pycryptodome-tests": {"depends": ["pycryptodome"], "file_name": "pycryptodome-tests.tar", "imports": [], "install_dir": "site", "name": "pycryptodome-tests", "package_type": "package", "sha256": "7b27def39aceeda4cb69f1f79f2c38c0e54e549b44103ea4833591f2613441e5", "unvendored_tests": false, "version": "3.20.0"}, "pydantic": {"depends": ["typing-extensions", "pydantic_core", "annotated-types"], "file_name": "pydantic-2.10.5-py3-none-any.whl", "imports": ["pydantic"], "install_dir": "site", "name": "pydantic", "package_type": "package", "sha256": "67672b969d871958cbbc7635b6aecc79ba048653b3f52a37abf40b1b51e2da36", "unvendored_tests": false, "version": "2.10.5"}, "pydantic-core": {"depends": [], "file_name": "pydantic_core-2.27.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pydantic_core"], "install_dir": "site", "name": "pydantic_core", "package_type": "package", "sha256": "4d0f2b2ab4ea7540d406edc9cad629265cf3203b61b3d4d3e8e39e8f8272d2bd", "unvendored_tests": false, "version": "2.27.2"}, "pydecimal": {"depends": [], "file_name": "pydecimal-1.0.0-py2.py3-none-any.whl", "imports": ["_pydecimal"], "install_dir": "site", "name": "pydecimal", "package_type": "cpython_module", "sha256": "2ff595c5305117591c171f8b647b1d29019770b2e2d7ace28cf1e85ba6bc7732", "unvendored_tests": false, "version": "1.0.0"}, "pydoc-data": {"depends": [], "file_name": "pydoc_data-1.0.0-py2.py3-none-any.whl", "imports": ["pydoc_data"], "install_dir": "site", "name": "pydoc_data", "package_type": "cpython_module", "sha256": "496ab7efd15627cb00f6e29804b4a97f410a9930327b697a46893e3faae8beb1", "unvendored_tests": false, "version": "1.0.0"}, "pyerfa": {"depends": ["numpy"], "file_name": "pyerfa-2.0.1.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["erfa"], "install_dir": "site", "name": "pyerfa", "package_type": "package", "sha256": "d51659089adac984080156f199a12de271ac4ea0e0c91605095773aaaee45afd", "unvendored_tests": true, "version": "2.0.1.4"}, "pyerfa-tests": {"depends": ["pyerfa"], "file_name": "pyerfa-tests.tar", "imports": [], "install_dir": "site", "name": "pyerfa-tests", "package_type": "package", "sha256": "1e1085c3ff7bb614f24c636aaf2e6b66e2cc44f7c7c7ef9d7e008ec6386f9d4f", "unvendored_tests": false, "version": "2.0.1.4"}, "pygame-ce": {"depends": [], "file_name": "pygame_ce-2.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pygame"], "install_dir": "site", "name": "pygame-ce", "package_type": "package", "sha256": "620744a5f339a7a2cd60cd5551d9ef9821e4a2cbc058ad7d6162c9a2f2984cbb", "unvendored_tests": true, "version": "2.4.1"}, "pygame-ce-tests": {"depends": ["pygame-ce"], "file_name": "pygame-ce-tests.tar", "imports": [], "install_dir": "site", "name": "pygame-ce-tests", "package_type": "package", "sha256": "38ce046ca57811be922e96f2e52b8dc5a857c5ebbbe27ab7aab2e75b6e3ada9b", "unvendored_tests": false, "version": "2.4.1"}, "pygments": {"depends": [], "file_name": "pygments-2.17.2-py3-none-any.whl", "imports": ["pygments"], "install_dir": "site", "name": "Pygments", "package_type": "package", "sha256": "9dda0eae04f0fa840126e38060ad5c7ff58c3c5f13b0b9b69a1f3fc1c9242b74", "unvendored_tests": false, "version": "2.17.2"}, "pyheif": {"depends": ["cffi"], "file_name": "pyheif-0.8.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["py<PERSON><PERSON>"], "install_dir": "site", "name": "py<PERSON><PERSON>", "package_type": "package", "sha256": "08d3fb566b663961fc5c7fbc6ba6b94aeded07277bea09e7620ba71af91de605", "unvendored_tests": false, "version": "0.8.0"}, "pyiceberg": {"depends": ["click", "fsspec", "mmh3", "pydantic", "pyparsing", "requests", "rich", "sortedcontainers", "sqlalchemy", "strictyaml"], "file_name": "pyi<PERSON>berg-0.6.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "ae35808295ebf465ed01396f8654212032d15636274a682358ca36a7883cde6c", "unvendored_tests": false, "version": "0.6.0"}, "pyinstrument": {"depends": [], "file_name": "pyinstrument-4.4.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pyinstrument"], "install_dir": "site", "name": "pyinstrument", "package_type": "package", "sha256": "75c3d8acaa200d6126eb237347a378f1cec60262349b3676beb48b34529c6f9f", "unvendored_tests": false, "version": "4.4.0"}, "pynacl": {"depends": ["cffi"], "file_name": "pynacl-1.5.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["nacl"], "install_dir": "site", "name": "pynacl", "package_type": "package", "sha256": "00a6aaaee25db55f6d7cb816104cd39e67adf35027262735ccf65e33f93551e7", "unvendored_tests": false, "version": "1.5.0"}, "pyodide-http": {"depends": [], "file_name": "pyodide_http-0.2.1-py3-none-any.whl", "imports": ["pyodide_http"], "install_dir": "site", "name": "pyodide-http", "package_type": "package", "sha256": "53cf46105a7b262ab3762867e2f702eae57ace58c35b359275c154a3209a193e", "unvendored_tests": false, "version": "0.2.1"}, "pyodide-unix-timezones": {"depends": [], "file_name": "pyodide_unix_timezones-1.0.0-py3-none-any.whl", "imports": ["unix_timezones"], "install_dir": "site", "name": "pyodide-unix-timezones", "package_type": "package", "sha256": "b7c6660701a0fcb44690d2dbf2b4edee306b1533491819905635056181058024", "unvendored_tests": false, "version": "1.0.0"}, "pyparsing": {"depends": [], "file_name": "pyparsing-3.1.2-py3-none-any.whl", "imports": ["pyparsing"], "install_dir": "site", "name": "pyparsing", "package_type": "package", "sha256": "06d085f8ab95e16daf7e56918eb53efb5bc17ef2901f50f9e70b9f572c48c919", "unvendored_tests": false, "version": "3.1.2"}, "pyproj": {"depends": ["certifi", "sqlite3"], "file_name": "pyproj-3.6.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "5300304f876387f74b8f97d6b009d238d01627fb2fd68be793c30f4d8d7ea57c", "unvendored_tests": false, "version": "3.6.1"}, "pyrsistent": {"depends": [], "file_name": "pyrsistent-0.20.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["_pyrsistent_version", "pyrsistent"], "install_dir": "site", "name": "pyrsistent", "package_type": "package", "sha256": "d84ca435cd894d85422c55daa96b18d45acdeb91e1e65a471f006b1e163c234f", "unvendored_tests": false, "version": "0.20.0"}, "pysam": {"depends": [], "file_name": "pysam-0.22.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pysam"], "install_dir": "site", "name": "pysam", "package_type": "package", "sha256": "afbda30634034ebfb53ce1c98c00eaee5d5e1f584e2bd348129bd22d8b3e7963", "unvendored_tests": false, "version": "0.22.0"}, "pyshp": {"depends": [], "file_name": "pyshp-2.3.1-py2.py3-none-any.whl", "imports": ["shapefile"], "install_dir": "site", "name": "pyshp", "package_type": "package", "sha256": "5fab24a9e50089d54f68574360ce0fd5aa2b12d691a8d4a9c7d53b9f5ca0537e", "unvendored_tests": false, "version": "2.3.1"}, "pytest": {"depends": ["atomicwrites", "attrs", "more-itertools", "pluggy", "py", "setuptools", "six", "iniconfig", "exceptiongroup"], "file_name": "pytest-8.1.1-py3-none-any.whl", "imports": ["_pytest", "pytest"], "install_dir": "site", "name": "pytest", "package_type": "package", "sha256": "8e1a7e36cd780ecc7ba00e3ec641194bee78a89f876511445fe93181975d45eb", "unvendored_tests": false, "version": "8.1.1"}, "pytest-asyncio": {"depends": ["pytest"], "file_name": "pytest_asyncio-0.23.7-py3-none-any.whl", "imports": ["pytest_asyncio"], "install_dir": "site", "name": "pytest-asyncio", "package_type": "package", "sha256": "d6dc75e2cd86062e563e0a36c19c9efb797e00a57c56059c36b6d3ac69de0fbc", "unvendored_tests": false, "version": "0.23.7"}, "pytest-benchmark": {"depends": [], "file_name": "pytest_benchmark-4.0.0-py3-none-any.whl", "imports": ["pytest_benchmark"], "install_dir": "site", "name": "pytest-benchmark", "package_type": "package", "sha256": "e32f721490e339cf702e62d9c73c0a532f9f476bbc357d1cf53cc1e56897ac06", "unvendored_tests": false, "version": "4.0.0"}, "python-dateutil": {"depends": ["six"], "file_name": "python_dateutil-2.9.0.post0-py2.py3-none-any.whl", "imports": ["dateutil"], "install_dir": "site", "name": "python-dateutil", "package_type": "package", "sha256": "8ac867e0b178d358f2fd3bbc9be73931ec81b2e93c4579a14c001a8fcd25c9ff", "unvendored_tests": false, "version": "2.9.0.post0"}, "python-flint": {"depends": [], "file_name": "python_flint-0.6.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["flint"], "install_dir": "site", "name": "python-flint", "package_type": "package", "sha256": "ef0a8f87f52402ee5fdd09477aa862fc07e5d8e1219c1ae9863a7afa46f3c103", "unvendored_tests": false, "version": "0.6.0"}, "python-magic": {"depends": ["libmagic"], "file_name": "python_magic-0.4.27-py2.py3-none-any.whl", "imports": ["magic"], "install_dir": "site", "name": "python-magic", "package_type": "package", "sha256": "8b2508b2ee73eb3395c48315add872e5eac13139b89debdc63d4712cd708905a", "unvendored_tests": false, "version": "0.4.27"}, "python-sat": {"depends": ["six"], "file_name": "python_sat-1.8.dev13-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pysat"], "install_dir": "site", "name": "python-sat", "package_type": "package", "sha256": "ea2279237bc57ec4f6bce6341569db8eecc70782e3a083abbdaecb66c0eedd1f", "unvendored_tests": false, "version": "1.8.dev13"}, "python-solvespace": {"depends": [], "file_name": "python_solvespace-3.0.8-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["python_solvespace"], "install_dir": "site", "name": "python-solvespace", "package_type": "package", "sha256": "e7062210acc94f42924aeaea0d82fc4f310d1429885aa95eab1dff12b200ca59", "unvendored_tests": false, "version": "3.0.8"}, "pytz": {"depends": [], "file_name": "pytz-2024.1-py2.py3-none-any.whl", "imports": ["pytz"], "install_dir": "site", "name": "pytz", "package_type": "package", "sha256": "2a5da4f4fd74792d0c7ec56aaf95dea350515acc249cf6272c95b145572752d3", "unvendored_tests": false, "version": "2024.1"}, "pywavelets": {"depends": ["numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy"], "file_name": "pywavelets-1.7.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pywt"], "install_dir": "site", "name": "pywavelets", "package_type": "package", "sha256": "07c1a0b25eb7ba1bf3a7d060c5139ef0baa92ac2453205feaa5b7701a66175cd", "unvendored_tests": true, "version": "1.7.0"}, "pywavelets-tests": {"depends": ["pywavelets"], "file_name": "pywavelets-tests.tar", "imports": [], "install_dir": "site", "name": "pywavelets-tests", "package_type": "package", "sha256": "f64419cdca0ccb3f062172dadde818bbfde549a8fdf693a5f81aa6fdfc2889ba", "unvendored_tests": false, "version": "1.7.0"}, "pyxel": {"depends": [], "file_name": "pyxel-1.9.10-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["pyxel"], "install_dir": "site", "name": "pyxel", "package_type": "package", "sha256": "c8cd54409de7f7f2c7d42aa67b7dea10d11b5799a7f1d43419271e885b007e4d", "unvendored_tests": false, "version": "1.9.10"}, "pyxirr": {"depends": [], "file_name": "pyxirr-0.10.6-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pyxirr"], "install_dir": "site", "name": "pyxirr", "package_type": "package", "sha256": "ea0666a4aa0a879734eba22fd8b07f6bd556c85150f7956a12354386c7114e70", "unvendored_tests": false, "version": "0.10.6"}, "pyyaml": {"depends": [], "file_name": "pyyaml-6.0.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["_yaml", "yaml"], "install_dir": "site", "name": "pyyaml", "package_type": "package", "sha256": "1b7ef7d7a97f77589eabb8db8d14aabcda375e4e7dfa30915482e690cce56e8d", "unvendored_tests": false, "version": "6.0.2"}, "rasterio": {"depends": ["numpy", "affine", "gdal", "attrs", "certifi", "click", "cligj"], "file_name": "rasterio-1.4.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rasterio"], "install_dir": "site", "name": "rasterio", "package_type": "package", "sha256": "a6d4ed2469d09e65adeef10b211e28a37b29716688650edab7da417e2cf6c536", "unvendored_tests": false, "version": "1.4.2"}, "rateslib": {"depends": ["numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "rateslib-1.6.0-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["rateslib"], "install_dir": "site", "name": "rateslib", "package_type": "package", "sha256": "6b7568e39708d3aeb969ff0cc819cdb9ef3b6b10feae3a395fe1722ed544dcdb", "unvendored_tests": false, "version": "1.6.0"}, "rebound": {"depends": ["numpy"], "file_name": "rebound-4.4.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rebound"], "install_dir": "site", "name": "rebound", "package_type": "package", "sha256": "0efc08aca788873a90de633f86725f299dddbf5b33eda44ecd8c074b91dc42c5", "unvendored_tests": false, "version": "4.4.3"}, "reboundx": {"depends": ["rebound", "numpy"], "file_name": "reboundx-4.3.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["reboundx"], "install_dir": "site", "name": "reboundx", "package_type": "package", "sha256": "7ec689d657e2501d7dfaddc2b872f967d632d74796563293428ac7fb9274760b", "unvendored_tests": false, "version": "4.3.0"}, "referencing": {"depends": ["attrs", "rpds-py"], "file_name": "referencing-0.34.0-py3-none-any.whl", "imports": ["referencing"], "install_dir": "site", "name": "referencing", "package_type": "package", "sha256": "ce717e99ae281bcbf22bd76ad55094f42f4084553e174685c44bd41cffebb824", "unvendored_tests": true, "version": "0.34.0"}, "referencing-tests": {"depends": ["referencing"], "file_name": "referencing-tests.tar", "imports": [], "install_dir": "site", "name": "referencing-tests", "package_type": "package", "sha256": "65ac064bd63a4c3f41f74672060077d4f8f87cb3a4419c6d895f72ef9f5ef836", "unvendored_tests": false, "version": "0.34.0"}, "regex": {"depends": [], "file_name": "regex-2024.9.11-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["regex"], "install_dir": "site", "name": "regex", "package_type": "package", "sha256": "f1c288d5600021e235c5ce51365b325b03f3b453e37858e6c2c9c650760aec79", "unvendored_tests": true, "version": "2024.9.11"}, "regex-tests": {"depends": ["regex"], "file_name": "regex-tests.tar", "imports": [], "install_dir": "site", "name": "regex-tests", "package_type": "package", "sha256": "8acb2431b9a9c5a6e1c164e5c40f61d6386ac2102ba2fb60e28f0156dc43675e", "unvendored_tests": false, "version": "2024.9.11"}, "requests": {"depends": ["charset-normalizer", "idna", "urllib3", "certifi"], "file_name": "requests-2.31.0-py3-none-any.whl", "imports": ["requests"], "install_dir": "site", "name": "requests", "package_type": "package", "sha256": "641c13821b2401738d818f66e6a05840f95e5a0049e58a18e2d69180a2f3797e", "unvendored_tests": false, "version": "2.31.0"}, "retrying": {"depends": ["six"], "file_name": "retrying-1.3.4-py3-none-any.whl", "imports": ["retrying"], "install_dir": "site", "name": "retrying", "package_type": "package", "sha256": "80e9673f6a620f434a7c07d734c921f6003cb7f37d82f33dc5f0579ccad96397", "unvendored_tests": false, "version": "1.3.4"}, "rich": {"depends": [], "file_name": "rich-13.7.1-py3-none-any.whl", "imports": ["rich"], "install_dir": "site", "name": "rich", "package_type": "package", "sha256": "d60870c3b2ec9d3ec588318637d1dd58e51b8b0fcdda57d4f130b42e6e70cd7a", "unvendored_tests": false, "version": "13.7.1"}, "river": {"depends": ["numpy", "pandas", "scipy"], "file_name": "river-0.22.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["river"], "install_dir": "site", "name": "river", "package_type": "package", "sha256": "045c01d5b186c3aeba4a956f4c5775d2638fbb8f8cb770a4509b57cd18b3f7a0", "unvendored_tests": true, "version": "0.22.0"}, "river-tests": {"depends": ["river"], "file_name": "river-tests.tar", "imports": [], "install_dir": "site", "name": "river-tests", "package_type": "package", "sha256": "1542e658976658321c6b9aa9c401f4aa624be9f30ba132938cb9ec043a82529d", "unvendored_tests": false, "version": "0.22.0"}, "robotraconteur": {"depends": ["numpy"], "file_name": "robotraconteur-1.2.2-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["RobotRaconteur"], "install_dir": "site", "name": "RobotRaconteur", "package_type": "package", "sha256": "53a92169bea76ef918e6ed997a0a3112307b071b9038a914853c6d8d64dbf229", "unvendored_tests": false, "version": "1.2.2"}, "rpds-py": {"depends": [], "file_name": "rpds_py-0.18.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rpds"], "install_dir": "site", "name": "rpds-py", "package_type": "package", "sha256": "919ca8eddf19532610c695cf05db7d23f049f1db033d42319732cea4a7171105", "unvendored_tests": false, "version": "0.18.0"}, "ruamel-yaml": {"depends": [], "file_name": "ruamel.yaml-0.18.6-py3-none-any.whl", "imports": ["rua<PERSON>"], "install_dir": "site", "name": "ruamel.yaml", "package_type": "package", "sha256": "a5978cad153653d7f5f826015b247e5ea8511070df3e4f5f73763f98e182d317", "unvendored_tests": false, "version": "0.18.6"}, "rust-abi-test": {"depends": [], "file_name": "rust_abi_test-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["rust-abi-test"], "install_dir": "site", "name": "rust-abi-test", "package_type": "package", "sha256": "82cf65f43ea331a13c8ae75b891a0894705cad1ce8b3c3a4f3e326eca2465bef", "unvendored_tests": false, "version": "1.0"}, "rust-panic-test": {"depends": [], "file_name": "rust_panic_test-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["rust-panic-test"], "install_dir": "site", "name": "rust-panic-test", "package_type": "package", "sha256": "e06aa30bd5d1feee6e8ba24b6a3bc880bb1d5a0e6cd70996441dd4a507ea7726", "unvendored_tests": false, "version": "1.0"}, "scikit-image": {"depends": ["packaging", "numpy", "scipy", "networkx", "pillow", "imageio", "pywavelets", "lazy_loader"], "file_name": "scikit_image-0.25.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["skimage"], "install_dir": "site", "name": "scikit-image", "package_type": "package", "sha256": "ee1b66156a35fffe3939acdb4efd5ed06d93653e25948ede908343748852059d", "unvendored_tests": true, "version": "0.25.0"}, "scikit-image-tests": {"depends": ["scikit-image"], "file_name": "scikit-image-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-image-tests", "package_type": "package", "sha256": "b86953f5b7c073e8e4f156f95bf41b8fbcdc48ec01cfcb09e319fe0b08b7a114", "unvendored_tests": false, "version": "0.25.0"}, "scikit-learn": {"depends": ["scipy", "joblib", "threadpoolctl"], "file_name": "scikit_learn-1.6.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["sklearn"], "install_dir": "site", "name": "scikit-learn", "package_type": "package", "sha256": "69357b5aa98623e02b891521d40d2535e5f5828bc00082a402434464d194d920", "unvendored_tests": true, "version": "1.6.1"}, "scikit-learn-tests": {"depends": ["scikit-learn"], "file_name": "scikit-learn-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-learn-tests", "package_type": "package", "sha256": "a75a3b9b8fb003b121aefc18ad0e8d59f720c839ce5bdd1ccb3b211028d1bb4f", "unvendored_tests": false, "version": "1.6.1"}, "scipy": {"depends": ["numpy", "openblas"], "file_name": "scipy-1.14.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["scipy"], "install_dir": "site", "name": "scipy", "package_type": "package", "sha256": "1500a98f723f7d095a2488f4313eda55b5c5a8ff387dedbb30a7da90c723ff8d", "unvendored_tests": true, "version": "1.14.1"}, "scipy-tests": {"depends": ["scipy"], "file_name": "scipy-tests.tar", "imports": [], "install_dir": "site", "name": "scipy-tests", "package_type": "package", "sha256": "0e543f9ac3b655780fc9a9bfadc977d014b082ea9b7268e2b5c6cf6be5f88106", "unvendored_tests": false, "version": "1.14.1"}, "screed": {"depends": [], "file_name": "screed-1.1.3-py2.py3-none-any.whl", "imports": ["bigtests", "screed"], "install_dir": "site", "name": "screed", "package_type": "package", "sha256": "4e0aa7aa73d3b7482725d0b7e1854ddd5000b4b93fbba7c69320d6878a670e76", "unvendored_tests": true, "version": "1.1.3"}, "screed-tests": {"depends": ["screed"], "file_name": "screed-tests.tar", "imports": [], "install_dir": "site", "name": "screed-tests", "package_type": "package", "sha256": "4b39c82477428831cf651dcb39e00639a40f16e8c63019a95450e8b5d24c554d", "unvendored_tests": false, "version": "1.1.3"}, "setuptools": {"depends": ["pyparsing"], "file_name": "setuptools-69.5.1-py3-none-any.whl", "imports": ["_distutils_hack", "pkg_resources", "setuptools"], "install_dir": "site", "name": "setuptools", "package_type": "package", "sha256": "ee2d8b37ab8c1235c192ea6156c73f740b28730ad952e37ad6ccd34517a44402", "unvendored_tests": false, "version": "69.5.1"}, "shapely": {"depends": ["numpy"], "file_name": "shapely-2.0.6-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["shapely"], "install_dir": "site", "name": "shapely", "package_type": "package", "sha256": "d00043eb8f4f39874d7806d8901224fb71bdc3a495c89a5a3902124bf7a7905d", "unvendored_tests": true, "version": "2.0.6"}, "shapely-tests": {"depends": ["shapely"], "file_name": "shapely-tests.tar", "imports": [], "install_dir": "site", "name": "shapely-tests", "package_type": "package", "sha256": "2db411db191e6c2278d7873ebd3913d147728b3acfc3676a9a2a993b531bd809", "unvendored_tests": false, "version": "2.0.6"}, "sharedlib-test": {"depends": [], "file_name": "sharedlib-test-1.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "sharedlib-test", "package_type": "shared_library", "sha256": "8cf4f5f308c6e0ee3c2c5f7b12edbf39146916bf1725f5f4172a516bc7b92d2f", "unvendored_tests": false, "version": "1.0"}, "sharedlib-test-py": {"depends": ["sharedlib-test"], "file_name": "sharedlib_test_py-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["sharedlib_test"], "install_dir": "site", "name": "sharedlib-test-py", "package_type": "package", "sha256": "0de335a535f04cc1ffddcfae6301f39acfb618d2f23141ec3ee58fef24aaf909", "unvendored_tests": false, "version": "1.0"}, "simplejson": {"depends": [], "file_name": "simplejson-3.19.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "507150ff1c02e37add295acbf55fa46c06cd10e871f0292c65259212aca0db1b", "unvendored_tests": true, "version": "3.19.2"}, "simplejson-tests": {"depends": ["<PERSON><PERSON><PERSON>"], "file_name": "simplejson-tests.tar", "imports": [], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>-tests", "package_type": "package", "sha256": "7da8ae6e4927e3b21fa401e7dc514b8439eda185da889c4306131243041a6660", "unvendored_tests": false, "version": "3.19.2"}, "sisl": {"depends": ["pyparsing", "numpy", "scipy", "tqdm", "xarray", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "sisl-0.15.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["sisl_toolbox", "sisl"], "install_dir": "site", "name": "sisl", "package_type": "package", "sha256": "d0af4abdeccd1c0517eaede7c907bd68266338a728d2e582aa52f3d0e1d955db", "unvendored_tests": true, "version": "0.15.1"}, "sisl-tests": {"depends": ["sisl"], "file_name": "sisl-tests.tar", "imports": [], "install_dir": "site", "name": "sisl-tests", "package_type": "package", "sha256": "ea37320e834321a57923c55170d2e997448af5e835a549696364aac30a748ad8", "unvendored_tests": false, "version": "0.15.1"}, "six": {"depends": [], "file_name": "six-1.16.0-py2.py3-none-any.whl", "imports": ["six"], "install_dir": "site", "name": "six", "package_type": "package", "sha256": "dec42129a0842a566de2df35483f25c70c36120fcce95cd3776a2c8666386926", "unvendored_tests": false, "version": "1.16.0"}, "smart-open": {"depends": ["wrapt"], "file_name": "smart_open-7.0.4-py3-none-any.whl", "imports": ["smart_open"], "install_dir": "site", "name": "smart-open", "package_type": "package", "sha256": "790e31b1bf7c47e0145922514ac310ab78b738111d7e7428ffe98bfd11810ca8", "unvendored_tests": false, "version": "7.0.4"}, "sortedcontainers": {"depends": [], "file_name": "sortedcontainers-2.4.0-py2.py3-none-any.whl", "imports": ["sortedcontainers"], "install_dir": "site", "name": "sortedcontainers", "package_type": "package", "sha256": "eae2058ef13dc55a114b807abe2103c196753578bf681761bdebc750a584b4ee", "unvendored_tests": false, "version": "2.4.0"}, "soupsieve": {"depends": [], "file_name": "soupsieve-2.5-py3-none-any.whl", "imports": ["soupsieve"], "install_dir": "site", "name": "soupsieve", "package_type": "package", "sha256": "1b2386e9fa963577813a5275eea482150d3c554a7bf88ca229facf376b00cadf", "unvendored_tests": false, "version": "2.5"}, "sourmash": {"depends": ["screed", "cffi", "deprecation", "cachetools", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy", "sqlite3", "bitstring"], "file_name": "sourmash-4.8.11-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["sourmash"], "install_dir": "site", "name": "sourmash", "package_type": "package", "sha256": "907d2b3d80d5763ccef77694bcc9b131f1fb5f9991743659787b494fa82f2bfd", "unvendored_tests": false, "version": "4.8.11"}, "soxr": {"depends": ["numpy"], "file_name": "soxr-0.5.0.post1-cp312-abi3-pyodide_2024_0_wasm32.whl", "imports": ["soxr"], "install_dir": "site", "name": "soxr", "package_type": "package", "sha256": "95d658e7738ca79828e68eaa7b08881583c2f9b36de1df15b2881157f19e123f", "unvendored_tests": false, "version": "0.5.0.post1"}, "sparseqr": {"depends": ["pyc<PERSON><PERSON>", "cffi", "numpy", "scipy", "suitesparse"], "file_name": "sparseqr-1.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["sparseqr"], "install_dir": "site", "name": "sparseqr", "package_type": "package", "sha256": "d57e7ad244d42a03d54301302788e1f4c35530fee3af4486582699cf0b085afd", "unvendored_tests": false, "version": "1.2"}, "sqlalchemy": {"depends": ["sqlite3", "typing-extensions"], "file_name": "sqlalchemy-2.0.29-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["sqlalchemy"], "install_dir": "site", "name": "sqlalchemy", "package_type": "package", "sha256": "c7a8968c5baa4ed3ebf6fb8c8b778897644e4041655ebf03ad4e318a50f26a30", "unvendored_tests": true, "version": "2.0.29"}, "sqlalchemy-tests": {"depends": ["sqlalchemy"], "file_name": "sqlalchemy-tests.tar", "imports": [], "install_dir": "site", "name": "sqlalchemy-tests", "package_type": "package", "sha256": "abad3948692eca7035490ccf8864f03afda636e066a66153aeb15624092ab51a", "unvendored_tests": false, "version": "2.0.29"}, "sqlite3": {"depends": [], "file_name": "sqlite3-1.0.0-py2.py3-none-any.whl", "imports": ["sqlite3", "_sqlite3"], "install_dir": "site", "name": "sqlite3", "package_type": "cpython_module", "sha256": "983bdb719fa8ff5f54d644d35ec5136411e99f7796675a5f538896f40bba5bd1", "unvendored_tests": false, "version": "1.0.0"}, "ssl": {"depends": ["openssl"], "file_name": "ssl-1.0.0-py2.py3-none-any.whl", "imports": ["ssl", "_ssl"], "install_dir": "site", "name": "ssl", "package_type": "cpython_module", "sha256": "ab685560e77eee7bcf747e6e392a8e87a65120c18cd693b99294ffb68173d645", "unvendored_tests": false, "version": "1.0.0"}, "stack-data": {"depends": ["executing", "asttokens", "pure-eval"], "file_name": "stack_data-0.6.3-py3-none-any.whl", "imports": ["stack_data"], "install_dir": "site", "name": "stack-data", "package_type": "package", "sha256": "61e5384576ec0eb211d43c6e634b7fedd428a66479e685b36fb062325cf90d6f", "unvendored_tests": false, "version": "0.6.3"}, "statsmodels": {"depends": ["numpy", "scipy", "pandas", "patsy", "packaging"], "file_name": "statsmodels-0.14.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["statsmodels"], "install_dir": "site", "name": "statsmodels", "package_type": "package", "sha256": "51643d73dd0d0d97e6965adc25a63bb8ddad775709a4ad90b6a52f0190474598", "unvendored_tests": false, "version": "0.14.4"}, "strictyaml": {"depends": ["python-dateutil"], "file_name": "strictyaml-1.7.3-py3-none-any.whl", "imports": ["strictyaml"], "install_dir": "site", "name": "strictyaml", "package_type": "package", "sha256": "eece58eb1d454df0c0b344d8466088209d5902c4a3979b0c6297e4e1876234f4", "unvendored_tests": false, "version": "1.7.3"}, "suitesparse": {"depends": ["openblas"], "file_name": "suitesparse-5.11.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "suitesparse", "package_type": "shared_library", "sha256": "6b48a63b88bbe290cf073ff0ea748e40e8c3b12723e4cb3dd2d389a09323f1e8", "unvendored_tests": false, "version": "5.11.0"}, "svgwrite": {"depends": [], "file_name": "svgwrite-1.4.3-py3-none-any.whl", "imports": ["svgwrite"], "install_dir": "site", "name": "svgwrite", "package_type": "package", "sha256": "23344b445be8a670281f3c2972b59e3de063a21cb86ec25068cb84f8095e2e79", "unvendored_tests": false, "version": "1.4.3"}, "swiglpk": {"depends": [], "file_name": "swiglpk-5.0.10-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["swiglpk"], "install_dir": "site", "name": "swiglpk", "package_type": "package", "sha256": "5a3049d419227869b45afd28307c19cf7e61362c7dc73c06df36cdc301ba3a76", "unvendored_tests": false, "version": "5.0.10"}, "sympy": {"depends": ["mpmath"], "file_name": "sympy-1.13.3-py3-none-any.whl", "imports": ["isympy", "sympy"], "install_dir": "site", "name": "sympy", "package_type": "package", "sha256": "e210b73979b46989ea717a7d4c5fe26c70a8ffb800b636599c99bed5030a19c4", "unvendored_tests": true, "version": "1.13.3"}, "sympy-tests": {"depends": ["sympy"], "file_name": "sympy-tests.tar", "imports": [], "install_dir": "site", "name": "sympy-tests", "package_type": "package", "sha256": "ff3126c8408df84023773a2a177b50d3b4f9ba05d55e743c8aa47c2cbc861411", "unvendored_tests": false, "version": "1.13.3"}, "tblib": {"depends": [], "file_name": "tblib-3.0.0-py3-none-any.whl", "imports": ["tblib"], "install_dir": "site", "name": "tblib", "package_type": "package", "sha256": "e8f5550207a6886a41a04093028fef2723e47c2901e75a13c6ca1a11a122eecd", "unvendored_tests": false, "version": "3.0.0"}, "termcolor": {"depends": [], "file_name": "termcolor-2.4.0-py3-none-any.whl", "imports": ["termcolor"], "install_dir": "site", "name": "termcolor", "package_type": "package", "sha256": "8a0cc504d1b55625f31a026ad5ed4652f14d2f58ae3f640b8cf5b7b6b5382580", "unvendored_tests": false, "version": "2.4.0"}, "test": {"depends": [], "file_name": "test-1.0.0-py2.py3-none-any.whl", "imports": ["test"], "install_dir": "site", "name": "test", "package_type": "cpython_module", "sha256": "49986e9183d31ae7d0f1bea3555536b2d9518583f1f2bc30524143f1d6c81c05", "unvendored_tests": false, "version": "1.0.0"}, "texttable": {"depends": [], "file_name": "texttable-1.7.0-py2.py3-none-any.whl", "imports": ["texttable"], "install_dir": "site", "name": "texttable", "package_type": "package", "sha256": "4ee998f366f3d4a30cc67df4b377f612abc3b6e65e68b6786cc1759f4a54daf5", "unvendored_tests": false, "version": "1.7.0"}, "threadpoolctl": {"depends": [], "file_name": "threadpoolctl-3.5.0-py3-none-any.whl", "imports": ["threadpoolctl"], "install_dir": "site", "name": "threadpoolctl", "package_type": "package", "sha256": "11e0051e2f1bb799c66bab1adae92028260b62d3293617b73506f52163b6db31", "unvendored_tests": false, "version": "3.5.0"}, "tiktoken": {"depends": ["regex", "requests"], "file_name": "tiktoken-0.8.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tiktoken", "tiktoken_ext"], "install_dir": "site", "name": "tiktoken", "package_type": "package", "sha256": "ecae7d6a572cbd327596035f54a6f152d5533178690cc73d855d431da8aadb6d", "unvendored_tests": false, "version": "0.8.0"}, "tomli": {"depends": [], "file_name": "tomli-2.0.1-py3-none-any.whl", "imports": ["to<PERSON>li"], "install_dir": "site", "name": "to<PERSON>li", "package_type": "package", "sha256": "c4091cb54feb4ef6cf3c16fc71325653bc3446ddbe7a0472ee31f30acde6ce91", "unvendored_tests": false, "version": "2.0.1"}, "tomli-w": {"depends": [], "file_name": "tomli_w-1.0.0-py3-none-any.whl", "imports": ["tomli_w"], "install_dir": "site", "name": "tomli-w", "package_type": "package", "sha256": "66a509a3d28bdac8b3025601ece019c02d207af856e8f6f206b7454cd9db09bc", "unvendored_tests": false, "version": "1.0.0"}, "toolz": {"depends": [], "file_name": "toolz-0.12.1-py3-none-any.whl", "imports": ["tlz", "toolz"], "install_dir": "site", "name": "toolz", "package_type": "package", "sha256": "dfe9234f5e97f001668546d332e303139634aa7f3b4f20381767764a4cbe9227", "unvendored_tests": true, "version": "0.12.1"}, "toolz-tests": {"depends": ["toolz"], "file_name": "toolz-tests.tar", "imports": [], "install_dir": "site", "name": "toolz-tests", "package_type": "package", "sha256": "4e978c1ffa2b004611aa5ffced1f2f3637038d842ff0d450d801c3fb7f601d6b", "unvendored_tests": false, "version": "0.12.1"}, "tqdm": {"depends": [], "file_name": "tqdm-4.66.2-py3-none-any.whl", "imports": ["tqdm"], "install_dir": "site", "name": "tqdm", "package_type": "package", "sha256": "f42c0b122ecfa942b98d6880f95ffa99af8961e156852803614104f5b6e884d9", "unvendored_tests": false, "version": "4.66.2"}, "traitlets": {"depends": [], "file_name": "traitlets-5.14.3-py3-none-any.whl", "imports": ["traitlets"], "install_dir": "site", "name": "traitlets", "package_type": "package", "sha256": "7e927415cc96814281c1a1423de440f00c3674c617efd691bc457eb31194f9ac", "unvendored_tests": true, "version": "5.14.3"}, "traitlets-tests": {"depends": ["traitlets"], "file_name": "traitlets-tests.tar", "imports": [], "install_dir": "site", "name": "traitlets-tests", "package_type": "package", "sha256": "c29166cd74e0e82064d9d46c0119658db71a1184fa6c5f06b21bacc80d0ae5e6", "unvendored_tests": false, "version": "5.14.3"}, "traits": {"depends": [], "file_name": "traits-6.4.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["traits"], "install_dir": "site", "name": "traits", "package_type": "package", "sha256": "959502d5e4c811011cb077754ae0d9dd099e2026c1cb95af56c442ea2129bc7b", "unvendored_tests": true, "version": "6.4.3"}, "traits-tests": {"depends": ["traits"], "file_name": "traits-tests.tar", "imports": [], "install_dir": "site", "name": "traits-tests", "package_type": "package", "sha256": "4a955fd2808f9d70d4b3b3211ae504b7ebd9b5009cdb3c0918ea63016587c272", "unvendored_tests": false, "version": "6.4.3"}, "tree-sitter": {"depends": [], "file_name": "tree_sitter-0.23.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tree_sitter"], "install_dir": "site", "name": "tree-sitter", "package_type": "package", "sha256": "a3d3ec9d19fd7b02812fd89bdbc7d7aa8b0daf7456ed0be539ba58004ad9d5ec", "unvendored_tests": false, "version": "0.23.2"}, "tree-sitter-go": {"depends": ["tree-sitter"], "file_name": "tree_sitter_go-0.23.3-cp39-abi3-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tree_sitter_go"], "install_dir": "site", "name": "tree-sitter-go", "package_type": "package", "sha256": "9c76d494eb7d71c489a2b8bcf2738af17afd054bc46576b758b11d296564e167", "unvendored_tests": false, "version": "0.23.3"}, "tree-sitter-java": {"depends": ["tree-sitter"], "file_name": "tree_sitter_java-0.23.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["tree_sitter_java"], "install_dir": "site", "name": "tree-sitter-java", "package_type": "package", "sha256": "7b2010cc5dfc396aeb03b2cb5eb8409fa61fa730662ec8d5c9937495446814e4", "unvendored_tests": false, "version": "0.23.4"}, "tree-sitter-python": {"depends": ["tree-sitter"], "file_name": "tree_sitter_python-0.23.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["tree_sitter_python"], "install_dir": "site", "name": "tree-sitter-python", "package_type": "package", "sha256": "5c138f760c99784f0d855d68d59ddffd1f3e8256848ed4fa9c7279d9465887ca", "unvendored_tests": false, "version": "0.23.4"}, "tskit": {"depends": ["numpy", "svgwrite", "jsonschema", "rpds-py"], "file_name": "tskit-0.6.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tskit"], "install_dir": "site", "name": "tskit", "package_type": "package", "sha256": "236a2742046218fc6cb807cec0d9d15995df7867c605a9cfc4ec5530dd2aa068", "unvendored_tests": false, "version": "0.6.0"}, "typing-extensions": {"depends": [], "file_name": "typing_extensions-4.11.0-py3-none-any.whl", "imports": ["typing_extensions"], "install_dir": "site", "name": "typing-extensions", "package_type": "package", "sha256": "06f019e2da1427a5d5fbafb50292041df2a6fd701deb5ee55a950af58630599d", "unvendored_tests": false, "version": "4.11.0"}, "tzdata": {"depends": [], "file_name": "tzdata-2024.1-py2.py3-none-any.whl", "imports": ["tzdata"], "install_dir": "site", "name": "tzdata", "package_type": "package", "sha256": "431000930002a00f28b1209bd0058cf5f050628c218f9668d566f0f565b7aaa8", "unvendored_tests": false, "version": "2024.1"}, "uncertainties": {"depends": ["future"], "file_name": "uncertainties-3.1.7-py2.py3-none-any.whl", "imports": ["uncertainties"], "install_dir": "site", "name": "uncertainties", "package_type": "package", "sha256": "4fb985a7cc28943be089b3e637b499dc5a85fb0222f09c2fce9dc509356a8bf1", "unvendored_tests": true, "version": "3.1.7"}, "uncertainties-tests": {"depends": ["uncertainties"], "file_name": "uncertainties-tests.tar", "imports": [], "install_dir": "site", "name": "uncertainties-tests", "package_type": "package", "sha256": "77efa40c23932a3029c1a1a73937c8d528331dde5e776f1baaf0ef54af74593a", "unvendored_tests": false, "version": "3.1.7"}, "unyt": {"depends": ["numpy", "packaging", "sympy"], "file_name": "unyt-3.0.3-py3-none-any.whl", "imports": ["unyt"], "install_dir": "site", "name": "unyt", "package_type": "package", "sha256": "286a2413d3033cd60c2f5d66a25063409de4abbb6e2eff40d2fcddda7f46181b", "unvendored_tests": true, "version": "3.0.3"}, "unyt-tests": {"depends": ["unyt"], "file_name": "unyt-tests.tar", "imports": [], "install_dir": "site", "name": "unyt-tests", "package_type": "package", "sha256": "30d56dc9d34c368baeb9eb3cdf0834e2a5e2b80e55e019f3b2bb1275996bfd39", "unvendored_tests": false, "version": "3.0.3"}, "urllib3": {"depends": [], "file_name": "urllib3-2.2.3-py3-none-any.whl", "imports": ["urllib3"], "install_dir": "site", "name": "urllib3", "package_type": "package", "sha256": "a9c1cd9fab2d7ac7eaab2bf2131e42731932cf0468ca35ee194673cddfe0e75c", "unvendored_tests": false, "version": "2.2.3"}, "vega-datasets": {"depends": ["pandas"], "file_name": "vega_datasets-0.9.0-py3-none-any.whl", "imports": ["vega_datasets"], "install_dir": "site", "name": "vega-datasets", "package_type": "package", "sha256": "afee8361c641b2bf0f6b720651b6b16b84dbcf7fcb219130079965a44e6ab411", "unvendored_tests": true, "version": "0.9.0"}, "vega-datasets-tests": {"depends": ["vega-datasets"], "file_name": "vega-datasets-tests.tar", "imports": [], "install_dir": "site", "name": "vega-datasets-tests", "package_type": "package", "sha256": "981697cae099e44fa9277671be16887f161f3983619018fea3c46fe2662a2182", "unvendored_tests": false, "version": "0.9.0"}, "wcwidth": {"depends": [], "file_name": "wcwidth-0.2.13-py2.py3-none-any.whl", "imports": ["wcwidth"], "install_dir": "site", "name": "wcwidth", "package_type": "package", "sha256": "c866f293d3346f3e6e2755fef9158ef2bca35b31994c895f4bcca7f1e26741a1", "unvendored_tests": false, "version": "0.2.13"}, "webencodings": {"depends": [], "file_name": "webencodings-0.5.1-py2.py3-none-any.whl", "imports": ["webencodings"], "install_dir": "site", "name": "webencodings", "package_type": "package", "sha256": "2e4e365072d18658bd0e6b92ea63fb3f8671e219220ec492ba2dabcc9358c1c6", "unvendored_tests": false, "version": "0.5.1"}, "wordcloud": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "wordcloud-1.9.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["wordcloud"], "install_dir": "site", "name": "wordcloud", "package_type": "package", "sha256": "f3f72976aa158ac9650542992fb92aef4783ecbf5caffeb933ce4c4f750031e1", "unvendored_tests": false, "version": "1.9.3"}, "wrapt": {"depends": [], "file_name": "wrapt-1.16.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["wrapt"], "install_dir": "site", "name": "wrapt", "package_type": "package", "sha256": "18fb084e9d128b8b8485ec09a6145905f40901825d6878cf271bf5f791926a54", "unvendored_tests": false, "version": "1.16.0"}, "xarray": {"depends": ["numpy", "packaging", "pandas"], "file_name": "xarray-2024.11.0-py3-none-any.whl", "imports": ["xarray"], "install_dir": "site", "name": "xarray", "package_type": "package", "sha256": "4824cedbcc4cd955dd0d51d19378e08af10e5a2b33106166a3e6362f1d9fb51a", "unvendored_tests": true, "version": "2024.11.0"}, "xarray-tests": {"depends": ["xarray"], "file_name": "xarray-tests.tar", "imports": [], "install_dir": "site", "name": "xarray-tests", "package_type": "package", "sha256": "13619b56e2daa7d10b7ed511c515338ff2a937411cdeb525a2a62887eecf4192", "unvendored_tests": false, "version": "2024.11.0"}, "xgboost": {"depends": ["numpy", "scipy", "setuptools"], "file_name": "xgboost-2.1.2-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["xgboost"], "install_dir": "site", "name": "xgboost", "package_type": "package", "sha256": "ed97ec094ba53b66cf9b1207ccd1247c923ad5e73d4acea57c2121fac68b76d1", "unvendored_tests": false, "version": "2.1.2"}, "xlrd": {"depends": [], "file_name": "xlrd-2.0.1-py2.py3-none-any.whl", "imports": ["xlrd"], "install_dir": "site", "name": "xlrd", "package_type": "package", "sha256": "9583caaa30861e72a1fea543557b2825c4c2698a56fac9a0f6d78f48026de554", "unvendored_tests": false, "version": "2.0.1"}, "xxhash": {"depends": [], "file_name": "xxhash-3.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["xxhash"], "install_dir": "site", "name": "xxhash", "package_type": "package", "sha256": "db9e71a64e455bb9634c4974e5d8b28fd77e9bf2ccac9d7c64c1c663f47c74cf", "unvendored_tests": false, "version": "3.4.1"}, "xyzservices": {"depends": [], "file_name": "xyzservices-2024.4.0-py3-none-any.whl", "imports": ["xyzservices"], "install_dir": "site", "name": "xyzservices", "package_type": "package", "sha256": "bc067e4c613fd64656b165db9f9310cf3b0ae76690178564db19c003b958f874", "unvendored_tests": true, "version": "2024.4.0"}, "xyzservices-tests": {"depends": ["xyzservices"], "file_name": "xyzservices-tests.tar", "imports": [], "install_dir": "site", "name": "xyzservices-tests", "package_type": "package", "sha256": "a6a3d537feb9f4b72d6ae1ad51307a8924ed5b207a619a6cdcdc98ea0706a4e2", "unvendored_tests": false, "version": "2024.4.0"}, "yarl": {"depends": ["multidict", "idna"], "file_name": "yarl-1.9.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["yarl"], "install_dir": "site", "name": "yarl", "package_type": "package", "sha256": "1dddd1ceb1be4fe53fbb59abd0e5a4322e96733d8ef54b15ecad6cc9f3443e81", "unvendored_tests": false, "version": "1.9.4"}, "yt": {"depends": ["ewah_bool_utils", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sympy", "setuptools", "packaging", "unyt", "cmyt", "colorspacious", "tqdm", "to<PERSON>li", "tomli-w"], "file_name": "yt-4.3.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["yt"], "install_dir": "site", "name": "yt", "package_type": "package", "sha256": "d96575b1ca5e7b60cc0c47bb5e8a9484c33ce86af4c9c150dcc5f3ec040067eb", "unvendored_tests": false, "version": "4.3.1"}, "zarr": {"depends": ["numpy", "as<PERSON><PERSON><PERSON>", "numcodecs"], "file_name": "zarr-2.18.3-py3-none-any.whl", "imports": ["zarr"], "install_dir": "site", "name": "zarr", "package_type": "package", "sha256": "4e0f9e6ed6a89ba8049039ec35098f577abd9d2eea30c1c3ad52f5134bd9dc0e", "unvendored_tests": true, "version": "2.18.3"}, "zarr-tests": {"depends": ["zarr"], "file_name": "zarr-tests.tar", "imports": [], "install_dir": "site", "name": "zarr-tests", "package_type": "package", "sha256": "9c8f1b6f806895e2bcee21a8f72884271b11201238c123e06c00d129c5f5515f", "unvendored_tests": false, "version": "2.18.3"}, "zengl": {"depends": [], "file_name": "zengl-2.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zengl", "_zengl"], "install_dir": "site", "name": "zengl", "package_type": "package", "sha256": "a03f1a39ce0f0bdc207b658afa568ba9b594db2cfaf7f090c5b187c24ae0e59b", "unvendored_tests": false, "version": "2.7.1"}, "zfpy": {"depends": ["numpy"], "file_name": "zfpy-1.0.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zfpy"], "install_dir": "site", "name": "zfpy", "package_type": "package", "sha256": "51095cc2d359d5dc707758ae406955ee01df4db161b41d5eef448b004ced6b0f", "unvendored_tests": false, "version": "1.0.1"}, "zstandard": {"depends": ["cffi"], "file_name": "zstandard-0.22.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zstandard"], "install_dir": "site", "name": "zstandard", "package_type": "package", "sha256": "ee0c5176ad68915b9600c5a2dc84924e975c50055d334a966be7fff7e4584b59", "unvendored_tests": false, "version": "0.22.0"}}}