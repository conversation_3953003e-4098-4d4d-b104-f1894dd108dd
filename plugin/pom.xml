<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.polarizon.rag</groupId>
        <artifactId>ai-app-ms</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath> <!-- 可忽略 -->
    </parent>

    <name>ai-app-ms-plugin</name>
    <description>rag: ai-app</description>
    <organization>
        <name>天海宸光 Polarizon</name>
        <url>https://www.polarizon.com/</url>
    </organization>

    <groupId>com.polarizon.rag</groupId>
    <artifactId>ai-app-plugin</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.sourceOld>17</maven.compiler.sourceOld>
        <maven.compiler.target>17</maven.compiler.target>
        <biz.server.port>8022</biz.server.port>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-doc.version>2.0.2</spring-doc.version>
        <lang3.version>3.12.0</lang3.version>
        <collections4.version>4.4</collections4.version>
        <guava.version>31.1-jre</guava.version>
        <fastjson.version>1.2.80</fastjson.version>
        <okhttp.version>4.9.3</okhttp.version>
        <httpclient.version>4.5.13</httpclient.version>
        <hutool-version>5.6.5</hutool-version>
        <mongock.version>5.3.4</mongock.version>
        <redisson.version>3.24.3</redisson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.mongock</groupId>
                <artifactId>mongock-bom</artifactId>
                <version>${mongock.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

         <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool-version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
            <version>${hutool-version}</version>
        </dependency>

        <!-- commons-collections4 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${collections4.version}</version>
        </dependency>

        <!-- commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${lang3.version}</version>
        </dependency>

        <!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>1.7.0</version>
        </dependency>

        <!-- okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <!-- HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>

        <!-- spring apo -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- mongock -->
        <dependency>
            <groupId>io.mongock</groupId>
            <artifactId>mongodb-springdata-v4-driver</artifactId>
        </dependency>
        <dependency>
            <groupId>io.mongock</groupId>
            <artifactId>mongock-springboot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        
        <!-- lib库 -->
        <dependency>
            <groupId>com.polarizon.rag</groupId>
            <artifactId>kb-manage-api</artifactId>
            <version>1.3.0-beta.0</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>user-authority-api</artifactId> 
            <version>1.14.0-zhiku-auth-beta.6-alpha</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>push-message-api</artifactId>
            <version>1.3.0-rag-training-beta.4</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo.faas</groupId>
            <artifactId>common-lib</artifactId>
            <version>4.8.1</version>
        </dependency>
        <!-- public-common-lib -->
        <dependency>
            <groupId>com.polarizon.public</groupId>
            <artifactId>common-lib</artifactId>
            <version>1.0.164-beta.0</version>
        </dependency>
        
        <dependency>
            <groupId>com.polarizon.rag</groupId>
            <artifactId>ai-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- common-lib -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>common-lib</artifactId>
            <version>1.0.9</version>
            <optional>true</optional>
        </dependency>
        <!-- api引入 -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>constant-collection-api</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.phantomthief</groupId>
            <artifactId>buffer-trigger</artifactId>
            <version>0.2.21</version>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.aventrix.jnanoid</groupId>
            <artifactId>jnanoid</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!--JWT-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>
        <!--解决升级jdk版本，Handler dispatch failed;nested exception is java.lang.NoClassDefFoundError: javax/xml/bind/DatatypeConverter-->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.4</version>
        </dependency>

        <dependency>
            <groupId>com.unfbx</groupId>
            <artifactId>chatgpt-java</artifactId>
            <version>1.1.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.samskivert/jmustache -->
        <dependency>
            <groupId>com.samskivert</groupId>
            <artifactId>jmustache</artifactId>
            <version>1.15</version>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.18.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.haibiiin</groupId>
            <artifactId>json-repair</artifactId>
            <version>0.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.rag</groupId>
            <artifactId>chenqi-helper-api</artifactId>
            <version>1.4.0-zhiku-beta.6</version>
        </dependency>

    </dependencies> 

    <!--指定使用maven打包-->
	<build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 分发配置，必须与 settings.xml 的 id 一致 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://192.168.20.201:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://192.168.20.201:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
