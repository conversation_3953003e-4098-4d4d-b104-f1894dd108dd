chat:
  max-concurrent-tasks: "${CHAT.MAX_CONCURRENT_TASKS:5}"
  default-max-token: "${CHAT.DEFAULT_MAX_TOKEN:8192}"
  use-reranker: true
  default-rerank-top-k: 4
  # api 1200 q/min, 暂时设置为15
  # 这个是业务处理的线程数，要大于llmMaxRequests
  llm-thread-pool-core-pool-size: 200
  common-thread-pool-core-pool-size: 120
  # 这个是整个应用，大模型调用客户端的并发数
  #  对于长上下文的调用，太高的并发反而会因为过多的prefill消耗kvcache而限制生成吞吐量，一般benchmark测试60并发就能达到模型最大吞吐量了，再高吞吐量就会掉了
  llm-max-requests: 30
  inspectMaxChunkSize: "${CHAT.INSPECT_MAX_CHUNK_SIZE:20}"
  process:
    string-replacements:
      - from: "&amp;"
        to: "&"
      - from: "&lt;"
        to: "<"
      - from: "&gt;"
        to: ">"
      - from: "&quot;"
        to: "\""
      - from: "&apos;"
        to: "'"
      - from: "\\\\*"
        to: "*"
      - from: "\\*"
        to: "*"
#       全角字符替换为半角字符的规则
      - from: "，"
        to: ","
      - from: "："
        to: ":"
      - from: "［"
        to: "["
      - from: "］"
        to: "]"
      - from: "｛"
        to: "{"
      - from: "｝"
        to: "}"
      - from: "＂"
        to: "\""
      - from: "＇"
        to: "'"
    # 为了保证效果，不出现额外的字符，同种情况下，符号多的写前面优先匹配，不同情况，
    thought-response-pattern-list:
      - "(?s)([\\s\\S]*```\\s*response\\s*)(\\S+[\\s\\S]*)"
    endFlags:
      - "\n```"
      - "```" # 这个放最后

# 联网检索
# TODO 改成 tool.websearch
websearch:
  bochaai:
    api-key: ${BOCHA_AI_API_KEY:sk-64a51a771fa14727acc7ed84532cfeb0}
  rate-limit:
    enabled: true
    second: "1 4 300" # 1分钟内最多请求 300 次，超时设置为 300 s
    minute: "60 190 300" # 1小时内最多请求 190 次，超时设置为 300 s
    hour: "86400 9800 300" # 1天内最多请求 9800 次，超时设置为 300 s
      
spring:
  mvc:
    async:
      request-timeout: 600000  # 设置异步请求超时时间为10分钟（复现，如果非流式回答，会超时异常）

resilience4j:
  retry:
#    configs:
#      default:
#          maxAttempts: 1  
    instances:
      instructGenerate:
        maxAttempts: 5
        waitDuration: 200
        retryExceptions:
          - com.polarizon.rag.plugin.exception.MyJsonParseException
          - com.polarizon.rag.plugin.exception.EmptyException
      bocha:
        maxAttempts: 5
        enableExponentialBackoff: true
        # nextWaitTime = Min(MaxWaitDuration,  waitDuration * (multiplier ^ (attempt - 1)) )
        exponentialBackoffMultiplier: 2
        exponentialMaxWaitDuration: 20s
        waitDuration: 2000
        retryExceptions:
          - com.polarizon.rag.plugin.exception.TooManyRequestsException

# 默认知识库配置
model-identity:
  description: "我是{{modelDisplayName}}，我可以帮助你回答问题、撰写文档等。欢迎随时向我咨询"
  # 默认知识库ID，可通过环境变量 KNOWLEDGE_BASE_DEFAULT_KB_ID 覆盖
  default-kb-id: ${KNOWLEDGE_BASE_DEFAULT_KB_ID:} 