package com.polarizon.rag.plugin.tool;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.polarizon.rag.plugin.bean.dto.websearch.WebSearchParam;
import com.polarizon.rag.plugin.bean.dto.websearch.WebSearchResponse;
import com.polarizon.rag.plugin.bean.dto.websearch.WebSearchResultDTO;
import com.polarizon.rag.plugin.config.BochaAiDualRateLimit;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.exception.LowBalanceRequestsException;
import com.polarizon.rag.plugin.exception.TooManyRequestsException;
import com.polarizon.rag.plugin.util.FeignUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;

@Service
@Slf4j
public class BochaWebTool {

    @Autowired
    private BochaAiDualRateLimit bochaAiDualRateLimit;

    @Retry(name = "bocha", fallbackMethod = "fallbackForBochaWebSearch")
    public List<WebSearchResponse.WebPageValue> search(Integer count, String question) {
        Callable<List<WebSearchResponse.WebPageValue>> listCallable =
            bochaAiDualRateLimit.dualLimiterCall(() ->
                webSearchSummary(count, question)
            );
        try {
            return listCallable.call();
        } catch (TooManyRequestsException | LowBalanceRequestsException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessExecutionException("bocha 联网检索出错, msg: " + e.getMessage(), e);
        }
    }

    private List<WebSearchResponse.WebPageValue> webSearchSummary(Integer count, String question) {
        if (StringUtils.isBlank(question)) {
            return new ArrayList<>();
        }
        WebSearchParam param = WebSearchParam.of(question, count);
        log.debug("werSearchChunksParm:{}", JSONUtils.getJacksonJsonString(param));
        WebSearchResultDTO<WebSearchResponse> webSearchResponse = FeignUtil.getWebSearchClient().webSearch(param);
        if (Objects.isNull(webSearchResponse)) {
            throw new BusinessExecutionException("webSearch error!");
        }
        Integer code = webSearchResponse.getCode();
        if (code == 429) {
            throw new TooManyRequestsException("Too many requests");
        } else if (code == 403) {
            throw new LowBalanceRequestsException("webSearch low balance");
        } else if (code != 200) {
            throw new BusinessExecutionException(StrUtil.format("webSearch error!code: {},msg: {}", code,
                webSearchResponse.getMsg()));
        }
        WebSearchResponse.WebSearchWebPages webPages = webSearchResponse.getData().getWebPages();
        return webPages.getValue();
    }

    // fallback 方法：注意参数顺序和类型需匹配原始方法 + Throwable
    public List<WebSearchResponse.WebPageValue> fallbackForBochaWebSearch(Integer count, String question,
                                                                          Throwable t) throws Throwable {
        log.warn("Fallback triggered for question: {}, error: {}", question, t.getMessage());
        throw t;
//        return new ArrayList<>(); // 返回空列表作为降级结果
    }
}
