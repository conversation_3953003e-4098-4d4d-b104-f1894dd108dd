package com.polarizon.rag.plugin.service.instruct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.instruct.OtherConfig;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterResult;
import com.polarizon.rag.plugin.config.ModelIdentityConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.interceptor.AnswerModeInterceptor;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.tool.RetrievalHandleService;
import com.polarizon.rag.plugin.tool.llm.LlmHandleService;
import com.polarizon.rag.plugin.util.FluxUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class InstructChatService {
    @Autowired
    private RetrievalHandleService retrievalHandleService;
    @Autowired
    private LlmHandleService llmHandleService;
    @Autowired
    private AnswerModeInterceptor answerModeInterceptor;
    @Autowired
    private ChatHelper chatHelper;

    /**
     * 公共的指令对话
     */
    public void commonInstructChat(InstructChatContext iCC) {
        InstructDO instructDO = iCC.getInstructDO();
        
        // 强制设置回答模式为MUST_CONTAINS_REFERENCE
        ConversationConfig config = iCC.getConversationConfig();
        if (config != null) {
            iCC.setConversationConfig(answerModeInterceptor.enforceReferenceMode(config));
        }
        
        Boolean needSearch = checkSearchNeed(instructDO);
        GenerateParam.Sources sources = new GenerateParam.Sources();
        if (needSearch) {
            Pair<QueryRewriterResult, GenerateParam.Sources> retrievalResult =
                retrievalHandleService.multiRetrieve(iCC, true, 0);
            sources = retrievalResult.getValue();
            iCC.setQueryRewriterResult(retrievalResult.getKey());
            
            // 处理模型身份查询
            QueryRewriterResult queryRewriterResult = retrievalResult.getKey();
            if (queryRewriterResult != null && Boolean.TRUE.equals(queryRewriterResult.getModelIdentityQuery())) {
                
                // 使用工具类处理模型身份查询的流式返回
                chatHelper.streamModelIdentityAnswer(iCC);
                return;
            } 

            if (CollectionUtil.isEmpty(sources.getAllSources())) {
                throw new BusinessExecutionException("知识为空，终止任务!");
            }
        }

        iCC.getGenerateParam().setSources(sources);
        // needAnswer 在controller 那里控制，这里统一流式处理
        EventSource eventSource =
            llmHandleService.handleLlmCall(iCC, true, true, new CompletableFuture<>());
        iCC.setRealEventSource(eventSource);
    }

    /**
     * 检查指令是否需要检索知识
     */
    private Boolean checkSearchNeed(InstructDO instructDO) {
        OtherConfig otherConfig = instructDO.getOtherConfig();
        Assert.notNull(otherConfig, () -> new BusinessExecutionException("otherConfig is null"));
        return otherConfig.getIsSearch();
    }
}
