package com.polarizon.rag.plugin.algoapp;

import com.alibaba.fastjson.JSON;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.plugin.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.exception.SystemBusyExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.util.Constants.STREAM_ANSWER;

@Slf4j
public class ChatHelper {


    @NotNull
    static Flux<ServerSentEvent<String>> buildErrorRespData(Throwable e) {
        Integer status = ResultDTO.Constants.FAILURE;
        if (e instanceof SystemBusyExecutionException) {
            status = SystemBusyExecutionException.STATUS_CODE;
        }
        return Flux.just(ServerSentEvent.<String>builder().event(STREAM_ANSWER)
            .data(JSON.toJSONString(NewResultDTO.error(status, e.getMessage(), null, MDC.get(TRACE_ID)))).build());
    }

    static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer) {
        return doSinkNext(content, sink, answer, MDC.get(TRACE_ID));
    }

    static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer,
                                              String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(answer)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

}
