package com.polarizon.rag.plugin.common.util;

import com.polarizon.gendo.common.config.namespace.NamespaceProvider;
import com.polarizon.gendo.common.util.AwsUtils;
import com.polarizon.gendo.common.util.KafkaUtil;
import com.polarizon.gendo.common.util.SpringBeanUtil;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.Objects;

/**
 * @Description 获取Bean实例工具类
 * @create 2023-06-28 15:26
 */
public class GetBeanHelper {
    /**
     * @Description 单例模式获取AwsUtils实例
     */
    private volatile static AwsUtils awsUtils;

    /**
     * @Description 单例模式获取KafkaUtil实例
     */
    private volatile static KafkaUtil kafkaUtil;


    /**
     * @Description 获取AwsUtils实例
     * @return
     */
    public static AwsUtils getAwsUtils() {
        if (awsUtils == null) {
            synchronized (AwsUtils.class) {
                if (awsUtils == null) {
                    awsUtils = SpringBeanUtil.getBean(AwsUtils.class);
                }
            }
        }
        return awsUtils;
    }

    /**
     * @Description 获取KafkaUtil实例
     * @return
     */
    public static KafkaUtil getKafkaUtil() {
        if (kafkaUtil == null) {
            synchronized (KafkaUtil.class) {
                if (kafkaUtil == null) {
                    kafkaUtil = SpringBeanUtil.getBean(KafkaUtil.class);
                }
            }
        }
        return kafkaUtil;
    }

    public static MongoTemplate mongoTemplate;
    /**
     * @Description 获取MongoTemplate
     * @return
     */
    public static MongoTemplate getMongoTemplate() {
        synchronized (MongoTemplate.class) {
            if (mongoTemplate == null) {
                mongoTemplate = SpringBeanUtil.getBean(MongoTemplate.class);
            }
        }
        return mongoTemplate;
    }
    /**
     * 命名空间提供者
     */
    private static NamespaceProvider namespaceProvider;

    /**
     * 获取名称空间提供者
     */
    public static NamespaceProvider getNamespaceProvider() {
        if (Objects.isNull(namespaceProvider)) {
            synchronized (NamespaceProvider.class) {
                if (Objects.isNull(namespaceProvider)) {
                    namespaceProvider = SpringBeanUtil.getBean(NamespaceProvider.class);
                }
            }
        }
        return namespaceProvider;
    }
}
