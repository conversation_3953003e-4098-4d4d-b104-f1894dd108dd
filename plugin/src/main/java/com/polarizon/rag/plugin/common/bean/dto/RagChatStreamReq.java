package com.polarizon.rag.plugin.common.bean.dto;

import java.util.List;

import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.RetrievalConfig;
import com.polarizon.rag.instruct.PromptTemplateParam;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatHistory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "聊天请求参数")
public class RagChatStreamReq {

    @ApiModelProperty(value = "对话配置", required = true)
    private ConversationConfig conversationConfig;

    @ApiModelProperty(value = "检索配置", required = true)
    private RetrievalConfig retrievalConfig;

    @ApiModelProperty(value = "用户的问题", required = true)
    private String question;

    @ApiModelProperty(value = "是否使用流式响应", required = false)
    private boolean stream = true;

    @ApiModelProperty(value = "历史消息列表", required = false)
    private List<ChatHistory> history;

    @ApiModelProperty(value = "参数列表", required = false)
    private List<PromptTemplateParam> parameters;

    @ApiModelProperty(value = "对话消息ID", required = true)
    private String messageId;
}