package com.polarizon.rag.plugin.application.writing.faas;

import com.alibaba.fastjson.JSONObject;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.aiapp.webdoc.WebDocDO;
import com.polarizon.rag.aiapp.webdoc.feign.WebDocDOFeign;
import com.polarizon.rag.chat.ConversationDO;
import com.polarizon.rag.chat.ConversationFileDO;
import com.polarizon.rag.chat.feign.ConversationDOFeign;
import com.polarizon.rag.plugin.application.common.service.ConversationFileService;
import com.polarizon.rag.plugin.application.writing.bean.AssistantParam;
import com.polarizon.rag.plugin.application.writing.bean.ReportParam;
import com.polarizon.rag.plugin.application.writing.bean.WritingChatParam;
import com.polarizon.rag.plugin.application.writing.config.WebdocParamConfig;
import com.polarizon.rag.plugin.application.writing.service.WebDocWritingService;
import com.polarizon.rag.plugin.application.writing.service.WebdocAssistantService;
import com.polarizon.rag.plugin.common.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.common.service.chat.ChatService;
import com.polarizon.rag.plugin.common.util.Constants;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.polarizon.rag.plugin.common.util.Constants.X_ACCOUNT_HEADER_NAME;

@Slf4j
@Component
@Validated
@RestController
@RequestMapping(path = "/webdoc")
public class WebDocWritingFaas {

    @Autowired
    private WebDocDOFeign webDocDOFeign;
    @Autowired
    private ConversationDOFeign conversationDOFeign;
    @Autowired
    private ChatService chatService;
    @Autowired
    private WebDocWritingService webDocWritingService;
    @Autowired
    private WebdocAssistantService webdocAssistantService;
    @Autowired
    private ConversationFileService conversationFileService;
    /**
     * 默认的RAG文件块
     */
    @Autowired
    private WebdocParamConfig webdocParamConfig;


    /**
     * 获取webdoc相关的parmas
     */
    @PostMapping(path = "/params")
    public ResultDTO<JSONObject> getReportParams() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("report", webdocParamConfig.getReport());
        jsonObject.put("assistant", webdocParamConfig.getAssistant());
        return ResultDTO.ok("success", jsonObject);
    }

    /**
     * 写报告
     */
    @PostMapping(path = "/reportStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> webdocReport(@RequestHeader(Constants.X_ACCOUNT_HEADER_NAME) String userAccount,
                                                      @RequestBody @Valid ReportParam param) throws IOException {
        ConversationDO conversationDO = checkConversationDO(param.getWebdocId(), userAccount);
        // 更新webdocIsTmp 为 false
        webDocWritingService.updateWebdocIsTmp(param.getWebdocId(), userAccount, false);
        return webDocWritingService.reportStream(param.getWebdocId(), userAccount, param, conversationDO);
    }

    /**
     * 写作助手
     */
    @PostMapping(path = "/assistant", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> webdocAssistant(@RequestHeader(Constants.X_ACCOUNT_HEADER_NAME) String userAccount,
                                                         @RequestBody @Valid AssistantParam param) throws IOException {
        ConversationDO conversationDO = checkConversationDO(param.getWebdocId(), userAccount);

        return webdocAssistantService.assistantStream(userAccount, param, conversationDO);
    }

    /**
     * 写作文档的对话
     */
    @PostMapping(path = "/chatStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> chatStream(@RequestHeader(Constants.X_ACCOUNT_HEADER_NAME) String userAccount,
                                                    @RequestBody @Valid WritingChatParam param) {
        ConversationDO conversationDO = checkConversationDO(param.getWebdocId(), userAccount);

        return chatService.chatStream(userAccount, param, conversationDO);
    }

    @PostMapping(path = "/{webdocId}/conversation/{conversationId}/file")
    public ResultDTO<List<? extends ConversationFileDO>> webdocConversationFile(@RequestHeader(X_ACCOUNT_HEADER_NAME) String userAccount,
                                                                                @PathVariable("webdocId") String webdocId,
                                                                                @PathVariable("conversationId") String conversationId,
                                                                                @RequestParam("files") MultipartFile[] multipartFiles) throws IOException {
        // check webdocId
        checkConversationDO(webdocId, userAccount);

        return ResultDTO.ok("success", conversationFileService.conversationFileUpload(userAccount, conversationId, multipartFiles));
    }


    private ConversationDO checkConversationDO(String webdocId, String userAccount) {
        // 获取 webdoc
        WebDocDO webDocDO = webDocDOFeign.findOneByID(null, null, webdocId).getData();
        if (Objects.isNull(webDocDO)) {
            throw new BusinessExecutionException("请选择写作文档，webdocId：" + webdocId);
        }
        // 判断是否是该用户的数据
        if (!Objects.equals(webDocDO.getOwnerID(), userAccount)) {
            throw new BusinessExecutionException("请选择自己的写作文档");
        }

        // 获取对话信息
        ConversationDO conversationDO = conversationDOFeign.findOneByID(null, null, webDocDO.getConversationID()).getData();
        if (Objects.isNull(conversationDO)) {
            throw new BusinessExecutionException("请选择会话框");
        }

        return conversationDO;
    }


}
