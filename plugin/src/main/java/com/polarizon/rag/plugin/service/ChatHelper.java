package com.polarizon.rag.plugin.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.tokenizers.Tokenizer;
import com.alibaba.dashscope.tokenizers.TokenizerFactory;
import com.alibaba.fastjson.JSON;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.ModelEnableEnum;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.ModelTypeEnum;
import com.polarizon.rag.chat.ChatModeConfig;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.KbTypeEnum;
import com.polarizon.rag.chat.MessageRoleEnum;
import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.instruct.OtherConfig;
import com.polarizon.rag.instruct.ParamTypeEnum;
import com.polarizon.rag.instruct.PromptTemplateParam;
import com.polarizon.rag.params.ModelInfoDOParams;
import com.polarizon.rag.plugin.bean.constant.Constants;
import com.polarizon.rag.plugin.bean.constant.InstructConstants;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.instruct.InstructChatParam;
import com.polarizon.rag.plugin.bean.enums.ChatModeEnum;
import com.polarizon.rag.plugin.config.ModelIdentityConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.tool.llm.ChatCompletionUsage;
import com.polarizon.rag.plugin.util.FeignUtil;
import com.polarizon.rag.plugin.util.FluxUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import com.unfbx.chatgpt.entity.chat.ResponseFormat;
import io.pebbletemplates.pebble.PebbleEngine;
import io.pebbletemplates.pebble.template.PebbleTemplate;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.FluxSink;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.X_TRACE_ID_HEADER_NAME;
import static com.polarizon.rag.plugin.bean.constant.InstructConstants.SEARCH_HELP_ME_SEARCH_CODE;
import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;

/**
 * 各种关于chat 的公共方法。 它不依赖其它服务；被其它服务依赖！
 */
@Slf4j
@Service
public class ChatHelper {
    @Autowired
    private ModelIdentityConfig modelIdentityConfig;
    // 输出窗口检测大小，匹配 "```" 的 buffer 窗口大小
    public static final String END_FLAG = "```";
    public static final int LLM_OUTPUT_CHECK_WINDOWS_SIZE = END_FLAG.length();
    public static int DEFAULT_MAX_TOKEN = 8192;
    public static String defaultSystemPrompt;
    public static String defaultUserPrompt;
    public static String defaultNoChunkUserPrompt;
    public static String defaultNoChunkSystemPrompt;
    public static Integer defaultRerankTopK;
    public static List<String> endFlags;

    public static int llmOutputCheckWindowsSize;
    public static int inspectMaxChunkSize = 20;
    private static  final PebbleEngine engine = new PebbleEngine.Builder()
        // 不进行转义 
        .autoEscaping(false)
        // 检查变量是否确实
        .strictVariables(true)
        // 保留变量后的换行符
        .newLineTrimming(false)
        .build();

    /**
     * 构建完整的答案 params:
     * 
     * @param param 聊天参数
     * @param resp 回答内容
     * @param style 回答风格
     * @param chatContext 事件回调参数
     * @return 完整的答案
     */
    @NotNull
    public static ChatRes buildCompleteAnswer(ChatParam param, String resp,  String style,
        BaseChatContext chatContext) {
        StringBuffer reasoningAnswerSB = chatContext.getReasoningAnswerSB();
        if (reasoningAnswerSB == null) {
            reasoningAnswerSB = new StringBuffer();
        }
        return FluxUtil.buildCompleteAnswer(param, resp,  style, chatContext, reasoningAnswerSB);
    }

    /**
     * 获取question
     */
    public static String extractQuestion(InstructChatParam instructChatParam, InstructDO instructDO) {
        OtherConfig otherConfig = instructDO.getOtherConfig();
        if (!otherConfig.getIsSearch()) {
            return null;
        }

        return instructChatParam.getInstructParam().getPromptTemplate().getDisplayParams().stream()
            .filter(param -> Objects.equals(param.getName(), otherConfig.getSearchKey())).findFirst()
            .map(PromptTemplateParam::getValue).orElse(null);
    }

    public static ModelInfoDO getLlmModelInfoByConversationConfig(ConversationConfig conversationConfig) {
        if (conversationConfig == null || conversationConfig.getModelConfigs() == null || StringUtils.isBlank(conversationConfig.getModelConfigs().getLlmModelInfoID())) {
            throw new BusinessExecutionException("大模型配置为空");
        }
        String modelInfoID = conversationConfig.getModelConfigs().getLlmModelInfoID();
        ResultDTO<ModelInfoDO> resultDTO = FeignUtil.getModelInfoDOFeign().findOneByID(null, null, modelInfoID);
        return FeignUtil.getDataNonNull(resultDTO, "modelInfo 不存在，modelInfoID：" + modelInfoID);
    }

    public static void setTraceId(@NotNull EventSource eventSource) {
        MDC.put(TRACE_ID, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
    }

    public static void setTraceId(String traceID) {
        MDC.put(TRACE_ID, traceID);
    }

    @NotNull
    public static List<Message> buildRagMessages(GenerateParam generateParam) {
        PromptTemplateParam questionParam = ChatHelper.buildPromptTemplateInputParam(InstructConstants.QUERY, generateParam.getQuestion());
        return InstructHelper.buildInstructChatMessage(generateParam.getPromptTemplate(), List.of(questionParam), SEARCH_HELP_ME_SEARCH_CODE,
            generateParam.getSources(), InstructConstants.REFERENCE_KEY, generateParam.getHistory());

        // List<Message> messages = new ArrayList<>();
        // // 系统提示词
        // Message sysMsg = Message.builder()
        // .role(Message.Role.SYSTEM)
        // .content(generateParam.getSystemPrompt())
        // .build();
        // messages.add(sysMsg);
        //
        // // 历史上下文消息
        // if (CollectionUtils.isNotEmpty(generateParam.getHistory())) {
        // generateHistory(messages, generateParam.getHistory());
        // }
        //
        // // 当前提问
        // String context = chunksToString(generateParam.getSources(), 0);
        // Message message = Message.builder()
        // .role(Message.Role.USER)
        // .content(generateParam.getUserPrompt()
        // .replace("{{context}}", context)
        // .replace("{{question}}", generateParam.getQuestion())
        // .replace("{{ context }}", context)
        // .replace("{{ question }}", generateParam.getQuestion()))
        // .build();
        // messages.add(message);
        // return messages;
    }

    /**
     * 知识优先级：临时>知识库>联网
     */
    @NotNull
    public static String chunksToString(GenerateParam.Sources sources, Integer startNum) {
        List<ChatRes.SourceBOResp> allSources = sources.getAllSources();
        if (CollectionUtils.isEmpty(allSources)) {
            return "";
        }
        return IntStream.range(0, allSources.size())
            .mapToObj(i -> {
                Integer sourceNumber = (i + startNum + 1);
                ChatRes.SourceBOResp sourceBOResp = allSources.get(i);
                sourceBOResp.setSourceNumber(sourceNumber);
                if (KbTypeEnum.KB_CHUNK.equals(sourceBOResp.getKbType()) || KbTypeEnum.TMP_KB_CHUNK.equals(sourceBOResp.getKbType())) {
                    return "资料[" + sourceNumber + "]:" + sourceBOResp.getContent() + "\n";
                } else if (KbTypeEnum.WEB_SEARCH_CHUNK.equals(sourceBOResp.getKbType())) {
                    return "资料[" + sourceNumber + "]:" + sourceBOResp.getSummary() + "\n";
                } else {
                    throw new BusinessExecutionException("未知的KbTypeEnum类型：" + sourceBOResp.getKbType());
                }
            })
            .collect(Collectors.joining());
    }

    @NotNull
    public static Message buildReplacedMessage(Message.Role role, String content,
        List<PromptTemplateParam> promptTemplateParams,
        List<PromptTemplateParam> displayParams, GenerateParam.Sources sources,
        String referenceKey) {
        if (CollectionUtils.isNotEmpty(promptTemplateParams)) {
            Map<String, Object> replaceMap = buildReplaceMap(displayParams, sources, referenceKey, promptTemplateParams);
            content = compileTemplate(content,replaceMap);
        }
        return Message.builder()
            .role(role)
            .content(content)
            .build();
    }

    private static String compileTemplate(String templateStr, Map<String, Object> context) {
        PebbleTemplate template = engine.getLiteralTemplate(templateStr);

        StringWriter writer = new StringWriter();
        try {
            template.evaluate(writer, context);
        } catch (Exception e) {
            throw new BusinessExecutionException("模板解析失败", e);
        }
        return writer.toString();

    }

    @NotNull
    private static Map<String, Object> buildReplaceMap(List<PromptTemplateParam> displayParams, GenerateParam.Sources sources,
        String referenceKey, List<PromptTemplateParam> promptTemplateParams) {
        Map<String, Object> replaceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(promptTemplateParams)) {
            replaceMap = promptTemplateParams.stream().collect(Collectors.toMap(
                PromptTemplateParam::getName,
                param -> {
                    String value = displayParams.stream()
                        .filter(displayParam -> Objects.equals(displayParam.getName(), param.getName()))
                        .findFirst()
                        .map(PromptTemplateParam::getValue)
                        .orElse(null);
                    if (ParamTypeEnum.Dict.equals(param.getType()) && value != null) {
                        return param.getMap().getOrDefault(value, "");
                    }
                    return value != null ? value : "";
                },
                (existing, replacement) -> replacement));
        }
        // TODO 梳理 chunk传参 ，到底是用入参，还是从 promptTemplateParams 传入
        // 参考内容替换
        if (StringUtils.isNotBlank(referenceKey)) {
            if (null != sources) {
                String context = "";
                if (CollectionUtils.isNotEmpty(sources.getAllSources())) {
                    context = chunksToString(sources, 0);
                }
                replaceMap.put(referenceKey, context);
            }
        }
        return replaceMap;
    }

    public static void generateHistory(List<Message> messages, List<ChatHistory> histories) {
        histories.forEach(history -> {
            Message.Role role = Message.Role.USER;
            if (MessageRoleEnum.robot == history.getRole()) {
                role = Message.Role.ASSISTANT;
            }
            Message historyMsg = Message.builder()
                .role(role)
                .content(history.getContent())
                .build();
            messages.add(historyMsg);
        });
    }

    public static ChatCompletion buildChatCompletion(GenerateParam generateParam, List<Message> messages,
        Boolean respJsonFormat) {
        ChatModeConfig chatModeConfig = generateParam.getChatModeConfig();
        ModelInfoDO modelInfoDO = generateParam.getModelInfoDO();
        ChatCompletionUsage chatCompletion = ChatCompletionUsage.builder()
            .messages(messages)
            .model(modelInfoDO.getName())
            .temperature(chatModeConfig.getTemperature())
            .presencePenalty(chatModeConfig.getPresencePenalty())
            .frequencyPenalty(chatModeConfig.getFrequencyPenalty())
            .topP(chatModeConfig.getTopP().doubleValue())
            .streamOptions(new ChatCompletionUsage.StreamOptions().setIncludeUsage(true))
            .build();

        if (respJsonFormat != null && respJsonFormat) {
            chatCompletion.setResponseFormat(ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT.getName()).build());
        }

        int maxOutputTokens =
            getMaxOutputTokens(modelInfoDO, chatCompletion.getMessages());

        chatCompletion.setMaxTokens(maxOutputTokens);
        return chatCompletion;
    }

    private static int getMaxOutputTokens(ModelInfoDO modelConfig, List<Message> messages) {
        // 默认4k
        int maxOutputTokens = 8192;

        // 先使用老的配置
        Integer maxTokens = modelConfig.getMaxTokens();
        if (maxTokens != null && maxTokens > 0) {
            maxOutputTokens = maxTokens;
        }

        // 判断是否是新的配置，并使用
        Integer configMaxOutputTokens = modelConfig.getMaxOutputTokens();
        Integer contextLength = modelConfig.getContextLength();
        // 说明是新的配置，否则是老的配置
        boolean isNewConfig =
            configMaxOutputTokens != null && configMaxOutputTokens > 0 && contextLength != null && contextLength > 0;
        // TODO 目前仅适配了 qwen 的分词器
        // 参考 com.unfbx.chatgpt.utils.TikTokensUtil
        if (isNewConfig && modelConfig.getName().toUpperCase().contains("QWEN")) {
            // 计算token
            Tokenizer tokenizer = TokenizerFactory.qwen();
            Long inputTokenCount = 0L;
            // # every message follows <|start|>{role/name}\n{content}<|end|>\n
            int tokensPerMessage = 4;
            // tokens_per_name = -1 // if there's a name, the role is omitted
            for (Message message : messages) {
                inputTokenCount += tokensPerMessage;
                inputTokenCount += tokenizer.encodeOrdinary(message.getContent()).size();
                inputTokenCount += tokenizer.encodeOrdinary(message.getRole()).size();
            }
            // every reply is primed with <|start|>assistant<|message|>
            inputTokenCount += 3;

            // TODO 输入字符截断

            int leftTokens = (int)(contextLength - inputTokenCount - 50);
            maxOutputTokens = Math.min(leftTokens, configMaxOutputTokens);
            log.debug("maxOutputTokens: {}; [inputTokenCount:{}, contextLength:{}, leftTokens:{}]",
                maxOutputTokens, inputTokenCount, contextLength, leftTokens);
            if (maxOutputTokens < 0) {
                throw new BusinessExecutionException("maxOutputTokens is less than 0");
            }
        }
        return maxOutputTokens;
    }

    /**
     * 获取chatCompletion的回答（非流式）
     *
     * @param chatCompletionResponse chatCompletion的回答
     * @return 回答
     */
    public static String getChatCompletionAnswer(ChatCompletionResponse chatCompletionResponse) {
        if (CollectionUtils.isNotEmpty(chatCompletionResponse.getChoices())
            && ObjectUtils.isNotEmpty(chatCompletionResponse.getChoices().get(0))
            && ObjectUtils.isNotEmpty(chatCompletionResponse.getChoices().get(0).getMessage())) {
            return chatCompletionResponse.getChoices().get(0).getMessage().getContent();
        }
        return "";
    }

    /**
     * 获取chatCompletion的回答（流式）
     *
     * @param resp chatCompletion的回答
     * @return 回答
     */
    public static String getChatCompletionStreamAnswer(ChatCompletionResponse resp) {
        if (CollectionUtils.isNotEmpty(resp.getChoices())
            && ObjectUtils.isNotEmpty(resp.getChoices().get(0).getDelta())
            && ObjectUtils.isNotEmpty(resp.getChoices().get(0).getDelta().getContent())) {
            return resp.getChoices().get(0).getDelta().getContent();
        }
        return "";
    }

    /**
     * 主动关掉和大模型的连接
     * 
     * @param chatContext
     */
    public static void releaseResource(BaseChatContext chatContext) {
        if (chatContext.getRealEventSource() != null) {
            chatContext.getRealEventSource().cancel();
        }
        Map<String, CompletableFuture<?>> futureMap = chatContext.getFutureMap();
        if (futureMap != null) {
            futureMap.forEach((name, completableFuture) -> {
                if (completableFuture != null && !completableFuture.isDone()) {
                    completableFuture.cancel(true);
                }
            });
        }
        Map<String, EventSource> eventSourceMap = chatContext.getEventSourceMap();
        if (eventSourceMap != null) {
            eventSourceMap.forEach((name, eventSource) -> {
                if (eventSource != null) {
                    eventSource.cancel();
                }
            });
        }
        Map<String, BaseChatContext> subChatContextMap = chatContext.getSubChatContextMap();
        if (ObjectUtils.isNotEmpty(subChatContextMap)) {
            subChatContextMap.forEach((k, v) -> {
                releaseResource(v);
            });
        }
    }
    
    /**
     * 主动关掉和大模型的连接
     * 
     * @param chatContext
     */
    public static void fillLogMap(String preName, BaseChatContext chatContext, HashMap<String,List<String>> logMap) {
        if (logMap == null) {
            logMap = new HashMap<>();
        }
        Map<String, CompletableFuture<?>> futureMap = chatContext.getFutureMap();
        if (futureMap != null) {
            List<String> list = futureMap.keySet().stream().map(name -> preName + "-" + name).toList();
            List<String> futures = logMap.getOrDefault("futures", new ArrayList<>());
            futures.addAll(list);
            logMap.putIfAbsent("futures", futures);
        }
        Map<String, EventSource> eventSourceMap = chatContext.getEventSourceMap();
        if (eventSourceMap != null) {
            List<String> list = eventSourceMap.keySet().stream().map(name -> preName + name).toList();
            List<String> eventSources = logMap.getOrDefault("eventSource", new ArrayList<>());
            eventSources.addAll(list);
            logMap.putIfAbsent("eventSource", eventSources);
        }
        Map<String, BaseChatContext> subChatContextMap = chatContext.getSubChatContextMap();
        if (ObjectUtils.isNotEmpty(subChatContextMap)) {
            HashMap<String, List<String>> finalLogMap = logMap;
            subChatContextMap.forEach((k, v) -> {
                fillLogMap(k+"-", v, finalLogMap);
            });
        }
    }

    private static ChatModeConfig fromChatModeEnum(ChatModeEnum chatModeEnum) {
        ChatModeConfig chatModeConfig = new ChatModeConfig();
        chatModeConfig.setCode(chatModeEnum.getCode());
        chatModeConfig.setModeName(chatModeEnum.getModeName());
        chatModeConfig.setTemperature(chatModeEnum.getTemperature());
        chatModeConfig.setPresencePenalty(chatModeEnum.getPresencePenalty());
        chatModeConfig.setFrequencyPenalty(chatModeEnum.getFrequencyPenalty());
        chatModeConfig.setTopP(chatModeEnum.getTopP());;
        return chatModeConfig;
    }

    public static <T> T deepCopyObject(T original) {
        if (original == null) {
            return null;
        }
        String json = JSON.toJSONString(original);
        return (T)JSON.parseObject(json, original.getClass());
    }

    public static void setCustomTraceId(String traceID, int chapterNum, int subchapterNum) {
        MDC.put(TRACE_ID, StrUtil.format("{}] - [chapter({})_{}", traceID, chapterNum, subchapterNum));
    }

    public static void setCustomTraceId(String traceID, Object chapterNum) {
        MDC.put(TRACE_ID, StrUtil.format("{}] - [chapter({})", traceID, chapterNum));
    }

    @NotNull
    public static PromptTemplateParam buildPromptTemplateInputParam(String name, String value) {
        PromptTemplateParam chapterParam = new PromptTemplateParam();
        chapterParam.setName(name);
        chapterParam.setValue(value);
        chapterParam.setType(ParamTypeEnum.Input);
        return chapterParam;
    }

    public static ChatModeConfig getValidChatModeConfig(ChatModeConfig chatModeConfig) {
        if (chatModeConfig == null) {
            // 未设置则用平衡的参数填充
            return fromChatModeEnum(ChatModeEnum.DEFAULT);
        }
        // 检查 chatModeConfig 的参数
        Float temperature = chatModeConfig.getTemperature();
        Float presencePenalty = chatModeConfig.getPresencePenalty();
        Float frequencyPenalty = chatModeConfig.getFrequencyPenalty();
        Float topP = chatModeConfig.getTopP();

        if (temperature == null || temperature > 2.0 && temperature < 0) {
            throw new BusinessExecutionException("temperature must be between 0 and 2");
        }
        if (presencePenalty == null || presencePenalty > 2.0 && presencePenalty < 0) {
            throw new BusinessExecutionException("presencePenalty must be between -2 and 2");
        }
        if (frequencyPenalty == null || frequencyPenalty > 2.0 && frequencyPenalty < 0) {
            throw new BusinessExecutionException("frequencyPenalty must be between -2 and 2");
        }
        if (topP == null || topP <= 0 || topP > 1) {
            throw new BusinessExecutionException("topP must be between 0 and 1");
        }
        return chatModeConfig;
    }

    public static ModelInfoDO getDefaultModel(ModelTypeEnum modelType) {
        return FeignUtil.getModelInfoDOFeign()
            .conditions(null, null, ModelInfoDOParams.builder().type(List.of(modelType)).enable(List.of(ModelEnableEnum.ENABLE)).build())
            .getData().stream().max(Comparator.comparing(ModelInfoDO::getCreateTime)).orElse(null);
    }

    public static PromptTemplateParam extractParam(List<PromptTemplateParam> outlineTemplateParam, String paramName,
        String msg) {
        return outlineTemplateParam.stream()
            .filter(promptTemplateParam -> paramName.equals(promptTemplateParam.getName())).findAny()
            .orElseThrow(() -> new BusinessExecutionException(msg));
    }

    public static <T extends BaseChatContext> void handleReleaseResource(T t) {
        ChatHelper.releaseResource(t);
        HashMap<String, List<String>> hashMap = new HashMap<>();
        ChatHelper.fillLogMap("main", t, hashMap);
        log.debug("tasks canceled:\n{}", JSONUtils.getJacksonJsonString(hashMap));
    }


    /**
     * 处理模型身份查询的流式返回
     *
     * @param chatContext 聊天上下文
     */
    public void streamModelIdentityAnswer(BaseChatContext chatContext) {
        String modelDisplayName = chatContext.getLlmModelInfoDO().getDisplayName();
        String modelIdentityAnswer = modelIdentityConfig.getDescription();
        modelIdentityAnswer = modelIdentityAnswer.replace("{{modelDisplayName}}", modelDisplayName);
        FluxSink<ServerSentEvent<String>> sink = chatContext.getSink();
        try {
            // 从聊天参数中获取是否使用流式输出
            boolean useStream = chatContext.getChatParam() != null && chatContext.getChatParam().isStream();

            // 设置完整答案到上下文中
            chatContext.setCompleteAnswerSB(new StringBuffer(modelIdentityAnswer));

            if (useStream) {
                // 模拟流式输出，将答案按字符分割
                for (int i = 0; i < modelIdentityAnswer.length(); i++) {
                    // 每个字符作为一个流式输出
                    FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(String.valueOf(modelIdentityAnswer.charAt(i)), chatContext.getChatParam().getQuestion()), sink, STREAM_ANSWER);
                    try {
                        // 添加短暂延迟，模拟真实的流式输出
                        Thread.sleep(20);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            // TODO 计算usage,设置到chatContext中
            // 发送完整答案
            FluxUtil.doSinkNext(
                ChatHelper.buildCompleteAnswer(
                    chatContext.getChatParam(),
                    modelIdentityAnswer,
                    Constants.DEFAULT_STYLE,
                    chatContext
                ),
                sink,
                Constants.COMPLETE_ANSWER
            );

            sink.complete();
        } catch (Exception e) {
            // 在流中处理异常，而不是让它传播到全局异常处理器
            log.error("处理模型身份查询时发生错误: {}", e.getMessage(), e);
            sink.error(e);
        }
    }
}
