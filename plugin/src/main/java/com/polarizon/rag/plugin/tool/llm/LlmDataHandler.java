package com.polarizon.rag.plugin.tool.llm;

import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_REASONING;
import static com.polarizon.rag.plugin.bean.constant.Constants.THINK_REASONING_END_FLAG;
import static com.polarizon.rag.plugin.bean.constant.Constants.THINK_REASONING_START_FLAG;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import com.polarizon.rag.plugin.service.BaseChatContext;
import com.polarizon.rag.plugin.util.FluxUtil;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.codec.ServerSentEvent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatCompletionResponseWithReasoning;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.util.LLMContentUtils;
import com.polarizon.rag.plugin.util.LLMContentUtils.ReasoningMaskHandleResult;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.FluxSink;

/**
 * 处理LLM响应中推理内容的处理器类
 */
@Slf4j
public class LlmDataHandler {


    public static interface HandlerCompleteAnswer {
        void handleCompleteAnswer(String completeAnswer);
    }

    public static interface HandlerReasoning {
        void handleReasoning(String reasoningProcess);
    }

    // 生成参数
    private final GenerateParam generateParam;
    // 流式输出sink
    private final FluxSink<ServerSentEvent<String>> sink;
    // 推理答案缓冲区
    private final StringBuffer reasoningAnswerBuffer;
    // 显示内容缓冲区
    private final StringBuffer showContentSB;
    // 内容缓冲区
    private final StringBuffer completeAnswerSB;
    private final Usage usage;
    
    
    // 缓存推理过程，用于判断是否已经输出了推理过程
    // 长度为两个flag的和，用于保证即使去除START_FLAG之后，endflag仍然够窗口匹配END_FLAG
    private final CircularFifoQueue<String> reasoningLastTokens = new CircularFifoQueue<>(THINK_REASONING_START_FLAG.length() + THINK_REASONING_END_FLAG.length());
    // 是否匹配到推理过程的原子引用
    private final AtomicReference<Boolean> reasoningMatchedAF = new AtomicReference<>(false);
    // 事件计数的原子引用
    private final AtomicReference<Integer> eventNumAR = new AtomicReference<>(0);
    // 是否首次处理的原子引用
    private final AtomicReference<Boolean> isFirstHandledAR = new AtomicReference<>(true);
    
    // 推理处理是否完成的原子引用
    private final AtomicReference<Boolean> reasoningHandleFinishAF = new AtomicReference<>(false);
    // 是否需要答案
    private final boolean needAnswer;
    // 是否使用流式输出
    private final Boolean stream;
    // 推理处理回调
    private HandlerCompleteAnswer handlerCompleteAnswer;
    private HandlerReasoning handlerReasoning;

    /**
     * 构造函数
     */
    public LlmDataHandler(
        BaseChatContext chatContext,
        boolean needAnswer) {
        this.reasoningAnswerBuffer = chatContext.getReasoningAnswerSB();
        this.showContentSB = chatContext.getShowAllContentSB();
        this.completeAnswerSB = chatContext.getCompleteAnswerSB();
        this.generateParam = chatContext.getGenerateParam();
        this.sink = chatContext.getSink();
        this.needAnswer = needAnswer;
        this.stream = chatContext.getChatParam().isStream();
        this.usage = chatContext.getUsage();
    }

    public LlmDataHandler setHandlerCompleteAnswer(HandlerCompleteAnswer handlerCompleteAnswer) {
        this.handlerCompleteAnswer = handlerCompleteAnswer;
        return this;
    }

    public LlmDataHandler setHandlerReasoning(HandlerReasoning handlerReasoning) {
        this.handlerReasoning = handlerReasoning;
        return this;
    }
    /**
     * 处理LLM响应中的推理内容
     * 
     * @param data LLM响应数据
     * @return 处理后的聊天答案，如果仍在处理推理则返回null
     */
    public String handleLlmData(String data) {
        // 处理DONE情况
        if ("[DONE]".equals(data)) {
            return handleDoneData();
        }

        ChatCompletionResponseWithReasoning resp = JSON.parseObject(data, ChatCompletionResponseWithReasoning.class);
        Usage respUsage = resp.getUsage();
        if (respUsage != null) {
            usage.setCompletionTokens(respUsage.getCompletionTokens());
            usage.setPromptTokens(respUsage.getPromptTokens());
            usage.setTotalTokens(respUsage.getTotalTokens());
        }

        // 首先尝试处理reasoning_content字段
        String llmData = Optional.ofNullable(resp.getChoices())
            .filter(list -> !list.isEmpty())
            .map(list -> list.get(0))
            .map(ChatCompletionResponseWithReasoning.ChatChoice::getDelta)
            .map(ChatCompletionResponseWithReasoning.Message::getReasoningContent)
            .orElse(null);

        if (llmData != null) {
            reasoningAnswerBuffer.append(llmData);
            handleReasoningStreamSink(llmData);
            reasoningMatchedAF.set(true);
            return null;
        }

        // 如果没有reasoning_content，尝试处理think标签
        String answerContent = Optional.ofNullable(resp.getChoices())
            .filter(list -> !list.isEmpty())
            .map(list -> list.get(0))
            .map(ChatCompletionResponseWithReasoning.ChatChoice::getDelta)
            .map(ChatCompletionResponseWithReasoning.Message::getContent)
            .orElse(null);

        if (answerContent == null) {
            return null;
        }

        reasoningLastTokens.add(answerContent);
        eventNumAR.set(eventNumAR.get() + 1);

        // 处理第一个token的情况
        if (isFirstHandledAR.get()) {
            isFirstHandledAR.set(false);
            String outputTokenSB = getBufferedStrings();
            // 如果第一个token不是推理开始标签，则剩余数据直接当成是最终生成内容，直接返回
            if (!outputTokenSB.startsWith(THINK_REASONING_START_FLAG)) {
                reasoningHandleFinishAF.set(true);
                
                handleRes(resp.getChoices().get(0).getDelta().getContent()); 
                return JSONObject.toJSONString(resp);
            }

            reasoningMatchedAF.set(true);
            String content = outputTokenSB.replaceAll(THINK_REASONING_START_FLAG, "");
            showContentSB.append(THINK_REASONING_START_FLAG);

            if (content.contains(THINK_REASONING_END_FLAG)) {
                reasoningHandleFinishAF.set(true);
                String reasoning = StringUtils.substringBefore(content, THINK_REASONING_END_FLAG);
                String answer = StringUtils.substringAfter(content, THINK_REASONING_END_FLAG);

                reasoningAnswerBuffer.append(reasoning);
                showContentSB.append(reasoning).append(THINK_REASONING_END_FLAG);
                handleReasoningStreamSink(reasoning);
                reasoningLastTokens.clear();
                
                handleRes(answer);
                return JSONObject.toJSONString(ChatCompletionResponseWithReasoning.ofDeltatContent(answer));
            }
            return null;
        }

        // 检查缓存内容中是否有结束标签
        String outputTokenSB = getBufferedStrings();
        if (outputTokenSB.contains(THINK_REASONING_END_FLAG)) {
            reasoningHandleFinishAF.set(true);
            String reasoning = StringUtils.substringBefore(outputTokenSB, THINK_REASONING_END_FLAG);
            String answer = StringUtils.substringAfter(outputTokenSB, THINK_REASONING_END_FLAG);

            reasoningAnswerBuffer.append(reasoning);
            showContentSB.append(reasoning).append(THINK_REASONING_END_FLAG);
            handleReasoningStreamSink(reasoning);
            reasoningLastTokens.clear();

            handleRes(answer);
            return JSONObject.toJSONString(ChatCompletionResponseWithReasoning.ofDeltatContent(answer));
        }
        handleRes(answerContent);
        return null;
    }

    /**
     * 处理推理完成的DONE情况
     */
    private String handleDoneData() {
        log.debug("handleDoneData");
        String outputTokenSB = getBufferedStrings();
        // 已经取到过推理开始标签。就不用处理以前的数据了
        if (reasoningMatchedAF.get()) {
            return null;
        }
        // 下面开始都是处理旧数据
        if (eventNumAR.get() < reasoningLastTokens.maxSize()) {
            ReasoningMaskHandleResult result = LLMContentUtils.reasoningMaskHandle(outputTokenSB);
            if (result != null) {
                reasoningHandleFinishAF.set(true);
                String reasoning = result.getReasoningContent();
                String answer = result.getAnswerContent();

                if (reasoning != null) {
                    reasoningAnswerBuffer.append(reasoning);
                    showContentSB.append(reasoning);
                    handleReasoningStreamSink(reasoning);
                }
                reasoningLastTokens.clear();
                handleRes(answer);
                return JSONObject.toJSONString(ChatCompletionResponseWithReasoning.ofDeltatContent(answer));
            }
            handleRes(outputTokenSB);
            return JSONObject.toJSONString(ChatCompletionResponseWithReasoning.ofDeltatContent(outputTokenSB));
        }

        reasoningAnswerBuffer.append(outputTokenSB);
        showContentSB.append(outputTokenSB);
        handleReasoningStreamSink(outputTokenSB);

        String msg = StrUtil.format(
            "LLM 返回数据结束异常：没有匹配到reasoning结束标签[{}]," +
                "但模型已经输出已经结束(可能是输出token数(maxTokens)过小导致数据被截断。)。reasoningContent：\n{}",
            THINK_REASONING_END_FLAG, outputTokenSB);
        log.error(msg);
        throw new BusinessExecutionException(msg);
    }

    /**
     * 处理最终的数据
     * @param outputTokenSB
     */
    private void handleRes(String outputTokenSB) {
        Optional.ofNullable(this.handlerCompleteAnswer).ifPresent(handler -> handler.handleCompleteAnswer(outputTokenSB));
        completeAnswerSB.append(outputTokenSB);
    }

    /**
     * 处理推理内容的流式输出
     */
    private void handleReasoningStreamSink(String reasoningContent) {
        boolean isStream = stream == null || stream;
        boolean reasoningContentNotBlank = StringUtils.isNotBlank(reasoningContent);
        // 如果推理内容为空，或者不是流式输出，或者不需要答案，则不处理
        if (!reasoningContentNotBlank || !isStream || !needAnswer) {
            return;
        }
        // 构建流式推理内容
        ChatRes streamReasonContent = ChatRes.buildStreamReasonContent(reasoningContent, generateParam.getQuestion());
        // 发送流式推理内容
        FluxUtil.doSinkNext(streamReasonContent, sink, STREAM_REASONING);
        Optional.ofNullable(this.handlerReasoning).ifPresent(handler -> handler.handleReasoning(reasoningContent));
    }

    /**
     * 从token队列中获取缓存的字符串
     */
    private String getBufferedStrings() {
        StringBuilder outputTokenSB = new StringBuilder();
        for (String lastToken : reasoningLastTokens) {
            outputTokenSB.append(lastToken == null ? "" : lastToken);
        }
        return outputTokenSB.toString();
    }
}
