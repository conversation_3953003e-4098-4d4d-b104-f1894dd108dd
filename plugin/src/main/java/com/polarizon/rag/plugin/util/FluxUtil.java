package com.polarizon.rag.plugin.util;

import com.alibaba.fastjson.JSON;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.chat.MessageRoleEnum;
import com.polarizon.rag.plugin.bean.constant.Constants;
import com.polarizon.rag.plugin.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.bean.dto.aiagent.AgentChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.exception.LowBalanceRequestsException;
import com.polarizon.rag.plugin.exception.SystemBusyExecutionException;
import com.polarizon.rag.plugin.exception.TooManyRequestsException;
import com.polarizon.rag.plugin.service.BaseChatContext;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.service.InstructHelper;
import com.polarizon.rag.plugin.service.instruct.InstructChatContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static com.polarizon.rag.plugin.bean.constant.Constants.ERROR_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;

/**
 * Flux 工具类，提供通用封装方法。
 */
@Slf4j
public class FluxUtil {

    /**
     * 封装 Flux.create()，统一添加 onCancel 逻辑。
     *
     * @param chatContext 背压策略。
     * @param handler                用于处理 FluxSink 的消费者。
     * @return 返回封装后的 Flux。
     */
    public static <T, R extends BaseChatContext> Flux<T> createWithOnCancelRag(AtomicReference<R> chatContext, BiConsumer<FluxSink<T>,R> handler) {
        return Flux.create(sink -> {
            // 添加统一的 onCancel 逻辑
            sink.onCancel(() -> {
                log.debug("sink onCancel");
                ChatHelper.handleReleaseResource(chatContext.get()); // 如果需要上下文，可传递参数进来
            });

//            initContext(agentChatParam, sink, agentCode, instructChatContext);
            // 执行外部传入的业务逻辑
            handler.accept(sink,  chatContext.get());
        });
    }

    /**
     * 封装 Flux.create()，统一添加 onCancel 逻辑。
     *
     * @param chatContextAR 背压策略。
     * @param handler                用于处理 FluxSink 的消费者。
     * @return 返回封装后的 Flux。
     */
    public static <T, R extends InstructChatContext> Flux<T> createWithOnCancel(AtomicReference<R> chatContextAR,
                                                                                AgentChatParam agentChatParam,
                                                                                Consumer<AtomicReference<R>> handler) {
        return Flux.create(sink -> {
            // 添加统一的 onCancel 逻辑
            sink.onCancel(() -> {
                log.debug("sink onCancel");
                ChatHelper.handleReleaseResource(chatContextAR.get()); // 如果需要上下文，可传递参数进来
            });

            InstructHelper.initContext(agentChatParam, sink, chatContextAR.get());
            // 执行外部传入的业务逻辑
            handler.accept(chatContextAR);
        });
    }

    @NotNull
    public static <T extends BaseChatContext> Flux<ServerSentEvent<String>> addEventCallback(T t) {
        String traceId = MDC.get(TRACE_ID);
        return t.getServerSentEventFlux()
            .doOnComplete(() -> {
                MDC.put(TRACE_ID, traceId);
                StopWatchUtils chatWatch = t.getStopWatch();
                if (chatWatch != null && chatWatch.getStopWatch() != null) {
                    StopWatch stopWatch = chatWatch.getStopWatch();
                    if (stopWatch != null) {
                        if (stopWatch.isRunning()) {
                            stopWatch.stop();
                        }
                        log.debug("sseDoOnComplete;watch:\n{}", chatWatch.prettyPrintJson(TimeUnit.SECONDS));
                    }
                } else {
                    log.debug("sseDoOnComplete");
                }
                HashMap<String, List<String>> hashMap = new HashMap<>();
                ChatHelper.fillLogMap("main", t, hashMap);
                log.debug("process succeed:\n{}", JSONUtils.getJacksonJsonString(hashMap));
            })
            .doOnCancel(() -> {
                log.debug("ssefDoOnCancel");
                ChatHelper.handleReleaseResource(t);
            })
            // 转换成封装后的实体== exceptionHandler
            .onErrorResume(e -> {
                ChatHelper.handleReleaseResource(t);
                return buildErrorRespData(e);
            })
//            .doOnTerminate(() -> {
//                MDC.put(TRACE_ID, traceId);
//                log.debug("ssefDoOnTerminate");
//            })
            // TODO 使生效
            // .doFinally(signalType -> {
            // log.debug("doFinally,signalType:{}", signalType);
            // releaseResource(param.getRealEventSourceAtomicReference());
            // })
            ;
    }

    @NotNull
    static Flux<ServerSentEvent<String>> buildErrorRespData(Throwable e) {
        
        Integer status = getStatusByException(e);

        String msg = e.getMessage();
        Throwable cause = e.getCause();
        if (e instanceof CompletionException && cause != null) {
            msg = cause.getMessage();
            status = getStatusByException(cause);
        }

        log.error("onErrorResume! msg:{} ,status:{} ", msg, status, e);
        return Flux.just(ServerSentEvent.<String>builder().event(ERROR_ANSWER)
            .data(JSON.toJSONString(NewResultDTO.error(status, msg, null, MDC.get(TRACE_ID)))).build());
    }

    private static Integer getStatusByException(Throwable cause) {
        Integer status = ResultDTO.Constants.FAILURE;
        if (cause instanceof SystemBusyExecutionException 
            || cause instanceof TooManyRequestsException 
            || cause instanceof LowBalanceRequestsException) {
            status = SystemBusyExecutionException.STATUS_CODE;
        }
        return status;
    }

    /**
     * 构建完整的答案 params:
     *
     * @param param 聊天参数
     * @param resp 回答内容
     * @param style 回答风格
     * @param chatContext 事件回调参数
     * @param reasoningAnswerSB 推理答案
     * @return 完整的答案
     */
    @NotNull
    public static ChatRes buildCompleteAnswer(ChatParam param, String resp, String style,
                                              BaseChatContext chatContext, StringBuffer reasoningAnswerSB) {
        ChatRes chatRes = new ChatRes();
        chatRes.setQuestion(param.getQuestion());
        GenerateParam generateParam = chatContext.getGenerateParam();
        if (generateParam != null && generateParam.getSources()!=null) {
            List<ChatRes.SourceBOResp> allSources = generateParam.getSources().getAllSources();
            chatRes.setSource(allSources);
        }
        // String validResp = handleNoSourceResp(resp, CollectionUtils.isEmpty(allSources));
        String validResp = resp;
        chatRes.setResponse(validResp);
        Boolean containReasoning = false;
        if (reasoningAnswerSB != null && StringUtils.isNotBlank(reasoningAnswerSB.toString())) {
            containReasoning = true;
        }
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setStyle(style);

        List<ChatHistory> history = param.getHistory() == null ? new ArrayList<>() : param.getHistory();
        history.add(new ChatHistory().setRole(MessageRoleEnum.user).setContent(param.getQuestion()));
        ChatHistory chatHistory = new ChatHistory().setRole(MessageRoleEnum.system);
        ChatHistory e = chatHistory.setContent(resp);
        if (containReasoning) {
            chatHistory.setReasoningContent(reasoningAnswerSB.toString());
            chatRes.setReasoningContent(reasoningAnswerSB.toString());
        }
        history.add(e);
        chatRes.setHistory(history);
        // chatRes.setQueryRewriterResult(chatContext.getQueryRewriterResult());
        chatRes.setUsage(chatContext.getAggregateUsage());
        chatRes.setUsageDetail(chatContext.buildUsageDetail("main"));
        return chatRes;
    }

    public static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String event) {
        return doSinkNext(content, sink, event, MDC.get(TRACE_ID));
    }
    
    public static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String event,
                                                     String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(event)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }
}