package com.polarizon.rag.plugin.handler.strategy.application;

import com.alibaba.fastjson.JSON;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.application.ApplicationDO;
import com.polarizon.rag.application.InstructDO;
import com.polarizon.rag.kb.KnowledgeBaseDO;
import com.polarizon.rag.kb.params.KnowledgeBaseDOParams;
import com.polarizon.rag.plugin.handler.strategy.MutationStrategy;
import com.polarizon.rag.plugin.util.FeignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class ApplicationStrategy implements MutationStrategy<ApplicationDO> {

    // TODO application 初始化设置，考虑兼容老的！！！

    /**
     * 1. 后AOP修正正确的值权限的值：isPublic，visible
     * 2. 同时初始化instructList。
     * 3. kbList ? 需要兼容rag？ （不倾向于兼容，直接从application 获取）
     * <p>
     * 权限:
     * isPublic: false，visible: [createUserID] <- 创建时，这个位初始值。
     * 没有知识库：isPublic: false, visible: [createUserID]
     * 一旦有私有： isPublic: false, visible: [kbCreateUserID]
     * 没有一个私有： isPublic: true, visible: [public] （后端设置的默认zhi？）
     *
     * @param list 任务
     */
    @Override
    public void addHandler(List<ApplicationDO> list) {
        String idsStr = extractIDsStr(list);
        log.info("ApplicationStrategy addHandler ApplicationIds:{}", idsStr);
        list.forEach(ApplicationStrategy::handleModifyKbIDList);
    }

    @Override
    public void updateHandler(List<ApplicationDO> task, List<ApplicationDO> preTask) {
         List<ApplicationDO> kbIdsUpdatedApps = calDiffApp(task, preTask);
        if (CollectionUtils.isNotEmpty(kbIdsUpdatedApps)) {
            kbIdsUpdatedApps.forEach(ApplicationStrategy::initAuthorization);
        }
    }
    
        
    private static List<ApplicationDO> calDiffApp(List<ApplicationDO> task, List<ApplicationDO> preTask) {
        String taskIdsStr = extractIDsStr(task);
        log.info("ApplicationStrategy updateHandler taskIdsStr:{}", taskIdsStr);
        List<ApplicationDO> kbIdsUpdatedApps = getKbIdsUpdatedApplications(task, preTask);
        if (CollectionUtils.isEmpty(kbIdsUpdatedApps)) {
            log.debug("ApplicationStrategy updateHandler noKbUpdatedApps:{}", taskIdsStr);
            return Collections.emptyList();
        }

        // log diff
        Set<String> kdIdsUpdateAppIDSet =
            kbIdsUpdatedApps.stream().map(ApplicationDO::getId).collect(Collectors.toSet());
        List<ApplicationDO> preTaskApps = extractBaseFieldAppList(preTask, kdIdsUpdateAppIDSet);
        List<ApplicationDO> taskAppList =
            kbIdsUpdatedApps.stream().map(ApplicationStrategy::extractAppBaseFiled).toList();
        log.info("ApplicationStrategy updateHandler taskAppList:{}, preTaskApps:{} .", JSON.toJSONString(taskAppList)
            , JSON.toJSONString(preTaskApps));
        
        return kbIdsUpdatedApps;
    }

    @NotNull
    private static List<ApplicationDO> getKbIdsUpdatedApplications(List<ApplicationDO> task,
                                                                   List<ApplicationDO> preTask) {
        return task.stream().filter(applicationDO -> {
            Optional<ApplicationDO> first =
                preTask.stream().filter(preApp1 -> preApp1.getId().equals(applicationDO.getId())).findFirst();
            List<String> kbIDList = applicationDO.getKbIDList();
            if (kbIDList == null) {
                kbIDList = List.of();
            }
            List<String> kbIDListPre = first.orElse(new ApplicationDO()).getKbIDList();
            if (kbIDListPre == null) {
                kbIDListPre = List.of();
            }
            return !CollectionUtils.isEqualCollection(kbIDList, kbIDListPre);
        }).toList();
    }

    public static void handleModifyKbIDList(ApplicationDO applicationDO) {
        initAuthorization(applicationDO);
        // TODO 通过应用分类，设置不同类型的 Instruct 分类
        // rag 类
        ResultDTO<List<InstructDO>> listResultDTO = FeignUtil.getInstructDOFeign().listAll(null, null, null);
        List<InstructDO> instructDOList = FeignUtil.getListDataNonEmpty(listResultDTO, "指令不存在");
        List<String> instructIds = instructDOList.stream().map(AbstractBaseBO::getId).toList();
        applicationDO.setInstructIDList(instructIds);

        FeignUtil.getApplicationDOFeign().update(null, null, applicationDO.getCreateBy(), true, applicationDO);
    }

    private static void initAuthorization(ApplicationDO applicationDO) {
        List<String> kbIDList = applicationDO.getKbIDList();
        List<String> visible = List.of(applicationDO.getCreateBy());
        boolean isPublic = false;
        // 没有知识库：isPublic: false, visible: [createUserID]
        if (CollectionUtils.isEmpty(kbIDList)) {
            // 同上面默认
        } else {
            ResultDTO<List<KnowledgeBaseDO>> listResultDTO =
                FeignUtil.getKnowledgeBaseApi().conditions(null, null, KnowledgeBaseDOParams.builder().ids(kbIDList).build(), null);
            List<KnowledgeBaseDO> knowledgeBaseDOS = FeignUtil.getListDataNonEmpty(listResultDTO, "知识库不存在");
            Optional<KnowledgeBaseDO> any =
                knowledgeBaseDOS.stream().filter(knowledgeBaseDO -> !knowledgeBaseDO.getIsPublic()).findAny();
            // 一旦有私有： isPublic: false, visible: [kbCreateUserID]
            if (any.isPresent()) {
                log.debug("knowledgeBaseDOID不是public的，id:{},修改应用(ID:{})为私有", any.get().getId(), applicationDO.getId());
                isPublic = false;
                visible = List.of(any.get().getCreateBy());
            } else {
                // 没有一个私有： isPublic: true, visible: [public] 
                isPublic = true;
                visible = List.of("public");
            }
        }
        applicationDO.setIsPublic(isPublic);
        applicationDO.setVisible(visible);
    }
    
    // task 的元素获取id，平成字符串，用逗号隔开
    private static String extractIDsStr(List<ApplicationDO> list) {
        return list.stream().map(AbstractBaseBO::getId).collect(Collectors.joining(","));
    }

    @NotNull
    private static List<ApplicationDO> extractBaseFieldAppList(List<ApplicationDO> preTask,
                                                               Set<String> kdIdsUpdateAppIDSet) {
        return preTask.stream().filter(applicationDO -> kdIdsUpdateAppIDSet.contains(applicationDO.getId())).map(ApplicationStrategy::extractAppBaseFiled).toList();
    }

    @NotNull
    private static ApplicationDO extractAppBaseFiled(ApplicationDO applicationDO) {
        ApplicationDO applicationDO1 = new ApplicationDO();
        applicationDO1.setId(applicationDO.getId());
        applicationDO1.setKbIDList(applicationDO.getKbIDList());
        applicationDO1.setName(applicationDO.getName());
        return applicationDO1;
    }



}
