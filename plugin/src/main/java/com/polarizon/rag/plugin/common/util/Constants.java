package com.polarizon.rag.plugin.common.util;

public interface Constants {

    String X_ACCOUNT_HEADER_NAME = "X-Account";
    String X_TRACE_ID_HEADER_NAME = "X-TRACE-ID";
    String X_REQUEST_ID_HEADER_NAME = "X-Request-Id";
    String X_Token = "X-Token";
    
    /**模型相关**/

    String DEFAULT_EMBEDDING_MODEL_NAME = "bge-m3";
    String LLM_MODEL_NAME = "Qwen1.5-14B-Chat-Int4";
    String QWEN_2_5_14_B = "qwen2.5-14b-instruct-gptq-int4";
    String OPENAI_API = "openai-api";
    String QWEN_1_5_7_B_CHAT = "Qwen1.5-7B-Chat";
    String CHATGLM_3_6_B = "chatglm3-6b";
    Float DEFAULT_SCORE_THRESHOLD = 0.1f;


    /**对话相关**/

    Boolean ENABLE_PARENT_INDEX_DEFAULT = false;
    String STREAM_ANSWER = "answer";
    String STREAM_REASONING = "reasoning";
    String COMPLETE_ANSWER = "completeAnswer";
    String EXTRA_CONTENT = "extraContent";
    String MESSAGE_CHANGE = "messageChange";
    String PROCESS_STATUS = "processStatus";
    String ERROR_ANSWER = "errorAnswer";

    String NO_CHUNKS_ANSWER =  "未在知识库中检索到相关知识，请问问其他问题吧。";
    String FILE_PARSE_FAILURE_ANSWER = "未能成功解析您提供的文件，请确保提供的文件包含具体内容，或确保提供的文件未损坏且格式符合要求，或者提供更多的参考，我就可以为您提供帮助，准确地回答您的问题。";
//    Integer NO_CHUNKS_ANSWER_STATUS = 100;
    
    String NO_CONTENT = "noContent";
    String NONE = "NONE";


    /**extraContent start*/
    String OUTLINE_EXTRA_CONTENT_TYPE_NAME = "outline";

    /**extraContent start*/
    String WARNING_EXTRA_CONTENT_TYPE_NAME = "warning";
    String INSPECTION_EXTRA_CONTENT_TYPE_NAME = "inspection";
    String INSPECTION_EXTRA_CONTENT_NAME = "审查结果";
    String OUTLINE_EXTRA_CONTENT_NAME = "大纲";

    /**extraContent end*/
    
    
    Float DEFAULT_TEMPERATURE = 0.7f;
    
    Integer LLM_MAX_TOKEN = 32768;
    
    String DEFAULT_NO_CHUNK_SYSTEM_PROMPT = "";
    String DEFAULT_NO_CHUNK_USER_PROMPT = """
        ## 角色
        你是一位有礼貌的人工智能助手，正在与用户进行对话。
        ## 任务
        你需要从用户提问的问题本身和历史对话中分析用户希望了解的内容，并基于给定材料准确、专业地回答。
        ## 要求
        1、如果用户没有特殊要求，你的回答语言必须与用户提问的语言一致。比如，当用户使用英文提问时，你也要使用英文回答。
        2、如果没有提供材料，直接使用自身知识回答；如果提供的材料缺乏有效信息，先说明缺乏有效信息再用自身知识回答。
        3、请根据对话内容灵活调节语气，确保回答内容连贯通顺，避免不必要的客套话。
        ## 给定材料
        {{context}}
        ## 用户提问
        {{question}}
        ## 输出格式要求
        请严格按照要求，基于给定材料回答用户提问，回答采用markdown格式的文本，但不要标注```markdown```。无需附加其它解释或备注，不要使用代码块结构，除非用户要求写代码。
        """;
        
    String DEFAULT_SYSTEM_PROMPT = """
    """;
    String DEFAULT_USER_PROMPT = """
        你需要诚实的回答用户的问题，采用问题的语言来进行回答。
        已知信息:
        ------
        {{context}}
        ------
        上面是一些已知信息，其中可能存在和问题相关的信息，也可能存在与问题无关的信息。请你根据已知信息中与问题相关的部分，简洁和专业地回答问题。如果无法从已知信息中得到答案，请说“根据已知信息无法回答该问题”。你应当使用问题的语言进行回答。
        问题：<<{{question}}>>
        """;

    String GENERATION_PATH =  "/rest/v1/generation";
    
    Integer MAX_EVENT_LOG_NUM = 3;

    /**
     * 宸启帮我查的code
     */
    String SEARCH_HELP_ME_SEARCH = "search:help-me-search";

    /**
     * 写规划报告
     */
    String WRITE_PLAN_REPORT_CODE = "write:plan_report";
    /**
     * 写规划报告正文
     */
    String WRITE_PLAN_REPORT_BODY_CODE = "write:plan_report_body";
    /**
     * 写规划报告优化
     */
    String WRITE_PLAN_REPORT_CHECK_CODE = "write:plan_report_check";

    /**
     * 大纲生成完成标识
     */
    String GENERATE_OUTLINE_COMPLETE = "[outlineFinish]";

    /**
     * 正文生成完成标识
     */
    String GENERATE_BODY_COMPLETE = "[bodyFinish]";
    String THINK_REASONING_START_FLAG = "<think>";    
    String THINK_REASONING_END_FLAG = "</think>";
    
    String THINK_REASONING_REGEX = "(?s)(.*<think>.*</think>\\s*)([\\s\\S]*)";


    String MESSAGE_TYPE_DEFAULT = "DEFAULT";
    String MESSAGE_TYPE_NO_CHUNKS_ANSWER = "NO_CHUNKS_ANSWER";
    String MESSAGE_TYPE_ERROR = "ERROR";
    String MESSAGE_RESPONSE_ERROR = "服务异常，请稍后重试";

    
}
