package com.polarizon.rag.plugin.tool.llm;

import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_STYLE;
import static com.polarizon.rag.plugin.bean.constant.Constants.MAX_EVENT_LOG_NUM;
import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.X_TRACE_ID_HEADER_NAME;

import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import com.polarizon.rag.plugin.service.BaseChatContext;
import com.polarizon.rag.plugin.util.FluxUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.codec.ServerSentEvent;

import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.util.StopWatchUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import reactor.core.publisher.FluxSink;

@Slf4j
public class LLmCommonEventSourceListener extends EventSourceListener {


    // 用于打印所有通过流式输出的content的内容，包括<think>标签式的推理过程，不包括 reasoning_content
    private StopWatchUtils stopWatchUtils;


    private Boolean noChunks;
    private BaseChatContext chatContext;
    private FluxSink<ServerSentEvent<String>> sink;
    private final LlmDataHandler llmDataHandler;

    // 防止日志过多
    private Integer onEventNum = 0;

    // 是否在无知识时，增加提示通知信息
    // 提示内容
    private static String noChunkPreInfo = "";
    // private static String noChunkPreInfo = "根据知识库中已知信息无法回答该问题。AI综合所有相关数据后，参考性解读如下:";
    // 是否已经提示过了
    private AtomicReference<Boolean> noChunkPreInfoAR = new AtomicReference<>(false);

    /**
     * 构造函数
     * @param chatContext 事件回调参数
     * @param sink 流式sink
     * @param noChunks 是否没有知识
     */
    public LLmCommonEventSourceListener(BaseChatContext chatContext,
                                        FluxSink<ServerSentEvent<String>> sink,
                                        Boolean noChunks) {
        this.chatContext = chatContext;
        this.sink = sink;
        this.noChunks = noChunks;

        // Initialize reasoning handler with all required parameters
        this.llmDataHandler = new LlmDataHandler(chatContext,true);
        this.llmDataHandler.setHandlerCompleteAnswer(s -> handleChatAnswer(s));
    }

    @Override
    public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type,
                        @NotNull String data) {
        super.onEvent(eventSource, id, type, data);
        debugLog(id, type, data);

        // 使用推理处理器处理数据
        llmDataHandler.handleLlmData(data);
    }

    private void handleChatAnswer(String answer) {
        if (answer == null) {
            return;
        }

        // 添加没知识时的通知
        if (noChunks && !noChunkPreInfoAR.getAndSet(true)) {
            chatContext.getCompleteAnswerSB().append(noChunkPreInfo);
        }

        // 处理流式响应
        if (chatContext.getChatParam().isStream()) {
            FluxUtil.doSinkNext(
                ChatRes.buildStreamAnswer(answer,  chatContext.getChatParam().getQuestion()),
                sink, STREAM_ANSWER);
        }
    }

    /**
     * 打印debug日志
     * @param id 事件ID
     * @param type 事件类型
     * @param data 事件数据
     */
    private void debugLog(@Nullable String id, @Nullable String type, String data) {
        if (onEventNum < MAX_EVENT_LOG_NUM) {
            log.debug("llmOriginalOutput:,id:{},type:{},data{}:{}", id, type, onEventNum, data);
            onEventNum++;
        }
    }

    @Override
    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        super.onOpen(eventSource, response);
        ChatHelper.setTraceId(eventSource);
        if (stopWatchUtils != null) {
            stopWatchUtils.start("llmAnswer");
        }
        log.debug("queryOutLlmSseOnOpen:response:{}", response);
    }

    @Override
    public void onClosed(@NotNull EventSource eventSource) {
        super.onClosed(eventSource);
        ChatHelper.setTraceId(eventSource);
        if (stopWatchUtils != null) {
            stopWatchUtils.stop();
        }
        log.debug("queryLlmSseOnClosed:{}", eventSource.request());

        try {
            FluxUtil.doSinkNext(
                ChatHelper.buildCompleteAnswer(chatContext.getChatParam(), chatContext.getCompleteAnswerSB().toString(),
                     DEFAULT_STYLE, chatContext),
                sink, COMPLETE_ANSWER, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
            log.debug("llmInfo:\n{}",
                JSONUtils.getJacksonJsonString(
                    Map.of("allContent", chatContext.getCompleteAnswerSB().toString(), 
                        "reasoningAnswer", chatContext.getReasoningAnswerSB().toString(),
                        "usage", chatContext.getAggregateUsage()
//                        ,
//                        "usageDetail", chatContext.buildUsageDetail("main")
                        )));
            sink.complete();
        } catch (Exception e) {
            sink.error(e);
        }
    }

    @Override
    public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t,
                          @Nullable Response response) {
        super.onFailure(eventSource, t, response);
        if (stopWatchUtils != null) {
            stopWatchUtils.stop();
        }
        LlmHandleService.handleOnFailure(t, response, sink, null, true);
    }

}