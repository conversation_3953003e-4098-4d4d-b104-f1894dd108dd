package com.polarizon.rag.plugin.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.polarizon.rag.plugin.common.exception.BusinessExecutionException;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LLMContentUtils {
    public static final List<Pattern> THOUGHT_RESPONSE_PATTERN_LIST = new ArrayList<>();

    /**
     * @param input eg:
     *              // """
     *              //            ```thought\\n材料信息中包含了一些关于博物馆建设...\\n```\\n\\n```response\\n# 关于博物馆建设项目推进情况的报告\\n\\n## 一、引言\\n\\n### （一）报告背景\\n近期，我市在推进博物馆建设过程中遇到了一系列问题...。\\n```
     *              //            """;
     * @return 返回过滤后的文本
     */
    public static String thoughtMaskHandle(String input) {
        Optional<Pattern> first = THOUGHT_RESPONSE_PATTERN_LIST.stream().filter(patternIn -> {
            Matcher matcher = patternIn.matcher(input);
            return matcher.find();
        }).findFirst();
        if (first.isEmpty()) {
            return null;
        }
        Matcher matcher = first.get().matcher(input);
        // 必须需要触发匹配！
        matcher.find();
        if (matcher.groupCount() != 2) {
            String message =
                    StrUtil.format("thoughtMaskHandle正则组捕获匹配失败: input:[{}], regex:[{}] groupCount:[{}]",
                            input, matcher.pattern(), matcher.groupCount());
            throw new BusinessExecutionException(message);
        }
        String responseValidPart = matcher.group(2);
        log.debug("thoughtMaskHandle匹配成功: responseValidPart:[{}]， input:[{}], ", responseValidPart, input);
        return responseValidPart;

    }

    // r1 带 </think> 的标签的推理类型
    public static String[] reasoningMaskHandle(String input) {
        if (input == null) {
            return null;
        }

        final String target = "</think>";
        int index = input.indexOf(target);

        if (index == -1) {
            return null;
        }

        // TODO 测试
        String before = input.substring(0, index);
        String after = input.substring(index + target.length());
        log.debug("reasoningMaskHandle: input:[{}], before:[{}],after:[{}]", input, before, after);
        return new String[]{before, after};
    }

}
