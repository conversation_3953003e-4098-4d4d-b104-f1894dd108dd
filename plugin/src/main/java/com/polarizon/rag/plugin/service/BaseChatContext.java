package com.polarizon.rag.plugin.service;

import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.RetrievalConfig;
import com.polarizon.rag.plugin.tool.llm.Usage;
import com.polarizon.rag.plugin.tool.llm.UsageDetail;

import io.github.futures4j.Futures;
import org.springframework.http.codec.ServerSentEvent;

import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterResult;
import com.polarizon.rag.plugin.util.StopWatchUtils;

import lombok.Data;
import lombok.experimental.Accessors;
import okhttp3.sse.EventSource;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Accessors(chain = true)
public class BaseChatContext {
    // ----- 输入相关
    // 聊天参数
    private ChatParam chatParam;
    // 生成过程参数
    private GenerateParam generateParam;
    // 问题重写
    private QueryRewriterResult queryRewriterResult;
    // 检索配置
    private RetrievalConfig retrievalConfig;
    // 会话配置
    private ConversationConfig conversationConfig;
    // 大模型信息
    private ModelInfoDO llmModelInfoDO;


    // ----- SSE 相关
    // 用于记录大模型sse的状态
    private EventSource realEventSource;
    // reactor Flux 链接
    private Flux<ServerSentEvent<String>> serverSentEventFlux;
    // StopWatchUtils
    private StopWatchUtils stopWatch;
    // SSE 数据控制持有对象
    private FluxSink<ServerSentEvent<String>> sink;

    // ----- 请求相关 (ReasoningHandler 生成)
    // 用于 sink 出去的reasoningAnswerBuffer存储
    private StringBuffer reasoningAnswerSB = new StringBuffer();
    // 用于打印所有通过流式输出的content的内容，包括<think>标签式的推理过程，不包括 reasoning_content
    private StringBuffer showAllContentSB = new StringBuffer();
    // 用于 sink 出去的 answerContentBuffer 存储
    private StringBuffer completeAnswerSB = new StringBuffer();
    private Usage usage = new Usage();


    /** 任务管理相关信息*/
    /**
     * 当前流程的子流程的上下文，用于存储子流程的参数
     */
    private Map<String, BaseChatContext> subChatContextMap =  new ConcurrentHashMap<>();
    /**
     * 当前流程的子流程的cf，用于流程控制 - cf 的cancel
     */
    private Map<String, CompletableFuture<?>> futureMap =  new ConcurrentHashMap<>();
    /**
     * 当前流程的子流程的eventSource，用于流程控制 - EventSource 的cancel 
     */
    private Map<String, EventSource> eventSourceMap =  new ConcurrentHashMap<>();
    
    
    /**
     * 浅克隆
     * @param from 源对象
     */
    public void shallowCloneFrom(BaseChatContext from){
        this.setChatParam(from.getChatParam())
            .setGenerateParam(from.getGenerateParam())
            .setQueryRewriterResult(from.getQueryRewriterResult())
            .setRetrievalConfig(from.getRetrievalConfig())
            .setConversationConfig(from.getConversationConfig())
            .setLlmModelInfoDO(from.getLlmModelInfoDO())
            
            .setRealEventSource(from.getRealEventSource())
            .setServerSentEventFlux(from.getServerSentEventFlux())
            .setStopWatch(from.getStopWatch())
            .setSink(from.getSink())
            
            .setReasoningAnswerSB(from.getReasoningAnswerSB())
            .setShowAllContentSB(from.getShowAllContentSB())
            .setCompleteAnswerSB(from.getCompleteAnswerSB());
    }
    
    public BaseChatContext cleanLlmHandleStringBuffer(){
        // 清空stringBuffer
        this.setReasoningAnswerSB(new StringBuffer())
            .setCompleteAnswerSB(new StringBuffer())
            .setShowAllContentSB(new StringBuffer());
        return this;
    }
    public BaseChatContext cleanTaskContext(){
        // 清空stringBuffer
        return this.setSubChatContextMap(new ConcurrentHashMap<>())
            .setFutureMap(new ConcurrentHashMap<>())
            .setEventSourceMap(new ConcurrentHashMap<>());
    }

    /**
     * @param key 
     * @param future
     * @param cancelDependently 增加 主分支和获取问答子任务的 关联取消关系
     */
    public  void addFuture(String key, CompletableFuture<?> future, Boolean cancelDependently){
        if (futureMap == null) {
            futureMap = new ConcurrentHashMap<>();
        }
        futureMap.put(key, future);
        Collection<CompletableFuture<?>> oldFutures = futureMap.values();
        if (cancelDependently) {
            // 增加 主分支和获取问答子任务的 关联取消关系
            oldFutures.forEach(oldFuture -> {
                Futures.forwardCancellation(oldFuture, future);
            });
            Futures.forwardCancellation(future, oldFutures);
        }
    }

    /**
     * 迭代聚合当前context的usage和子context的usage，直到subChatContextMap为空
     * @return 聚合后的Usage对象
     */
    public Usage getAggregateUsage() {
        Usage totalUsage = new Usage();
        // 先处理当前上下文的usage
        totalUsage.setCompletionTokens(this.getUsage().getCompletionTokens());
        totalUsage.setPromptTokens(this.getUsage().getPromptTokens());
        totalUsage.setTotalTokens(this.getUsage().getTotalTokens());

        // 处理子上下文的usage
        if (this.getSubChatContextMap() != null && !this.getSubChatContextMap().isEmpty()) {
            for (BaseChatContext subContext : this.getSubChatContextMap().values()) {
                Usage subUsage = subContext.getAggregateUsage(); // 递归获取子上下文的usage
                totalUsage.setCompletionTokens(totalUsage.getCompletionTokens() + subUsage.getCompletionTokens());
                totalUsage.setPromptTokens(totalUsage.getPromptTokens() + subUsage.getPromptTokens());
                totalUsage.setTotalTokens(totalUsage.getTotalTokens() + subUsage.getTotalTokens());
            }
        }
        
        return totalUsage;
    }
    
    public UsageDetail buildUsageDetail(String contextName) {
        UsageDetail usageDetail = new UsageDetail();
        usageDetail.setContextName(contextName)
            .setUsage(this.getUsage());

        Map<String, BaseChatContext> subChatContextMap = this.getSubChatContextMap();
        if (subChatContextMap != null && !subChatContextMap.isEmpty()) {
            // 创建并设置子上下文详情列表
            List<UsageDetail> subUsageDetails = new ArrayList<>();
            subChatContextMap.forEach((k, v) -> {
                UsageDetail subUsageDetail = v.buildUsageDetail(k);
                subUsageDetails.add(subUsageDetail);
            });
            usageDetail.setSubUsageDetails(subUsageDetails); // 设置整个列表
        }
        return usageDetail;
    }


}
