package com.polarizon.rag.plugin.tool.llm;

import cn.hutool.http.ContentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.config.openai.AuthOpenAiInterceptor;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.chat.BaseChatCompletion;
import com.unfbx.chatgpt.exception.BaseException;
import com.unfbx.chatgpt.exception.CommonError;
import com.unfbx.chatgpt.function.KeyRandomStrategy;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.polarizon.rag.plugin.bean.constant.Constants.X_TRACE_ID_HEADER_NAME;

/**
 * 装饰者，为了将X_TRACE_ID_HEADER_NAME添加到请求头。（Interceptor方式加不进去）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomOpenAiClient {
    @Autowired
    private LlmConfig llmConfig;
    private final Map<String, OpenAiStreamClient> clientCache = new ConcurrentHashMap<>();
    
    @Getter
    private Dispatcher dispatcher;
    @Getter
    private ConnectionPool connectionPool;

    @PostConstruct
    public void init() {
        Integer llmMaxRequests = llmConfig.getLlmMaxRequests();
        dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(llmMaxRequests);
        dispatcher.setMaxRequestsPerHost(llmMaxRequests);
        connectionPool = new ConnectionPool(
            llmMaxRequests, // 最大空闲连接数
            5, // 保持连接的时间
            TimeUnit.MINUTES
        );
        
    }

    @SuppressWarnings("KotlinInternalInJava")
    @NotNull
    public OpenAiStreamClient getOpenAiStreamClient(ModelInfoDO model) {
        String cacheKey = model.getApiKey() + "|" + model.getEndpointUrl();
        
        return clientCache.computeIfAbsent(cacheKey, k -> {
            try {
                
                OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .dispatcher(dispatcher)
                    .connectionPool(connectionPool)
                    .addInterceptor(new AuthOpenAiInterceptor(model.getApiKey()))
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(3000, TimeUnit.SECONDS)
                    .readTimeout(3000, TimeUnit.SECONDS)
                    .protocols(Collections.singletonList(Protocol.HTTP_1_1))
                    .build();
                
                String baseurl = model.getEndpointUrl();
                baseurl = baseurl.endsWith("v1") ? baseurl.replaceAll("v1", "") : baseurl;
                
                return OpenAiStreamClient.builder()
                    .apiKey(Collections.singletonList(model.getApiKey()))
                    .keyStrategy(new KeyRandomStrategy())
                    .okHttpClient(okHttpClient)
                    .apiHost(baseurl)
                    .build();
            } catch (Exception e) {
                throw new RuntimeException("Failed to create OpenAI client: " + e.getMessage(), e);
            }
        });
    }

    public <T extends BaseChatCompletion> EventSource streamChatCompletion(T chatCompletion,
                                                                           EventSourceListener eventSourceListener,
                                                                           String traceId, OpenAiStreamClient client) {
        if (Objects.isNull(eventSourceListener)) {
            log.error("参数异常：EventSourceListener不能为空，可以参考：com.unfbx.chatgpt.sse.ConsoleEventSourceListener");
            throw new BaseException(CommonError.PARAM_ERROR);
        }
        if (!chatCompletion.isStream()) {
            chatCompletion.setStream(true);
        }
        try {
            EventSource.Factory factory = EventSources.createFactory(client.getOkHttpClient());
            ObjectMapper mapper = new ObjectMapper();
            String requestBody = mapper.writeValueAsString(chatCompletion);
            Request request = new Request.Builder()
                .url(client.getApiHost() + "v1/chat/completions")
                .header(X_TRACE_ID_HEADER_NAME, traceId)
                .post(RequestBody.create(MediaType.parse(ContentType.JSON.getValue()), requestBody))
                .build();
            //创建事件
            return factory.newEventSource(request, eventSourceListener);
        } catch (Exception e) {
//            log.error("请求参数解析异常：", e);
            throw new BusinessExecutionException("请求参数解析异常：" + e.getMessage(),e);
        }
    }

}
