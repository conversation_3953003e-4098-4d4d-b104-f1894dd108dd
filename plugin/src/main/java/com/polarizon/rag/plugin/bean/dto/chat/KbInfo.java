package com.polarizon.rag.plugin.bean.dto.chat;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

import static com.polarizon.rag.plugin.bean.constant.Constants.ENABLE_PARENT_INDEX_DEFAULT;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class KbInfo {
    private String kbID;
    private String kbName;
    private String question;
    private String originalQuestion;
    private String rewrittenQuestion;
    private String embeddingUrl;
    private String embeddingApiKey;
    private String embeddingModel;
    private Boolean enableParentIndex;

    /**
     * 回答问题用原问题
     *
     * @param kbInfos
     */
    public static void resetQuestionInfo(List<KbInfo> kbInfos) {
        kbInfos.forEach(kbInfo -> kbInfo.setQuestion(kbInfo.getOriginalQuestion()));
    }

    /**
     * 回答问题用 originalQuestion , searchChunk 和 rerank 用 rewrittenQuestion
     *
     * @param kbInfos
     * @param rewrittenQuestion
     */
    public static List<KbInfo> buildQuestionInfoWithRewrittenQuestion(List<KbInfo> kbInfos, String originalQuestion,
        String rewrittenQuestion) {
        return kbInfos.stream().map(kbInfo -> {
            kbInfo.setRewrittenQuestion(rewrittenQuestion);
            return new KbInfo()
                .setKbID(kbInfo.getKbID())
                .setKbName(kbInfo.getKbName())
                .setQuestion(rewrittenQuestion)
                .setOriginalQuestion(originalQuestion)
                .setRewrittenQuestion(rewrittenQuestion)
                .setEmbeddingUrl(kbInfo.getEmbeddingUrl())
                .setEmbeddingApiKey(kbInfo.getEmbeddingApiKey())
                .setEmbeddingModel(kbInfo.getEmbeddingModel())
                .setEnableParentIndex(ENABLE_PARENT_INDEX_DEFAULT)
                ;
        }).toList();
    }
}
