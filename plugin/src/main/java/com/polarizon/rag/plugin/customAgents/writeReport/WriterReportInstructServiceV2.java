package com.polarizon.rag.plugin.customAgents.writeReport;

import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.PROCESS_STATUS;
import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_REASONING;
import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.customAgents.writeReport.ThinkTanksReportConstants.OUTLINE_TEMPLATE;
import static com.polarizon.rag.plugin.customAgents.writeReport.ThinkTanksReportV2Constants.WRITE_REPORT_3_GEN_CHAPTER_CONTENT;
import static com.polarizon.rag.plugin.customAgents.writeReport.ThinkTanksReportV2Constants.WRITE_REPORT_4_GEN_BODY_CONTENT;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

import com.polarizon.rag.plugin.tool.llm.LlmHandleService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.instruct.PromptTemplateParam;
import com.polarizon.rag.kb.KnowledgeBaseDO;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.instruct.InstructChatParam;
import com.polarizon.rag.plugin.bean.dto.instruct.Outline;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.service.InstructHelper;
import com.polarizon.rag.plugin.service.instruct.InstructChatContext;
import com.polarizon.rag.plugin.tool.RetrievalHandleService;
import com.polarizon.rag.plugin.util.FluxUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import com.unfbx.chatgpt.entity.chat.BaseMessage;
import com.unfbx.chatgpt.entity.chat.Message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import io.github.futures4j.ExtendedFuture;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.FluxSink;

/**
 * 第二版的[智库]报告生成
 */
@Slf4j
@Service
public class WriterReportInstructServiceV2 {
    @Autowired
    private LlmConfig llmConfig;
    @Autowired
    private RetrievalHandleService retrievalHandleService;
    @Autowired
    private LlmHandleService llmHandleService;

    @NotNull
    public static Outline buildOutline(List<PromptTemplateParam> displayParams) {
        return buildOutline(displayParams, OUTLINE_TEMPLATE);
    }

    @NotNull
    public static Outline buildOutline(List<PromptTemplateParam> displayParams, String paramName) {
        String outlineJsonParam =
                ChatHelper.extractParam(displayParams, paramName, StrUtil.format("{} 参数缺失", paramName)).getValue();

        return Outline.fromJson(outlineJsonParam);
    }

    /**
     * [
     * （每个章节的步骤）
     * 用模板的每个章节 生成问题  （r1_step_question） -》
     * 问题做检索 -》
     * 检索内容 回答问题  （r1_step_single）
     * -》 回答问题会作为正文生成的参考资料 （r1_step_article ;当前章节的artical,不同章节的这一步没法并行，因为目前后面的章节，依赖前面的章节内容，以保持全文风格一致，禹宏在尝试优化）
     * ]
     */
    public void writeReport(WriteReportChatContext chatContext) {
        
        PromptTemplateParam targetParam =
            ChatHelper.extractParam(chatContext.getInstructDO().getPromptTemplate().getDisplayParams(),
                ThinkTanksReportV2Constants.TARGET, "target参数缺失");
        FluxSink<ServerSentEvent<String>> sink = chatContext.getSink();
        List<String> kbIDListParam = new ArrayList<>();
        List<KnowledgeBaseDO> validKnowledgeBaseDOS =
            RetrievalHandleService.getValidKnowledgeBaseDOS(kbIDListParam);
        boolean validKnowledgeBaseEmtpy = CollUtil.isEmpty(validKnowledgeBaseDOS);
        // TODO 怎么处理没有有效的知识库,不生成正文
        if (validKnowledgeBaseEmtpy) {
//            throw new BusinessExecutionException("该应用没有有效的知识库");
        }
        // 获取 topic 和 outline 参数
        List<PromptTemplateParam> chatDisplayParams =
            chatContext.getInstructParam().getPromptTemplate().getDisplayParams();
        Outline outline = buildOutline(chatDisplayParams, ThinkTanksReportConstants.OUTLINE);
        List<Outline.Chapter> chapters = outline.getChapters();

        // outline 和 outlineTemplate 的参数
        PromptTemplateParam outlineParam = ChatHelper.extractParam(chatDisplayParams,
            ThinkTanksReportV2Constants.OUTLINE, "outline参数缺失");
        PromptTemplateParam topicParam = ChatHelper.extractParam(chatDisplayParams, ThinkTanksReportV2Constants.TOPIC
            , "topic参数缺失");
        
        String question = topicParam.getValue();
        FluxUtil.doSinkNext(
            ChatRes.buildStreamAnswer("正文开始生成...",  question), sink, PROCESS_STATUS);
        
        String traceID = MDC.get(TRACE_ID);
        AtomicInteger sourceStartNumAtomic = new AtomicInteger(0);
        // 为每个章节创建longTimeJob的CompletableFuture
        WriteReportChatContext finalChatContext = chatContext;
        // 处理问题答案的异步任务，可以提前异步并行处理
        List<CompletableFuture<String>> answerGenCfList = new ArrayList<>();

        CountDownLatch chapterOneFinishCD = new CountDownLatch(1);
        for (int i = 0; i < chapters.size(); i++) {
            int chapterNum = i;
            Outline.Chapter chapter = chapters.get(i);
            String chapterName = chapter.getChapterName();
            int chapterNumForHuman = chapterNum + 1;
            PromptTemplateParam chapterParam =
                ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.CHAPTER,
                    Assert.notBlank(chapterName, "第{}章章节名称为空", chapterNumForHuman));
            CompletableFuture<String> chapterAnswerGenCF = 
                ExtendedFuture.supplyAsync(() -> // 1.生成报告章节检索问题列表 - r1_step_question
                {
                    List<PromptTemplateParam> promptTemplateParams = 
                        List.of(targetParam, chapterParam, topicParam, outlineParam);
                    return handleGenChapterQuestions(sink, traceID, chapterNumForHuman, question, promptTemplateParams,
                         finalChatContext);
                }, llmConfig.getLlmPoolExecutor())
                .asCancellableByDependents(true)
                .thenApplyAsync(questions -> // 2. 检索数据 +  qNum * （3.基于检索结果回答问题  r1_step_single）
                    handleRetrieveNAnswer(questions, traceID, chapterNumForHuman, question, topicParam, 
                        outlineParam, finalChatContext, sourceStartNumAtomic, chapterOneFinishCD)
                    , llmConfig.getCommonPoolExecutor())
                    .whenCompleteAsync((chapterAnswer, ex)-> {
                        if (chapterNumForHuman == 1) {
                            if (chapterOneFinishCD.getCount() > 0) {
                                chapterOneFinishCD.countDown();
                            }
                        }
                        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
                        if (ex != null) {
                            // TODO traceId 为空，走不到这里
//                            log.error("chapterAnswerGenCF 出现异常: {}", ex.getMessage(), ex);
                            // TODO 局部异常,不断链接?
                            sink.error(ex);
                            ChatHelper.releaseResource(chatContext);
                        }
                    },   llmConfig.getCommonPoolExecutor());

            chatContext.addFuture(StrUtil.format("第{}章知识准备",  chapterNumForHuman), chapterAnswerGenCF,true);
            answerGenCfList.add(chapterAnswerGenCF);
        }

        CompletableFuture<Void> bodyCf = ExtendedFuture
            .anyOf(answerGenCfList.toArray(new CompletableFuture[0]))
            .thenRunAsync(() ->
                handleGenReportBody(chapters, answerGenCfList, targetParam, topicParam, outlineParam, traceID,
                    question, finalChatContext)
            , llmConfig.getLlmPoolExecutor()).whenCompleteAsync((chapterAnswer, ex)-> {
                ChatHelper.setCustomTraceId(traceID, "main");
                if (ex != null) {
                    // TODO traceId 为空，走不到这里
                    log.error("chapterAnswerGenCF 出现异常: {}", ex.getMessage(), ex);
                    // TODO 局部异常,不断链接?
                    sink.error(ex);
                    ChatHelper.releaseResource(chatContext);
                }
            },   llmConfig.getCommonPoolExecutor());

        chatContext.addFuture("正文主题生成", bodyCf,true);
    }

    private void handleGenReportBody(List<Outline.Chapter> chapters,
                                     List<CompletableFuture<String>> answerGenCfList, PromptTemplateParam targetParam,
                                     PromptTemplateParam topicParam, PromptTemplateParam outlineParam, String traceID,
                                     String question, WriteReportChatContext writeReportCC) {
        FluxSink<ServerSentEvent<String>> sink = writeReportCC.getSink();
        String instructCode = WRITE_REPORT_4_GEN_BODY_CONTENT;
        // 通过 instruct 的 code 获取
        InstructDO genReportBodyInstructDO = InstructHelper.getInstructDOByCode(instructCode);
        // 第一个章节的"前一个结果" ， 初始内容为"# 标题"
        String lastChapterResult = buildTitle(question);
        // 存储章节结果: 存储章节结果: key:章节序号，value: <推理结果,正文>
        Map<Integer,ImmutablePair<String,String>> chapterResultMap = new HashMap<>();
        // 顺序处理章节内容生成
        for (int chapterIndex = 0; chapterIndex < chapters.size(); chapterIndex++) {
//            if (Thread.currentThread().isInterrupted()) {
//                log.error("线程被中断，终止报告chapter-{} 生成", chapterIndex);
//                sink.error(new CancellationException("线程被中断，终止报告生成"));
//                // 抛出异常，并结束所有任务
//                answerGenCfList.forEach(cf -> cf.cancel(true));
//                // TODO 测试，需要 completeExceptionally？
//                answerGenCfList.forEach(cf -> cf.completeExceptionally(new CancellationException("线程被中断，终止报告生成")));
//                break;
//            }
            // TODO 不需要上面手动检查 join 会被 响应interruption（抛出 Interruption ） ?
            String answers = answerGenCfList.get(chapterIndex).join();
            int chapterNumForHuman = chapterIndex+1;
            if (StringUtils.isBlank(answers)) {
                log.error("第{}章知识不足(非潜研),终止报告生成", chapterNumForHuman);
                sink.error(new BusinessExecutionException("第" + chapterNumForHuman + "章知识不足(非潜研),终止报告生成"));
                // 抛出异常，并结束所有任务
//                answerGenCfList.forEach(cf -> cf.completeExceptionally(new BusinessExecutionException("第" + chapterNumForHuman + "章答案为空")));
                break;
            }
            Outline.Chapter chapter = chapters.get(chapterIndex);
            String chapterName = chapter.getChapterName();
            List<PromptTemplateParam> promptTemplateParams =
                buildGenChapterBodyTemplateParams(chapterName, chapterNumForHuman, answers, lastChapterResult, targetParam
                    , topicParam, outlineParam);

            // 发送流式推理内容
            String preReasoningContent = StrUtil.format("构思第{}章《{}》:\n", chapterNumForHuman, chapterName);
            FluxUtil.doSinkNext(ChatRes.buildStreamReasonContent(preReasoningContent,""), sink, STREAM_REASONING);
            InstructChatParam bodyQuestionInstructChatParam =
                InstructHelper.buildInstructChatParam(promptTemplateParams, WRITE_REPORT_4_GEN_BODY_CONTENT, false);
            InstructChatContext newInstructChatContext =
                writeReportCC.bornSubContext(genReportBodyInstructDO, bodyQuestionInstructChatParam,
                    "生成第{}章报告正文", chapterNumForHuman);
            String chapterBody = 
                handleGenChapterBody(traceID, chapterNumForHuman, question, newInstructChatContext);
            chapterResultMap.put(chapterIndex, new ImmutablePair<>(preReasoningContent+newInstructChatContext.getReasoningAnswerSB().toString(), chapterBody));

            // 输出流，每段内容末尾增加一个换行符
            FluxUtil.doSinkNext(
                ChatRes.buildStreamAnswer("\n", ""), sink, STREAM_ANSWER);
            // 第一章的lastChapterResult 为空，所以需要单独处理
            if (chapterIndex == 0) {
                lastChapterResult = "";
            } else if (!lastChapterResult.endsWith("\n")) {
                // 其他章节，可以增加一个换行符
                lastChapterResult += "\n";
            }
            lastChapterResult = lastChapterResult + chapterBody;
        }
        StringBuffer reasoningResultSB = new StringBuffer();
        // 获取所有推理结果
        List<String> chapterReasoningList = chapterResultMap.values().stream().map(ImmutablePair::getLeft).toList();
        for (int chapterIndex = 0; chapterIndex < chapterReasoningList.size(); chapterIndex++) {
            reasoningResultSB.append(chapterReasoningList.get(chapterIndex)).append("\n");
        }

        ChatRes completeAnswer = FluxUtil.buildCompleteAnswer(writeReportCC.getChatParam(), lastChapterResult,
             "DEFAULT", writeReportCC, reasoningResultSB);
        log.debug("报告已完成:\n{}", JSONUtils.getJacksonJsonString(completeAnswer));
        FluxUtil.doSinkNext(completeAnswer, sink, COMPLETE_ANSWER, traceID);
        sink.complete();
    }

    @NotNull
    private static String buildTitle(String question) {
        if (!question.endsWith("报告")) {
            question += "报告";
        }
        if (!question.startsWith("# ")){
            question = "# " + question;
        }
        return question;
    }

    @NotNull
    private static List<PromptTemplateParam> buildGenChapterBodyTemplateParams(
        String chapterName, int chapterNumForHuman, String answers, String draftContent, 
        PromptTemplateParam targetParam, PromptTemplateParam topicParam, PromptTemplateParam outlineParam) 
    {
        PromptTemplateParam chapterParam = ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.CHAPTER,
            Assert.notBlank(chapterName, "第{}章章节名称为空", chapterNumForHuman));
        PromptTemplateParam answersParam =
            ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.ANSWERS,
 //TODO 结果允许为空？
//                Assert.notBlank(answers, "answers 参数缺失"));
                answers);
        PromptTemplateParam draftParam =
            ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.DRAFT,
                Assert.notBlank(draftContent, "draft 参数缺失"));
        return List.of(targetParam, chapterParam, topicParam, outlineParam, draftParam, answersParam);
    }

    @NotNull
    private String handleRetrieveNAnswer(List<String> questions, String traceID, int chapterNumForHuman,
                                         String question, PromptTemplateParam topicParam,
                                         PromptTemplateParam outlineParam,
                                         WriteReportChatContext chatContext,
                                         AtomicInteger sourceStartNumAtomic,
                                         CountDownLatch countDownLatch
    ) {
        FluxSink<ServerSentEvent<String>> sink = chatContext.getSink();
        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
        String processMsg = StrUtil.format("第{}章信息检索中...", chapterNumForHuman);
        FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(processMsg, question), sink, PROCESS_STATUS);
        log.debug(processMsg);
        
        //finalQuestionIndex, new ImmutablePair<>(chapterQuestion, reportBodyResp)
        Map<Integer, ImmutablePair<String, String>> qaPairs = new ConcurrentHashMap<>();

        List<CompletableFuture<Void>> answerGenCFList = new ArrayList<>();
        for (int questionIndex = 0; questionIndex < questions.size(); questionIndex++) {
            String chapterQuestion = questions.get(questionIndex);
            int finalQuestionIndex = questionIndex;
            
            CompletableFuture<Void> answerFuture =
                ExtendedFuture.supplyAsync(()->{
                        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
                        chatContext.getChatParam().setQuestion(chapterQuestion);
                        return retrievalHandleService
                            .multiRetrieve(chatContext, false, finalQuestionIndex).getValue();
                        }, 
                        llmConfig.getRetrivalPoolExecutor())
                    .asCancellableByDependents(true)
                    .thenAcceptAsync(sources -> // 3.基于检索结果生成章节作为知识问答内容  r1_step_single
                    {
                        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
                        log.debug("第{}章的question({})[{}], sources:{}", chapterNumForHuman, finalQuestionIndex, 
                            chapterQuestion, JSONUtils.getJacksonJsonString(sources));
                        List<String> chunks = sources.getAllSourcesStrList();
                        // 没搜出内容
                        if (CollectionUtils.isEmpty(chunks) || chunks.stream().noneMatch(StringUtils::isNotBlank)) {
                            log.warn("第{}章的question({})[{}]未找到相关知识,跳过生成", chapterNumForHuman, 
                                finalQuestionIndex, chapterQuestion);
                            FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(
                                StrUtil.format("第{}章的问题({})[{}]未找到相关知识,跳过回答问题", 
                                    chapterNumForHuman, finalQuestionIndex, chapterQuestion), 
                                     chapterQuestion), 
                                sink, PROCESS_STATUS);

                        } else {
                            // 设置 sources
                            // 添加到总列表中 
//                            eventCallbackParam.getSourceBORespListRef().get().addAll(sourceStartNum, questionSourceBOResp);
                            // 改成设置到generateParam中
                            GenerateParam generateParam = chatContext.getGenerateParam();
                            generateParam.setSources(generateParam.getSources().appendSources(sources));
                            
                            FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(
                                StrUtil.format("开始生成第{}章的问题({})[{}]的回答...", chapterNumForHuman, 
                                    finalQuestionIndex, chapterQuestion),
                                 chapterQuestion), sink, PROCESS_STATUS);
                            PromptTemplateParam questionParam =
                                ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.QUESTION,
                                    Assert.notBlank(chapterQuestion, "question参数缺失"));
                            String chunkStr = ChatHelper.chunksToString(sources, sourceStartNumAtomic.getAndAdd(chunks.size()));
                            PromptTemplateParam contextParam =
                                ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportV2Constants.CONTEXT,
                                    Assert.notBlank(chunkStr, "chunkStr参数缺失"));
                            
                            InstructChatParam bodyInstructChatParam =
                                InstructHelper.buildInstructChatParam(List.of(topicParam, contextParam, outlineParam,
                                        questionParam),
                                    WRITE_REPORT_3_GEN_CHAPTER_CONTENT, false);
                            // 通过 instruct 的 code 获取
                            InstructDO reportBodyInstructDO =
                                InstructHelper.getInstructDOByCode(WRITE_REPORT_3_GEN_CHAPTER_CONTENT);
                            InstructChatContext subContext =
                                chatContext.bornSubContext(reportBodyInstructDO, bodyInstructChatParam,
                                    "第{}章的问题({})[{}]的答案生成", chapterNumForHuman, finalQuestionIndex, chapterQuestion);
                            
                            CompletableFuture<String> bodyGenerateCF = new CompletableFuture<>();
                            if (chapterNumForHuman != 1) {
                                countDownLatch.await(600, TimeUnit.SECONDS);
                            }
                            String reportBodyResp =
                                llmHandleService.handleInstructGenerateWithRetry(
                                    StrUtil.format("生成第{}章章节正文", chapterNumForHuman), subContext, 
                                    false, false, bodyGenerateCF, Function.identity(), null);

                            FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(
                                StrUtil.format("第{}章的问题({})[{}]的回答生成完成", chapterNumForHuman,
                                    finalQuestionIndex, chapterQuestion),
                                chapterQuestion), sink, PROCESS_STATUS);

                            log.debug("第{}章,reportBodyResp:{}", chapterNumForHuman,
                                JSONUtils.getJacksonJsonString(reportBodyResp));
                            qaPairs.put(finalQuestionIndex, new ImmutablePair<>(chapterQuestion, reportBodyResp));
                        }
//                                return CompletableFuture.completedFuture(content);
                    }, llmConfig.getLlmPoolExecutor())
                    .whenCompleteAsync( (result ,ex )-> {
                        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
                        if (ex!=null) {
                            // TODO traceId 为空，走不到这里
                            log.error("出现异常: {}", ex.getMessage());
                            // TODO 局部异常,不断链接?
                            sink.error(ex);
                            //                    chapterOutlineQuestionFuture.completeExceptionally(ex);
                        }
                    },  llmConfig.getCommonPoolExecutor());
            chatContext.addFuture(
                StrUtil.format("第{}章的问题({})[{}]的答案生成", chapterNumForHuman, finalQuestionIndex, chapterQuestion),
                answerFuture,true);
            answerGenCFList.add(answerFuture);
        }
        if (CollectionUtils.isEmpty(answerGenCFList)) {
            return "";
        }
        CompletableFuture<Void> allAnswerGenCf =
            ExtendedFuture.allOf(answerGenCFList.toArray(new CompletableFuture[0]));
        chatContext.addFuture(StrUtil.format("第{}章所有问题的答案生成等待", chapterNumForHuman), allAnswerGenCf,true);
        // 由于这个方法是异步的，所以这里需要等待所有的检索任务完成
        // 注意：这里必须在异步中运行，不阻塞主线程，且需要阻塞异步线程，等[智库][v2]-报告：4.生成报告章节正文待结果，;
        allAnswerGenCf.join();
        if (chapterNumForHuman == 1) {
            countDownLatch.countDown();
        }
        log.debug("allOf 结束生成第{}章所有问题的答案...", chapterNumForHuman);
        return buildAnswerParam(qaPairs);
    }

    private String handleGenChapterBody(String traceID, int chapterNumForHuman,
                                        String question, InstructChatContext instructChatContext) {
        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
        FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(
            StrUtil.format("第{}章生成检索正文的问题中...", chapterNumForHuman),
             question), instructChatContext.getSink(), PROCESS_STATUS);

        CompletableFuture<String> bodyQuestionFuture = new CompletableFuture<>();

        log.debug("bodyQuestionFuture.isCancelled:{}",bodyQuestionFuture.isCancelled());
        instructChatContext.addFuture( StrUtil.format("生成章节({})正文", chapterNumForHuman), bodyQuestionFuture, true);
        // TODO 这里面报错，不会断流
        return
            llmHandleService.handleInstructGenerateWithRetry(StrUtil.format("生成章节({})正文", chapterNumForHuman), 
                instructChatContext, true, false, bodyQuestionFuture, Function.identity(), null);
    }

    private List<String> handleGenChapterQuestions(FluxSink<ServerSentEvent<String>> sink, String traceID,
                                                   int chapterNumForHuman, String question,
                                                   List<PromptTemplateParam> promptTemplateParams,
                                                   InstructChatContext instructChatContext) {
        ChatHelper.setCustomTraceId(traceID, chapterNumForHuman);
        FluxUtil.doSinkNext(ChatRes.buildStreamAnswer(
            StrUtil.format("第{}章生成检索正文的问题中...", chapterNumForHuman),
             question), sink, PROCESS_STATUS);

        InstructChatParam bodyQuestionInstructChatParam =
            InstructHelper.buildInstructChatParam(promptTemplateParams,
                ThinkTanksReportV2Constants.WRITE_REPORT_1_GEN_CHAPTER_QUESTIONS,false);
        // 通过 instruct 的 code 获取
        InstructDO chapterQuestionInstructDO =
            InstructHelper.getInstructDOByCode(ThinkTanksReportV2Constants.WRITE_REPORT_1_GEN_CHAPTER_QUESTIONS);
        InstructChatContext newICC =
            instructChatContext.bornSubContext(chapterQuestionInstructDO, bodyQuestionInstructChatParam,
                "生成用于检索({})正文知识的问提", chapterNumForHuman);
        
        CompletableFuture<String> bodyQuestionFuture = new CompletableFuture<>();
        return llmHandleService.handleInstructGenerateWithRetry(
            StrUtil.format("生成用于检索({})正文知识的问提", chapterNumForHuman), newICC, false, false, 
            bodyQuestionFuture, str -> JSONUtils.parseObjectSanitized(str, new TypeReference<>() {}), null);
    }

    private static String buildAnswerParam(Map<Integer, ImmutablePair<String, String>> qaPairs) {
        if (Collections.isEmpty(qaPairs)) {
            return "";
        }
        List<Message> messages = new ArrayList<>();
        qaPairs.keySet().stream().sorted().toList().forEach(questionIndex -> {
            String question = qaPairs.get(questionIndex).getLeft();
            String answer = qaPairs.get(questionIndex).getRight();
            Message message = Message.builder().role(BaseMessage.Role.USER).content(question).build();
            messages.add(message);
            Message answerMsg = Message.builder().role(BaseMessage.Role.ASSISTANT).content(answer).build();
            messages.add(answerMsg);
        });
        return JSON.toJSONString(messages);
    }
}
