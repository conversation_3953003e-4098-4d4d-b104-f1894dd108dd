package com.polarizon.rag.plugin.common.service.chat;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.util.SpringEnvUtil;
import com.polarizon.rag.chat.*;
import com.polarizon.rag.kb.KnowledgeFileChunkDO;
import com.polarizon.rag.kb.KnowledgeFileDO;
import com.polarizon.rag.kb.feign.KnowledgeFileChunkDOFeign;
import com.polarizon.rag.kb.feign.KnowledgeFileDOFeign;
import com.polarizon.rag.kb.params.KnowledgeFileChunkDOParams;
import com.polarizon.rag.kb.params.KnowledgeFileDOParams;
import com.polarizon.rag.plugin.common.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.common.bean.dto.RagChatStreamReq;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.common.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.common.exception.SystemBusyExecutionException;
import com.polarizon.rag.plugin.common.util.Constants;
import com.polarizon.rag.plugin.common.util.FeignUtil;
import com.unfbx.chatgpt.exception.BaseException;
import com.unfbx.chatgpt.exception.CommonError;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static com.polarizon.rag.plugin.common.configs.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.common.util.Constants.*;


/**
 * 各种关于chat 的公共方法。
 * 它不依赖其它服务；被其它服务依赖！
 */
@Slf4j
@Service
public class ChatHelper {
    public static int DEFAULT_MAX_TOKEN = 8192;
    // 会从 chatService 的配置 赋值
    public static String defaultModelName;
    public static String defaultSystemPrompt;
    public static String defaultUserPrompt;
    public static String defaultNoChunkUserPrompt;
    public static String defaultNoChunkSystemPrompt;
    public static Integer defaultRerankTopK;
    public static List<String> endFlags;

    public static int llmOutputCheckWindowsSize;
    public static int inspectMaxChunkSize = 20;

    public static List<SourceBO> convertSourceList(List<ChatRes.SourceBOResp> sourceBORespList) {
        // 保存答案
        List<SourceBO> sourceBOList = new ArrayList<>();
        if (sourceBORespList != null) {
            sourceBOList = sourceBORespList.stream()
                .map(source -> buildSource(source, source.getKnowledgeFileChunkBO(), source.getKnowledgeFileBO()))
                .toList();
        }
        return sourceBOList;
    }

    private static SourceBO buildSource(ChatRes.SourceBOResp source, KnowledgeFileChunkDO knowledgeFileChunkBO,
                                        KnowledgeFileDO knowledgeFileBO) {
        SourceBO sourceBO = new SourceBO();
        sourceBO.setSourceNumber(source.getSourceNumber());
        sourceBO.setFileID(source.getFileID());
        sourceBO.setKbChunkID(source.getKbChunkID());
        sourceBO.setScore(source.getScore());
        sourceBO.setKbType(source.getKbType());
        sourceBO.setDisplayUrl(source.getDisplayUrl());
        sourceBO.setSummary(source.getSummary());
        sourceBO.setWebName(source.getWebName());
        sourceBO.setContent(source.getContent());
        sourceBO.setFileIndex(source.getFileIndex());

        return buildSource(sourceBO, knowledgeFileChunkBO, knowledgeFileBO);
    }

    public static SourceBO buildSource(SourceBO source, KnowledgeFileChunkDO chunk, KnowledgeFileDO knowledgeFileDO) {
        SourceBO sourceBO = new SourceBO();
        sourceBO.setSourceNumber(source.getSourceNumber());
        sourceBO.setFileID(source.getFileID());
        sourceBO.setScore(source.getScore());
        sourceBO.setKbType(source.getKbType());
        sourceBO.setDisplayUrl(source.getDisplayUrl());
        sourceBO.setSummary(source.getSummary());
        sourceBO.setWebName(source.getWebName());
        sourceBO.setContent(source.getContent());
        sourceBO.setFileIndex(source.getFileIndex());

        if (chunk != null) {
            sourceBO.setKbChunkID(chunk.getId());
            sourceBO.setCaptureID(chunk.getCaptureID());
            sourceBO.setChapterID(chunk.chapterID);
            sourceBO.setChapterName(chunk.getChapterName());
            sourceBO.setParentChapterID(chunk.getParentChapterID());
            sourceBO.setParentChapterName(chunk.getParentChapterName());
            sourceBO.setContent(chunk.getContent());
            sourceBO.setChunkID(chunk.getChunkID());
            sourceBO.setPageNumber(chunk.getPageNumber());
            sourceBO.setCapture(chunk.getCapture());
        }
        if (knowledgeFileDO != null) {
            sourceBO.setFileName(knowledgeFileDO.getName());
            sourceBO.setKnowledgeBaseID(knowledgeFileDO.getKnowledgeBaseID());
            sourceBO.setChunkSize(knowledgeFileDO.getChunkSize());
            sourceBO.setCaptureSize(knowledgeFileDO.getCaptureSize());
            sourceBO.setFormat(knowledgeFileDO.getFormat());
            sourceBO.setFileBytes(knowledgeFileDO.getFileBytes());
            sourceBO.setEnable(knowledgeFileDO.getEnable());
            sourceBO.setStatus(knowledgeFileDO.getStatus());
            sourceBO.setInputFileS3(knowledgeFileDO.getInputFileS3());
            sourceBO.setFileS3(knowledgeFileDO.getFileS3());
            sourceBO.setParserVersion(knowledgeFileDO.getParserVersion());
        }

        return sourceBO;
    }

    @NotNull
    public static MessageDO buildMessageDO(ConversationDO conversationDO, List<String> kbIds, String content,
                                           MessageRoleEnum messageRoleEnum, List<SourceBO> source, String messageStyle,
                                           List<ExtraContentBO> extraContentBO, String reasoningContent) {
        MessageDO messageDO = new MessageDO();
        messageDO.setConversationID(conversationDO.getId());
        messageDO.setChatTime(System.currentTimeMillis());
        messageDO.setRole(messageRoleEnum);
        messageDO.setContent(content);
        messageDO.setReasoningContent(reasoningContent);
        messageDO.setKbIDList(kbIds);
        messageDO.setSource(source);
        messageDO.setConversationConfig(conversationDO.getConversationConfig());
        messageDO.setStyle(messageStyle);
        messageDO.setRequestId(MDC.get(TRACE_ID));
        messageDO.setExtraContent(extraContentBO);
        return messageDO;
    }

    @NotNull
    public static MessageDO buildMessageDO(ConversationDO conversationDO, List<String> kbIds, String content,
                                           MessageRoleEnum messageRoleEnum, List<SourceBO> source, String messageStyle,
                                           List<ExtraContentBO> extraContentBO, String reasoningContent, String remark) {
        MessageDO messageDO = buildMessageDO(conversationDO, kbIds, content, messageRoleEnum, source, messageStyle, extraContentBO, reasoningContent);
        messageDO.setRemark(remark);
        return messageDO;
    }


    @NotNull
    public static Flux<ServerSentEvent<String>> buildErrorRespData(Throwable e) {
        Integer status = ResultDTO.Constants.FAILURE;
        if (e instanceof SystemBusyExecutionException) {
            status = SystemBusyExecutionException.STATUS_CODE;
        }
        return Flux.just(ServerSentEvent.<String>builder().event(ERROR_ANSWER)
            .data(JSON.toJSONString(NewResultDTO.error(status, e.getMessage(), null, MDC.get(TRACE_ID)))).build());
    }

    public static void doErrorSinkNext(String messageId, String question, String response, FluxSink<ServerSentEvent<String>> sink, String traceId) {
        // 设置ChatRes
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageId);
        chatRes.setQuestion(question);
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setResponse(response);
        chatRes.setSource(Lists.newArrayList());
        chatRes.setStyle(Constants.MESSAGE_TYPE_ERROR);

        // 推送消息
        ChatHelper.doErrorSinkNext(chatRes, response, sink, traceId);
        sink.complete();
    }

    public static <T> ServerSentEvent<String> doErrorSinkNext(T data, String msg, FluxSink<ServerSentEvent<String>> sink, String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(ERROR_ANSWER)
            .data(JSON.toJSONString(NewResultDTO.error(ResultDTO.Constants.FAILURE, msg, data, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

    static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String answer) {
        return doSinkNext(content, sink, answer, MDC.get(TRACE_ID));
    }

    static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer) {
        return doSinkNext(content, sink, answer, MDC.get(TRACE_ID));
    }

    public static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String answer,
                                                     String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(answer)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

    public static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer,
                                                         String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(answer)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

    public static <T> ServerSentEvent<String> doSinkNext(FluxSink<ServerSentEvent<String>> sink, String event, NewResultDTO<T> resultDTO) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(event)
            .data(JSON.toJSONString(resultDTO))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }


    public static void handleOnFailure(Throwable t, Response response, FluxSink<ServerSentEvent<String>> sink,
                                       CompletableFuture<String> future, Boolean isComplete) {
        String errorMsg = t == null ? "" : t.getMessage();
        String responseCode = response == null ? "" : String.valueOf(response.code());
        String responseMsg = response == null ? "" : response.message();
        String responseBodyErrorMsg = extractResponseError(response);
        String message =
            "大模型调用失败:" + errorMsg + "[responseCode]:" + responseCode + "; [responseMsg]:" + responseMsg
                + "; [errorMsg]:" + responseBodyErrorMsg;


        Exception ex = t == null ? new BusinessExecutionException(message) : new BusinessExecutionException(message, t);

        // 有 future，说明是中间过程,交给上层处理
        if (future != null) {
            future.completeExceptionally(ex);
        }
        if (isComplete != null && isComplete) {
            sink.error(ex);

        }
    }

    public static void cancelRealEventSource(AtomicReference<EventSource> realEventSourceAtomicReference) {
        if (realEventSourceAtomicReference.get() != null) {
            realEventSourceAtomicReference.get().cancel();
        }
    }

    private static String extractResponseError(Response response) {
        String responseBodyErrorMsg = "";
        try {
            String responseBody = "";
            if (response != null) {
                responseBody = response.body() != null ? response.body().string() : "No response body";
                if (StringUtils.isNotEmpty(responseBody)) {
                    JSONObject json = null;
                    try {
                        json = JSON.parseObject(responseBody);
                        responseBodyErrorMsg = json.getJSONObject("error").getString("message");
                    } catch (Exception e) {
                        responseBodyErrorMsg = responseBody;
                    }
                }
            }
            //            log.error("Error response body: " + responseBody);
        } catch (Exception e) {
            log.error("Failed to read response body: " + e.getMessage());
        }
        return responseBodyErrorMsg;
    }

    /**
     * 外部大模型调用
     */
    public static EventSource queryAgent(String userAccount, RagChatStreamReq aiAgentChatStreamReq, String traceId, List<String> kbIdList, ConversationDO conversationDO,
                                         FluxSink<ServerSentEvent<String>> sink) {
        KnowledgeFileDOFeign knowledgeFileApi = FeignUtil.getKnowledgeFileApi();
        KnowledgeFileChunkDOFeign knowledgeFileChunkApi = FeignUtil.getKnowledgeFileChunkApi();

        EventSourceListener eventSourceListener = new EventSourceListener() {

            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type, @NotNull String data) {
                MDC.put(TRACE_ID, traceId);
                super.onEvent(eventSource, id, type, data);

                // 内容处理
                NewResultDTO<ChatRes> chatResDTO = JSONObject.parseObject(data, new TypeReference<>() {
                });
                ChatRes chatRes = chatResDTO.getData();
                chatResDTO.setRequestId(traceId);

                // 响应过程
                if (!Objects.equals(type, ERROR_ANSWER) && !Objects.equals(type, COMPLETE_ANSWER)) {
                    ChatHelper.doSinkNext(chatRes, sink, type, traceId);
                    return;
                }

                // 异常处理
                if (Objects.equals(type, ERROR_ANSWER)) {
                    errorAnswer(conversationDO, kbIdList, userAccount, traceId, aiAgentChatStreamReq.getQuestion(), chatResDTO, sink);
                    return;
                }

                // 正常完成
                if (Objects.equals(type, COMPLETE_ANSWER)) {
                    completeAnswer(conversationDO, kbIdList, userAccount, type, chatRes, traceId, sink, knowledgeFileApi, knowledgeFileChunkApi);
                    return;
                }
            }

            @Override
            public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                MDC.put(TRACE_ID, traceId);
                super.onOpen(eventSource, response);
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                MDC.put(TRACE_ID, traceId);
                super.onClosed(eventSource);
                sink.complete();
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
                MDC.put(TRACE_ID, traceId);
                super.onFailure(eventSource, t, response);
                handleOnFailure(t, response, sink, null, true);
            }
        };

        // 开始请求，返回EventSource
        return streamChatCompletion(userAccount, aiAgentChatStreamReq, traceId, eventSourceListener);
    }

    /**
     * 正常回答
     *
     * @param conversationDO        会话
     * @param kbIdList              知识库ID列表
     * @param userAccount           用户账号
     * @param type                  事件类型
     * @param chatRes               事件数据
     * @param traceId               请求ID
     * @param sink                  流式响应
     * @param knowledgeFileApi      文件API
     * @param knowledgeFileChunkApi 知识库chunk API
     */
    private static void completeAnswer(ConversationDO conversationDO, List<String> kbIdList, String userAccount, String type, ChatRes chatRes, String traceId, FluxSink<ServerSentEvent<String>> sink, KnowledgeFileDOFeign knowledgeFileApi, KnowledgeFileChunkDOFeign knowledgeFileChunkApi) {
        try {
            // 获取文件ID、chunkID
            List<String> fileIds = CollectionUtils.emptyIfNull(chatRes.getSource())
                .stream()
                .map(ChatRes.SourceBOResp::getFileID)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

            List<String> chunkIds = CollectionUtils.emptyIfNull(chatRes.getSource())
                .stream()
                .map(ChatRes.SourceBOResp::getKbChunkID)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

            // 并行获取文件和chunk数据
            Map<String, KnowledgeFileDO> knowledgeFileDOMap = new HashMap<>();
            Map<String, KnowledgeFileChunkDO> knowledgeFileChunkDOMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(fileIds)) {
                knowledgeFileApi.conditions(null, null, KnowledgeFileDOParams.builder().ids(fileIds).build()).getData()
                    .forEach(knowledgeFileDO -> knowledgeFileDOMap.put(knowledgeFileDO.getId(), knowledgeFileDO));
            }

            if (CollectionUtils.isNotEmpty(chunkIds)) {
                knowledgeFileChunkApi.conditions(null, null, KnowledgeFileChunkDOParams.builder().chunkID(chunkIds).build()).getData()
                    .forEach(knowledgeFileChunkDO -> knowledgeFileChunkDOMap.put(knowledgeFileChunkDO.getChunkID(), knowledgeFileChunkDO));
            }

            // 设置文件、chunk信息
            for (ChatRes.SourceBOResp sourceBOResp : CollectionUtils.emptyIfNull(chatRes.getSource())) {
                String fileID = sourceBOResp.getFileID();
                String chunkID = sourceBOResp.getKbChunkID();

                sourceBOResp.setKnowledgeFileBO(knowledgeFileDOMap.get(fileID));
                sourceBOResp.setKnowledgeFileChunkBO(knowledgeFileChunkDOMap.get(chunkID));
            }

            // 重写溯源
            ChatSourceService.rewriteChatResSource(chatRes, userAccount);

            // 保存消息
            String messageId = ChatHelper.saveAnswerMessage(conversationDO, kbIdList, chatRes.getStyle(), userAccount, chatRes.getSource(), chatRes.getResponse(), chatRes.getReasoningContent());

            // 设置消息ID
            chatRes.setId(messageId);

            // 推送消息
            ChatHelper.doSinkNext(chatRes, sink, type, traceId);
            sink.complete();
        } catch (Exception e) {
            log.error("Error processing event data: {}", e.getMessage(), e);
            handleOnFailure(e, null, sink, null, true);
        }
    }

    /**
     * 异常回答
     *
     * @param conversationDO 会话
     * @param kbIdList       知识库ID列表
     * @param userAccount    用户账号
     * @param traceId        请求ID
     * @param question       问题
     * @param response       回答
     * @param sink           流式响应
     */
    private static void errorAnswer(ConversationDO conversationDO, List<String> kbIdList, String userAccount, String traceId, String question, String response, FluxSink<ServerSentEvent<String>> sink) {
        // 保存消息
        String messageId = ChatHelper.saveAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), response, null);

        // 推送消息
        doErrorSinkNext(messageId, question, response, sink, traceId);
    }

    /**
     * 异常回答-重载
     *
     * @param conversationDO
     * @param kbIdList
     * @param userAccount
     * @param traceId
     * @param question
     * @param chatResDTO
     * @param sink
     */
    private static void errorAnswer(ConversationDO conversationDO, List<String> kbIdList, String userAccount, String traceId, String question, NewResultDTO<ChatRes> chatResDTO, FluxSink<ServerSentEvent<String>> sink) {
        // 保存消息
        String messageId = ChatHelper.saveErrorAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), chatResDTO, null);

        // 推送消息
        doErrorSinkNext(messageId, question, MESSAGE_RESPONSE_ERROR, sink, traceId);
    }

    private static OkHttpClient okHttpClient() {
        return (new OkHttpClient.Builder()).connectTimeout(10L, TimeUnit.SECONDS).writeTimeout(300L, TimeUnit.SECONDS).readTimeout(300L, TimeUnit.SECONDS).build();
    }

    public static <T> EventSource streamChatCompletion(String userAccount, T chatCompletion, String traceId, EventSourceListener eventSourceListener) {
        String aiAgentUrl = SpringEnvUtil.getEnv("com.polarizon.feign.ai-agent-api-url", "http://ai-agent:20502");
        if (Objects.isNull(eventSourceListener)) {
            log.error("参数异常：EventSourceListener不能为空，可以参考：com.unfbx.chatgpt.sse.ConsoleEventSourceListener");
            throw new BaseException(CommonError.PARAM_ERROR);
        }

        try {
            EventSource.Factory factory = EventSources.createFactory(okHttpClient());
            ObjectMapper mapper = new ObjectMapper();
            String requestBody = mapper.writeValueAsString(chatCompletion);
            Request request = new Request.Builder()
                .url(aiAgentUrl + "/ai-agent/v1/ragChat")
                .addHeader(X_ACCOUNT_HEADER_NAME, userAccount)
                .addHeader("X-Request-Id", traceId)
                .post(RequestBody.create(MediaType.parse(ContentType.JSON.getValue()), requestBody))
                .build();
            //创建事件
            return factory.newEventSource(request, eventSourceListener);
        } catch (Exception e) {
            throw new BusinessExecutionException("请求参数解析异常：" + e.getMessage(), e);
        }
    }

    public static String saveAnswerMessage(ConversationDO conversationDO, List<String> kbIds, String messageStyle, String userAccount, List<ChatRes.SourceBOResp> source, String content, String reasoningContent) {
        List<SourceBO> sourceBOList = ChatHelper.convertSourceList(source);
        MessageDO messageDO = ChatHelper.buildMessageDO(conversationDO, kbIds, content, MessageRoleEnum.robot, sourceBOList, messageStyle, Lists.newArrayList(), reasoningContent);
        return saveAnswerMessage(userAccount, messageDO, sourceBOList);
    }

    public static String saveErrorAnswerMessage(ConversationDO conversationDO, List<String> kbIds, String messageStyle, String userAccount, List<ChatRes.SourceBOResp> source, NewResultDTO<ChatRes> chatResDTO, String reasoningContent) {
        List<SourceBO> sourceBOList = ChatHelper.convertSourceList(source);
        MessageDO messageDO = ChatHelper.buildMessageDO(conversationDO, kbIds, MESSAGE_RESPONSE_ERROR, MessageRoleEnum.robot, sourceBOList, messageStyle, Lists.newArrayList(), reasoningContent, chatResDTO.getMsg());
        return saveAnswerMessage(userAccount, messageDO, sourceBOList);
    }

    private static String saveAnswerMessage(String userAccount, MessageDO messageDO, List<SourceBO> sourceBOList) {
        MessageDO data = FeignUtil.getMessageApi().add(null, null, userAccount, messageDO).getData();
        // 更新message
        MessageDO build = MessageDO.builder().id(data.getId()).source(sourceBOList).build();
        FeignUtil.getMessageApi().update(null, null, userAccount, true, build);
        //log.debug("saveAnswerMessage:{}", JSON.toJSONString(messageDO));
        return data.getId();
    }

    public static MessageDO saveQuestionMessage(String userAccount, String question, ConversationDO conversationDO, List<String> kbIds) {
        MessageDO messageDO =
            ChatHelper.buildMessageDO(conversationDO, kbIds, question, MessageRoleEnum.user, null,
                Constants.MESSAGE_TYPE_DEFAULT, null, null);
        return FeignUtil.getMessageApi().add(null, null, userAccount, messageDO).getData();
    }

}
