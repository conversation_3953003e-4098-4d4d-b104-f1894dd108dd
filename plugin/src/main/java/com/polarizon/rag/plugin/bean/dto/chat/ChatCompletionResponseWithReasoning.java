package com.polarizon.rag.plugin.bean.dto.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unfbx.chatgpt.entity.assistant.run.ToolCall;
import com.polarizon.rag.plugin.tool.llm.Usage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatCompletionResponseWithReasoning {
    private String id;
    private String object;
    private long created;
    private String model;
    private List<ChatChoice> choices;
    private Usage usage;
    private String warning;
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChatChoice implements Serializable {

        private long index;
        /**
         * 请求参数stream为true返回是delta
         */
        @JsonProperty("delta")
        private Message delta;
        /**
         * 请求参数stream为false返回是message
         */
        @JsonProperty("message")
        private Message message;
        @JsonProperty("finish_reason")
        private String finishReason;
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Message implements Serializable {
        /**
         * 旧的content属性仅仅支持字符类型
         */
        private String content;

        @JsonProperty("reasoning_content")
        private String reasoningContent;
    }
    public static ChatCompletionResponseWithReasoning ofDeltatContent(String content) {
        return new ChatCompletionResponseWithReasoning().setChoices(
            Collections.singletonList(new ChatChoice().setDelta(new Message().setContent(content))));
    }
}
