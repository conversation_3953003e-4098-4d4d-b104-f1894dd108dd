package com.polarizon.rag.plugin.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.rag.plugin.bean.constant.LlmProcessConstants;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.exception.MyJsonParseException;
import io.github.haibiiin.json.repair.JSONRepair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class JSONUtils {

    // 使用第三方库修复
    private static final JSONRepair JSON_REPAIR = new JSONRepair();
    public static final String REGEX = "```json\\s*([\\s\\S]*?)```";
    public static final Pattern PATTERN = Pattern.compile(REGEX);
    public static List<LlmConfig.Replacement> stringReplacements;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 整理特殊字符
    public static String sanitizeJsonString(String input) {
        String newStr = extractJsonFromMarkdown(input);
        if (StringUtils.isBlank(newStr)) {
            return "";
        }
        for (LlmConfig.Replacement stringReplacement : stringReplacements) {
            newStr = newStr.replace(stringReplacement.getFrom(), stringReplacement.getTo());
        }
        return newStr;
    }

    // JSON字符串格式错误，例如多了一个}
    public static String fixExtraBraces(String jsonString) {
        Stack<Character> stack = new Stack<>();
        StringBuilder jsonBuilder = new StringBuilder();
        boolean isStart = false;
        for (char c : jsonString.toCharArray()) {
            if (c == '{') {
                isStart = true;
                stack.push(c);
            } else if (c == '}') {
                stack.pop();
                if (stack.isEmpty()) {
                    isStart = false;
                    jsonBuilder.append(c);
                    return jsonBuilder.toString();
                }
            }
            if (isStart) {
                jsonBuilder.append(c);
            }
        }
        return jsonBuilder.toString();
    }

    // 方法：将 JSON 字符串转换为对象
    public static <T> T parseObjectSanitized(String jsonString, Class<T> clazz) {
        // 先用原字符串解析
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (JSONException jsonException) {
            try {
                String correctJSON = JSON_REPAIR.handle(jsonString);
                return JSON.parseObject(correctJSON, clazz);
            } catch (Exception e1) {
                String sanitizeJsonString = "";
                try {
                    // 先用原字符串解析，若解析失败，则先清理特殊字符
                    sanitizeJsonString = sanitizeJsonString(jsonString);
                    return JSON.parseObject(sanitizeJsonString, clazz);
                } catch (Exception exception) {
                    try {
                        String correctJSON = JSON_REPAIR.handle(sanitizeJsonString);
                        return JSON.parseObject(correctJSON, clazz);
                    } catch (Exception e2) {
                        try {
                            // 用自己的代码尝试解析
                            sanitizeJsonString = fixExtraBraces(sanitizeJsonString);
                            return JSON.parseObject(sanitizeJsonString, clazz);
                        } catch (Exception e) {
                            throw new MyJsonParseException(
                                StrUtil.format("JSON 解析异常:{}; jsonString:{}", exception.getMessage(), sanitizeJsonString), exception);
                        }
                    }
                }
            }

        }
    }

    // 方法：将 JSON 字符串转换为对象
    public static <T> T parseObjectSanitized(String jsonString, TypeReference<T> typeReference) {
        // 先用原字符串解析
        try {
            return JSON.parseObject(jsonString, typeReference);
        } catch (JSONException jsonException) {
            try {
                String correctJSON = JSON_REPAIR.handle(jsonString);
                return JSON.parseObject(correctJSON, typeReference);
            } catch (Exception e1) {
                String sanitizeJsonString = "";
                try {
                    // 先用原字符串解析，若解析失败，则先清理特殊字符
                    sanitizeJsonString = sanitizeJsonString(jsonString);
                    return JSON.parseObject(sanitizeJsonString, typeReference);
                } catch (Exception exception) {
                    try {
                        String correctJSON = JSON_REPAIR.handle(sanitizeJsonString);
                        return JSON.parseObject(correctJSON, typeReference);
                    } catch (Exception e2) {
                        try {
                            // 用自己的代码尝试解析
                            sanitizeJsonString = fixExtraBraces(sanitizeJsonString);
                            return JSON.parseObject(sanitizeJsonString, typeReference);
                        } catch (Exception e) {
                            throw new MyJsonParseException(
                                StrUtil.format("JSON 解析异常:{}; jsonString:{}", exception.getMessage(), sanitizeJsonString), exception);
                        }
                    }
                }
            }

        }
    }

    /**
     * 使 @JsonProperty 生效 
     * @param obj
     * @return
     */
    public static String getJacksonJsonString(Object obj) {
        // 在需要打印的地方替换为：
        String json;
        try {
            json = objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new BusinessExecutionException("Failed to serialize object");
        }
        return json;
    }

    private static String extractJsonFromMarkdown(String input) {
        // 匹配 Markdown 格式的 JSON 代码块
        Matcher matcher = PATTERN.matcher(input);

        if (matcher.find()) {
            return matcher.group(1).trim(); // 提取 JSON 内容
        } else {
            return input; // 如果没有匹配到，返回 原内容
        }
    }
    private static String extraStart = """
            
                               {{
                                "key1":"多一个左花括号",
                                "key2":"value2"
                              }
        """;
    private static String extraEnd = """
            
                               {
                                "key1":"多一个右花括号",
                                "key2":"value2"
                              }}
        """;
    // 中文“
    private static String chineseStart = """
           ｛
            "左花括号":"是中文的",
            "key2":"value2"
          }
    
        """;
    private static String missEnd = """
    {
    "key1":"value1",
    "右花括号":"是中文的"
    ｝
""";
    // 逗号是中文的
    private static String chineseComma = """
            [{"name":"中国五百强企业查询"，"arguments":{"公司名称":"阿里巴巴"}}，
            {"name":"中国五百强企业查询","arguments":{"公司名称":"腾讯","年份":["2020","2021"]}｝]
            """;
    public static void main(String[] args) {
        stringReplacements = LlmProcessConstants.defaultStringReplacements;
//        System.out.println(a2);
//        System.out.println("-----------------------");
//        System.out.println(a3);
//        System.out.println("-----------------------");
//        System.out.println(a4);
//        System.out.println("-----------------------");

        
//        System.out.println(JSONUtils.parseObjectSanitized(extraStart, new TypeReference<Map<String, Object>>() {}));
        System.out.println("-----------------------");
        System.out.println(JSONUtils.parseObjectSanitized(extraEnd, new TypeReference<Map<String, Object>>() {}));
        System.out.println("-----------------------");
        System.out.println(JSONUtils.parseObjectSanitized(chineseStart, new TypeReference<Map<String, Object>>() {}));
        System.out.println("-----------------------");
        System.out.println(JSONUtils.parseObjectSanitized(missEnd, new TypeReference<Map<String, Object>>() {}));
        System.out.println("-----------------------");
        for (int i = 0; i < 100; i++) {
            new Thread(() -> {
                List<Map<String, Object>> x = JSONUtils.parseObjectSanitized(chineseComma, new TypeReference<>() {
                });
                log.debug(JSONUtils.getJacksonJsonString(x));
            }).start();
        }
        

    }
}
