// package com.polarizon.rag.plugin.customAgents.smartInspect.chapter_splitter;

// import cn.hutool.core.util.StrUtil;
// import com.alibaba.fastjson.JSONObject;
// import com.polarizon.rag.plugin.util.DocumentUtils;
// import lombok.AllArgsConstructor;
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.experimental.Accessors;
// import lombok.extern.slf4j.Slf4j;
// import org.jetbrains.annotations.NotNull;

// import java.io.StringReader;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.function.Function;
// import java.util.stream.Collectors;

// @Slf4j
// @AllArgsConstructor
// public class ListHandler {

//     @Data
//     @Accessors(chain = true)
//     @NoArgsConstructor
//     public static class ElementMapping {
//         private String element;
//         private int originalIndex; // 在原始列表中的索引
//         private int newIndex;      // 在处理后的newList中的索引
//         private int concatedIndex; // 在拼接后的concatedList中的索引
//         private int splitIndex;    // 在concatedList元素中的拆分位置

//         public ElementMapping(String element, int originalIndex, int newIndex, int concatedIndex, int splitIndex) {
//             if (element == null){
//                 throw new IllegalArgumentException("element can not be null");
//             }
//             this.element = element;
//             this.originalIndex = originalIndex;
//             this.newIndex = newIndex;
//             this.concatedIndex = concatedIndex;
//             this.splitIndex = splitIndex;
//         }

//         @Override
//         public String toString() {
//             return StrUtil.format("OriginalIndex: {}, NewIndex: {}, ConcatIndex: {}, SplitIndex: {},Element: {}",
//                 originalIndex, newIndex, concatedIndex, splitIndex, element);
//         }
//     }

//     public static List<ElementMapping> handleList(List<String> originalList) {
//         return handleList(originalList, ListHandler::mockConcat);
//     }

//     public static List<ElementMapping> handleList(List<String> originalList,
//         Function<List<String>, List<String>> function) {
//         // 1. 过滤空字符串并保留原始索引
//         List<String> newList = new ArrayList<>();
//         List<Integer> originalIndices = new ArrayList<>();
//         for (int i = 0; i < originalList.size(); i++) {
//             String s = originalList.get(i);
//             if (s != null && !s.trim().isEmpty()) {
//                 newList.add(s);
//                 originalIndices.add(i);
//             }
//         }
//         log.debug("OriginalList: \n{}", JSONUtils.getJacksonJsonString(originalList));
//         log.debug("newList: \n{}", JSONUtils.getJacksonJsonString(newList));
//         // 2. 调用mock拼接方法
//         List<String> concatedList = function.apply(newList);
//         log.debug("ConcatedList:\n{}", JSONUtils.getJacksonJsonString(concatedList));

//         //  3. 拆分拼接后的列表并验证数据一致性
//         List<String> recoveredList = splitConcatedList(concatedList);
        
//         if (recoveredList.size() != newList.size()) {
//             String msg =
//                 StrUtil.format("段落聚合后数据段落数对不上，聚合前,聚合后-size:[{}],[{}]；\n聚合前text:\n[{}]，聚合后：text:\n[{}]",
//                     newList.size(), recoveredList.size(), newList, recoveredList);
//             throw new IllegalStateException(msg);
//         }
//         log.debug("RecoveredList:\n{}", JSONUtils.getJacksonJsonString(recoveredList));

//         // 4. 构建映射关系
//         return buildMappings(newList, originalIndices, concatedList);
//     }

//     private static List<String> splitConcatedList(List<String> concatedList) {
//         return concatedList.stream().flatMap(s -> {
//             if (s == null)
//                 return new ArrayList<String>().stream();
//             String[] parts = s.split("\n");
//             List<String> validParts = new ArrayList<>();
//             for (String part : parts) {
//                 if (!part.trim().isEmpty())
//                     validParts.add(part);
//             }
//             return validParts.stream();
//         }).collect(Collectors.toList());
//     }

//     private static List<ElementMapping> buildMappings(List<String> newList, List<Integer> originalIndices,
//         List<String> concatedList) {
//         List<ElementMapping> mappings = new ArrayList<>();
//         List<ConcatSegment> concatSegments = buildConcatSegments(concatedList);
//         log.debug("ConcatSegments:\n{}", JSONUtils.getJacksonJsonString(concatSegments));
//         // 为每个newList元素创建映射
//         for (int newIndex = 0; newIndex < newList.size(); newIndex++) {
//             int originalIndex = originalIndices.get(newIndex);

//             // 找到对应的concat段
//             for (int concatIndex = 0; concatIndex < concatSegments.size(); concatIndex++) {
//                 ConcatSegment segment = concatSegments.get(concatIndex);
//                 if (newIndex >= segment.start && newIndex <= segment.end) {
//                     int splitIndex = newIndex - segment.start;
//                     mappings.add(
//                         new ElementMapping(newList.get(newIndex), originalIndex, newIndex, concatIndex, splitIndex));
//                     break;
//                 }
//             }
//         }

//         return mappings;
//     }

//     @NotNull
//     public static List<ConcatSegment> buildConcatSegments(List<String> concatedList) {
//         List<ConcatSegment> concatSegments = new ArrayList<>();

//         // 计算每个concated元素对应的newList范围
//         int currentIndex = 0;
//         for (String concatStr : concatedList) {
//             if (concatStr==null) {
//                 throw new IllegalArgumentException("concatStr can not be null");
//             }
//             List<String> lines = DocumentUtils.readLines(new StringReader(concatStr));
//             int elementCount = lines.size();
//             concatSegments.add(
//                 new ConcatSegment(concatStr, currentIndex, currentIndex + elementCount - 1, lines));
//             currentIndex += elementCount;
//         }
//         return concatSegments;
//     }

//     // Mock拼接方法示例实现
//     private static List<String> mockConcat(List<String> newList) {
//         List<String> result = new ArrayList<>();
//         for (int i = 0; i < newList.size(); i++) {
//             if (i % 2 == 0) {
//                 if (i + 1 < newList.size()) {
//                     result.add(newList.get(i) + "\n" + newList.get(i + 1));
//                 } else {
//                     result.add(newList.get(i));
//                 }
//             }
//         }
//         return result;
//         //        return List.of( "A\nB", "C\nc", "DE");
//     }

//     @Data
//     @Accessors(chain = true)
//     @NoArgsConstructor
//     @AllArgsConstructor
//     public static class ConcatSegment {
//         String content;
//         int start;
//         int end;
//         List<String> lines;

//         @Override
//         public String toString() {
//             return StrUtil.format("Start: {}, End: {}, Content: {}", start, end, content);
//         }
//     }

//     public static void main(String[] args) {
//         List<String> original = List.of("", "A", "B", "", "C", "D", "E");
//         List<ElementMapping> mappings = handleList(original);

//         mappings.forEach(System.out::println);
//         /* 示例输出：
//            OriginalIndex: 1, NewIndex: 0, ConcatIndex: 0, SplitIndex: 0
//            OriginalIndex: 2, NewIndex: 1, ConcatIndex: 0, SplitIndex: 1
//            OriginalIndex: 4, NewIndex: 2, ConcatIndex: 1, SplitIndex: 0
//            OriginalIndex: 5, NewIndex: 3, ConcatIndex: 1, SplitIndex: 1
//            OriginalIndex: 6, NewIndex: 4, ConcatIndex: 2, SplitIndex: 0
//          */
//     }
// }
