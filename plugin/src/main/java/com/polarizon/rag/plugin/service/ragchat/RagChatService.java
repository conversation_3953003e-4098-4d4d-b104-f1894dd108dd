package com.polarizon.rag.plugin.service.ragchat;

import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.NO_CHUNKS_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.NO_CHUNKS_ANSWER_STYLE;
import static com.polarizon.rag.plugin.bean.constant.InstructConstants.RAG_COMMON;
import static com.polarizon.rag.plugin.bean.constant.InstructConstants.RAG_NO_REF;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import com.polarizon.rag.ModelEnableEnum;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.chat.AnswerModeEnum;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.MessageRoleEnum;
import com.polarizon.rag.chat.TemplateSourceEnum;
import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.instruct.PromptTemplate;
import com.polarizon.rag.plugin.bean.dto.aiagent.RagChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterResult;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.config.ModelIdentityConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.interceptor.AnswerModeInterceptor;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.service.InstructHelper;
import com.polarizon.rag.plugin.service.agent.AgentChatService;
import com.polarizon.rag.plugin.tool.RetrievalHandleService;
import com.polarizon.rag.plugin.tool.llm.LlmHandleService;
import com.polarizon.rag.plugin.util.FluxUtil;
import com.polarizon.rag.plugin.util.StopWatchUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

@Service
@Slf4j
@Getter
public class RagChatService {
    @Value("${com.polarizon.feign.llm-api-url:http://llm-ms:20522}")
    private String generationApiUrl;
    // @Value("${llm.defaultModelName:qwen2.5-32b-instruct-gptq}")
    // private String defaultModelName;
    // // 默认两个模型一样的
    // @Value("${llm.ragDefaultModelName:${llm.defaultModelName}}")
    // private String ragDefaultModelName;
    @Value("${CHAT.RERANK.DEFAULT_RERANK_TOP_K:10}")
    private Integer defaultRerankTopK;

    @Value("${CHAT.LLM.DEFAULT_MAX_TOKEN:2048}")
    private String defaultMaxToken;
    @Value("${answer.noChunkPreInfo:根据知识库中已知信息无法回答该问题。AI综合所有相关数据后，参考性解读如下。}")
    private String noChunkPreInfo;
    @Value("${answer.use-reranker: false}")
    private Boolean useReranker;
    @Autowired
    private LlmConfig llmConfig;
    @Autowired
    private AgentChatService agentChatService;
    @Autowired
    private RetrievalHandleService retrievalHandleService;
    @Autowired
    private AnswerModeInterceptor answerModeInterceptor;
    @Autowired
    private ModelIdentityConfig modelIdentityConfig;
    @Autowired
    private ChatHelper chatHelper;
    @Autowired
    private LlmHandleService llmHandleService;

    /**
     * rag 对话提问
     * 
     * @param userAccount 提问的用户
     * @param ragChatParam 提问的参数
     * @return 流式返回的答案
     */
    @NotNull
    public Flux<ServerSentEvent<String>> ragChat(String userAccount, RagChatParam ragChatParam) {
        AtomicReference<RagChatContext> initChatContextAR = new AtomicReference<>(new RagChatContext());
        Flux<ServerSentEvent<String>> sseFlux;
        sseFlux =  FluxUtil.createWithOnCancelRag(initChatContextAR,
            (sink, chatContext) ->{
                initContext(ragChatParam, sink, chatContext);
                handleRagChat(chatContext);
            });
        initChatContextAR.get().setServerSentEventFlux(sseFlux);
        return FluxUtil.addEventCallback(initChatContextAR.get());
    }

    private void handleRagChat(RagChatContext chatContext) {
        FluxSink<ServerSentEvent<String>> sink = chatContext.getSink();
        // 强制设置回答模式为MUST_CONTAINS_REFERENCE
        ConversationConfig config = chatContext.getConversationConfig();
        config = answerModeInterceptor.enforceReferenceMode(config);
        chatContext.setConversationConfig(config);
        
        // 查询相关知识
        Pair<QueryRewriterResult, GenerateParam.Sources> retrievalResult = 
            retrievalHandleService.multiRetrieve(chatContext, true, 0);
        chatContext.setQueryRewriterResult(retrievalResult.getKey());

        // 处理模型身份查询
        QueryRewriterResult queryRewriterResult = retrievalResult.getKey();
        if (queryRewriterResult != null && Boolean.TRUE.equals(queryRewriterResult.getModelIdentityQuery())) {
            log.debug("模型身份查询，返回固定答案");
            // 使用工具类处理模型身份查询的流式返回
            chatHelper.streamModelIdentityAnswer(chatContext);
            return;
        }

        // 未搜寻到文档片段，则不走大模型,用托底回答
        ConversationConfig conversationConfig = chatContext.getConversationConfig();
        boolean skipLLM = conversationConfig.getAnswerMode() == null
            || conversationConfig.getAnswerMode().equals(AnswerModeEnum.MUST_CONTAINS_REFERENCE);
        boolean isNoChunks = CollectionUtils.isEmpty(retrievalResult.getValue().getAllSources());
        // 构建 生成式参数
        String instructCode = isNoChunks ? RAG_NO_REF : RAG_COMMON;
        GenerateParam generateParam = buildGenerateParamRag(chatContext, retrievalResult.getValue(),  instructCode);
        chatContext.setGenerateParam(generateParam);
        if (isNoChunks && skipLLM) {
            // 托底回答
            answerDirectly(chatContext, chatContext.getChatParam(), sink);
            sink.complete();
        } else {
            // 从大模型获取答案
            answerFromLLm(chatContext, isNoChunks, sink);
        }
    }

    /**
     * rag 对话 - 从大模型获取答案
     */
    @NotNull
    private void answerFromLLm(RagChatContext chatContext, boolean noChunks, FluxSink<ServerSentEvent<String>> sink) {
        chatContext.getStopWatch().stop();
        chatContext.getStopWatch().start("llmConnect");
        EventSource eventSource = llmHandleService.queryLlmSse(sink, chatContext, noChunks);
        chatContext.setRealEventSource(eventSource);
        Map<String, EventSource> map = 
            chatContext.getEventSourceMap() == null ? new ConcurrentHashMap<>() : chatContext.getEventSourceMap();
        map.put("llm", eventSource);
        chatContext.setEventSourceMap(map);
    }

    /**
     * rag对话 - 直接回答
     */
    @NotNull
    private void answerDirectly(RagChatContext chatContext, ChatParam param, FluxSink<ServerSentEvent<String>> sink) {
        // 回答内容是
        chatContext.setCompleteAnswerSB(new StringBuffer(NO_CHUNKS_ANSWER));
        // 发最后的答案
        FluxUtil.doSinkNext(ChatHelper.buildCompleteAnswer(param, NO_CHUNKS_ANSWER, NO_CHUNKS_ANSWER_STYLE, chatContext), sink, COMPLETE_ANSWER);
        log.debug("answerDirectly:{}", NO_CHUNKS_ANSWER);
    }


    public static <T> void initContext(RagChatParam ragChatParam, FluxSink<T> sink, RagChatContext chatContext) {
        ChatParam chatParam = ChatParam.fromRagChatParam(ragChatParam);
        StopWatchUtils watchUtils = new StopWatchUtils("rag");

        ModelInfoDO llmModelInfoDO =
            ChatHelper.getLlmModelInfoByConversationConfig(ragChatParam.getConversationConfig());
        Assert.isTrue(Objects.equals(llmModelInfoDO.getEnable(), ModelEnableEnum.ENABLE), () -> new BusinessExecutionException("大模型已禁用，请换模型: " + llmModelInfoDO.getName()));

        chatContext.setChatParam(chatParam);

        chatContext.setRetrievalConfig(ragChatParam.getRetrievalConfig());
        chatContext.setConversationConfig(ragChatParam.getConversationConfig());
        chatContext.setLlmModelInfoDO(llmModelInfoDO);

        chatContext.setStopWatch(watchUtils);
        chatContext.setSink((FluxSink<ServerSentEvent<String>>) sink);
    }

    /**
     * 构建生成提问参数
     */
    private static GenerateParam buildGenerateParamRag(RagChatContext ragChatContext, GenerateParam.Sources sources,
                                                       String instructCode) {
        ChatParam param = ragChatContext.getChatParam();
        ConversationConfig conversationConfig = ragChatContext.getConversationConfig();
        ModelInfoDO modelInfoDO = ragChatContext.getLlmModelInfoDO();
        
        GenerateParam generateParam = new GenerateParam()
            .setChatModeConfig(ChatHelper.getValidChatModeConfig(param.getChatModeConfig()))
            .setModelInfoDO(modelInfoDO)
            .setQuestion(param.getQuestion())
            .setSources(sources);

        InstructDO instructDO = InstructHelper.getInstructDOByCode(instructCode);
        PromptTemplate ragPromptTemplate = instructDO.getPromptTemplate();
        TemplateSourceEnum templateSourceEnumOrDefault = GenerateParam.getTemplateSourceEnumOrDefault(conversationConfig);
        if (TemplateSourceEnum.CUSTOM.equals(templateSourceEnumOrDefault)) {
            PromptTemplate conversationConfigPromptTemplate = conversationConfig.getPromptTemplate();
            if (conversationConfigPromptTemplate == null) {
                throw new BusinessExecutionException("conversationConfigPromptTemplate is null");
            }
            ragPromptTemplate.setSystemContent(conversationConfigPromptTemplate.getSystemContent());
            ragPromptTemplate.setUserContent(conversationConfigPromptTemplate.getUserContent());
        }

        // 设置提示词
        generateParam.setPromptTemplate(ragPromptTemplate)
            .setTemplateSourceEnum(templateSourceEnumOrDefault);
        // 选择默认的时候，使用默认的提示词
        // setPromptOrDefault(generateParam, noChunks);

        // 过滤system类型的历史
        List<ChatHistory> history = param.getHistory() == null ? new ArrayList<>() : param.getHistory();
        List<ChatHistory> chatHistories = history.stream().filter(
                chatHistory -> chatHistory.getRole().equals(MessageRoleEnum.user) || chatHistory.getRole()
                    .equals(MessageRoleEnum.robot))
            .toList();
        generateParam.setHistory(chatHistories);

        return generateParam;
    }

}
