package com.polarizon.rag.plugin.service.instruct;

import cn.hutool.core.util.StrUtil;
import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.plugin.bean.dto.instruct.InstructChatParam;
import com.polarizon.rag.plugin.service.BaseChatContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper=false)
@Data
@Accessors(chain = true)
public class InstructChatContext extends BaseChatContext {
    /**
     * 指令
     */
    private InstructDO instructDO;

    /**
     * instructParam
     */
    private InstructChatParam.InstructParam instructParam;

    public InstructChatContext bornSubContext(InstructDO instructDO, InstructChatParam instructChatParam, 
                                              CharSequence template, Object... params){
        InstructChatContext newOne = new InstructChatContext();
        newOne.shallowCloneFrom(this);
        newOne.setInstructDO(instructDO)
            .setInstructParam(instructChatParam.getInstructParam())
            .cleanLlmHandleStringBuffer()
            .cleanTaskContext();
        String contextName = StrUtil.format(template, params);
        this.getSubChatContextMap().put(contextName, newOne);
        return newOne;
    }
}
