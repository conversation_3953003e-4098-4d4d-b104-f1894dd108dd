package com.polarizon.rag.plugin.util;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class MarkdownUtils {
    public static Map<String, Map<String, String>> parseMarkdownToMap(String markdown) {
        String[] lines = markdown.split("\n");
        
        String currentH1 = null;
        String currentH2 = null;
        StringBuilder content = new StringBuilder();
        
        Map<String, Map<String, String>> structure = new LinkedHashMap<>();
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            if (line.startsWith("# ")) {
                // Handle H1
                if (currentH1 != null && currentH2 != null && content.length() > 0) {
                    addToStructure(structure, currentH1, currentH2, content.toString());
                    content = new StringBuilder();
                }
                currentH1 = line.substring(2).trim();
                currentH2 = null;
                structure.put(currentH1, new HashMap<>());
            } else if (line.startsWith("## ")) {
                // Handle H2
                if (currentH2 != null && content.length() > 0) {
                    addToStructure(structure, currentH1, currentH2, content.toString());
                    content = new StringBuilder();
                }
                currentH2 = line.substring(3).trim();
            } else {
                // Handle content
                if (content.length() > 0) content.append(" ");
                content.append(line);
            }
        }
        
        // Add final content
        if (currentH1 != null && currentH2 != null && content.length() > 0) {
            addToStructure(structure, currentH1, currentH2, content.toString());
        }
        
        return structure;
    }
    
    private static void addToStructure(Map<String, Map<String, String>> structure, 
                                     String h1, String h2, String content) {
        if (!structure.containsKey(h1)) {
            structure.put(h1, new LinkedHashMap<>());
        }
        structure.get(h1).put(h2, content);
    }
    public static void main(String[] args) {
        String markdownData = """
            # 一、产业概念说明
            ## （一）基本概念
            云计算是一种能够通过网络以便利的、按需付费的方式获取计算资源（包括网络、服务器、存储、应用和服务等）并提高其可用性的模式。这些资源来自一个共享的可配置的资源池，并能够以最省力和无人干预的方式获取和释放。云计算最早起源于工业界，旨在促进产业发展和提高效益。随着时间的发展，云计算已经成为信息技术发展和服务模式创新的集中体现，为各行各业提供了强大的技术支持和资源分配能力。
            ## （二）相关其它产业
            与云计算相关的其他产业包括IT基础设施、人工智能、大数据、物联网等。IT基础设施主要涉及服务器、数据中心、网络设备等硬件设施，以及操作系统、数据库管理系统等软件设施。人工智能和大数据则是云计算的重要应用领域，通过云计算的强大算力支持，推动了人工智能算法的训练和大数据的处理分析。物联网则借助云计算实现大规模设备的连接和数据处理。
            ## （三）产业关联分析
            云计算与上述相关产业之间存在密切的联系。在IT基础设施方面，云计算依赖于强大的硬件设施和软件平台的支持，同时也促进了这些设施的更新换代。在人工智能和大数据领域，云计算提供了必要的算力支持，使得复杂的人工智能算法和大数据分析成为可能。而在物联网领域，云计算则负责处理海量的设备连接和数据传输，是物联网实现大规模应用的基础。
            ## （四）产业链条分析
            云计算产业链条包括上游、中游和下游三个部分。上游主要是芯片及设备提供商，提供服务器、交换机、CPU芯片等硬件设备。中游是云计算服务提供商，如亚马逊AWS、微软Azure、阿里云等，它们提供IaaS、PaaS、SaaS等不同层次的服务。下游则是应用领域，涵盖了互联网、金融、政府、传统制造业、软件业、医疗、教育等多个行业。
                        
            # 二、现状和趋势分析
            ## （一）国内产业现状
            2023年，我国云计算市场规模达6165亿元，同比增长35.5%，大幅高于全球增速。其中，公有云市场规模4562亿元，同比增长40.1%；私有云市场规模1563亿元，同比增长20.8%。从厂商层面来看，阿里云、天翼云、移动云、华为云、腾讯云、联通云占据中国公有云IaaS市场份额前六。不同地区如山东、深圳、湖北等都在积极推动云计算在制造业、智慧城市、大数据等领域的应用，以促进产业升级和智能化发展。
            ## （二）国际产业现状
            全球云计算市场进入稳定增长阶段，2023年全球云计算市场规模达到5864亿美元，增速19.4%。从服务商层面来看，微软云和亚马逊云凭借AI云产品改造和市场布局优势，分别以962.13亿美元和907.57亿美元的营收稳居全球市场前两名。第二梯队服务商保持积极追赶态势，但受市场布局先手影响，营收增长有限，转向业务整合，通过深化主营业务技术优势，拉高领域技术壁垒，推动其业务增长。
            ## （三）产业趋势前景分析
            短期内，随着AI原生带来的云计算技术革新以及大模型规模化应用落地，我国云计算产业发展将迎来新一轮增长曲线，预计到2027年我国云计算市场规模将超过2.1万亿元。中期来看，云计算技术将更加侧重提升易用、安全、稳定、优化等精细化管理水平，同时“云+AI”服务模式创新发展，将开启云计算产业智能化新纪元。长期而言，全球主要国家本土云计算战略仍将继续升级，全球云计算开放度或将收紧，这将影响国际云计算市场的竞争格局。
                        
            # 三、战略目标和任务
            ## （一）战略目标
            深圳市在云计算产业中拥有丰富的技术积累和强大的创新能力，特别是在人工智能和大数据领域。然而，也存在一些不足，如高端人才短缺和核心技术自主化程度不高。目前的战略目标是提升云计算技术的自主研发能力和市场竞争力。新的战略目标应包括加强云计算与人工智能的深度融合，提高云计算服务的质量和安全性，以及推动云计算在各行业的广泛应用。
            ## （二）重点任务
            根据上述战略目标，深圳市政府的关键任务包括：1) 加强云计算技术研发，特别是云计算与人工智能的融合技术；2) 推动云计算在各行业的应用，如工业云、医疗云等；3) 建立和完善云计算的安全标准和管理体系，确保数据安全和个人隐私保护；4) 加强人才培养和引进，提升云计算领域的专业人才储备。
            ## （三）行动建议
            为实现上述重点任务，深圳市政府可以采取以下行动建议：1) 设立专项基金，支持云计算技术研发和创新项目；2) 制定优惠政策，吸引国内外云计算企业和人才落户深圳；3) 与其他地区相比，如北京和浙江，深圳可以借鉴其在云计算政策上的成功经验，如‘揭榜挂帅’和‘赛马制’等方式，推动云计算重大研究项目和关键技术攻关；4) 加强与高校和科研机构的合作，共同培养云计算领域的高端人才。
                        
                """;

        Map<String, Map<String, String>> object = parseMarkdownToMap(markdownData);
        
        System.out.println(JSONUtils.getJacksonJsonString(object));
    }
}