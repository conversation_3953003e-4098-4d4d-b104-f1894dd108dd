package com.polarizon.rag.plugin.service.chat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.tokenizers.Tokenizer;
import com.alibaba.dashscope.tokenizers.TokenizerFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.application.InstructDO;
import com.polarizon.rag.application.OtherConfig;
import com.polarizon.rag.application.ParamTypeEnum;
import com.polarizon.rag.application.PromptTemplate;
import com.polarizon.rag.application.PromptTemplateParam;
import com.polarizon.rag.application.params.InstructDOParams;
import com.polarizon.rag.chat.ChatModeConfig;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.ConversationDO;
import com.polarizon.rag.chat.ExtraContentBO;
import com.polarizon.rag.chat.MessageDO;
import com.polarizon.rag.chat.MessageRoleEnum;
import com.polarizon.rag.chat.MessageStyleEnum;
import com.polarizon.rag.chat.ModelConfig;
import com.polarizon.rag.chat.ModelInfoDO;
import com.polarizon.rag.chat.SourceBO;
import com.polarizon.rag.chat.TemplateSourceEnum;
import com.polarizon.rag.chat.params.ModelInfoDOParams;
import com.polarizon.rag.kb.KbEnableEnum;
import com.polarizon.rag.kb.KnowledgeBaseDO;
import com.polarizon.rag.kb.KnowledgeFileChunkDO;
import com.polarizon.rag.kb.KnowledgeFileDO;
import com.polarizon.rag.kb.params.KnowledgeBaseDOParams;
import com.polarizon.rag.plugin.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.EventCallbackParam;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.HandleAnswerContentDTO;
import com.polarizon.rag.plugin.bean.dto.chat.JsonExtraContentChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.KbInfo;
import com.polarizon.rag.plugin.bean.dto.chat.ReasonChatCompletionResponse;
import com.polarizon.rag.plugin.bean.dto.chat.SearchChunksRep;
import com.polarizon.rag.plugin.bean.dto.instruct.InstructChatParam;
import com.polarizon.rag.plugin.bean.dto.instruct.Outline;
import com.polarizon.rag.plugin.bean.enums.ChatModeEnum;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.config.openai.AuthOpenAiInterceptor;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.exception.InappropriateContentException;
import com.polarizon.rag.plugin.exception.SystemBusyExecutionException;
import com.polarizon.rag.plugin.util.FeignUtil;
import com.polarizon.rag.plugin.util.LLMContentUtils;
import com.polarizon.rag.plugin.util.MarkdownUtils;
import com.polarizon.rag.plugin.util.SpringContextHolder;
import com.polarizon.rag.plugin.util.StopWatchUtils;
import com.samskivert.mustache.Mustache;
import com.unfbx.chatgpt.OpenAiClient;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import com.unfbx.chatgpt.entity.chat.ResponseFormat;
import com.unfbx.chatgpt.function.KeyRandomStrategy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.service.chat.ChatService.END_FLAG;
import static com.polarizon.rag.plugin.util.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.util.Constants.DEFAULT_EMBEDDING_MODEL_NAME;
import static com.polarizon.rag.plugin.util.Constants.ENABLE_PARENT_INDEX_DEFAULT;
import static com.polarizon.rag.plugin.util.Constants.MAX_EVENT_LOG_NUM;
import static com.polarizon.rag.plugin.util.Constants.STREAM_REASONING;
import static com.polarizon.rag.plugin.util.Constants.THINK_REASONING_END_FLAG;
import static com.polarizon.rag.plugin.util.Constants.THINK_REASONING_START_FLAG;
import static com.polarizon.rag.plugin.util.InstructConstants.SEARCH_HELP_ME_SEARCH;
import static com.polarizon.rag.plugin.util.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.util.Constants.X_TRACE_ID_HEADER_NAME;
import static com.polarizon.rag.plugin.util.LLMContentUtils.checkIsContainThought;
import static com.polarizon.rag.plugin.util.LlmProcessConstants.END_FLAG_LIST;
import static com.polarizon.rag.plugin.util.ThinkTanksReportConstants.OUTLINE_TEMPLATE;
import static com.polarizon.rag.plugin.util.ThinkTanksReportConstants.OUTLINE_TEMPLATE_MAP;
import com.polarizon.rag.plugin.service.model.ModelService;
import com.polarizon.rag.chat.ModelTypeEnum;


/**
 * 各种关于chat 的公共方法。
 * 它不依赖其它服务；被其它服务依赖！
 */
@Slf4j
@Service
public class ChatHelper {
    public static int DEFAULT_MAX_TOKEN = 8192;
    public static final String QUERY = "question";
    public static final String REFERENCE_KEY = "context";
    // 会从 chatService 的配置 赋值
    public static String defaultModelName;
    public static String RAG_DEFAULT_MODEL_NAME;
    public static String defaultSystemPrompt;
    public static String defaultUserPrompt;
    public static String defaultNoChunkUserPrompt;
    public static String defaultNoChunkSystemPrompt;
    public static Integer defaultRerankTopK ;
    public static Boolean defaultSearchWeb;
    public static List<String> endFlags;
    public static String defaultPrompt;
    public static Integer defaultSearchChunksTopK;

    public static int llmOutputCheckWindowsSize;
    public static int inspectMaxChunkSize = 20;
    public static String noChunkPreInfo = "根据知识库中已知信息无法回答该问题。AI综合所有相关数据后，参考性解读如下:";

    public static List<SourceBO> convertSourceList(List<ChatRes.SourceBOResp> sourceBORespList) {
        // 保存答案
        List<SourceBO> sourceBOList = new ArrayList<>();
        if (sourceBORespList != null) {
            sourceBOList = sourceBORespList.stream()
                .map(source -> buildSource(source, source.getKnowledgeFileChunkBO(), source.getKnowledgeFileBO()))
                .toList();
        }
        return sourceBOList;
    }

    private static SourceBO buildSource(ChatRes.SourceBOResp source, KnowledgeFileChunkDO knowledgeFileChunkBO,
        KnowledgeFileDO knowledgeFileBO) {
        SourceBO sourceBO = new SourceBO();
        sourceBO.setFileID(source.getFileID());
        sourceBO.setKbChunkID(source.getKbChunkID());
        sourceBO.setScore(source.getScore());
        sourceBO.setKbType(source.getKbType());
        sourceBO.setDisplayUrl(source.getDisplayUrl());
        sourceBO.setSummary(source.getSummary());

        return buildSource(sourceBO, knowledgeFileChunkBO, knowledgeFileBO);
    }

    public static SourceBO buildSource(SourceBO source, KnowledgeFileChunkDO chunk, KnowledgeFileDO knowledgeFileDO) {
        SourceBO sourceBO = new SourceBO();
        sourceBO.setFileID(source.getFileID());
        sourceBO.setKbChunkID(source.getKbChunkID());
        sourceBO.setScore(source.getScore());
        sourceBO.setKbType(source.getKbType());
        sourceBO.setDisplayUrl(source.getDisplayUrl());
        sourceBO.setSummary(source.getSummary());
        if (chunk != null) {
            sourceBO.setCaptureID(chunk.getCaptureID());
            sourceBO.setChapterID(chunk.chapterID);
            sourceBO.setChapterName(chunk.getChapterName());
            sourceBO.setParentChapterID(chunk.getParentChapterID());
            sourceBO.setParentChapterName(chunk.getParentChapterName());
            sourceBO.setContent(chunk.getContent());
            sourceBO.setChunkID(chunk.getChunkID());
            sourceBO.setPageNumber(chunk.getPageNumber());
            sourceBO.setCapture(chunk.getCapture());
        }
        if (knowledgeFileDO != null) {
            sourceBO.setFileName(knowledgeFileDO.getName());
            sourceBO.setKnowledgeBaseID(knowledgeFileDO.getKnowledgeBaseID());
            sourceBO.setChunkSize(knowledgeFileDO.getChunkSize());
            sourceBO.setCaptureSize(knowledgeFileDO.getCaptureSize());
            sourceBO.setFormat(knowledgeFileDO.getFormat());
            sourceBO.setFileBytes(knowledgeFileDO.getFileBytes());
            sourceBO.setEnable(knowledgeFileDO.getEnable());
            sourceBO.setStatus(knowledgeFileDO.getStatus());
            sourceBO.setInputFileS3(knowledgeFileDO.getInputFileS3());
            sourceBO.setFileS3(knowledgeFileDO.getFileS3());
            sourceBO.setParserVersion(knowledgeFileDO.getParserVersion());
        }

        return sourceBO;
    }

    @NotNull
    public static MessageDO buildMessageDO(ConversationDO conversationDO, String content,
        MessageRoleEnum messageRoleEnum, List<SourceBO> source, MessageStyleEnum messageStyleEnum,
        List<ExtraContentBO> extraContentBO, String reasoningContent) {
        MessageDO messageDO = new MessageDO();
        messageDO.setConversationID(conversationDO.getId());
        messageDO.setChatTime(System.currentTimeMillis());
        messageDO.setRole(messageRoleEnum);
        messageDO.setContent(content);
        messageDO.setReasoningContent(reasoningContent);
        messageDO.setKbIDList(conversationDO.getKbIDList());
        messageDO.setSource(source);
        messageDO.setConversationConfig(conversationDO.getConversationConfig());
        messageDO.setStyle(messageStyleEnum);
        messageDO.setRequestId(MDC.get(TRACE_ID));
        messageDO.setExtraContent(extraContentBO);
        return messageDO;
    }
    @NotNull
    public static ChatRes buildCompleteAnswer(ChatParam param, String resp, Long questionTime,
        List<ChatRes.SourceBOResp> source, MessageStyleEnum messageStyleEnum,
        EventCallbackParam eventCallbackParam) {
        ChatRes chatRes = new ChatRes();
        chatRes.setQuestion(param.getQuestion());
        chatRes.setResponse(resp);
        AtomicReference<StringBuffer> reasoningAnswerSBAtomicRef = eventCallbackParam.getReasoningAnswerSBAtomicRef();
        Boolean containReasoning = false;
        StringBuffer stringBuffer = reasoningAnswerSBAtomicRef.get();
        if (reasoningAnswerSBAtomicRef != null) {
            if(stringBuffer!=null && StringUtils.isNotEmpty(stringBuffer.toString())){
                containReasoning = true;
            }
        }
        chatRes.setQuestionTime(questionTime);
        chatRes.setStyle(messageStyleEnum);

        List<ChatHistory> history = param.getHistory();
        history.add(new ChatHistory().setRole(MessageRoleEnum.user).setContent(param.getQuestion()));
        ChatHistory chatHistory = new ChatHistory().setRole(MessageRoleEnum.system);
        ChatHistory e = chatHistory.setContent(resp);
        if (containReasoning) {
            chatHistory.setReasoningContent(stringBuffer.toString());
            chatRes.setReasoningContent(stringBuffer.toString());
        }
        history.add(e);
        chatRes.setHistory(history);
        chatRes.setSource(source);
        if (eventCallbackParam.getStream() != null && !eventCallbackParam.getStream()) {
            chatRes.setEvaluateInfo(EventCallbackParam.buildAnswerTraceDO(eventCallbackParam));
        }
        chatRes.setQueryRewriterResult(eventCallbackParam.getQueryRewriterResult());
        return chatRes;
    }

    @NotNull
    public static JsonExtraContentChatRes buildCompleteAnswerWithExtraContent(ChatParam param, String resp, Long questionTime,
        List<ChatRes.SourceBOResp> source, MessageStyleEnum messageStyleEnum, EventCallbackParam eventCallbackParam, 
        List<JsonExtraContentChatRes.SourceBOResp.JsonContentExtraContentBO> extraContent) {
        JsonExtraContentChatRes chatRes = new JsonExtraContentChatRes();
        String question = param.getQuestion();
        chatRes.setQuestion(question);
        chatRes.setResponse(resp);
        chatRes.setQuestionTime(questionTime);
        chatRes.setStyle(messageStyleEnum);

        List<ChatHistory> history = param.getHistory();
        if (StringUtils.isNotBlank(question)) {
            history.add(new ChatHistory().setRole(MessageRoleEnum.user).setContent(question));
        }
        if (StringUtils.isNotBlank(resp)) {
            history.add(new ChatHistory().setRole(MessageRoleEnum.system).setContent(resp));            
        }
        chatRes.setHistory(history);
        chatRes.setSource(source);
        if (eventCallbackParam.getStream() != null && !eventCallbackParam.getStream()) {
            chatRes.setEvaluateInfo(EventCallbackParam.buildAnswerTraceDO(eventCallbackParam));
        }
        chatRes.setQueryRewriterResult(eventCallbackParam.getQueryRewriterResult());
        chatRes.setExtraContent(extraContent);
        return chatRes;
    }

    @NotNull
    static Flux<ServerSentEvent<String>> buildErrorRespData(Throwable e) {
        Integer status = ResultDTO.Constants.FAILURE;
        if (e instanceof SystemBusyExecutionException) {
            status = SystemBusyExecutionException.STATUS_CODE;
        }
        return Flux.just(ServerSentEvent.<String>builder().event(STREAM_ANSWER)
            .data(JSON.toJSONString(NewResultDTO.error(status, e.getMessage(), null, MDC.get(TRACE_ID)))).build());
    }

    static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String answer) {
        return doSinkNext(content, sink, answer, MDC.get(TRACE_ID));
    }

    static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer) {
        return doSinkNext(content, sink, answer, MDC.get(TRACE_ID));
    }

    static ServerSentEvent<String> doSinkNext(ChatRes content, FluxSink<ServerSentEvent<String>> sink, String answer,
        String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(answer)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

    static <T> ServerSentEvent<String> doSinkNext(T content, FluxSink<ServerSentEvent<String>> sink, String answer,
        String traceId) {
        ServerSentEvent<String> serverSentEvent = ServerSentEvent.<String>builder().event(answer)
            .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", content, traceId)))
            .build();
        sink.next(serverSentEvent);
        return serverSentEvent;
    }

    public static Integer getRerankTopKOrDefault(Integer defaultRerankTopK, ConversationConfig conversationConfig) {
        if (conversationConfig != null) {
            Integer rerankTopN = conversationConfig.getRerankTopN();
            if (rerankTopN != null) {
                return rerankTopN;
            }
        }
        return defaultRerankTopK;
    }

    public static Boolean isSearchWebOrDefault(Boolean defaultIsWebSearch, ConversationConfig conversationConfig) {
        if (conversationConfig != null) {
            Boolean isWebSearch = conversationConfig.getIsSearchWeb();
            if (isWebSearch != null) {
                return isWebSearch;
            }
        }
        return defaultIsWebSearch;
    }

    public static Integer getSearchTopKOrDefault(Integer defaultRerankTopK, ConversationConfig conversationConfig) {
        return getRerankTopKOrDefault(defaultRerankTopK, conversationConfig) + 10;
    }

    public static Integer getSearchTopKOrDefault(Integer defaultRerankTopK, ConversationConfig conversationConfig,
        InstructDO instructDO) {
        // 指令配置了，则使用指令的配置
        if (instructDO != null) {
            OtherConfig otherConfig = instructDO.getOtherConfig();
            if (otherConfig != null) {
                Integer rerankTopK = otherConfig.getRerankTopK();
                if (rerankTopK != null && rerankTopK >0) {
                    return rerankTopK + 10;
                }
            }
        }
        return getRerankTopKOrDefault(defaultRerankTopK, conversationConfig) + 10;
    }

    /**
     * 获取question
     */
    public static String extractQuestion(InstructChatParam instructChatParam, InstructDO instructDO) {
        OtherConfig otherConfig = instructDO.getOtherConfig();

        if (!otherConfig.getIsSearch()) {
            return null;
        }

        return instructChatParam.getInstruct().getPromptTemplate().getDisplayParams().stream()
            .filter(param -> Objects.equals(param.getName(), otherConfig.getSearchKey())).findFirst()
            .map(PromptTemplateParam::getValue).orElse(null);
    }

    public static InstructDO getInstructDOByCode(String code) {
        InstructDOParams params = InstructDOParams.builder().code(List.of(code)).build();

        ResultDTO<Page<InstructDO>> pageResultDTO =
            FeignUtil.getInstructDOFeign().conditionsPage(null, null, params, PageRequest.of(0, 1));
        Page<InstructDO> instructDOPage = FeignUtil.getDataNonNull(pageResultDTO);
        Optional<InstructDO> any = instructDOPage.stream().findAny();
        return any.orElseThrow(() -> new BusinessExecutionException("未找到对应的指令"));
    }

    public static ModelInfoDO getModelInfoByConversationConfig(ConversationConfig conversationConfig,
        LlmConfig llmConfig) {
        if (conversationConfig == null) {
            return getDefaultModelInfoFromYml(llmConfig);
        }
        String modelInfoID = conversationConfig.getModelInfoID();
        if (StringUtils.isNotBlank(modelInfoID)) {
            ResultDTO<ModelInfoDO> resultDTO = FeignUtil.getModelInfoDOFeign().findOneByID(null, null, modelInfoID);
            return FeignUtil.getDataNonNull(resultDTO, "modelInfo 不存在，modelInfoID：" + modelInfoID);
        }
        ModelConfig modelConfig = conversationConfig.getModelConfig();
        if (modelConfig == null) {
            return getDefaultModelInfoFromYml(llmConfig);
        }
        // 兼容老的 conversation，没有modelInfoID,则通过 modelName 获取
        return getModelConfigByModelName(modelConfig.getApiParamModelName(), llmConfig);
    }

    public static ModelInfoDO getModelConfigByModelName(String modelName, LlmConfig llmConfig) {
        if (StringUtils.isNotBlank(modelName)) {
            // 获取数据库自信的配置
            ResultDTO<List<ModelInfoDO>> listResultDTO = FeignUtil.getModelInfoDOFeign()
                .conditions(null, null, ModelInfoDOParams.builder().name(List.of(modelName)).build());
            List<ModelInfoDO> modelInfos = FeignUtil.getListDataNonEmpty(listResultDTO, "指定模型不存在： " + modelName);
            return modelInfos.get(0);
        }

        return getDefaultModelInfoFromYml(llmConfig);
    }

    @NotNull
    private static ModelInfoDO getDefaultModelInfoFromYml(LlmConfig llmConfig) {
        // 如果没有找到，尝试获取默认的 modelProviderName
        Optional<LlmConfig.ModelConfig> modelConfigOptional =
            llmConfig.getModels().stream()
                .filter(config -> config.getName().equals(defaultModelName)).findFirst();

        // 如果找到默认模型，返回;如果仍然没有找到，抛出异常
        return modelConfigOptional
            .orElseThrow(() -> new BusinessExecutionException("yml default Model not found"))
            .toModelInfo();
    }

    public static void setTraceId(@NotNull EventSource eventSource) {
        MDC.put(TRACE_ID, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
    }

    public static void setTraceId(String traceID) {
        MDC.put(TRACE_ID, traceID);
    }

    @NotNull
    public static List<Message> buildRagMessages(GenerateParam generateParam) {
        PromptTemplate promptTemplate = generateParam.getPromptTemplate();

        // 新增提示词可编辑
        if (!TemplateSourceEnum.DEFAULT.equals(generateParam.getTemplateSourceEnum()) && promptTemplate != null) {
            List<PromptTemplateParam> displayParams = promptTemplate.getDisplayParams();

            if (CollectionUtils.isEmpty(displayParams)) {
                throw new BusinessExecutionException("promptTemplateParam is null");
            }
            PromptTemplateParam promptTemplateParam = 
                displayParams.stream()
                    .filter(p->QUERY.equals(p.getName()))
                    .findFirst().orElseThrow(()->new BusinessExecutionException("query promptTemplateParam is null"));
            promptTemplateParam.setValue(generateParam.getQuestion());
            return buildInstructChatMessage(promptTemplate, displayParams, SEARCH_HELP_ME_SEARCH,
                generateParam.getChunkContent(), REFERENCE_KEY, generateParam.getHistory());
        }

        List<Message> messages = new ArrayList<>();
        // 系统提示词
        Message sysMsg = Message.builder()
            .role(Message.Role.SYSTEM)
            .content(generateParam.getSystemPrompt())
            .build();
        messages.add(sysMsg);

        // 历史上下文消息
        if (CollectionUtils.isNotEmpty(generateParam.getHistory())) {
            generateHistory(messages, generateParam.getHistory());
        }

        // 当前提问
        String context = "";
        List<String> chunkContent = generateParam.getChunkContent();
        if (CollectionUtils.isNotEmpty(chunkContent)) {
            context = IntStream.range(0, chunkContent.size())
                .mapToObj(i -> "已知信息" + (i + 1) + ":\n" + chunkContent.get(i) + "\n")
                .collect(Collectors.joining());
        }
        Message message = Message.builder()
            .role(Message.Role.USER)
            .content(generateParam.getUserPrompt()
                .replace("{{context}}", context)
                .replace("{{question}}", generateParam.getQuestion())
                .replace("{{ context }}", context)
                .replace("{{ question }}", generateParam.getQuestion()))
            .build();
        messages.add(message);
        return messages;
    }

    /**
     *  构建指令聊天消息
     *  给大模型的提示词顺序遵从：
     *   1. 系统提示词
     *   2. 历史上下文消息
     *   3. 用户提示词
     * 
     * @param promptTemplate 提示模板，包含系统和用户的提示内容及参数,提前设置好的
     * @param displayParams  显示参数，用于替换用户提示词中的参数，用户实时输入的参数
     * @param instructCode   指令代码，用于区分不同的指令类型
     * @param chunks         分块的内容，用于构建参考内容
     * @param referenceKey   参考键，用于在用户提示词中引用参考内容
     * @param history        聊天历史记录，用于提供上下文信息
     * @return 返回构建的聊天消息列表
     */
    public static List<Message> buildInstructChatMessage(PromptTemplate promptTemplate, 
        List<PromptTemplateParam> displayParams, String instructCode,
        List<String> chunks, String referenceKey, List<ChatHistory> history) {
        List<Message> messages = new ArrayList<>();
        
        // 系统提示词
        String systemContent = promptTemplate.getSystemContent();
        if (systemContent != null) {
            Message sysMsg =
                buildReplacedMessage(Message.Role.SYSTEM, systemContent,
                    promptTemplate.getSystemContentParams(), displayParams, chunks, referenceKey);
            messages.add(sysMsg);
        }
        

        // 宸启帮我查需要上下文信息
        // TODO 增加 otherConfig 的 memory
        if (SEARCH_HELP_ME_SEARCH.equals(instructCode) && CollectionUtils.isNotEmpty(history)) {
            generateHistory(messages, history);
        }

        // 用户提示词替换
        String userContent = promptTemplate.getUserContent();
        if (userContent != null) {
            Message message =
                buildReplacedMessage(Message.Role.USER, userContent,
                    promptTemplate.getUserContentParams(), displayParams, chunks, referenceKey);
            messages.add(message);
        }
        
        return messages;
    }

    @NotNull
    private static Message buildReplacedMessage(Message.Role role, String content,
        List<PromptTemplateParam> promptTemplateParams, List<PromptTemplateParam> displayParams, List<String> chunks,
        String referenceKey) {
        if (CollectionUtils.isNotEmpty(promptTemplateParams)) {
            Map<String, Object> replaceMap = buildReplaceMap(displayParams, chunks, referenceKey, promptTemplateParams);
            content = Mustache.compiler().compile(content).execute(replaceMap);
        }
        return Message.builder()
            .role(role)
            .content(content)
            .build();
    }

    @NotNull
    private static Map<String, Object> buildReplaceMap(List<PromptTemplateParam> displayParams, List<String> chunks,
        String referenceKey, List<PromptTemplateParam> promptTemplateParams) {
        Map<String, Object> replaceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(promptTemplateParams)) {
            replaceMap = promptTemplateParams.stream().collect(Collectors.toMap(
                PromptTemplateParam::getName,
                param -> {
                    String value = displayParams.stream()
                        .filter(displayParam -> Objects.equals(displayParam.getName(), param.getName()))
                        .findFirst()
                        .map(PromptTemplateParam::getValue)
                        .orElse(null);
                    if (ParamTypeEnum.Dict.equals(param.getType()) && value != null) {
                        return param.getMap().getOrDefault(value, "");
                    }
                    return value != null ? value : "";
                },
                (existing, replacement) -> replacement
            ));
        }
        // TODO 梳理 chunk传参 ，到底是用入参，还是从 promptTemplateParams 传入
        // 参考内容替换
        if (StringUtils.isNotBlank(referenceKey)) {
            if (CollectionUtils.isNotEmpty(chunks)) {
                String context = "";
                context = IntStream.range(0, chunks.size())
                    .mapToObj(i -> (i + 1) + ":\n" + chunks.get(i) + "\n")
                    .collect(Collectors.joining());
                replaceMap.put(referenceKey, context);
            }
        }
        return replaceMap;
    }

    public static void generateHistory(List<Message> messages, List<ChatHistory> histories) {
        histories.forEach(history -> {
            Message.Role role = Message.Role.USER;
            if (MessageRoleEnum.robot == history.getRole()) {
                role = Message.Role.ASSISTANT;
            }
            Message historyMsg = Message.builder()
                .role(role)
                .content(history.getContent())
                .build();
            messages.add(historyMsg);
        });
    }

    public static ChatCompletion buildChatCompletion(GenerateParam generateParam, List<Message> messages,
        Boolean respJsonFormat) {
        ChatModeConfig chatModeConfig = generateParam.getChatModeConfig();
        ModelInfoDO modelInfoDO = generateParam.getModelInfoDO();
        ChatCompletion chatCompletion = ChatCompletion.builder()
            .messages(messages)
            .model(modelInfoDO.getName())
            .temperature(chatModeConfig.getTemperature())
            .presencePenalty(chatModeConfig.getPresencePenalty())
            .frequencyPenalty(chatModeConfig.getFrequencyPenalty())
            .topP(chatModeConfig.getTopP().doubleValue())
            .build();

        if (respJsonFormat != null && respJsonFormat) {
            chatCompletion.setResponseFormat(ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT.getName()).build());
        }
        
        int maxOutputTokens =
            getMaxOutputTokens(modelInfoDO, chatCompletion.getMessages());

        chatCompletion.setMaxTokens(maxOutputTokens);
//        log.debug("chatCompletion:\n{}", JSON.toJSONString(chatCompletion));
        return chatCompletion;
    }

    private static int getMaxOutputTokens(ModelInfoDO modelConfig, List<Message> messages) {
        // 默认4k
        int maxOutputTokens = 4096;

        // 先使用老的配置
        Integer maxTokens = modelConfig.getMaxTokens();
        if (maxTokens != null && maxTokens > 0) {
            maxOutputTokens = maxTokens;
        }

        // 判断是否是新的配置，并使用
        Integer configMaxOutputTokens = modelConfig.getMaxOutputTokens();
        Integer contextLength = modelConfig.getContextLength();
        // 说明是新的配置，否则是老的配置
        boolean isNewConfig =
            configMaxOutputTokens != null && configMaxOutputTokens > 0 && contextLength != null && contextLength > 0;
        // TODO 目前仅适配了 qwen 的分词器
        // 参考 com.unfbx.chatgpt.utils.TikTokensUtil
        if (isNewConfig && modelConfig.getName().toUpperCase().contains("QWEN")) {
            // 计算token
            Tokenizer tokenizer = TokenizerFactory.qwen();
            Long inputTokenCount = 0L;
            //  # every message follows <|start|>{role/name}\n{content}<|end|>\n
            int tokensPerMessage = 4;
//            tokens_per_name = -1  // if there's a name, the role is omitted
            for (Message message : messages) {
                inputTokenCount += tokensPerMessage;
                inputTokenCount += tokenizer.encodeOrdinary(message.getContent()).size();
                inputTokenCount +=  tokenizer.encodeOrdinary(message.getRole()).size();
            }
            // every reply is primed with <|start|>assistant<|message|>
            inputTokenCount += 3;

            // TODO 输入字符截断
            
            int leftTokens = (int)(contextLength - inputTokenCount - 50);
            maxOutputTokens = Math.min(leftTokens, configMaxOutputTokens);
            log.debug("maxOutputTokens: {}; [inputTokenCount:{}, contextLength:{}, leftTokens:{}]",
                maxOutputTokens, inputTokenCount, contextLength, leftTokens);
            if (maxOutputTokens < 0) {
                throw new BusinessExecutionException("maxOutputTokens is less than 0");
            }
        }
        return maxOutputTokens;
    }

    @SuppressWarnings("KotlinInternalInJava")
    @NotNull
    public static OpenAiStreamClient getOpenAiStreamClient(ModelInfoDO model) {
        // 创建 OkHttpClient，加入外部API请求需要的配置
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
//            .addInterceptor(new ReqUrlInterceptor(model.getPrefixUrl()))
            .addInterceptor(new AuthOpenAiInterceptor(model.getApiKey()))
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(3000, TimeUnit.SECONDS)
            .readTimeout(3000, TimeUnit.SECONDS)
            .protocols(Collections.singletonList(Protocol.HTTP_1_1))
            .build();
        String baseurl = model.getEndpointUrl();

        baseurl = baseurl.endsWith("v1") ? baseurl.replaceAll("v1","") : baseurl;

        return OpenAiStreamClient.builder()
            .apiKey(Collections.singletonList(model.getApiKey()))
            .keyStrategy(new KeyRandomStrategy())
            .okHttpClient(okHttpClient)
            //自己做的代理地址
            .apiHost(baseurl)
            .build();
    }

    /**
     * 获取chatCompletion的回答（非流式）
     *
     * @param chatCompletionResponse chatCompletion的回答
     * @return 回答
     */
    public static String getChatCompletionAnswer(ChatCompletionResponse chatCompletionResponse) {
        if (CollectionUtils.isNotEmpty(chatCompletionResponse.getChoices())
            && ObjectUtils.isNotEmpty(chatCompletionResponse.getChoices().get(0))
            && ObjectUtils.isNotEmpty(chatCompletionResponse.getChoices().get(0).getMessage())) {
            return chatCompletionResponse.getChoices().get(0).getMessage().getContent();
        }
        return "";
    }

    /**
     * 获取chatCompletion的回答（流式）
     *
     * @param resp chatCompletion的回答
     * @return 回答
     */
    public static String getChatCompletionStreamAnswer(ChatCompletionResponse resp) {
        if (CollectionUtils.isNotEmpty(resp.getChoices())
            && ObjectUtils.isNotEmpty(resp.getChoices().get(0).getDelta())
            && ObjectUtils.isNotEmpty(resp.getChoices().get(0).getDelta().getContent())) {
            return resp.getChoices().get(0).getDelta().getContent();
        }
        return "";
    }


    public static void handleOnEvent(HandleAnswerContentDTO handleAnswerContentDTO) {
        String data = handleAnswerContentDTO.getData();
        ChatParam param = handleAnswerContentDTO.getChatParam();
        FluxSink<ServerSentEvent<String>> sink = handleAnswerContentDTO.getSink();
        AtomicReference<StringBuffer> completeAnswerARSB = handleAnswerContentDTO.getSinkSBAF();
        AtomicReference<StringBuffer> showContentARSB = handleAnswerContentDTO.getShowAllContentARSB();
        boolean needAnswer = handleAnswerContentDTO.isNeedAnswer();
        if ("[DONE]".equals(data)) {
            log.info("LLM 返回数据结束了");
            return;
        }

        ReasonChatCompletionResponse resp = JSON.parseObject(data, ReasonChatCompletionResponse.class);
        // 提前返回处理空choices的情况
        if (CollectionUtils.isEmpty(resp.getChoices())) {
            return;
        }

        // 提前返回处理空delta的情况
        ReasonChatCompletionResponse.ChatChoice choice = resp.getChoices().get(0);
        if (ObjectUtils.isEmpty(choice.getDelta())) {
            return;
        }

        // 提前返回处理空content的情况
        String answer = choice.getDelta().getContent();
        if (StringUtils.isEmpty(answer)) {
            return;
        }
        completeAnswerARSB.get().append(answer);
        showContentARSB.get().append(answer);
        // 处理流式响应
        handleStreamSink(param.getStream(), sink, needAnswer, answer, param.getQuestion());
    }

    public static void handleOnClosed(EventSource eventSource, ChatParam param,
        FluxSink<ServerSentEvent<String>> sink,
        AtomicReference<StringBuffer> showContentSBAR,
        AtomicReference<StringBuffer> completeAnswerSBAtomicRef,
        AtomicReference<StringBuffer> reasoningAnswerSBAtomicRef,
        AtomicReference<StopWatchUtils> chatWatchRef,
        List<ChatRes.SourceBOResp> source,
        EventCallbackParam eventCallbackParam,
        boolean needAnswer,
        boolean isComplete,
        CompletableFuture<String> future) {
        MDC.put(TRACE_ID, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
        StopWatch stopWatch = chatWatchRef.get().getStopWatch();
        if (stopWatch != null && stopWatch.isRunning()) {
            stopWatch.stop();
        }
        log.debug("queryLlmSseOnClosed:{}", eventSource.request());

        try {
            if (needAnswer && isComplete) {
                ChatHelper.doSinkNext(
                    ChatHelper.buildCompleteAnswer(param, completeAnswerSBAtomicRef.get().toString(),
                        System.currentTimeMillis(), source, MessageStyleEnum.DEFAULT, eventCallbackParam),
                    sink, COMPLETE_ANSWER, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
            }

            if (completeAnswerSBAtomicRef != null && completeAnswerSBAtomicRef.get() != null) {
                log.debug("sinkCompleteAnswerBuffer:\n {}", completeAnswerSBAtomicRef.get().toString());
            }

            if (showContentSBAR != null && showContentSBAR.get() != null) {
                log.debug("showContentSB:\n{}", showContentSBAR.get().toString());
            }


            if (reasoningAnswerSBAtomicRef != null && reasoningAnswerSBAtomicRef.get() != null) {
                log.debug("reasoningAnswerBuffer:\n{}", JSON.toJSONString(reasoningAnswerSBAtomicRef.get().toString()));
            }

            if (isComplete) {
                sink.complete();
            }

            if (future != null) {
                future.complete(completeAnswerSBAtomicRef.get().toString());
            }
        } catch (Exception e) {
            if (future != null) {
                future.completeExceptionally(e);
            }else {
                // TODO，这样才满足场景？ 是否时有future的时候，就不需要sink 错误 -> 链接终止
                //                sink.error(e);                
            }

            sink.error(e);
        }
    }

    public static void handleOnFailure(Throwable t, Response response, FluxSink<ServerSentEvent<String>> sink,
        CompletableFuture<String> future, Boolean isComplete) {
        String errorMsg = t == null ? "" : t.getMessage();
        String responseCode = response == null ? "" : String.valueOf(response.code());
        String responseMsg = response == null ? "" : response.message();
        String responseBodyErrorMsg = extractResponseError(response);
        String message =
            "外部大模型调用失败:" + errorMsg + "[responseCode]:" + responseCode + "; [responseMsg]:" + responseMsg 
            + "; [errorMsg]:" + responseBodyErrorMsg;
        

        Exception ex = t==null? new SystemBusyExecutionException(message) : new SystemBusyExecutionException(message, t);

        if (StringUtils.isNotBlank(responseBodyErrorMsg) && responseBodyErrorMsg.contains(InappropriateContentException.INAPPROPRIATE_CONTENT)){
            ex = t==null? new InappropriateContentException(message): new InappropriateContentException(message, t );
        }
        
        // 有 future，说明是中间过程,交给上层处理
        if (future != null) {
            future.completeExceptionally(ex);
        }
        if (isComplete != null && isComplete) {
            sink.error(ex);
            
        }
    }

    public static void cancelRealEventSource(EventCallbackParam param) {
        if (param.getRealEventSourceAtomicReference().get() != null) {
            param.getRealEventSourceAtomicReference().get().cancel();
        }
    }

    private static String extractResponseError(Response response) {
        String responseBodyErrorMsg = "";
        try {
            String responseBody = "";
            if (response != null) {
                responseBody = response.body() != null ? response.body().string() : "No response body";
                if (StringUtils.isNotEmpty(responseBody)) {
                    JSONObject json = null;
                    try {
                        json = JSON.parseObject(responseBody);
                        responseBodyErrorMsg = json.getJSONObject("error").getString("message");
                    } catch (Exception e) {
                        responseBodyErrorMsg = responseBody;
                    }
                }
            }
            //            log.error("Error response body: " + responseBody);
        } catch (Exception e) {
            log.error("Failed to read response body: " + e.getMessage());
        }
        return responseBodyErrorMsg;
    }

    @NotNull
    static OpenAiClient getOpenAiClient(ModelInfoDO model) {
        return OpenAiClient.builder()
            .apiKey(Collections.singletonList(model.getApiKey()))
            .keyStrategy(new KeyRandomStrategy())
            //自己做的代理地址
            .apiHost(model.getEndpointUrl())
            .build();
    }

    public static ChatModeConfig fromChatModeEnum(ChatModeEnum chatModeEnum) {
        ChatModeConfig chatModeConfig = new ChatModeConfig();
        chatModeConfig.setCode(chatModeEnum.getCode());
        chatModeConfig.setModeName(chatModeEnum.getModeName());
        chatModeConfig.setTemperature(chatModeEnum.getTemperature());
        chatModeConfig.setPresencePenalty(chatModeEnum.getPresencePenalty());
        chatModeConfig.setFrequencyPenalty(chatModeEnum.getFrequencyPenalty());
        chatModeConfig.setTopP(chatModeEnum.getTopP());
        ;
        return chatModeConfig;
    }

    public static <T> T deepCopyObject(T original) {
        if (original == null) {
            return null;
        }
        String json = JSON.toJSONString(original);
        return (T)JSON.parseObject(json, original.getClass());
    }

    private static void handleStreamSink(Boolean stream, FluxSink<ServerSentEvent<String>> sink, boolean needAnswer,
        String answer, String question) {
        if (stream == null || stream) {
            if (needAnswer) {
                ChatHelper.doSinkNext(
                    ChatRes.buildStreamAnswer(answer, System.currentTimeMillis(), question), sink, STREAM_ANSWER);
            }
        }
    }

    private static void handleChatAnswer(ChatParam param, String data, StringBuffer completeAnswerBuffer,
        StringBuffer showContentSB, FluxSink<ServerSentEvent<String>> sink, AtomicReference<Boolean> answeredAR,
        Boolean noChunks) {
        if ("[DONE]".equals(data)) {
            log.info("OpenAI返回数据结束了");
            return;
        }

        ReasonChatCompletionResponse resp = JSON.parseObject(data, ReasonChatCompletionResponse.class);
        // 提前返回处理空choices的情况
        if (CollectionUtils.isEmpty(resp.getChoices())) {
            return;
        }
        // 提前返回处理空delta的情况
        ReasonChatCompletionResponse.ChatChoice choice = resp.getChoices().get(0);
        if (ObjectUtils.isEmpty(choice.getDelta())) {
            return;
        }

        // 提前返回处理空content的情况
        String answer = choice.getDelta().getContent();
        if (StringUtils.isEmpty(answer)) {
            return;
        }
        // 添加没知识时的通知
        if (noChunks && !answeredAR.get()){
            answer = noChunkPreInfo + answer;
            answeredAR.set(true);
        }
        completeAnswerBuffer.append(answer);
        showContentSB.append(answer);
        // 处理流式响应
        if (param.getStream() == null || param.getStream()) {
            ChatHelper.doSinkNext(
                ChatRes.buildStreamAnswer(answer, System.currentTimeMillis(), param.getQuestion()),
                sink, STREAM_ANSWER);
        }
    }

    /**
     * 处理 reasoning_content这种形式的推理过程
     * 
     * @param param 
     * @param data
     * @param reasoningAnswerBuffer
     * @param sink
     * @param needAnswer
     * @param reasoningMatchedAF
     */
    public static void handleChatReason(ChatParam param, String data, StringBuffer reasoningAnswerBuffer,
        FluxSink<ServerSentEvent<String>> sink, boolean needAnswer, AtomicReference<Boolean>  reasoningMatchedAF) {
        Boolean stream = param.getStream();
        if ("[DONE]".equals(data)) {
            log.info("LLM 返回数据结束了");
            return;
        }
        ReasonChatCompletionResponse resp = JSON.parseObject(data, ReasonChatCompletionResponse.class);
        // 处理 思考（推理）过程
        if (CollectionUtils.isEmpty(resp.getChoices()) || ObjectUtils.isEmpty(resp.getChoices().get(0).getDelta())) {
            return;
        }

        String reasoningContent = resp.getChoices().get(0).getDelta().getReasoningContent();
        if (ObjectUtils.isEmpty(reasoningContent)) {
            return;
        }

        reasoningAnswerBuffer.append(reasoningContent);

        ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
        // 走到这里，说明是基于reasoning_content 的推理，设置为true，防止<think> 的方式
        // TODO 换一个专用的变量，比如 isReasoningContentType
        reasoningMatchedAF.set(true);

    }

    /**
     * 这个方法只处理推理过程<think>开头</think>结尾的模型输出。
     * 不兼容 没<think>打头这种功能情况（推理过程只有</think>的方式）
     *
     * 处理方式：
     *      解析正常响应里面的是否<think>打头，是则走推理，并以</think> 作为结束，否则走正常输出
     * @param param
     * @param data
     * @param sink
     * @param reasoningLastTokens
     * @param needAnswer
     * @param stream
     * @param reasoningMatchedAF
     * @param eventNumAR
     * @return
     */
    public static String handleChatReasonOldReasoningTypeStandard(ChatParam param, String data,
        FluxSink<ServerSentEvent<String>> sink,
        CircularFifoQueue<String> reasoningLastTokens, boolean needAnswer, Boolean stream,
        AtomicReference<Boolean> reasoningMatchedAF, AtomicReference<Integer> eventNumAR,
        StringBuffer showContentSB, StringBuffer reasoningAnswerBuffer, AtomicReference<Boolean> isFirstHandledAR, 
        AtomicReference<Boolean> reasoningHandleFinishAF) {
        
        // done 到这里了，且reasoningHandleFinishAF 没完成。有两种可能
        if ("[DONE]".equals(data)) {
            // 一种是reasoningLastTokens还没填满，大模型输出就完了，这种是总输出内容过短。不能算是异常。处理 reasoningLastTokens剩余的东西即可
            String outputTokenSB = ChatHelper.getBufferedStrings(reasoningLastTokens);
            if (eventNumAR.get() < reasoningLastTokens.maxSize()) {
                // 直接匹配
                String[] strings = LLMContentUtils.reasoningMaskHandle(outputTokenSB);
                // 匹配成功
                if (strings != null) {
                    reasoningHandleFinishAF.set(true);
                    String reasoningContent = strings[0];
                    String answer = strings[1];
                    if (StringUtils.isNotEmpty(reasoningContent)) {
                        reasoningAnswerBuffer.append(reasoningContent);
                        showContentSB.append(reasoningContent);
                        ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
                    }
                    reasoningLastTokens.clear();
                    // 给到下游
                    return JSONObject.toJSONString(ReasonChatCompletionResponse.ofDeltatContent(answer));
                }else {
                    // 否则给下游
                    return JSONObject.toJSONString(ReasonChatCompletionResponse.ofDeltatContent(outputTokenSB));
                }
            }else {
                // 另一种情况是reasoningLastTokens填满了，但是还没匹配到标签 -> 这种是可能是输出token数(maxTokens)设置过小，内容被截断

                reasoningAnswerBuffer.append(outputTokenSB);
                showContentSB.append(outputTokenSB);
                ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, outputTokenSB, stream);
                String msg = StrUtil.format(
                    "LLM 返回数据结束异常：没有匹配到reasoning结束标签[{}]," +
                    "但模型已经输出已经结束(可能是输出token数(maxTokens)过小导致数据被截断。)。reasoningContent：\n{}",
                    THINK_REASONING_END_FLAG, outputTokenSB);
                log.error(msg);
                // TODO 匹配结束标签，没匹配上才抛出异常，适配思考过程过短的问题（tokens数小于滑动窗口数）
                throw new BusinessExecutionException(msg);
            }
        }

        ReasonChatCompletionResponse resp = JSON.parseObject(data, ReasonChatCompletionResponse.class);
        // 提前返回处理空choices的情况
        if (CollectionUtils.isEmpty(resp.getChoices())) {
            return null;
        }
        // 提前返回处理空delta的情况
        ReasonChatCompletionResponse.ChatChoice choice = resp.getChoices().get(0);
        ReasonChatCompletionResponse.Message delta = choice.getDelta();
        if (ObjectUtils.isEmpty(delta)) {
            return null;
        }

        // 提前返回处理空content的情况
        String answerContent = delta.getContent();
        if (StringUtils.isEmpty(answerContent)) {
            return null;
        }

        // 不统一处理，截断的anwserContent因为下游会再次处理，这里统一处理就会重复，所以需要跟着reasoningAnswerBuffer处理
//        showContentSB.append(answerContent);
        reasoningLastTokens.add(answerContent);
        eventNumAR.set(eventNumAR.get() + 1);
        // 使用 token栈(reasoningLastTokens) 缓存 token，并缓存 THINK_REASONING_END_FLAG size 的个数
        // 检查 reasoningAnswerBuffer 是否大于 THINK_REASONING_END_FLAG, 不大于，则继续缓存
        if (eventNumAR.get() < reasoningLastTokens.maxSize()) {
            return null;
        }
            
        String outputTokenSB = ChatHelper.getBufferedStrings(reasoningLastTokens);
        

        // 第一匹配，则判断是否是推理模型
        if (isFirstHandledAR.get()) {
            isFirstHandledAR.set(false);
            if (outputTokenSB.startsWith(THINK_REASONING_START_FLAG)) {
                reasoningMatchedAF.set(true);
                // 去掉<think> 这个头标
                String reasoningContent = outputTokenSB.replaceAll(THINK_REASONING_START_FLAG, "");
                showContentSB.append(THINK_REASONING_START_FLAG);
                // 处理极限情况
                if (reasoningContent.contains(THINK_REASONING_END_FLAG)) {
                    // 确保后面的数据不进本方法，而是直接处理answerContent
                    reasoningHandleFinishAF.set(true);
                    // 提取推理内容和正常输出
                    reasoningContent = StringUtils.substringBefore(reasoningContent, THINK_REASONING_END_FLAG);
                    String subAnswerContent = StringUtils.substringAfter(reasoningContent, THINK_REASONING_END_FLAG);
                    // 立即处理缓存的推理内容
                    reasoningAnswerBuffer.append(reasoningContent);
                    showContentSB.append(reasoningContent).append(THINK_REASONING_END_FLAG);
                    ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
                    reasoningLastTokens.clear();
                    // answerContent给到下游
                    delta.setContent(subAnswerContent);
                    return JSONObject.toJSONString(resp);
                }else {
                    // 开始滑动匹配endFlag
                    return null;
                }
            }else {
                // 非推理模型，给到下游
                reasoningMatchedAF.set(false);
                reasoningHandleFinishAF.set(true);
                delta.setContent(outputTokenSB);
                return JSONObject.toJSONString(resp);
            }
        }
        // 这里是已经匹配到推理模型且还没处理完，则判断是否匹配到结束标签
        // 检查 reasoningAnswerBuffer ，如果存在"</think>" ,则提取它前面和后面的字符串。
        // 它前面的字符串作为reasoning内容输出，后面的内容作为answer输出
        // 不存在，则将 answerContent 作为reasoning内容输出
        String[] strings = LLMContentUtils.reasoningMaskHandle(outputTokenSB);
        // 匹配成功
        if (strings != null) {
            reasoningHandleFinishAF.set(true);
            String reasoningContent = strings[0];
            String answer = strings[1];
            if (StringUtils.isNotEmpty(reasoningContent)) {
                reasoningAnswerBuffer.append(reasoningContent);
                showContentSB.append(reasoningContent).append(THINK_REASONING_END_FLAG);
                ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
            }
            reasoningLastTokens.clear();
            // answer传递到下游,不用处理null，空下游自己会处理
            delta.setContent(answer);
            return JSONObject.toJSONString(resp);
        }else {
            // 否则，就发布窗口中的第一个（最早）的token 作为推理的内容
            String reasoningContent = reasoningLastTokens.poll();
            if (StringUtils.isNotEmpty(reasoningContent)) {
                reasoningAnswerBuffer.append(reasoningContent);
                showContentSB.append(reasoningContent);
                ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
            }
            // 下游置空
            return null;
        }
    }
    
    
    /**
     *  返回 截断的 answerContent
     *  依赖 isReasoning。
     * 背景:
     *  为了支持华坤提供的模型（只有</think>的方式）。
     *  想着他们改不了，或者不支持<think></think>这种完整的方式，需要切到这个方法
     * @param param
     * @param data
     * @param sink
     * @param reasoningLastTokens
     * @param needAnswer
     * @param stream
     * @param reasoningMatchedAF
     * @param eventNumAR
     * @return
     */
    public static String handleChatReasonOldReasoningType(ChatParam param, String data,
        FluxSink<ServerSentEvent<String>> sink,
        CircularFifoQueue<String> reasoningLastTokens, boolean needAnswer, Boolean stream,
        AtomicReference<Boolean> reasoningMatchedAF, AtomicReference<Integer> eventNumAR,
        StringBuffer showContentSB, StringBuffer reasoningAnswerBuffer, AtomicReference<Boolean> isFirstTokenAR) {

        // 处理过reasoning，则不处理了，扔到下游
        if (reasoningMatchedAF.get()) {
            return data;
        }

        if ("[DONE]".equals(data)) {
            // TODO 处理 reasoningLastTokens剩余的东西
            if(!reasoningMatchedAF.get()){
                String reasoningContent =  ChatHelper.getBufferedStrings(reasoningLastTokens);
                ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
                log.debug("LLM 返回数据结束异常：没有匹配到reasoning结束标签但输出已经完成(可能是输出token数(maxTokens)设置过小。)。reasoningContent：\n{}", reasoningContent);
                // TODO 匹配结束标签，没匹配上才抛出异常，适配思考过程过短的问题（tokens数小于滑动窗口数）
                throw new BusinessExecutionException(
                    "LLM 返回数据结束异常：没有匹配到reasoning结束标签但输出已经完成(可能是输出token数(maxTokens)设置过小或者内容过短。)");
            }else{
                log.debug("LLM 返回数据结束");
                //传递下去
                return data;
            }
        }

        ReasonChatCompletionResponse resp = JSON.parseObject(data, ReasonChatCompletionResponse.class);
        // 提前返回处理空choices的情况
        if (CollectionUtils.isEmpty(resp.getChoices())) {
            return data;
        }
        // 提前返回处理空delta的情况
        ReasonChatCompletionResponse.ChatChoice choice = resp.getChoices().get(0);
        ReasonChatCompletionResponse.Message delta = choice.getDelta();
        if (ObjectUtils.isEmpty(delta)) {
            return data;
        }

        // 提前返回处理空content的情况
        String answerContent = delta.getContent();
        if (StringUtils.isEmpty(answerContent)) {
            return data;
        }

        showContentSB.append(answerContent);
        reasoningLastTokens.add(answerContent);
        eventNumAR.set(eventNumAR.get() + 1);
        // 使用 token栈(reasoningLastTokens) 缓存 token，并缓存 THINK_REASONING_END_FLAG size 的个数
        // 检查 reasoningAnswerBuffer 是否大于 THINK_REASONING_END_FLAG, 不大于，则继续缓存
        if (eventNumAR.get() < reasoningLastTokens.maxSize()) {
            return data;
        }

        String outputTokenSB = ChatHelper.getBufferedStrings(reasoningLastTokens);

        // 检查 reasoningAnswerBuffer ，如果存在"</think>" ,则提取它前面和后面的字符串。
        // 它前面的字符串作为reasoning内容输出，后面的内容作为answer输出
        // 不存在，则将 answerContent 作为reasoning内容输出
        String[] strings = LLMContentUtils.reasoningMaskHandle(outputTokenSB);
        // 匹配成功
        if (strings != null) {
            String reasoningContent = strings[0];
            String answer = strings[1];
            // TODO 去掉<think> 这个头标（如果存在的话）
            if (StringUtils.isNotEmpty(reasoningContent)) {
                reasoningAnswerBuffer.append(reasoningContent);
            }
            // 内容过短，未等到匹配
            if (!isFirstTokenAR.get()) {
                isFirstTokenAR.set(true);
            }
            ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
            reasoningLastTokens.clear();
            reasoningMatchedAF.set(true);
            // TODO answer传递到下游
            if (StringUtils.isNotEmpty(answer)) {
                delta.setContent(answer);
                return JSONObject.toJSONString(resp);
            }else {
                delta.setContent("");
                return JSONObject.toJSONString(resp);
            }
        }
        if(! reasoningMatchedAF.get()){
            // 否则，就发布窗口中的第一个（最早）的token 作为推理的内容
            String reasoningContent = reasoningLastTokens.poll();
            // TODO 去掉<think> 这个投标（如果存在的话）
            //            if (reasoningContent != null) {
            //                reasoningContent = reasoningContent.replaceAll("<think>", "");
            //            }
            if (StringUtils.isNotEmpty(reasoningContent)) {
                // 去掉<think> 这个投标（如果存在的话）
                if (isFirstTokenAR.get()) {
                    reasoningContent = reasoningContent.replaceAll("<think>", "");
                    isFirstTokenAR.set(true);
                }
                reasoningAnswerBuffer.append(reasoningContent);
            }
            ChatHelper.handleReasoningStreamSink(param, sink, needAnswer, reasoningContent, stream);
            // 下游置空
            delta.setContent("");
            return JSONObject.toJSONString(resp);
        }
        return data;
    }

    public static  void handleOnEventWithThought(HandleAnswerContentDTO handleAnswerContentDTO) {
        EventSource eventSource = handleAnswerContentDTO.getEventSource();
            String data = handleAnswerContentDTO.getData();
        
            Boolean isStream = handleAnswerContentDTO.getChatParam().getStream();
            FluxSink<ServerSentEvent<String>> sink = handleAnswerContentDTO.getSink();
            AtomicReference<StringBuffer> sinkSBAF  = handleAnswerContentDTO.getSinkSBAF();
        boolean needAnswer = handleAnswerContentDTO.isNeedAnswer();
        AtomicReference<Boolean> responseMatchedAF = handleAnswerContentDTO.getResponseMatchedAF();
        AtomicReference<Boolean> responseFinishedAF = handleAnswerContentDTO.getResponseFinishedAF();
        AtomicReference<StringBuffer> preCheckTokenBuffer = handleAnswerContentDTO.getPreCheckTokenBuffer();
        CircularFifoQueue<String> lastTokens = handleAnswerContentDTO.getLastTokens();
        AtomicReference<Integer> realOutputNumAR = handleAnswerContentDTO.getRealOutputNumAR();
        AtomicReference<StringBuffer> showAllContentARSB = handleAnswerContentDTO.getShowAllContentARSB();
        String question = handleAnswerContentDTO.getQuestion();

            StringBuffer sinkSB = sinkSBAF.get();
            StringBuffer showAllContent = showAllContentARSB.get();
            // lastTokens 用于存放前两个token，[last2TokenStr , last1TokenStr]
            // 第一次是null
            if ("[DONE]".equals(data)) {
                // 如果没有遇到结束标志END_FLAG("```"),则输出剩余的token
                if (!responseFinishedAF.get()) {
                    String outputSingleToken = getBufferedStrings(lastTokens);
                    sinkSB.append(outputSingleToken);
                    handleStreamSink(isStream, sink, needAnswer, outputSingleToken, question);
                    log.debug("LLM 返回提前结束(!responseFinishedAF.get()). showAllContent:\n {}", showAllContent);
                    responseFinishedAF.set(true);
                }
                log.info("LLM 返回数据结束了");
                return;
            }

            ChatCompletionResponse resp = JSON.parseObject(data, ChatCompletionResponse.class);

            String answer = getChatCompletionStreamAnswer(resp);
            showAllContent.append(answer);

            // 处理“带有思考过程且思考过程在输出的前部分”的输出，只输出思考的结果
            if (!responseMatchedAF.get()) {
                // 获取“带有思考过程且思考过程在输出的前部分”的输出
                String string = preCheckTokenBuffer.get().append(answer).toString();
                String maskedResult = LLMContentUtils.thoughtMaskHandle(string);
                if (maskedResult == null) {
                    return;
                } else {
                    responseMatchedAF.set(true);
                    answer = maskedResult;
                }
            }
            realOutputNumAR.set(realOutputNumAR.get() + 1);
            lastTokens.add(answer);
            // 预匹配成功后且未匹配到“```”(防止多个thought)，预存窗口大小窗口存满了，开始处理待输出token
            if (realOutputNumAR.get() >= lastTokens.maxSize() && !responseFinishedAF.get()) {
                String outputTokenSB = getBufferedStrings(lastTokens);
                // 为了处理大模型输出结果中包含多个thought的情况，这里只取第一个"```"之前的字符
                if (outputTokenSB.contains(END_FLAG) ) {
                    String outputSingleToken = "";
                    for (String endFlag : END_FLAG_LIST) {
                        if (outputTokenSB.contains(endFlag)) {
                            outputSingleToken = StringUtils.substringBefore(outputTokenSB, endFlag);
                            break;
                        }
                    }
                    sinkSB.append(outputSingleToken);
                    handleStreamSink(isStream, sink, needAnswer, outputSingleToken, question);
                    log.debug("thought匹配到结束标记{}，正常结束输出。", END_FLAG);
                    responseFinishedAF.set(true);
                    // sink不complete 为了出发completeAnswer
                    //                sink.complete();
                    //                eventSource.cancel();
                    return;
                } else {
                    // 否则，就发布窗口中的第一个（最早）的token
                    String outputSingleToken = lastTokens.poll();
                    if (outputSingleToken != null) {
                        sinkSB.append(outputSingleToken);
                        handleStreamSink(isStream, sink, needAnswer, outputSingleToken, question);
                    }
                }
            }
        }

    @NotNull
    private static String getBufferedStrings(CircularFifoQueue<String> lastTokens) {
        StringBuilder outputTokenSB = new StringBuilder();
        for (String lastToken : lastTokens) {
            outputTokenSB.append(lastToken == null ? "" : lastToken);
        }
        return outputTokenSB.toString();
    }

    public static Map<Integer, Map.Entry<String, Map<Integer, Map.Entry<String, String>>>> fillSubchapterBody(
        Outline outline, List<Outline.Chapter> chapters, Map<Integer, Map<Integer, String>> totalBodyIndexedMap) {
        Map<Integer, Map.Entry<String, Map<Integer, Map.Entry<String, String>>>> outlineIndexedMap =
            outline.toIndexedMap();
        for (int i = 0; i < chapters.size(); i++) {
            int finalI = i;
            Outline.Chapter chapter = chapters.get(i);
            String chapterName = chapter.getChapterName();
            int chapterNum = finalI + 1;
            Map.Entry<String, Map<Integer, Map.Entry<String, String>>> subChapterMap = outlineIndexedMap.get(i);
            for (int j = 0; j < chapter.getSubChapterList().size(); j++) {
                int subchapterNum = j + 1;
                Map.Entry<String, String> subChapterBodyEntry = subChapterMap.getValue().get(j);
                subChapterBodyEntry.setValue(
                    Assert.notEmpty(totalBodyIndexedMap.get(i).get(j), "第{}章,第{}小节正文为空", chapterNum,
                        subchapterNum));
            }
        }
        return outlineIndexedMap;
    }


    @NotNull
    public static Flux<ServerSentEvent<String>> addEventCallback(EventCallbackParam param) {

        // 从容器拿bean，使@Asycn注解生效
        ChatCommonService chatCommonService = SpringContextHolder.getBean(ChatCommonService.class);
        return param.getServerSentEventFlux()
            .doOnComplete(() -> {
                log.debug("sseDoOnComplete");
                // 保存答案
                param.getChatWatchRef().get()
                    .timeCall("saveAnswerMessage", () -> ChatHelper.saveAnswerMessage(param));
                log.debug("sseDoOnComplete;watch:{}", param.getChatWatchRef().get().getStopWatch().prettyPrint());

                // 保存答案监测数据
                chatCommonService.saveAnswerTrace(param);
            })
            .doOnCancel(() -> {
                log.debug("ssefDoOnCancel");
                ChatHelper.cancelRealEventSource(param);
            })
            .onErrorResume(e -> {
                log.warn("onErrorResume:{}", e.getMessage(), e);
                ChatHelper.cancelRealEventSource(param);
                // 保存答案监测数据
                // 从容器拿bean，使@Asycn注解生效
                ChatService chatService = SpringContextHolder.getBean(ChatService.class);
                chatCommonService.saveAnswerTrace(param);
                return ChatHelper.buildErrorRespData(e);
            })
            .doOnTerminate(() -> {
                log.debug("ssefDoOnTerminate");
                ChatHelper.cancelRealEventSource(param);
            })
            // TODO 使生效
            //            .doFinally(signalType -> {
            //                log.debug("doFinally,signalType:{}", signalType);
            //                releaseResource(param.getRealEventSourceAtomicReference());
            //            })
            ;
    }

    /**
     * 外部大模型调用
     */
    public static EventSource queryOutLlmSse(ChatParam param, FluxSink<ServerSentEvent<String>> sink,
        EventCallbackParam eventCallbackParam, boolean noChunks) {
        StringBuffer reasoningAnswerBuffer = eventCallbackParam.getReasoningAnswerSBAtomicRef().get();
        // 用于打印所有通过流式输出的content的内容，包括<think>标签式的推理过程，不包括 reasoning_content
        StringBuffer allContentSB = eventCallbackParam.getAllContentARSB().get();
        StringBuffer completeAnswerBuffer = eventCallbackParam.getCompleteAnswerSBAR().get();
        GenerateParam generateParam = eventCallbackParam.getGenerateParamRef().get();
        StopWatchUtils chatWatchRef = eventCallbackParam.getChatWatchRef().get();

        // 配置yml文件获取相关模型信息，访问连接、apikey等。
        ModelInfoDO model = generateParam.getModelInfoDO();
        Boolean isReasoning = model.getIsReasoning();
        log.debug("queryLlmSse-generateParam:{}", JSON.toJSONString(generateParam));
        String traceId = MDC.get(TRACE_ID);
        // 创建 OkHttpClient，加入外部API请求需要的配置
        OpenAiStreamClient client = ChatHelper.getOpenAiStreamClient(model);
        Boolean finalIsReasoning = isReasoning;
        EventSourceListener eventSourceListener = new EventSourceListener() {
            // 防止日志过多
            private Integer onEventNum = 0;
            private AtomicReference<Boolean> answeredAR = new AtomicReference<>(false);

            AtomicReference<Integer> eventNumAR =new AtomicReference<>(0);

            AtomicReference<Boolean> reasoningMatchedAF = new AtomicReference<>(false);
            AtomicReference<Boolean> isFirstTokenAR = new AtomicReference<>(false);
            // 缓存推理过程，用于判断是否已经输出了推理过程
            // 长度为两个flag的和，用于保证即使去除START_FLAG之后，endflag仍然够窗口匹配END_FLAG
            CircularFifoQueue<String> reasoningLastTokens =
                new CircularFifoQueue<>(THINK_REASONING_START_FLAG.length()+THINK_REASONING_END_FLAG.length());
            // reasoning处理是否结束(同时作为是否经过判断)
            AtomicReference<Boolean> reasoningHandleFinishAF = new AtomicReference<>(false);

            AtomicReference<Boolean> isFirstHandledAR = new AtomicReference<>(true);
            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type,
                @NotNull String data) {
                super.onEvent(eventSource, id, type, data);
                handleOnEventWithReasonStandard(id, type, data);
            }

            /**
             * 依赖 isReasoning。 
             * 背景 
             *      为了支持华坤提供的模型（只有 的方式）。 
             *      想着他们改不了，或者不支持  这种完整的方式，需要切到这个方法
             * @param id 
             * @param type
             * @param data
             */
            private void handleOnEvent(@Nullable String id, @Nullable String type, String data) {
                // reasoning_content 的方式
                if(finalIsReasoning == null || !finalIsReasoning) {
                    handleChatAnswer(param, data, completeAnswerBuffer, allContentSB, sink, answeredAR, noChunks);
                }else {
                    handleChatReason(param, data, reasoningAnswerBuffer, sink, true, reasoningMatchedAF);
                    data = handleChatReasonOldReasoningType(param, data, sink, reasoningLastTokens, true,
                        param.getStream(), reasoningMatchedAF, eventNumAR, allContentSB, reasoningAnswerBuffer, 
                        isFirstTokenAR);
                    if (reasoningMatchedAF.get()) {
                        // 走后续处理 
                        handleChatAnswer(param, data, completeAnswerBuffer, allContentSB, sink, answeredAR, noChunks);
                    }
                }

                if (onEventNum < MAX_EVENT_LOG_NUM) {
                    log.debug("queryOutLlmSseOnEvent:,id:{},type:{},data{}:{}", id, type, onEventNum, data);
                    onEventNum++;
                }
            }

            /**
             * 这个个方法不依赖 isReasoning ,只处理标准情况
             * 不兼容 没<think>打头这种功能情况（t推理过程只有</think>的方式）
             * @param id 
             * @param type
             * @param data
             */
            private void handleOnEventWithReasonStandard(@Nullable String id, @Nullable String type, String data) {

         
                ChatHelper.handleChatReason(param, data, reasoningAnswerBuffer, sink, true, reasoningMatchedAF);
                // 处理过reasoning，则不处理了，扔到下游
                if(!reasoningHandleFinishAF.get()){
                    data = ChatHelper.handleChatReasonOldReasoningTypeStandard(param, data, sink, reasoningLastTokens, true,
                        param.getStream(), reasoningMatchedAF, eventNumAR, allContentSB, reasoningAnswerBuffer,
                        isFirstHandledAR, reasoningHandleFinishAF);
                }
                
                if (StringUtils.isNotEmpty(data)) {
                    // 走后续处理 
                    handleChatAnswer(param, data, completeAnswerBuffer, allContentSB, sink, answeredAR, noChunks);
                }

                if (onEventNum < MAX_EVENT_LOG_NUM) {
                    log.debug("queryOutLlmSseOnEvent:,id:{},type:{},data{}:{}", id, type, onEventNum, data);
                    onEventNum++;
                }
            }

            @Override
            public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                super.onOpen(eventSource, response);
                ChatHelper.setTraceId(eventSource);
                StopWatch stopWatch = chatWatchRef.getStopWatch();
                if (stopWatch != null && stopWatch.isRunning()) {
                    stopWatch.stop();
                    stopWatch.start("queryOutLlm");
                }
                log.debug("queryOutLlmSseOnOpen:response:{}", response);
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                super.onClosed(eventSource);
//                MDC.put(TRACE_ID, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
                StopWatch stopWatch = chatWatchRef.getStopWatch();
                if (stopWatch != null && stopWatch.isRunning()) {
                    stopWatch.stop();
                }
                log.debug("queryLlmSseOnClosed:{}", eventSource.request());

                ServerSentEvent<String> stringServerSentEvent;
                try {
                    stringServerSentEvent = ChatHelper.doSinkNext(
                        ChatHelper.buildCompleteAnswer(param, completeAnswerBuffer.toString(),
                            System.currentTimeMillis(),
                            eventCallbackParam.getSourceBORespListRef().get(), MessageStyleEnum.DEFAULT, eventCallbackParam),
                        sink, COMPLETE_ANSWER, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
                    log.debug("reasoningAnswerBuffer:\n{}", reasoningAnswerBuffer.toString());
                    log.debug("allContentSB :\n{}", allContentSB.toString());
                    log.debug("sinkCompleteAnswer:\n{}", completeAnswerBuffer.toString());
                    sink.complete();
                } catch (Exception e) {
                    sink.error(e);
                }
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t,
                @Nullable Response response) {
                super.onFailure(eventSource, t, response);
                handleOnFailure(t, response, sink, null, true);
            }
        };

        List<Message> messages = ChatHelper.buildRagMessages(generateParam);
        ChatCompletion chatCompletion = ChatHelper.buildChatCompletion(generateParam, messages,null);

        log.debug("chatCompletion:\n{}", JSON.toJSONString(chatCompletion));
        // 开始请求，返回EventSource
        return new CustomOpenAiClient(client).streamChatCompletion(chatCompletion, eventSourceListener, traceId);
    }

    // 保存答案,不异步保证一致性
    //    @Async
    public static void saveAnswerMessage(EventCallbackParam param) {
        List<SourceBO> sourceBOList = ChatHelper.convertSourceList(param.getSourceBORespListRef().get());
        String content="";
        AtomicReference<StringBuffer> completeAnswerSBAR = param.getCompleteAnswerSBAR();
        if(completeAnswerSBAR != null && completeAnswerSBAR.get() != null){
            content = completeAnswerSBAR.get().toString();
        }
        String reasoningContent="";
        AtomicReference<StringBuffer> reasoningAnswerSBAtomicRef = param.getReasoningAnswerSBAtomicRef();
        if(reasoningAnswerSBAtomicRef != null && reasoningAnswerSBAtomicRef.get() != null){
            reasoningContent = reasoningAnswerSBAtomicRef.get().toString();
        }
        MessageDO messageDO =
            ChatHelper.buildMessageDO(param.getConversationDO(), content,
                MessageRoleEnum.robot, sourceBOList, param.getMessageStyleEnum(),
                EventCallbackParam.buildExtractContentBO(param), reasoningContent);
        FeignUtil.getMessageApi().add(null, null, param.getUserAccount(), messageDO);
        log.debug("saveAnswerMessage:{}", JSON.toJSONString(messageDO));
    }

    public static void saveQuestionMessage(String userAccount, String question, ConversationDO conversationDO) {
        MessageDO messageDO =
            ChatHelper.buildMessageDO(conversationDO, question, MessageRoleEnum.user, null,
                MessageStyleEnum.DEFAULT,null ,null );
        FeignUtil.getMessageApi().add(null, null, userAccount, messageDO);
    }

    @NotNull
    public static ConversationDO getConversationDOWithKbIds(String conversationId) {
        ConversationDO conversationDO = getConversationDO(conversationId);
//        Assert.notEmpty(conversationDO.getKbIDList(), () -> new BusinessExecutionException("知识库不能为空"));
        return conversationDO;
    }

    @NotNull
    public static ConversationDO getConversationDO(String conversationId) {
        ResultDTO<ConversationDO> conversationDOResultDTO =
            FeignUtil.getConversationApi().findOneByID(null, null, conversationId);
        ConversationDO conversationDO = FeignUtil.getDataNonNull(conversationDOResultDTO, "对话不存在");
        log.debug("获取对话信息:{}", JSON.toJSONString(conversationDO));
        return conversationDO;
    }

    public static void setCustomTraceId(String traceID, int chapterNum, int subchapterNum) {
        MDC.put(TRACE_ID, StrUtil.format("{}_{}_{}", traceID, chapterNum, subchapterNum));
    }

    public static void setCustomTraceId(String traceID, Object chapterNum) {
        MDC.put(TRACE_ID, StrUtil.format("{}_{}", traceID, chapterNum));
    }

    @NotNull
    public static Outline buildOutline(List<PromptTemplateParam> displayParams) {
        return buildOutline(displayParams, OUTLINE_TEMPLATE);
    }
    @NotNull
    public static Outline buildOutline(List<PromptTemplateParam> displayParams, String paramName) {
        String outlineJsonParam =
            extractParam(displayParams, paramName, StrUtil.format("{}参数缺失",paramName)).getValue();

        return Outline.fromJson(outlineJsonParam);
    }

    @NotNull
    public static PromptTemplateParam buildPromptTemplateInputParam(String name, String value) {
        PromptTemplateParam chapterParam = new PromptTemplateParam();
        chapterParam.setName(name);
        chapterParam.setValue(value);
        chapterParam.setType(ParamTypeEnum.Input);
        return chapterParam;
    }

    public static InstructChatParam buildInstructChatParam(List<PromptTemplateParam> chapterParams,
        String code, String conversationID, Boolean isStream) {
        InstructChatParam.InstructParam outlineQuestionInstructParam =
            new InstructChatParam.InstructParam().setCode(code)
                .setPromptTemplate(new InstructChatParam.PromptTemplateVO().setDisplayParams(chapterParams));
        return new InstructChatParam().setInstruct(outlineQuestionInstructParam).setConversationID(conversationID)
            .setHistory(List.of()).setStream(isStream);
    }

    public static void releaseResource(AtomicReference<EventSource> realEventSourceAtomicRef) {
        if (realEventSourceAtomicRef!=null && realEventSourceAtomicRef.get() != null) {
            realEventSourceAtomicRef.get().cancel();
        }
    }

    @Deprecated
    public Outline buildOutlineTemplate(List<PromptTemplateParam> outlineTemplateParam,String reportType) {
        JSONObject outlineMap =
            extractParam(outlineTemplateParam, OUTLINE_TEMPLATE_MAP, "outlineTemplateMapp缺失").getMap();
        String outlineJsonStr = (String)Assert.notEmpty(outlineMap, "outlineMap为空").get(reportType);
        return Outline.fromMap(MarkdownUtils.parseMarkdownToMap(outlineJsonStr));
    }

    public static PromptTemplateParam extractParam(List<PromptTemplateParam> outlineTemplateParam, String paramName,
        String msg) {
        return outlineTemplateParam.stream()
            .filter(promptTemplateParam -> paramName.equals(promptTemplateParam.getName())).findAny()
            .orElseThrow(() -> new BusinessExecutionException(msg));
    }

    @NotNull
    public static List<KbInfo> getKbInfos(List<String> kbIds, String question) {
        if (CollectionUtils.isEmpty(kbIds)) {
            return Collections.emptyList();
        }
        List<KnowledgeBaseDO> knowledgeBaseDOList = getValidKnowledgeBaseDOS(kbIds);
        List<KbInfo> list = buildKbInfos(knowledgeBaseDOList, question);
        log.debug("获取知识库信息:{}", JSON.toJSONString(list));
        return list;
    }
    @NotNull
    public static List<KbInfo> buildKbInfos(List<KnowledgeBaseDO> knowledgeBaseDOList, String question) {
        if (CollectionUtils.isEmpty(knowledgeBaseDOList)) {
            return Collections.emptyList();
        }
        // TODO：每个知识库用不同的模型
        ModelInfoDO modelInfoDO = ModelService.getDefaultModel(ModelTypeEnum.EMBEDDING);
        
        List<KbInfo> list = knowledgeBaseDOList.stream().map(
            knowledgeBaseDO -> new KbInfo().setKbID(knowledgeBaseDO.getId()).setQuestion(question)
                .setKbName(knowledgeBaseDO.getName())
                .setEnableParentIndex(ENABLE_PARENT_INDEX_DEFAULT)
                .setEmbeddingModel(modelInfoDO.getName())
                .setEmbeddingApiKey(modelInfoDO.getApiKey())
                .setEmbeddingUrl(modelInfoDO.getEndpointUrl())).toList();
        log.debug("获取知识库信息:{}", JSON.toJSONString(list));
        return list;
    }

    @NotNull
    public static List<KnowledgeBaseDO> getValidKnowledgeBaseDOS(List<String> kbIds) {
        if (CollUtil.isEmpty(kbIds)) {
            return List.of();
        }
        ResultDTO<List<KnowledgeBaseDO>> listResultDTO = FeignUtil.getKnowledgeBaseApi().conditions(null, null, KnowledgeBaseDOParams.builder().ids(kbIds).build());
        List<KnowledgeBaseDO> knowledgeBaseDOList = FeignUtil.getListDataNonEmpty(listResultDTO, "知识库不存在");
        // 检查是否有被禁用的知识库
        List<KnowledgeBaseDO> disableList = knowledgeBaseDOList.stream()
            .filter(knowledgeBaseDO -> KbEnableEnum.DISABLE.equals(knowledgeBaseDO.getEnable())).toList();
        if (CollectionUtils.isNotEmpty(disableList)) {
            String kbNames = disableList.stream().map(KnowledgeBaseDO::getName).collect(Collectors.joining(","));
            throw new BusinessExecutionException("知识库已被禁用:" + kbNames);
        }
        return knowledgeBaseDOList;
    }

    public static boolean isNoChunks(List<SearchChunksRep.KbResult> searchChunks) {
        if (CollectionUtils.isEmpty(searchChunks)) {
            return true;
        }
        for (SearchChunksRep.KbResult searchChunk : searchChunks) {
            if (CollectionUtils.isNotEmpty(searchChunk.getSparseChunks())) {
                return false;
            }
            if (CollectionUtils.isNotEmpty(searchChunk.getDenseChunks())) {
                return false;
            }
        }
        return true;
    }

    public static void handleReasoningStreamSink(ChatParam param, FluxSink<ServerSentEvent<String>> sink, boolean needAnswer,
        String reasoningContent, Boolean stream) {
        if (StringUtils.isNotEmpty(reasoningContent)) {
            if (stream == null || stream) {
                if (needAnswer) {
                    ChatHelper.doSinkNext(
                        ChatRes.buildStreamReasonContent(reasoningContent, System.currentTimeMillis(), param.getQuestion()),
                        sink, STREAM_REASONING);
                }
            }
        }
    }

    /**
     * 走 后续 自己的 thought
     * 处理带有思考过程的输出，只输出思考的结果
     * @param instructDO 
     * @param handleAnswerContentDTO
     */
    public static void handleAnswerContent(InstructDO instructDO, HandleAnswerContentDTO handleAnswerContentDTO) {
        if (checkIsContainThought(instructDO)){
            ChatHelper.handleOnEventWithThought(handleAnswerContentDTO);
        }else {
            ChatHelper.handleOnEvent(handleAnswerContentDTO);
        }
    }
}
