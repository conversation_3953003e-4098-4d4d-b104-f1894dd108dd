package com.polarizon.rag.plugin.common.exception;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 业务执行异常类
 * @create 2023-06-25 20:31
 */
public class MyJsonParseException extends RuntimeException implements Serializable {

    @Serial
    private static final long serialVersionUID = 4776260176587620631L;

    public MyJsonParseException(String message) {
        super(message);
    }

    public MyJsonParseException(String message, Throwable cause) {
        super(message, cause);
    }
}
