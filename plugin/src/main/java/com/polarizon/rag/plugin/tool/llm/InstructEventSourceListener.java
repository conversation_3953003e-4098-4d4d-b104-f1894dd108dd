package com.polarizon.rag.plugin.tool.llm;

import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_STYLE;
import static com.polarizon.rag.plugin.bean.constant.Constants.MAX_EVENT_LOG_NUM;
import static com.polarizon.rag.plugin.bean.constant.Constants.STREAM_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.X_TRACE_ID_HEADER_NAME;

import java.util.concurrent.CompletableFuture;

import com.polarizon.rag.plugin.util.FluxUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSONObject;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.service.instruct.InstructChatContext;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import reactor.core.publisher.FluxSink;

/**
 * 这个方法不依赖isReasoning，只处理标准情况
 * 不兼容没有<think>打头这种功能情况（推理过程只有</think>的方式）
 *
 * 处理方式：
 *      解析正常响应里面的是否<think>打头，是则走推理，并以</think>作为结束，否则走正常输出
 */
@Slf4j
public class InstructEventSourceListener extends EventSourceListener {
    private static final boolean ENABLE_LOG = false;
    // 事件回调参数
    private InstructChatContext instructChatContext;
    // 当前指令是否需要回答
    private boolean needAnswer;
    // 是否为最后的回答指令
    private boolean isComplete;
    // 当前的请求
    private CompletableFuture<String> future;
    // 日志跟踪表示
    private String traceId;
    // 推理处理器
    private final LlmDataHandler llmDataHandler;
    // 防止日志过多
    private Integer onEventNum = 0;
    // 流式输出
    private FluxSink<ServerSentEvent<String>> sink;

    public InstructEventSourceListener(InstructChatContext instructChatContext,
                                      boolean needAnswer,
                                      boolean isComplete,
                                      CompletableFuture<String> future,
                                      String traceId) {
        this.instructChatContext = instructChatContext;
        this.sink = instructChatContext.getSink();
        this.isComplete = isComplete;
        this.future = future;
        this.traceId = traceId;
        this.needAnswer = needAnswer;
        // 只用ReasoningHandler管理推理流相关状态
        this.llmDataHandler = new LlmDataHandler(instructChatContext, needAnswer);
        this.llmDataHandler.setHandlerCompleteAnswer(s -> handleAnswerContent(s));
    }

    @Override
    public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type,
                        @NotNull String data) {
        debugLog(id, type, data);
        // 使用推理处理器处理数据
        llmDataHandler.handleLlmData(data);
    }


    @Override
    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        ChatHelper.setTraceId(traceId);
        StopWatch stopWatch = instructChatContext.getStopWatch().getStopWatch();
        if (stopWatch != null && stopWatch.isRunning()) {
            stopWatch.stop();
            stopWatch.start("queryOutLlm");
        }
        log.debug("queryOutLlmSseOnOpen:response:{}", response);
    }

    @Override
    public void onClosed(@NotNull EventSource eventSource) {
        handleOnClosed(eventSource);
    }

    @Override
    public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t,
                          @Nullable Response response) {
        super.onFailure(eventSource, t, response);
        LlmHandleService.handleOnFailure(t, response, sink, future, isComplete);
    }


    private void debugLog(String id, String type, String data) {
        if (ENABLE_LOG && onEventNum < MAX_EVENT_LOG_NUM) {
            log.debug("llmOriginalOutput:,id:{},type:{},data{}:{}", id, type, onEventNum, data);
            onEventNum++;
        }
    }

    /**
     * 走 后续 自己的 thought
     * 处理带有思考过程的输出，只输出思考的结果
     *
     */
    private void handleAnswerContent(String data) {
        handleOnEvent(data);
    }

    private void handleOnEvent(String data) {
        // 处理流式响应
        handleStreamSink(instructChatContext.getChatParam().isStream(), needAnswer, data, instructChatContext.getChatParam().getQuestion());
    }

    private void handleStreamSink(boolean stream, boolean needAnswer,
                                        String answer, String question) {
        if (stream && needAnswer) {
            FluxUtil.doSinkNext(
                ChatRes.buildStreamAnswer(answer,  question), sink, STREAM_ANSWER);
        }
    }


    /**
     * 处理流式响应结束
     * @param eventSource
     */
    private void handleOnClosed(EventSource eventSource) {
        ChatHelper.setTraceId(eventSource);
        StopWatch stopWatch = instructChatContext.getStopWatch().getStopWatch();
        if (stopWatch != null && stopWatch.isRunning()) {
            stopWatch.stop();
        }
        log.debug("queryLlmSseOnClosed:{}", eventSource.request());

        try {
            // 如果是最后一条消息，则输出完整答案
            if (needAnswer && isComplete) {
                FluxUtil.doSinkNext(
                        ChatHelper.buildCompleteAnswer(instructChatContext.getChatParam(), instructChatContext.getCompleteAnswerSB().toString(),
                                 DEFAULT_STYLE, instructChatContext),
                        sink, COMPLETE_ANSWER, eventSource.request().header(X_TRACE_ID_HEADER_NAME));
            }
            // 打印日志
            tryLogOnClosed();

            // 如果最后一条消息，则结束流
            if (isComplete) {
                sink.complete();
            }

            // 结束当前的future
            if (future != null) {
                future.complete(instructChatContext.getCompleteAnswerSB().toString());
            }
        } catch (Exception e) {
            if (future != null) {
                future.completeExceptionally(e);
            } else {
                // TODO，这样才满足场景？ 是否时有future的时候，就不需要sink 错误 -> 链接终止
                //                sink.error(e);
            }

            sink.error(e);
        }
    }

    private void tryLogOnClosed() {
        JSONObject jsonObject = new JSONObject();
        if (instructChatContext.getCompleteAnswerSB() != null) {
            jsonObject.put("llmContent", instructChatContext.getCompleteAnswerSB().toString());
        }

        if (instructChatContext.getShowAllContentSB() != null) {
            jsonObject.put("showContent", instructChatContext.getShowAllContentSB().toString());
        }


        if (instructChatContext.getReasoningAnswerSB() != null) {
            jsonObject.put("reasoningAnswer", instructChatContext.getReasoningAnswerSB().toString());
        }
        // 当前任务的tokens消耗
        if (instructChatContext.getUsage() != null) {
            jsonObject.put("usage", instructChatContext.getUsage());
        }
        
        log.debug("llmInfo:\n{}", jsonObject.toJSONString());
    }


}
