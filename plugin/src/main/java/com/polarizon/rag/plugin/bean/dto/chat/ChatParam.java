package com.polarizon.rag.plugin.bean.dto.chat;

import cn.hutool.core.lang.Assert;
import com.polarizon.rag.chat.ChatModeConfig;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.plugin.bean.dto.aiagent.RagChatParam;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatParam {
    @NotBlank
    private String question;
    private boolean stream;
    private List<ChatHistory> history;
    private ChatModeConfig chatModeConfig;
    
    public  ChatParam cloneChatParam(Boolean stream){
            return new ChatParam()
                    .setQuestion(this.question)
                    .setStream(stream)
                    .setHistory(this.history)
                    .setChatModeConfig(this.chatModeConfig);
    }

    /**
     * 将RagChatParam转换为ChatParam
     * chatModeConfig 使用conversation 的chatModeConfig
     * @param ragChatParam 
     * @return
     */
    public static ChatParam fromRagChatParam(RagChatParam ragChatParam){
        ConversationConfig conversationConfig = ragChatParam.getConversationConfig();
        Assert.notNull(conversationConfig,()->new BusinessExecutionException("conversationConfig is empty"));
        return new ChatParam()
                .setQuestion(ragChatParam.getQuestion())
                .setStream(ragChatParam.isStream())
                .setHistory(ragChatParam.getHistory())
                .setChatModeConfig(conversationConfig.getChatModeConfig());
    }
}
