package com.polarizon.rag.plugin.config;

import com.alibaba.fastjson.JSON;
import com.polarizon.rag.ModelEnableEnum;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.ModelSourceTypeEnum;
import com.polarizon.rag.ModelTypeEnum;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.util.JSONUtils;
import com.polarizon.rag.plugin.util.LLMContentUtils;
import com.polarizon.rag.plugin.bean.constant.LlmProcessConstants;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.concurrent.DefaultThreadFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_NO_CHUNK_SYSTEM_PROMPT;
import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_NO_CHUNK_USER_PROMPT;
import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_SYSTEM_PROMPT;
import static com.polarizon.rag.plugin.bean.constant.Constants.DEFAULT_USER_PROMPT;
import static com.polarizon.rag.plugin.bean.constant.LlmProcessConstants.DEFAULT_THOUGHT_RESPONSE_REGEX;

/**
 * <AUTHOR>
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "chat")
@Slf4j
public class LlmConfig {
    private Process process;
    private RagPrompt ragPrompt;
    private Integer defaultMaxToken;
    private Integer maxConcurrentTasks;
    private Boolean useReranker;
    private Integer inspectMaxChunkSize;
    private Integer defaultRerankTopK;
    private Integer llmMaxRequests;
    private Integer llmThreadPoolCorePoolSize;
    private Integer commonThreadPoolCorePoolSize;
    private Answer answer;

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class RagPrompt {
        private String systemPrompt;
        private String userPrompt;
        private String noChunkSystemPrompt;
        private String noChunkUserPrompt;
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class Process {
        private List<Replacement> stringReplacements;
        private List<String> thoughtResponsePatternList;
        private List<String> endFlags;
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class ModelConfig {
        private String endpointUrl;
        private String apiKey;

        /**
         * 最大输出token数(过时的配置)
         */
        private Integer maxTokens;

        /**
         * 模型最大上下文长度
         */
        private Integer contextLength;

        /**
         * 模型最大输入长度
         */
        public Integer maxInputLength;

        /**
         * 最大输出token数
         */
        public Integer maxOutputTokens;
        private String name;
        private ModelSourceTypeEnum sourceType;
        private ModelTypeEnum modelType;
        private ModelEnableEnum enable;
        private Boolean isReasoning;

        public ModelInfoDO toModelInfo() {
            return ModelInfoDO.builder()
                .endpointUrl(endpointUrl)
                .name(name) 
                .apiKey(apiKey)
                .maxTokens(maxTokens)
                .contextLength(contextLength)
                .maxInputLength(maxInputLength)
                .maxOutputTokens(maxOutputTokens)
                .sourceType(sourceType)
                .type(modelType)
                .enable(enable)
                .isReasoning(isReasoning)
                .build();
        }
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class Replacement {
        private String from;
        private String to;
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class Answer {
        private boolean skipNoChunk;
        private boolean useReranker;
        private String noChunkPreInfo;
        private String noChunkPreInfoStr;
        private String defaultSystemPrompt;
        private String defaultUserPrompt;
        private String defaultNoChunkUserPrompt;
        private String defaultNoChunkSystemPrompt;
    }

    private ThreadPoolExecutor llmPoolExecutor;
    private ThreadPoolExecutor commonPoolExecutor;
    private ThreadPoolExecutor retrivalPoolExecutor;

    @PostConstruct
    public void init() {
        log.info("LlmConfig 原始配置信息： {}", JSON.toJSONString(this, true));
        llmPoolExecutor =
            new ThreadPoolExecutor(llmThreadPoolCorePoolSize, llmThreadPoolCorePoolSize * 2, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>( Math.max(llmThreadPoolCorePoolSize * 10, 1000)), new DefaultThreadFactory("llmPoolExecutor"), new ThreadPoolExecutor.AbortPolicy());
        retrivalPoolExecutor =
            new ThreadPoolExecutor(commonThreadPoolCorePoolSize, commonThreadPoolCorePoolSize * 2, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>( Math.max(commonThreadPoolCorePoolSize * 10, 1000)), new DefaultThreadFactory("retrivalPoolExecutor"), new ThreadPoolExecutor.AbortPolicy());

        commonPoolExecutor =
            new ThreadPoolExecutor(commonThreadPoolCorePoolSize, commonThreadPoolCorePoolSize * 2, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>( Math.max(commonThreadPoolCorePoolSize * 10, 1000)), new DefaultThreadFactory("commonPoolExecutor"), new ThreadPoolExecutor.AbortPolicy());
        if (defaultMaxToken != null) {
            ChatHelper.DEFAULT_MAX_TOKEN = defaultMaxToken;
        }
        ChatHelper.inspectMaxChunkSize = inspectMaxChunkSize;

        ChatHelper.defaultRerankTopK = defaultRerankTopK != null ? defaultRerankTopK : 4;

        initRagPrompt();

        initProcess();

    }

    private void initRagPrompt() {
        if (ragPrompt == null) {
            ragPrompt = new RagPrompt();
            ragPrompt.setSystemPrompt(DEFAULT_SYSTEM_PROMPT);
            ragPrompt.setUserPrompt(DEFAULT_USER_PROMPT);
            ragPrompt.setNoChunkSystemPrompt(DEFAULT_NO_CHUNK_SYSTEM_PROMPT);
            ragPrompt.setNoChunkUserPrompt(DEFAULT_NO_CHUNK_USER_PROMPT);
        }
        ChatHelper.defaultSystemPrompt =
            StringUtils.isEmpty(ragPrompt.getSystemPrompt()) ? DEFAULT_SYSTEM_PROMPT : ragPrompt.getSystemPrompt();
        ChatHelper.defaultUserPrompt =
            StringUtils.isEmpty(ragPrompt.getUserPrompt()) ? DEFAULT_USER_PROMPT : ragPrompt.getUserPrompt();
        ChatHelper.defaultNoChunkSystemPrompt =
            StringUtils.isEmpty(ragPrompt.getNoChunkSystemPrompt()) ? DEFAULT_NO_CHUNK_SYSTEM_PROMPT
                : ragPrompt.getNoChunkSystemPrompt();
        ChatHelper.defaultNoChunkUserPrompt =
            StringUtils.isEmpty(ragPrompt.getNoChunkUserPrompt()) ? DEFAULT_NO_CHUNK_USER_PROMPT
                : ragPrompt.getNoChunkUserPrompt();
        // TODO 通过配置初始化
        //        ChatHelper.noChunkPreInfo = 
        //            StringUtils.isEmpty(answer.getNoChunkPreInfo())? "" : answer.getNoChunkPreInfo();
    }

    private void initProcess() {
        if (process == null) {
            process = new Process();
        }
        List<Replacement> stringReplacements = process.getStringReplacements();
        if (CollectionUtils.isNotEmpty(stringReplacements)) {
            JSONUtils.stringReplacements = stringReplacements;
            log.info("init config-stringReplacements from config: \n{}",
                JSON.toJSONString(JSONUtils.stringReplacements, true));
        } else {
            JSONUtils.stringReplacements = LlmProcessConstants.defaultStringReplacements;
            log.info("init defaultStringReplacements from default: \n{}",
                JSON.toJSONString(JSONUtils.stringReplacements, true));
        }

        List<String> thoughtResponsePatternList = process.getThoughtResponsePatternList();
        if (CollectionUtils.isNotEmpty(thoughtResponsePatternList)) {
            thoughtResponsePatternList.forEach(
                regex -> LLMContentUtils.THOUGHT_RESPONSE_PATTERN_LIST.add(Pattern.compile(regex, Pattern.DOTALL)));
            log.info("init thoughtResponsePatternList from config: \n{}",
                JSON.toJSONString(LLMContentUtils.THOUGHT_RESPONSE_PATTERN_LIST, true));
        } else {
            DEFAULT_THOUGHT_RESPONSE_REGEX.forEach(
                regex -> LLMContentUtils.THOUGHT_RESPONSE_PATTERN_LIST.add(Pattern.compile(regex, Pattern.DOTALL)));
            log.info("init thoughtResponsePatternList from default: \n{}", DEFAULT_THOUGHT_RESPONSE_REGEX);
        }

        List<String> endFlags = process.getEndFlags();
        if (CollectionUtils.isNotEmpty(endFlags)) {
            ChatHelper.endFlags = endFlags;
            log.info("init endFlags from config: \n{}", JSON.toJSONString(endFlags, true));
        } else {
            ChatHelper.endFlags = LlmProcessConstants.END_FLAG_LIST;
            log.info("init endFlags from default: \n{}", JSON.toJSONString(ChatHelper.endFlags, true));
        }

        ChatHelper.llmOutputCheckWindowsSize =
            ChatHelper.endFlags.stream().max(Comparator.comparingInt(String::length)).orElse("").length();
        log.info("init llmOutputCheckWindowsSize: \n{}", ChatHelper.llmOutputCheckWindowsSize);
    }
}
