package com.polarizon.rag.plugin.faas;

import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.tool.llm.CustomOpenAiClient;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.core5.concurrent.DefaultThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/monitor")
@Slf4j
public class CommonRestController {

    private ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1, new DefaultThreadFactory("monitorExecutor"));
    private boolean monitorEnable = false;
    
    @Autowired
    private LlmConfig llmConfig;
    @Autowired
    private CustomOpenAiClient customOpenAiClient;

    @PostConstruct
    public void init() {
        // 每隔 5 秒监控一次线程池状态        
        setPeriod(5);
    }

    private void monitorThreadPoolStatus() {
        log.info("ExecutorPoolStatus:\nllm: {}\ncmn: {}\nrtrvlT:{}\nconnPool: {}\ndispatcher: {}",
            getThreadPoolStatus(llmConfig.getLlmPoolExecutor()),
            getThreadPoolStatus(llmConfig.getCommonPoolExecutor()),
            getThreadPoolStatus(llmConfig.getRetrivalPoolExecutor()),
            getConnectionPoolStatus(),
            getDispatcherStatus()
        );
    }

    private String getThreadPoolStatus(ThreadPoolExecutor executor) {
        return String.format(
            "Active t: %d, Completed tsk: %d, Total tsk: %d, Queue s: %d, Core s: %d, Max s: %d",
            executor.getActiveCount(),
            executor.getCompletedTaskCount(),
            executor.getTaskCount(),
            executor.getQueue().size(),
            executor.getCorePoolSize(),
            executor.getMaximumPoolSize()
        );
    }

    private String getConnectionPoolStatus() {
        ConnectionPool connectionPool = customOpenAiClient.getConnectionPool();
            return String.format(
                "Idle connections: %d, Total connections: %d",
                connectionPool.idleConnectionCount(),
                connectionPool.connectionCount()
            );
    }

    private String getDispatcherStatus() {
        Dispatcher dispatcher = customOpenAiClient.getDispatcher();
        return String.format(
            "Running requests: %d, Queued requests: %d, Max requests: %d, Max requests per host: %d",
            dispatcher.runningCallsCount(),
            dispatcher.queuedCallsCount(),
            dispatcher.getMaxRequests(),
            dispatcher.getMaxRequestsPerHost()
        );
    }

    @PreDestroy
    public void destroy() {
        scheduler.shutdown();
    }

    @GetMapping("")
    public String toggleMonitoring(@RequestParam boolean enable, @RequestParam Integer period) {
        monitorEnable = enable;
        if (enable && period != null && period > 0) {
            setPeriod(period);            
        }
        return "Monitoring toggled - monitorEnable: " + enable;
    }

    private void setPeriod(int period) {
        scheduler.shutdownNow();
        scheduler = Executors.newScheduledThreadPool(1, new DefaultThreadFactory("monitorExecutor-"+period));
        scheduler.scheduleAtFixedRate(() -> {
            if (monitorEnable) {
                // 打印 llmPoolExecutor 状态
                monitorThreadPoolStatus();
            }
        }, 0, period, TimeUnit.SECONDS);
    }
}