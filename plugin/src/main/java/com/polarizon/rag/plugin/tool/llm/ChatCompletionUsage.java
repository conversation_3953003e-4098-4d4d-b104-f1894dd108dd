package com.polarizon.rag.plugin.tool.llm;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Data
@SuperBuilder
@Slf4j
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class ChatCompletionUsage extends ChatCompletion {
    @JsonProperty("stream_options")
    private StreamOptions streamOptions;
    
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class StreamOptions {
        /**
         * include_usage boolean
         * <a href="https://platform.openai.com/docs/api-reference/completions/create#completions-create-stream_options">输出模型usage</a>
         *
         * Optional
         * If set, an additional chunk will be streamed before the data: [DONE] message. 
         * The usage field on this chunk shows the token usage statistics for the entire request, and the choices field will always be an empty array.
         *
         * All other chunks will also include a usage field, but with a null value. 
         * NOTE: If the stream is interrupted, you may not receive the final usage chunk which contains the total token usage for the request.
         */
        @JsonProperty("include_usage")
        private Boolean includeUsage;
    }
    
}
