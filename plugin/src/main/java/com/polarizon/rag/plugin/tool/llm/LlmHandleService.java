package com.polarizon.rag.plugin.tool.llm;


import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;

import java.util.List;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;

import com.polarizon.rag.instruct.InstructDO;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.instruct.InstructRetryResult;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.exception.EmptyException;
import com.polarizon.rag.plugin.service.BaseChatContext;
import com.polarizon.rag.plugin.service.InstructHelper;
import com.polarizon.rag.plugin.service.instruct.InstructChatContext;
import com.polarizon.rag.plugin.util.JSONUtils;
import io.github.resilience4j.retry.annotation.Retry;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.plugin.exception.InappropriateContentException;
import com.polarizon.rag.plugin.exception.SystemBusyExecutionException;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.Message;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import reactor.core.publisher.FluxSink;

@Service
@Slf4j
public class LlmHandleService {
    @Autowired
    private CustomOpenAiClient customOpenAiClient;

    /**
     * 外部大模型调用失败
     * @param t 异常
     * @param response 响应
     * @param sink 流式输出sink
     * @param future 异步结果
     * @param isComplete 是否完成
     */
    public static void handleOnFailure(Throwable t, Response response, FluxSink<ServerSentEvent<String>> sink,
                                       CompletableFuture<String> future, Boolean isComplete) {
        String errorMsg = t == null ? "" : t.getMessage();
        String responseCode = response == null ? "" : String.valueOf(response.code());
        String responseMsg = response == null ? "" : response.message();
        String responseBodyErrorMsg = extractResponseError(response);
        String message =
            "外部大模型调用失败:" + errorMsg + "[responseCode]:" + responseCode + "; [responseMsg]:" + responseMsg
                + "; [errorMsg]:" + responseBodyErrorMsg;

        Exception ex = t == null ? new SystemBusyExecutionException(message) : new SystemBusyExecutionException(message, t);

        if (StringUtils.isNotBlank(responseBodyErrorMsg) && responseBodyErrorMsg.contains(InappropriateContentException.INAPPROPRIATE_CONTENT)) {
            ex = t == null ? new InappropriateContentException(message) : new InappropriateContentException(message, t);
        }

        // 有 future，说明是中间过程,交给上层处理
        if (future != null) {
            future.completeExceptionally(ex);
        }
        if (isComplete != null && isComplete) {
            sink.error(ex);
        }
    }





    private static String extractResponseError(Response response) {
        String responseBodyErrorMsg = "";
        try {
            String responseBody = "";
            if (response != null) {
                responseBody = response.body() != null ? response.body().string() : "No response body";
                if (StringUtils.isNotEmpty(responseBody)) {
                    JSONObject json = null;
                    try {
                        json = JSON.parseObject(responseBody);
                        responseBodyErrorMsg = json.getJSONObject("error").getString("message");
                    } catch (Exception e) {
                        responseBodyErrorMsg = responseBody;
                    }
                }
            }
            //            log.error("Error response body: " + responseBody);
        } catch (Exception e) {
            log.error("Failed to read response body: " + e.getMessage());
        }
        return responseBodyErrorMsg;
    }



    @NotNull
    private static String getBufferedStrings(CircularFifoQueue<String> lastTokens) {
        StringBuilder outputTokenSB = new StringBuilder();
        for (String lastToken : lastTokens) {
            outputTokenSB.append(lastToken == null ? "" : lastToken);
        }
        return outputTokenSB.toString();
    }

    /**
     * 外部大模型调用
     */
    public EventSource queryLlmSse(FluxSink<ServerSentEvent<String>> sink, BaseChatContext chatContext, boolean noChunks) {

        // 配置yml文件获取相关模型信息，访问连接、apikey等。
        ModelInfoDO model = chatContext.getGenerateParam().getModelInfoDO();
        log.debug("queryLlmSse-generateParam:{}", JSONUtils.getJacksonJsonString(chatContext.getGenerateParam()));
        String traceId = MDC.get(TRACE_ID);
        // 创建 OkHttpClient，加入外部API请求需要的配置
        OpenAiStreamClient client = customOpenAiClient.getOpenAiStreamClient(model);
        EventSourceListener eventSourceListener =
            new LLmCommonEventSourceListener(chatContext, sink, noChunks);

        List<Message> messages = ChatHelper.buildRagMessages(chatContext.getGenerateParam());
        ChatCompletion chatCompletion = ChatHelper.buildChatCompletion(chatContext.getGenerateParam(), messages, null);

        log.debug("chatCompletion:\n{}", JSONUtils.getJacksonJsonString(chatCompletion));
        // 开始请求，返回EventSource
        return customOpenAiClient.streamChatCompletion(chatCompletion, eventSourceListener, traceId, client);
    }


    @Retry(name = "instructGenerate", fallbackMethod = "fallbackForInstructGenerate")
    public <T> T handleInstructGenerateWithRetry(String jobName,
                                                 InstructChatContext instructChatContext,
                                                 boolean needAnswer,
                                                 boolean isComplete,
                                                 CompletableFuture<String> cf,
                                                 Function<String, T> parser,
                                                 Function<T, Boolean> emptyCheckFunction) {
        InstructRetryResult<T> instructRetryResult = new InstructRetryResult<>();
        String errorMsg = jobName + "出错";
        T result;
        try {
            ChatHelper.setTraceId(MDC.get(TRACE_ID));
            EventSource eventSource = handleLlmCall(instructChatContext, needAnswer, isComplete, cf);
            instructChatContext.getEventSourceMap().put(jobName, eventSource);
            instructRetryResult.setEventSource(eventSource);
            String resultJson = cf.get();
            T respDTO = parser.apply(resultJson);
            if (emptyCheckFunction != null && emptyCheckFunction.apply(respDTO)) {
                throw new EmptyException((respDTO != null ? respDTO.getClass().getName() : "null") + " 结果为空");
            }
            result = respDTO;
        } catch (InterruptedException e) {
            throw new CancellationException("任务被打断，msg:"+e.getMessage());
        } catch (ExecutionException e) {
            Throwable newException = e;
            Throwable cause = e.getCause();
            if (cause != null && cause.getCause() != null) {
                newException = cause.getCause();
            }
            throw new BusinessExecutionException(errorMsg + " ExecutionException: " + newException.getMessage(), newException);
        }
        return result;
    }


    /**
     * 执行指令
     *
     * @param instructChatContext 上下文信息
     * @param needAnswer 是否需要回答
     * @param isComplete 是否完成
     * @param future 异步结果
     * @return 事件源
     */
    public EventSource handleLlmCall(InstructChatContext instructChatContext,
                                     boolean needAnswer,
                                     boolean isComplete,
                                     CompletableFuture<String> future) {
        InstructDO instructDO = instructChatContext.getInstructDO();
        GenerateParam generateParam = instructChatContext.getGenerateParam();
        ModelInfoDO modelInfoDO = generateParam.getModelInfoDO();
        log.debug("queryLlmSse-generateParam:{}", JSONUtils.getJacksonJsonString(generateParam.ofMaskedNewOne()));

        // 根据模型配置获取OpenAi流式客户端
        OpenAiStreamClient client = customOpenAiClient.getOpenAiStreamClient(modelInfoDO);
        String traceId = MDC.get(TRACE_ID);

        // 创建EventSource监听器，完成sink推送消息等逻辑
        EventSourceListener eventSourceListener =
            new InstructEventSourceListener(instructChatContext, needAnswer, isComplete,future, traceId);

        // TODO 处理 rewriter
        // TODO 增加 tmpKbIDList
        // 构建chatCompletion请求对象
        List<Message> messages = InstructHelper.buildInstructChatMessage(instructDO.getPromptTemplate(),
            instructChatContext.getInstructParam().getPromptTemplate().getDisplayParams(), instructDO.getCode(),
            generateParam.getSources(), instructDO.getOtherConfig().getReferenceKey(), generateParam.getHistory());
        // 返回格式
        Boolean respJsonFormat = instructDO.getOtherConfig().getRespJsonFormat();
        ChatCompletion chatCompletion = ChatHelper.buildChatCompletion(generateParam, messages, respJsonFormat);
        log.debug("instructName:{}, instructCode:{}, chatCompletion:\n{}", instructDO.getName(), instructDO.getCode()
            , JSONUtils.getJacksonJsonString(chatCompletion));
        // 开始请求，返回EventSource
        return customOpenAiClient.streamChatCompletion(chatCompletion, eventSourceListener, traceId, client);
    }

    /**
     * 此方法有用到！不能删！
     * handleInstructGenerateWithRetry 的 fallback 方法：注意参数顺序和类型需匹配原始方法 + Throwable
     */
    public  <T> InstructRetryResult<T>  fallbackForInstructGenerate(String jobName,
                                                                    InstructChatContext instructChatContext,
                                                                    boolean needAnswer,
                                                                    boolean isComplete,
                                                                    CompletableFuture<String> cf,
                                                                    Function<String, T> parser,
                                                                    Function<T, Boolean> emptyCheckFunction, RuntimeException e){
        log.debug("大模型生成失败, jobName:{}, msg:{}", jobName, e.getMessage());
        throw e;
    }
}
