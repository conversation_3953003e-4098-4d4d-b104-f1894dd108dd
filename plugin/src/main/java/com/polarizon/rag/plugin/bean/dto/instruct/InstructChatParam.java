package com.polarizon.rag.plugin.bean.dto.instruct;

import com.polarizon.rag.instruct.ParamTypeEnum;
import com.polarizon.rag.instruct.PromptTemplateParam;
import com.polarizon.rag.plugin.bean.dto.aiagent.AgentChatParam;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class InstructChatParam {
    @NotNull
    private InstructParam instructParam;
    private Boolean stream;


    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class InstructParam {
        @NotBlank // TODO 生效？
        public String code;
        private PromptTemplateVO promptTemplate;
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class PromptTemplateVO {
        private List<PromptTemplateParam> displayParams;

        public static PromptTemplateVO fromAiAgentChatParam(List<AgentChatParam.Parameter> agentChatParamParameters) {
            if (CollectionUtils.isEmpty(agentChatParamParameters)) {
                return new PromptTemplateVO();
            }
            List<PromptTemplateParam> promptTemplateParams = new ArrayList<>(agentChatParamParameters.size());
            for (AgentChatParam.Parameter agentChatParamParameter : agentChatParamParameters) {
                String type = agentChatParamParameter.getType();
                PromptTemplateParam promptTemplateParam =
                    PromptTemplateParam.builder()
                        .name(agentChatParamParameter.getName())
                        .value(agentChatParamParameter.getValue())
                        .key(agentChatParamParameter.getKey())
                        .maxLength(agentChatParamParameter.getMaxLength())
                        .options(agentChatParamParameter.getOptions())
                        .map(agentChatParamParameter.getMap())
                        .isSelectedText(agentChatParamParameter.getIsSelectedText())
                        .arrayValue(agentChatParamParameter.getArrayValue())
                        .description(agentChatParamParameter.getDescription())
                        .build();
                if (StringUtils.isNotBlank(type)) {
                    promptTemplateParam.setType(ParamTypeEnum.valueOf(type));
                }
                promptTemplateParams.add(promptTemplateParam);
            }
            PromptTemplateVO promptTemplateVO = new PromptTemplateVO();
            promptTemplateVO.setDisplayParams(promptTemplateParams);
            return promptTemplateVO;
        }
    }
}
