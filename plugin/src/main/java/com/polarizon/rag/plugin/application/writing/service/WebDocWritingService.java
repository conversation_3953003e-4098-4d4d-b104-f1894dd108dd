package com.polarizon.rag.plugin.application.writing.service;

import static com.polarizon.rag.plugin.common.configs.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.common.service.chat.ChatHelper.handleOnFailure;
import static com.polarizon.rag.plugin.common.util.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.common.util.Constants.ERROR_ANSWER;
import static com.polarizon.rag.plugin.common.util.Constants.EXTRA_CONTENT;
import static com.polarizon.rag.plugin.common.util.Constants.MESSAGE_CHANGE;
import static com.polarizon.rag.plugin.common.util.Constants.MESSAGE_TYPE_ERROR;
import static com.polarizon.rag.plugin.common.util.Constants.X_ACCOUNT_HEADER_NAME;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.Nullable;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.util.SpringBeanUtil;
import com.polarizon.gendo.common.util.SpringEnvUtil;
import com.polarizon.rag.aiapp.webdoc.WebDocDO;
import com.polarizon.rag.aiapp.webdoc.WebdocReportTypeEnum;
import com.polarizon.rag.aiapp.webdoc.feign.WebDocDOFeign;
import com.polarizon.rag.chat.AnswerModeEnum;
import com.polarizon.rag.chat.ConversationConfig;
import com.polarizon.rag.chat.ConversationDO;
import com.polarizon.rag.chat.ExtraContentBO;
import com.polarizon.rag.chat.KBSearchConfig;
import com.polarizon.rag.chat.MessageDO;
import com.polarizon.rag.chat.MessageRoleEnum;
import com.polarizon.rag.chat.RetrievalConfig;
import com.polarizon.rag.chat.SourceBO;
import com.polarizon.rag.chat.WebSearchConfig;
import com.polarizon.rag.instruct.ParamTypeEnum;
import com.polarizon.rag.instruct.PromptTemplateParam;
import com.polarizon.rag.kb.KnowledgeFileChunkDO;
import com.polarizon.rag.kb.KnowledgeFileDO;
import com.polarizon.rag.kb.SplitStatusEnum;
import com.polarizon.rag.kb.feign.KnowledgeFileChunkDOFeign;
import com.polarizon.rag.kb.feign.KnowledgeFileDOFeign;
import com.polarizon.rag.kb.params.KnowledgeFileChunkDOParams;
import com.polarizon.rag.kb.params.KnowledgeFileDOParams;
import com.polarizon.rag.plugin.application.writing.bean.ReportParam;
import com.polarizon.rag.plugin.application.writing.config.WebdocParamConfig;
import com.polarizon.rag.plugin.common.bean.dto.AgentChatNoStreamReq;
import com.polarizon.rag.plugin.common.bean.dto.AgentChatStreamReq;
import com.polarizon.rag.plugin.common.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.common.client.AiAgentFeign;
import com.polarizon.rag.plugin.common.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.common.service.ConversationConfigService;
import com.polarizon.rag.plugin.common.service.RetrievalConfigService;
import com.polarizon.rag.plugin.common.service.chat.ChatHelper;
import com.polarizon.rag.plugin.common.service.chat.ChatSourceService;
import com.polarizon.rag.plugin.common.util.Constants;
import com.polarizon.rag.plugin.common.util.FeignUtil;
import com.unfbx.chatgpt.exception.BaseException;
import com.unfbx.chatgpt.exception.CommonError;

import cn.hutool.http.ContentType;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
public class WebDocWritingService {
    @Autowired
    private WebdocParamConfig webdocParamConfig;
    @Autowired
    private AiAgentFeign aiAgentFeign;
    private static final String MESSAGE_TYPE_REPORT = "REPORT";
    private static final String MESSAGE_TYPE_OUTLINE = "OUTLINE";
    private static final String CODE_FREE_WRITE = "freewrite:free_response";
    private static final String CODE_TECH_REPORT_BY_OUTLINE = "report:tech_report_by_outline";
    private static final String CODE_IND_REPORT_BY_OUTLINE = "report:ind_report_by_outline";
    private static final String CODE_REPORT_TITLE = "report:title";
    private static final String AGENT_CODE_FREE_WRITE = "freewrite:free_response";
    private static final String AGENT_CODE_REPORT_BY_OUTLINE = "report:report_by_outline";
    private static final String OUTLINE_TYPE_TECH = WebdocReportTypeEnum.TECHNOLOGY.getName();
    private static final String OUTLINE_TYPE_IND = WebdocReportTypeEnum.INDUSTRY.getName();

    @NotNull
    public Flux<ServerSentEvent<String>> reportStream(String webdocId, String userAccount, ReportParam param, ConversationDO conversationDO) {
        // 获取知识库列表
        List<String> kbIdList = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getKbSearchConfig).map(KBSearchConfig::getKbIDList).filter(CollectionUtils::isNotEmpty)
                .orElse(Lists.newArrayList());
        String displayContent = checkGetDisplayContent(param.getCode(), param.getParameters(), webdocParamConfig.getReport());
        String topic = param.getParameters().stream().filter(p -> Objects.equals(p.getName(), "topic")).findFirst().map(PromptTemplateParam::getValue).orElse(null);
        // 保存问题
        MessageDO saveQuestionMessage = ChatHelper.saveQuestionMessage(userAccount, displayContent, conversationDO, kbIdList);

        // 修改映射的请求的agentCode
        String reqAgentCode = null;
        String outlineType = null;
        switch (param.getCode()) {
            case CODE_TECH_REPORT_BY_OUTLINE:
                reqAgentCode = AGENT_CODE_REPORT_BY_OUTLINE;
                outlineType = OUTLINE_TYPE_TECH;
                break;
            case CODE_IND_REPORT_BY_OUTLINE:
                reqAgentCode = AGENT_CODE_REPORT_BY_OUTLINE;
                outlineType = OUTLINE_TYPE_IND;
                break;
            case CODE_FREE_WRITE:
                reqAgentCode = AGENT_CODE_FREE_WRITE;
                break;
            default:
                break;
        }
        String finalOutlineType = outlineType;

        // 如果有模板，则保存模板
        String outline = null;
        MessageDO saveOutlineMessage = null;
        if (Objects.equals(param.getCode(), CODE_TECH_REPORT_BY_OUTLINE) || Objects.equals(param.getCode(), CODE_IND_REPORT_BY_OUTLINE)) {
            // 获取outline
            outline = param.getParameters().stream().filter(p -> Objects.equals(p.getName(), "outline")).findFirst().map(PromptTemplateParam::getValue).orElse(null);
            if (!StringUtils.hasText(outline)) {
                throw new BusinessExecutionException("大纲不能为空");
            }
            saveOutlineMessage = saveOutline(userAccount, outline, conversationDO, outlineType);
        }
        final MessageDO finalSaveOutlineMessage = saveOutlineMessage;

        // 生成问题和模板，方便前端及时渲染
        String traceId = MDC.get(TRACE_ID);
        ChatRes chatRes = new ChatRes();
        if (Objects.nonNull(finalSaveOutlineMessage)) {
            chatRes.setMessages(Lists.newArrayList(saveQuestionMessage, finalSaveOutlineMessage));
        } else {
            chatRes.setMessages(Lists.newArrayList(saveQuestionMessage));
        }

        // 创建一个新的Flux来处理后续操作
        String finalReqAgentCode = reqAgentCode;
        return Flux.create(sink -> {
            // 立即发送初始消息
            ChatHelper.doSinkNext(chatRes, sink, MESSAGE_CHANGE, traceId);

            // 创建一个新的Flux来处理后续操作
            Flux<ServerSentEvent<String>> processingFlux = Flux.<ServerSentEvent<String>>defer(() -> {
                MDC.put(TRACE_ID, traceId);

                // 获取临时知识库列表
                List<String> temkbIdList = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getKbSearchConfig)
                        .map(KBSearchConfig::getTmpKbIDList).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList());

                // 知识库列表为空、非联网检索、必须有参考依据
                boolean isNotWebSearch = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getWebSearchConfig)
                        .map(WebSearchConfig::getIsSearchWeb).map(BooleanUtils::isFalse).orElse(true);
                boolean mustContainsReference = Optional.ofNullable(conversationDO.getConversationConfig()).map(ConversationConfig::getAnswerMode)
                        .map(AnswerModeEnum.MUST_CONTAINS_REFERENCE::equals).orElse(true);
                boolean existingContentReference = CollectionUtils.isEmpty(kbIdList) && isNotWebSearch && mustContainsReference;

                // 只要临时知识库列表不为空，就检查文件解析状态
                if (CollectionUtils.isNotEmpty(temkbIdList)) {
                    KnowledgeFileDOParams kbFileParams = KnowledgeFileDOParams.builder().knowledgeBaseID(temkbIdList).build();

                    // 检查文件解析状态，最多等待3分钟
                    long startTime = System.currentTimeMillis();
                    long timeout = 3 * 60 * 1000; // 3分钟超时

                    while (true) {
                        try {
                            List<KnowledgeFileDO> files = FeignUtil.getKnowledgeFileApi().conditions(null, null, kbFileParams).getData();

                            // 检查是否所有文件都解析失败并且没有其他参考
                            boolean allFilesFailed = CollectionUtils.isNotEmpty(files) && files.stream().allMatch(item -> item.getStatus().equals(SplitStatusEnum.FAILED));
                            if (allFilesFailed && existingContentReference) {
                                return handleReportFileParseFailure(userAccount, param, conversationDO, kbIdList);
                            }

                            // 检查是否所有文件都处理完成
                            boolean allFilesProcessed = files.stream().noneMatch(item -> item.getStatus().equals(SplitStatusEnum.INIT) || item.getStatus().equals(SplitStatusEnum.RUNNING));
                            if (allFilesProcessed) {
                                break;
                            }

                            // 检查是否超时
                            if (System.currentTimeMillis() - startTime > timeout) {
                                return handleReportTimeout(userAccount, param, conversationDO, kbIdList);
                            }

                            // 等待5秒后重试
                            Thread.sleep(5000);
                        } catch (Exception e) {
                            log.error("Failed to check knowledge base file status: {}", e.getMessage(), e);
                            return handleReportTimeout(userAccount, param, conversationDO, kbIdList);
                        }
                    }
                }

                // 获取对话配置、检索配置、用户提问
                ConversationConfig conversationConfig = ConversationConfigService.checkConversationConfig(conversationDO);
                RetrievalConfig retrievalConfig =
                        Objects.isNull(conversationDO.getRetrievalConfig()) ? RetrievalConfigService.defaultRetrievalConfig(kbIdList, null, conversationDO) : conversationDO.getRetrievalConfig();

                // 请求参数配置
                AgentChatStreamReq agentChatStreamReq = AgentChatStreamReq.builder()
                        .conversationConfig(conversationConfig)
                        .retrievalConfig(retrievalConfig).parameters(param.getParameters())
                        .stream(param.getStream()).history(Lists.newArrayList()).agentCode(finalReqAgentCode).build();

                // 获取大模型结果
                final AtomicReference<ChatRes> chatResAtomicReference = new AtomicReference<>();
                final AtomicReference<EventSource> realEventSourceAtomicRef = new AtomicReference<>();
                final AtomicReference<CompletableFuture<String>> titleAtomicReference = new AtomicReference<>();
                titleAtomicReference.set(new CompletableFuture<>());

                // 调用大模型，创建标题
                return Flux.<ServerSentEvent<String>>create(innerSink -> {
                            EventSource realEventSource = reportAgent(param.getCode(), webdocId, userAccount, displayContent, agentChatStreamReq, innerSink, chatResAtomicReference, titleAtomicReference, traceId, kbIdList, conversationDO, topic);

                            // 如果是技术报告，或者产业报告，可以异步生成标题， 自由写作则需要在大模型回答完毕之后再生成标题
                            // if (Objects.equals(param.getCode(), CODE_TECH_REPORT_BY_OUTLINE) || Objects.equals(param.getCode(), CODE_IND_REPORT_BY_OUTLINE)) {
                            //     setInstructTitle(userAccount, topic, finalOutlineType, param, conversationConfig, retrievalConfig, innerSink, titleAtomicReference, traceId);
                            // }

                            realEventSourceAtomicRef.set(realEventSource);
                        }).doOnComplete(() -> {
                            MDC.put(TRACE_ID, traceId);
                            log.debug("sseDoOnComplete");
                        })
                        .doOnCancel(() -> {
                            MDC.put(TRACE_ID, traceId);
                            log.debug("ssefDoOnCancel");
                            ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                        })
                        .onErrorResume(e -> {
                            MDC.put(TRACE_ID, traceId);
                            log.warn("onErrorResume:{}", e.getMessage(), e);
                            ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                            return ChatHelper.buildErrorRespData(e);
                        })
                        .doOnTerminate(() -> {
                            MDC.put(TRACE_ID, traceId);
                            log.debug("ssefDoOnTerminate");
                            ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                        });
            });

            // 订阅处理Flux并将结果发送到sink
            processingFlux.subscribeOn(Schedulers.boundedElastic()).subscribe(
                    sink::next,
                    sink::error,
                    sink::complete
            );
        });
    }

    /**
     * 处理报告文件解析失败情况
     */
    private Flux<ServerSentEvent<String>> handleReportFileParseFailure(String userAccount, ReportParam param,
                                                                       ConversationDO conversationDO, List<String> kbIdList) {
        String displayContent = checkGetDisplayContent(param.getCode(), param.getParameters(), webdocParamConfig.getReport());

        // 保存问题消息
        MessageDO messageDO = ChatHelper.saveQuestionMessage(userAccount, displayContent, conversationDO, kbIdList);

        // 构建解析失败响应
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageDO.getId());
        chatRes.setQuestion(displayContent);
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setResponse(Constants.FILE_PARSE_FAILURE_ANSWER);
        chatRes.setSource(Lists.newArrayList());
        chatRes.setStyle(Constants.MESSAGE_TYPE_ERROR);

        // 保存答案消息
        ChatHelper.saveAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), chatRes.getResponse(), null);

        // 返回SSE消息
        return Flux.just(ServerSentEvent.<String>builder()
                .event(Constants.COMPLETE_ANSWER)
                .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", chatRes, MDC.get(TRACE_ID))))
                .build());
    }

    /**
     * 处理报告超时情况
     */
    private Flux<ServerSentEvent<String>> handleReportTimeout(String userAccount, ReportParam param,
                                                              ConversationDO conversationDO, List<String> kbIdList) {
        String displayContent = checkGetDisplayContent(param.getCode(), param.getParameters(), webdocParamConfig.getReport());

        // 保存问题消息
        MessageDO messageDO = ChatHelper.saveQuestionMessage(userAccount, displayContent, conversationDO, kbIdList);

        // 构建超时响应
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageDO.getId());
        chatRes.setQuestion(displayContent);
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setResponse("文件解析超时，请稍后重试");
        chatRes.setSource(Lists.newArrayList());
        chatRes.setStyle(Constants.MESSAGE_TYPE_ERROR);

        // 保存答案消息
        ChatHelper.saveAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), chatRes.getResponse(), null);

        // 返回SSE消息
        return Flux.just(ServerSentEvent.<String>builder()
                .event(Constants.ERROR_ANSWER)
                .data(JSON.toJSONString(NewResultDTO.error(ResultDTO.Constants.FAILURE, "文件解析超时，请稍后重试", chatRes, MDC.get(TRACE_ID))))
                .build());
    }

    /**
     * 对接生成报告的agent
     */
    private static EventSource reportAgent(String code, String webdocId, String userAccount, String displayContent, AgentChatStreamReq agentChatStreamReq, FluxSink<ServerSentEvent<String>> sink,
                                           AtomicReference<ChatRes> chatResAtomicReference, AtomicReference<CompletableFuture<String>> titleAtomicReference, String traceId, List<String> kbIdList, ConversationDO conversationDO, String topic) {
        KnowledgeFileDOFeign knowledgeFileApi = FeignUtil.getKnowledgeFileApi();
        KnowledgeFileChunkDOFeign knowledgeFileChunkApi = FeignUtil.getKnowledgeFileChunkApi();

        EventSourceListener eventSourceListener = new EventSourceListener() {

            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type,
                                @NotNull String data) {
                MDC.put(TRACE_ID, traceId);
                super.onEvent(eventSource, id, type, data);

                // 内容处理
                NewResultDTO<ChatRes> chatResDTO = JSONObject.parseObject(data, new TypeReference<>() {
                });
                ChatRes chatRes = chatResDTO.getData();
                chatResDTO.setRequestId(traceId);

                // 响应过程
                if (!Objects.equals(type, ERROR_ANSWER) && !Objects.equals(type, COMPLETE_ANSWER)) {
                    ChatHelper.doSinkNext(chatRes, sink, type, traceId);
                    return;
                }

                // 异常处理
                if (Objects.equals(type, ERROR_ANSWER)) {
                    reportErrorAnswer(conversationDO, kbIdList, userAccount, type, traceId, displayContent, chatResDTO.getMsg(), sink);
                    return;
                }

                // 正常完成
                if (Objects.equals(type, COMPLETE_ANSWER)) {
                    // 如果是自由写作还需要再把标题总结出来
                    if (Objects.equals(code, CODE_FREE_WRITE)) {
                        setFreeWriteTitle(webdocId, chatRes.getQuestion(), chatRes.getResponse(), conversationDO.getConversationConfig(), conversationDO.getRetrievalConfig(), userAccount, traceId, sink, titleAtomicReference);
                    } else if (Objects.equals(code, CODE_TECH_REPORT_BY_OUTLINE) || Objects.equals(code, CODE_IND_REPORT_BY_OUTLINE)) {
                        setFreeWriteTitle(webdocId, topic,  chatRes.getResponse(), conversationDO.getConversationConfig(), conversationDO.getRetrievalConfig(), userAccount, traceId, sink, titleAtomicReference);
                    }

                    reportCompleteAnswer(conversationDO, kbIdList, userAccount, type, chatRes, traceId, titleAtomicReference, sink, knowledgeFileApi, knowledgeFileChunkApi);

                    saveReportSourceContent(webdocId, userAccount, chatRes.getResponse());
                    return;
                }
            }

            @Override
            public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                MDC.put(TRACE_ID, traceId);
                super.onOpen(eventSource, response);
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                MDC.put(TRACE_ID, traceId);
                super.onClosed(eventSource);
                sink.complete();
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t,
                                  @Nullable Response response) {
                MDC.put(TRACE_ID, traceId);
                super.onFailure(eventSource, t, response);
                handleOnFailure(t, response, sink, null, true);
            }
        };

        // 开始请求，返回EventSource
        return streamChatCompletion(userAccount, agentChatStreamReq, eventSourceListener);
    }

    public void updateWebdocIsTmp(String webdocId, String updateBy, boolean isTmp) {
        WebDocDO webDocDO = new WebDocDO();
        webDocDO.setId(webdocId);
        webDocDO.setIsTmp(isTmp);
        SpringBeanUtil.getBean(WebDocDOFeign.class).update(null, null, updateBy, webDocDO);
    }


    /**
     * 报告正常回答
     *
     * @param conversationDO        会话
     * @param kbIdList              知识库ID列表
     * @param userAccount           用户账号
     * @param type                  事件类型
     * @param chatRes               事件数据
     * @param traceId               请求ID
     * @param titleAtomicReference  标题
     * @param sink                  流式响应
     * @param knowledgeFileApi      文件API
     * @param knowledgeFileChunkApi 知识库chunk API
     */
    private static void reportCompleteAnswer(ConversationDO conversationDO, List<String> kbIdList, String userAccount, String type, ChatRes chatRes, String traceId, AtomicReference<CompletableFuture<String>> titleAtomicReference, FluxSink<ServerSentEvent<String>> sink, KnowledgeFileDOFeign knowledgeFileApi, KnowledgeFileChunkDOFeign knowledgeFileChunkApi) {
        try {
            // 获取文件ID、chunkID
            List<String> fileIds = CollectionUtils.emptyIfNull(chatRes.getSource())
                    .stream()
                    .map(ChatRes.SourceBOResp::getFileID)
                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                    .distinct()
                    .toList();

            List<String> chunkIds = CollectionUtils.emptyIfNull(chatRes.getSource())
                    .stream()
                    .map(ChatRes.SourceBOResp::getKbChunkID)
                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                    .distinct()
                    .toList();

            // 并行获取文件和chunk数据
            Map<String, KnowledgeFileDO> knowledgeFileDOMap = new HashMap<>();
            Map<String, KnowledgeFileChunkDO> knowledgeFileChunkDOMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(fileIds)) {
                knowledgeFileApi.conditions(null, null, KnowledgeFileDOParams.builder().ids(fileIds).build()).getData()
                        .forEach(knowledgeFileDO -> knowledgeFileDOMap.put(knowledgeFileDO.getId(), knowledgeFileDO));
            }

            if (CollectionUtils.isNotEmpty(chunkIds)) {
                knowledgeFileChunkApi.conditions(null, null, KnowledgeFileChunkDOParams.builder().chunkID(chunkIds).build()).getData()
                        .forEach(knowledgeFileChunkDO -> knowledgeFileChunkDOMap.put(knowledgeFileChunkDO.getChunkID(), knowledgeFileChunkDO));
            }

            // 设置文件、chunk信息
            for (ChatRes.SourceBOResp sourceBOResp : CollectionUtils.emptyIfNull(chatRes.getSource())) {
                String fileID = sourceBOResp.getFileID();
                String chunkID = sourceBOResp.getKbChunkID();

                sourceBOResp.setKnowledgeFileBO(knowledgeFileDOMap.get(fileID));
                sourceBOResp.setKnowledgeFileChunkBO(knowledgeFileChunkDOMap.get(chunkID));
            }

            // 获取 style
            String style = Objects.equals(Constants.MESSAGE_TYPE_NO_CHUNKS_ANSWER, chatRes.getStyle()) ? chatRes.getStyle() : MESSAGE_TYPE_REPORT;

            // 设置结果
            ExtraContentBO extraContentBO = new ExtraContentBO();
            extraContentBO.setContent(titleAtomicReference.get().get());
            extraContentBO.setType("title");
            extraContentBO.setName("title");
            chatRes.setExtraContent(Lists.newArrayList(extraContentBO));
            chatRes.setStyle(style);

            // log.info("chatRes before:{}", JSON.toJSONString(chatRes));
            // 重写溯源
            ChatSourceService.rewriteChatResSource(chatRes, userAccount);
            // log.info("chatRes after:{}", JSON.toJSONString(chatRes));

            // 保存消息
            String messageId = saveReportMessage(conversationDO, kbIdList, style, userAccount, chatRes.getSource(), chatRes.getResponse(), chatRes.getReasoningContent(), "", chatRes.getExtraContent());

            // 设置ChatRes
            chatRes.setId(messageId);

            // 推送消息
            ChatHelper.doSinkNext(chatRes, sink, type, traceId);
            sink.complete();
        } catch (Exception e) {
            log.error("Error processing event data: {}", e.getMessage(), e);
            handleOnFailure(e, null, sink, null, true);
        }
    }

    /**
     * 报告异常回答
     *
     * @param conversationDO 会话
     * @param kbIdList       知识库ID列表
     * @param userAccount    用户账号
     * @param type           事件类型
     * @param traceId        请求ID
     * @param question       问题
     * @param response       回答
     * @param sink           流式响应
     */
    private static void reportErrorAnswer(ConversationDO conversationDO, List<String> kbIdList, String userAccount, String type, String traceId, String question, String response, FluxSink<ServerSentEvent<String>> sink) {
        // 保存消息
        String messageId = saveReportMessage(conversationDO, kbIdList, MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), response, null, "", null);

        // 设置ChatRes
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageId);
        chatRes.setQuestion(question);
        chatRes.setResponse(response);
        chatRes.setStyle(MESSAGE_TYPE_ERROR);

        // 推送消息
        ChatHelper.doErrorSinkNext(chatRes, response, sink, traceId);
        sink.complete();
    }



    private MessageDO saveOutline(String userAccount, String outline, ConversationDO conversationDO, String outlineType) {
        // 保存大纲
        MessageDO messageDO =
                ChatHelper.buildMessageDO(conversationDO, null, outline, MessageRoleEnum.robot, null,
                        MESSAGE_TYPE_OUTLINE, Lists.newArrayList(buildOutLineTypeExtraContentBO(outlineType)), null);
        return FeignUtil.getMessageApi().add(null, null, userAccount, messageDO).getData();
    }

    /**
     * 保存报告源markdown 内容
     */
    private static void saveReportSourceContent(String webdocId, String updateBy, String sourceContent) {
        WebDocDO webDocDO = new WebDocDO();
        webDocDO.setId(webdocId);
        webDocDO.setSourceContent(sourceContent);
        SpringBeanUtil.getBean(WebDocDOFeign.class).update(null, null, updateBy, webDocDO);
    }

    /**
     * 设置自由写作标题
     */
    private static void setFreeWriteTitle(String webdocId, String defaultTitle, String content, ConversationConfig conversationConfig, RetrievalConfig retrievalConfig, String userAccount, String traceId, FluxSink<ServerSentEvent<String>> sink, AtomicReference<CompletableFuture<String>> titleAtomicReference) {
        String title = defaultTitle;
        try {
            List<PromptTemplateParam> parameters = Lists.newArrayList();
            parameters.add(PromptTemplateParam.builder().name("text").type(ParamTypeEnum.Input).value(content).build());
            AgentChatNoStreamReq agentChatNoStreamReq = AgentChatNoStreamReq.builder()
                    .conversationConfig(conversationConfig)
                    .retrievalConfig(retrievalConfig).parameters(parameters)
                    .history(Lists.newArrayList()).agentCode(CODE_REPORT_TITLE).build();
            log.info("设置标题请求：{}", JSON.toJSONString(agentChatNoStreamReq));
            String chatResStr = SpringBeanUtil.getBean(AiAgentFeign.class).agentChat(userAccount, traceId, agentChatNoStreamReq);
            NewResultDTO<ChatRes> chatResDTO = JSONObject.parseObject(chatResStr, new TypeReference<>() {
            });
            title = chatResDTO.getData().getResponse();
            title = title.trim();
        } catch (Exception e) {
            log.error("通过大模型设置标题失败：{}, 不过没关系，会切换成默认标题", e.getMessage());
        }

        CompletableFuture<String> titleFuture = titleAtomicReference.get();

        // 尝试设置文档的标题
        tryUpdateName(webdocId, title, userAccount);

        // 前端report 的流式 接口返回
        log.info("设置标题：{}", title);
        titleFuture.complete(title);
        ChatRes chatRes = new ChatRes();
        chatRes.setExtraContent(Lists.newArrayList(buildTitleExtraContentBO(title)));
        chatRes.setStyle(MESSAGE_TYPE_REPORT);
        ChatHelper.doSinkNext(chatRes, sink, EXTRA_CONTENT, traceId);

    }

    /**
     * 设置报告标题 文档名称自动生成，规则： 技术报告和产业报告指令为"主题+文体类型"，名称最多50个字符，主题过长需要进行提炼至总共50个字符以内。 自由写作指令，就是生成全文内容的标题或提炼为标题，总共50个字符以内
     */
    private void setInstructTitle(String userAccount, String topic, String outlineType, ReportParam param, ConversationConfig conversationConfig, RetrievalConfig retrievalConfig,
                                  FluxSink<ServerSentEvent<String>> sink, AtomicReference<CompletableFuture<String>> titleAtomicReference, String traceId) {
        String outlineTypeCN = outlineType.equals(OUTLINE_TYPE_TECH) ? "技术报告" : "产业报告";
        // 判断 topic 是否大于46个字符，小于46个字符就用topic + outlineTypeCN
        String title = topic + outlineTypeCN;
        if (StringUtils.hasText(title) && title.length() > 50) {
            try {
                List<PromptTemplateParam> parameters = Lists.newArrayList();
                parameters.add(PromptTemplateParam.builder().name("text").type(ParamTypeEnum.Input).value(topic).build());
                AgentChatNoStreamReq agentChatNoStreamReq = AgentChatNoStreamReq.builder()
                        .conversationConfig(conversationConfig)
                        .retrievalConfig(retrievalConfig).parameters(parameters)
                        .history(Lists.newArrayList()).agentCode(CODE_REPORT_TITLE).build();
                log.info("设置标题请求：{}", JSON.toJSONString(agentChatNoStreamReq));
                String chatResStr = aiAgentFeign.agentChat(userAccount, traceId, agentChatNoStreamReq);
                NewResultDTO<ChatRes> chatResDTO = JSONObject.parseObject(chatResStr, new TypeReference<>() {
                });
                title = chatResDTO.getData().getResponse();
                title = title.trim();
            } catch (Exception e) {
                log.error("通过大模型设置标题失败：{}, 不过没关系，会切换成默认标题", e.getMessage());
            }
        }
        CompletableFuture<String> titleFuture = titleAtomicReference.get();

        // 尝试设置文档的标题
        tryUpdateName(param.getWebdocId(), title, userAccount);
        
        // 前端report 的流式 接口返回
        log.info("设置标题：{}", title);
        titleFuture.complete(title);
        ChatRes chatRes = new ChatRes();
        chatRes.setExtraContent(Lists.newArrayList(buildTitleExtraContentBO(title)));
        chatRes.setStyle(MESSAGE_TYPE_REPORT);
        ChatHelper.doSinkNext(chatRes, sink, EXTRA_CONTENT, traceId);

    }

    /**
     * 设置报告标题 文档名称自动生成，规则： 技术报告和产业报告指令为"主题+文体类型"，名称最多50个字符，主题过长需要进行提炼至总共50个字符以内。 自由写作指令，就是生成全文内容的标题或提炼为标题，总共50个字符以内
     */

    public static String saveReportMessage(ConversationDO conversationDO, List<String> kbIds, String messageStyle, String userAccount, List<ChatRes.SourceBOResp> source, String content,
                                           String reasoningContent, String outline, List<ExtraContentBO> extraContentBOs) {
        List<SourceBO> sourceBOList = ChatHelper.convertSourceList(source);

        MessageDO messageDO = ChatHelper.buildMessageDO(conversationDO, kbIds, content, MessageRoleEnum.robot, sourceBOList, messageStyle, extraContentBOs, reasoningContent);
        MessageDO data = FeignUtil.getMessageApi().add(null, null, userAccount, messageDO).getData();

        //log.debug("saveAnswerMessage:{}", JSON.toJSONString(messageDO));
        return data.getId();
    }

    private static void tryUpdateName(String webdocId, String title, String userAccount) {
        // 查询文档
        WebDocDO webDocDO = SpringBeanUtil.getBean(WebDocDOFeign.class).findOneByID(null, null, webdocId).getData();
        if (Objects.isNull(webDocDO)) {
            log.warn("文档不存在：{}", webdocId);
            return;
        }
        if (StringUtils.hasText(webDocDO.getName()) && !Objects.equals(webDocDO.getName(), "新的文档")) {
            log.debug("文档名称已存在：{}", webDocDO.getName());
            return;
        }

        // 更新文档名称
        WebDocDO updateWebDocDO = new WebDocDO();
        updateWebDocDO.setId(webDocDO.getId());
        updateWebDocDO.setName(title);
        SpringBeanUtil.getBean(WebDocDOFeign.class).update(null, null, userAccount, updateWebDocDO);
    }

    private String checkGetDisplayContent(String code, List<PromptTemplateParam> parameters, List<WebdocParamConfig.Param> params) {
        WebdocParamConfig.Param config = params.stream().filter(p -> Objects.equals(p.getCode(), code)).findFirst().orElse(null);
        if (Objects.isNull(config)) {
            throw new BusinessExecutionException("参数异常：code不存在");
        }
        String displayContent = config.getDisplayContent();
        List<PromptTemplateParam> displayParams = config.getDisplayParams();
        if (CollectionUtils.isNotEmpty(displayParams)) {
            for (PromptTemplateParam displayParam : displayParams) {
                String name = displayParam.getName();
                String value = parameters.stream().filter(p -> Objects.equals(p.getName(), name)).findFirst().map(PromptTemplateParam::getValue).orElse(null);
                if (Objects.isNull(value)) {
                    throw new BusinessExecutionException("参数异常：" + name + "不存在");
                }
                displayContent = displayContent.replace("{{" + name + "}}", value);
            }
        }
        return displayContent;
    }


    private static ExtraContentBO buildTitleExtraContentBO(String title) {
        ExtraContentBO extraContentBO = new ExtraContentBO();
        extraContentBO.setContent(title);
        extraContentBO.setType("title");
        extraContentBO.setName("title");
        return extraContentBO;
    }

    private ExtraContentBO buildOutLineTypeExtraContentBO(String outlineType) {
        ExtraContentBO extraContentBO = new ExtraContentBO();
        extraContentBO.setContent(outlineType);
        extraContentBO.setType("outlineType");
        extraContentBO.setName("outlineType");
        return extraContentBO;
    }

    private static OkHttpClient longTimeoutOkHttpClient() {
        return (new OkHttpClient.Builder()).connectTimeout(10L, TimeUnit.SECONDS).writeTimeout(3000L, TimeUnit.SECONDS).readTimeout(3000L, TimeUnit.SECONDS).build();
    }

    public static <T> EventSource streamChatCompletion(String userAccount, T chatCompletion, EventSourceListener eventSourceListener) {
        String aiAgentUrl = SpringEnvUtil.getEnv("com.polarizon.feign.ai-agent-api-url", "http://ai-agent:20502");
        if (Objects.isNull(eventSourceListener)) {
            log.error("参数异常：EventSourceListener不能为空，可以参考：com.unfbx.chatgpt.sse.ConsoleEventSourceListener");
            throw new BaseException(CommonError.PARAM_ERROR);
        }

        String traceId = MDC.get(TRACE_ID);
        try {
            EventSource.Factory factory = EventSources.createFactory(longTimeoutOkHttpClient());
            ObjectMapper mapper = new ObjectMapper();
            String requestBody = mapper.writeValueAsString(chatCompletion);
            Request request = new Request.Builder()
                    .url(aiAgentUrl + "/ai-agent/v1/agentChat")
                    .addHeader(X_ACCOUNT_HEADER_NAME, userAccount)
                    .addHeader("Accept", "*/*")
                    .addHeader("X-Request-Id", traceId)
                    .post(RequestBody.create(MediaType.parse(ContentType.JSON.getValue()), requestBody))
                    .build();
            // 创建事件
            return factory.newEventSource(request, eventSourceListener);
        } catch (Exception e) {
            throw new BusinessExecutionException("请求参数解析异常：" + e.getMessage(), e);
        }
    }

}