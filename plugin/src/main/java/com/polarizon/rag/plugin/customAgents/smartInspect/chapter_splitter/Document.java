// package com.polarizon.rag.plugin.customAgents.smartInspect.chapter_splitter;

// import lombok.AllArgsConstructor;
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.experimental.Accessors;

// import java.util.Map;

// /**
//  * 文档类，用于存储文档内容和元数据 对应Python中的langchain_core.documents.Document类
//  */
// @Data
// @Accessors(chain = true)
// @NoArgsConstructor
// @AllArgsConstructor
// public class Document {
//     private String pageContent;
//     private Map<String, Object> metadata;

//     private ChapterMetadata chapterMetadata;

//     public Document(String text, Map<String, Object> newMetadata) {
//         this.pageContent = text;
//         this.metadata = newMetadata;
//         ChapterMetadata chapterMetadata = new ChapterMetadata();
//         chapterMetadata.setChapterName(newMetadata.get("chapterName") != null ? newMetadata.get("chapterName").toString() : "");
//         chapterMetadata.setChapterID(newMetadata.get("chapterID") != null ? Integer.parseInt(newMetadata.get("chapterID").toString()) : -1);
//         chapterMetadata.setParentChapterName(newMetadata.get("parentChapterName") != null ? newMetadata.get("parentChapterName").toString() : "");
//         chapterMetadata.setParentChapterID(newMetadata.get("parentChapterID") != null ? Integer.parseInt(newMetadata.get("parentChapterID").toString()) : 1);
//         chapterMetadata.setFileName(newMetadata.get("fileName") != null ? newMetadata.get("fileName").toString() : "");
//         this.chapterMetadata = chapterMetadata;
//     }

//     /**
//      * 章节元数据实体类
//      */
//     @Data
//     @Accessors(chain = true)
//     @NoArgsConstructor
//     @AllArgsConstructor
//     public static class ChapterMetadata {
//         private String chapterName;
//         private int chapterID;
//         private String parentChapterName;
//         private Integer parentChapterID;
//         private String fileName;
//     }


//     @Override
//     public String toString(){
//         return this.pageContent;
//     }
// }