// package com.polarizon.rag.plugin.customAgents.smartInspect;

// import cn.hutool.core.collection.CollUtil;
// import cn.hutool.core.lang.Tuple;
// import cn.hutool.core.util.StrUtil;
// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
// import com.polarizon.rag.chat.ConversationConfig;
// import com.polarizon.rag.chat.ConversationDO;
// import com.polarizon.rag.instruct.InstructDO;
// import com.polarizon.rag.instruct.PromptTemplateParam;
// import com.polarizon.rag.kb.KnowledgeBaseDO;
// import com.polarizon.rag.plugin.bean.dto.chat.ChatParam;
// import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
// import com.polarizon.rag.plugin.bean.dto.chat.EventCallbackParam;
// import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
// import com.polarizon.rag.plugin.bean.dto.chat.JsonExtraContentChatRes;
// import com.polarizon.rag.plugin.bean.dto.instruct.Document;
// import com.polarizon.rag.plugin.bean.dto.instruct.InspectionRespDTO;
// import com.polarizon.rag.plugin.bean.dto.instruct.InstructChatParam;
// import com.polarizon.rag.plugin.config.LlmConfig;
// import com.polarizon.rag.plugin.customAgents.smartInspect.chapter_splitter.ListHandler;
// import com.polarizon.rag.plugin.customAgents.writeReport.ThinkTanksReportConstants;
// import com.polarizon.rag.plugin.exception.BusinessExecutionException;
// import com.polarizon.rag.plugin.exception.SmartInspectException;
// import com.polarizon.rag.plugin.service.ChatHelper;
// import com.polarizon.rag.plugin.tool.RetrievalHandleService;
// import com.polarizon.rag.plugin.service.instruct.InstructChatService;
// import com.polarizon.rag.plugin.util.CommonUtils;
// import com.polarizon.rag.plugin.util.DocumentUtils;
// import com.polarizon.rag.plugin.util.StopWatchUtils;
// import lombok.extern.slf4j.Slf4j;
// import okhttp3.sse.EventSource;

// import org.apache.commons.compress.utils.Lists;
// import org.apache.commons.lang3.StringUtils;
// import org.jetbrains.annotations.NotNull;
// import org.slf4j.MDC;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.codec.ServerSentEvent;
// import org.springframework.stereotype.Service;
// import org.springframework.util.StopWatch;
// import reactor.core.publisher.FluxSink;

// import java.io.StringReader;
// import java.util.ArrayList;
// import java.util.HashSet;
// import java.util.LinkedList;
// import java.util.List;
// import java.util.Map;
// import java.util.Set;
// import java.util.concurrent.CompletableFuture;
// import java.util.concurrent.ConcurrentHashMap;
// import java.util.concurrent.atomic.AtomicReference;

// import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;
// import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
// import static com.polarizon.rag.plugin.bean.constant.Constants.EXTRA_CONTENT;
// import static com.polarizon.rag.plugin.bean.constant.Constants.PROCESS_STATUS;
// import static com.polarizon.rag.plugin.bean.constant.InstructConstants.SMART_INSPECTION_COMPLIANCE_INSPECTION_PARAGRAPH;

// @Slf4j
// @Service
// public class SmartInspectService {
//     public static final String INSPECTION_RULE_NAME = "审查文章中是否有不符合法律和规定的内容";

//     @Autowired
//     private LlmConfig llmConfig;
//     @Autowired
//     private InstructChatService instructChatService;
//     @Autowired
//     private RetrievalHandleService retrievalHandleService;

//     //    @Autowired
//     //    private InstructChatService instructChatService;
//     private EventSource smartInspection(ChatParam param, FluxSink<ServerSentEvent<String>> sink,
//                                         GenerateParam generateParam, List<ChatRes.SourceBOResp> source,
//                                         AtomicReference<StringBuffer> stringBufferAtomicReference, AtomicReference<StopWatchUtils> chatWatchRef,
//                                         SmartInspectEventCallbackParam eventCallbackParam, InstructChatParam instructChatParam, InstructDO smartInspectinstructDO,
//                                         List<String> chunks) {
//         // 发送大纲生成中的消息
//         ChatHelper.doSinkNext(
//             ChatRes.buildStreamAnswer("开始审查...", System.currentTimeMillis(), param.getQuestion()), sink,
//             PROCESS_STATUS);
//         log.debug("开始审查:{}", JSON.toJSONString(instructChatParam, true));
    
//         // TODO 参数传入
//         List<String> kbIDListParam = new ArrayList<>();
//         ConversationDO conversationDO = eventCallbackParam.getConversationDO();
//         List<KnowledgeBaseDO> validKnowledgeBaseDOS =
//             RetrievalHandleService.getValidKnowledgeBaseDOS(kbIDListParam);
//         boolean validKnowledgeBaseEmtpy = CollUtil.isEmpty(validKnowledgeBaseDOS);
//         // 处理没有有效的知识库
//         if (validKnowledgeBaseEmtpy) {
//             throw new BusinessExecutionException("该应用没有有效的知识库");
//         }
//         List<PromptTemplateParam> chatDisplayParams =
//             instructChatParam.getInstructParam().getPromptTemplate().getDisplayParams();
//         List<String> originalParagraphs =
//             ChatHelper.extractParam(chatDisplayParams, SmartInspectConstants.PARAGRAPHS, "paragraphs缺失").getArrayValue();
    
//         String traceID = MDC.get(TRACE_ID);
//         List<String> kbIDList = validKnowledgeBaseDOS.stream().map(KnowledgeBaseDO::getId).toList();
//         AtomicReference<EventSource> eventSourceAR = new AtomicReference<>();
//         Map<Integer, InspectionRespDTO.InspectionResult> inspectionResultIndexedMap = new ConcurrentHashMap<>();
//         Map<Integer, InspectionRespDTO.InspectionErrorResult> inspectionErrorResultIndexedMap = new ConcurrentHashMap<>();
    
    
//         // 提取之后，可能有多段被整合到同一个元素，后面需要拆分整合
//         // 这是外层的list，元素可能是聚合的多个段（line）
//         List<Document> documents = DocumentUtils.groupByChapterPython(originalParagraphs);
//         List<ListHandler.ElementMapping> elementMappings =
//             DocumentUtils.buildElementMapping(originalParagraphs, documents);
//         List<String> mergedParagraphs = documents.stream().map(Document::getPageContent).toList();
//         List<ListHandler.ConcatSegment> mergedSegments = ListHandler.buildConcatSegments(mergedParagraphs);
//         int mergedParagraphSize = mergedParagraphs.size();
//         List<CompletableFuture<Void>> taskList = new ArrayList<>(mergedParagraphSize);
//         int segmentSize = mergedSegments.size();
//         // 通过 instruct 的 code 获取
//         InstructDO instructDO =
//             ChatHelper.getInstructDOByCode(SMART_INSPECTION_COMPLIANCE_INSPECTION_PARAGRAPH);
//         for (int mergedParagraphIndex = 0; mergedParagraphIndex < segmentSize; mergedParagraphIndex++) {
//             //            ChatHelper.doSinkNext(ChatRes.buildStreamAnswer(
//             //                StrUtil.format("生成第{}章检索大纲的问题中...", chapterNumForHuman),
//             //                System.currentTimeMillis(), param.getQuestion()), sink, PROCESS_STATUS);
//             // 这是聚合过的 list 的元素，可能是聚合的多个段（line）
//             String mergedParagraphLines = mergedSegments.get(mergedParagraphIndex).getContent();
//             if (StringUtils.isBlank(mergedParagraphLines)) {
//                 continue;
//             }
    
//             ListHandler.ConcatSegment mergedSegment = mergedSegments.get(mergedParagraphIndex);
//             String paragraphIndexRangeForHuman = "";
//             // 注意：这里做了简单处理，其实可能原始段落可能是多个段落中存在空行，导致index不准确
//             int segmentOriginalStart =
//                 DocumentUtils.findElementMappingByNewIndex(elementMappings, mergedSegment.getStart(), mergedParagraphLines).getOriginalIndex();
//             int segmentOriginalEnd =
//                 DocumentUtils.findElementMappingByNewIndex(elementMappings, mergedSegment.getEnd(), mergedParagraphLines).getOriginalIndex();
//             ;
//             List<String> mergedSegmentLines = mergedSegment.getLines();
//             int mergedSegmentLineSize = mergedSegmentLines.size();
//             if (mergedSegmentLineSize > 1) {
//                 paragraphIndexRangeForHuman = StrUtil.format("[{}-{}]",
//                     segmentOriginalStart + 1, segmentOriginalEnd + 1);
//             } else {
//                 paragraphIndexRangeForHuman = StrUtil.format("[{}]", segmentOriginalStart + 1);
//             }
//             // TODO 多个审查规则的cf 管理
//             String finalParagraphIndexRangeForHuman = paragraphIndexRangeForHuman;
//             CompletableFuture<Void> complianceInspectCf =
//                 CompletableFuture.supplyAsync(() ->// 按句子检索法规文件
//                     {
//                         ChatHelper.setCustomTraceId(traceID, finalParagraphIndexRangeForHuman);
//                         ChatHelper.doSinkNext(ChatRes.buildStreamAnswer(
//                             StrUtil.format("检索第{}段规则文件中...", finalParagraphIndexRangeForHuman),
//                             System.currentTimeMillis(), param.getQuestion()), sink, PROCESS_STATUS);
//                         // 加分割的标识符，句号、问号，分号、冒号、感叹号
//                         String[] split = CommonUtils.splitSentence(mergedParagraphLines);
//                         // TODO 每个句子搜索并行
//                         List<ChatRes.SourceBOResp> allSourceBOResp = new ArrayList<>();
    
//                         JSONObject templateOtherConfig = instructDO.getPromptTemplate().getOtherConfig();
//                         CompletableFuture<Set<String>> setCompletableFuture =
//                             CompletableFuture.completedFuture(
//                                 // TODO 这里的 set 顺序？
//                                 new HashSet<>(
//                                     retrievalHandleService.handleMultiRetrieve(
//                                         eventCallbackParam.getConversationDO().getRetrievalConfig(),
//                                         List.of(split), 
//                                         finalParagraphIndexRangeForHuman,
//                                         templateOtherConfig.getFloat("scoreThreshold"),
//                                         templateOtherConfig.getFloat("sparseScoreThreshold"),
//                                         false, null, null
//                                     ).getAllSourcesStrList()));
//                         try {
//                             Set<String> chunkStrs = setCompletableFuture.get();
//                             return new Tuple(chunkStrs, allSourceBOResp);
//                         } catch (Exception e) {
//                             throw new BusinessExecutionException(e.getMessage(), e);
//                         }
//                     }, llmConfig.getLlmPoolExecutor())
//                     .thenAcceptAsync(tuple ->// 开始审查 ，可能有多个规则（指令）需要“跑“，当前只有一个"合规性"的规则
//                     {
//                         ChatHelper.setCustomTraceId(traceID, finalParagraphIndexRangeForHuman);
//                         ChatHelper.doSinkNext(ChatRes.buildStreamAnswer(
//                             StrUtil.format("第{}段检索规则文件结束，开始[{}]审查", finalParagraphIndexRangeForHuman, "合规性"),
//                             System.currentTimeMillis(), param.getQuestion()), sink, PROCESS_STATUS);
//                         List<ChatRes.SourceBOResp> sourceBORespList = tuple.get(1);
    
//                         // 下面是正确性审查的处理
    
//                         // mergedParagraphLines 字数过少、无chunk，都走大模型。
//                         PromptTemplateParam paragraphPromptTemplateParam =
//                             ChatHelper.buildPromptTemplateInputParam(SmartInspectConstants.PARAGRAPH, mergedParagraphLines);
//                         PromptTemplateParam contextPromptTemplateParam =
//                             ChatHelper.buildPromptTemplateInputParam(ThinkTanksReportConstants.CONTEXT, tuple.get(0).toString());
//                         InstructChatParam complianceInspectionChatParam = ChatHelper.buildInstructChatParam(
//                             List.of(paragraphPromptTemplateParam, contextPromptTemplateParam),
//                             SMART_INSPECTION_COMPLIANCE_INSPECTION_PARAGRAPH, instructChatParam.getConversationID(), false);
//                         //                    AtomicReference<StopWatchUtils> chatWatchRef1 = new AtomicReference<>();
//                         //                    StopWatchUtils newValue = new StopWatchUtils("chapterOutlineQuestion");
//                         //                    chatWatchRef1.set(newValue);
//                         // convert instruct param to  chatParam
//                         ChatParam chatParam =
//                             instructChatService.buildChatParam(complianceInspectionChatParam, instructDO.getOtherConfig(), conversationConfig.getChatModeConfig());
    
//                         final AtomicReference<List<ChatRes.SourceBOResp>> sourceBORespListRef = new AtomicReference<>();
//                         sourceBORespListRef.set(sourceBORespList);
//                         eventCallbackParam.setSourceBORespListRef(sourceBORespListRef);
//                         AtomicReference<StringBuffer> completeAnswerSBAR = new AtomicReference<>(new StringBuffer());
//                         eventCallbackParam.setCompleteAnswerSBAR(completeAnswerSBAR);
//                         String result = instructChatService.handleInstructGenerateWithRetry(chatParam, sink, generateParam, sourceBORespList,
//                             completeAnswerSBAR, chatWatchRef, eventCallbackParam, complianceInspectionChatParam,
//                             instructDO, chunks, false, false, new CompletableFuture<>(), String.class,
//                             StrUtil.format("审核第({})段出错", finalParagraphIndexRangeForHuman),
//                             //                    (resp -> resp == null || ObjectUtil.isEmpty(resp.getQuestions()))
//                             // TODO 解析放到里面去 实现解析错误重试
//                             (StringUtils::isEmpty))
//                             .getResult();
//                         InspectionRespDTO.InspectionLlmOutput inspectionLlmOutput =
//                             InspectionRespDTO.extractInspectionLlmOutput(result);
    
//                         // 存在needFix = false 的情况下，偶尔大模型出错，inspectedParagraph 的内容可能出现为空的错误情况，所以首先看needFix ,为false 则不额外处理
//                         boolean needFixAll = "需要修改".equals(inspectionLlmOutput.getJudgment());
//                         String inspectedParagraphAll = inspectionLlmOutput.getModifyResult();
//                         String summaryAll = inspectionLlmOutput.getAnalysis();
    
//                         if (!needFixAll) {
//                             for (int i = 0; i < mergedSegmentLineSize; i++) {
//                                 int newIndex = mergedSegment.getStart() + i;
//                                 ListHandler.ElementMapping elementMappingByNewIndex =
//                                     DocumentUtils.findElementMappingByNewIndex(elementMappings, newIndex, mergedParagraphLines);
//                                 String originalParagraph = mergedSegmentLines.get(i);
//                                 Integer originalParagraphIndex = elementMappingByNewIndex.getOriginalIndex();
//                                 String inspectedParagraph = originalParagraph;
//                                 InspectionRespDTO.InspectionResult complianceInspectionParagraph =
//                                     buildInspectResult(param, sink, eventCallbackParam, originalParagraphIndex,
//                                         needFixAll, inspectedParagraph, originalParagraph, sourceBORespList, INSPECTION_RULE_NAME,
//                                         summaryAll, finalParagraphIndexRangeForHuman);
//                                 inspectionResultIndexedMap.put(originalParagraphIndex, complianceInspectionParagraph);
//                             }
//                             return;
//                         }
    
//                         // 处理审查后的段落数量不一致，方便后面处理.把inspect后的结果，规整到mergedSegments，
//                         // 然后利用mapping信息再对应到原始的段落中
//                         List<String> mergedLines = mergedSegment.getLines();
//                         int mergedLinesSize = mergedLines.size();
//                         // 内部的行数，可能是一行或多行
//                         List<String> inspectedLines = DocumentUtils.readLines(new StringReader(inspectedParagraphAll));
//                         log.debug("准备组装审查结果：\ninspectedParagraphAll:{} ;\nmergeLines:{}", inspectedParagraphAll, mergedLines);
//                         int inspectedLineSize = inspectedLines.size();
//                         if (mergedLinesSize <= inspectedLineSize) {
//                             for (int i = 0; i < mergedLinesSize; i++) {
//                                 String inspectedParagraph = "";
//                                 int newIndex = mergedSegment.getStart() + i;
//                                 ListHandler.ElementMapping elementMappingByNewIndex =
//                                     DocumentUtils.findElementMappingByNewIndex(elementMappings, newIndex, mergedParagraphLines);
//                                 String originalParagraph =
//                                     elementMappingByNewIndex.getElement();
//                                 Integer originalParagraphIndex = elementMappingByNewIndex.getOriginalIndex();
//                                 Boolean needFix = needFixAll;
//                                 // 多出的lines，用换行拼接到最后一条
//                                 if (i == mergedLinesSize - 1) {
//                                     StringBuilder sb = new StringBuilder();
//                                     for (int j = i; j < inspectedLineSize; j++) {
//                                         sb.append(inspectedLines.get(j));
//                                     }
//     //                                     needFix = true;
//                                     inspectedParagraph = sb.toString();
//                                 } else {
//                                     inspectedParagraph = inspectedLines.get(i);
//                                 }
//                                 InspectionRespDTO.InspectionResult complianceInspectionParagraph =
//                                     buildInspectResult(param, sink, eventCallbackParam, originalParagraphIndex,
//                                         needFix, inspectedParagraph, originalParagraph, sourceBORespList, INSPECTION_RULE_NAME,
//                                         summaryAll, finalParagraphIndexRangeForHuman);
//                                 inspectionResultIndexedMap.put(originalParagraphIndex, complianceInspectionParagraph);
//                             }
//                         } else {
//                             // mergedLinesSize > inspectedLineSize
//                             for (int i = 0; i < inspectedLineSize; i++) {
//                                 int newIndex = mergedSegment.getStart() + i;
//                                 ListHandler.ElementMapping elementMappingByNewIndex =
//                                     DocumentUtils.findElementMappingByNewIndex(elementMappings, newIndex, mergedParagraphLines);
//                                 String originalParagraph =
//                                     elementMappingByNewIndex.getElement();
//                                 Integer originalParagraphIndex = elementMappingByNewIndex.getOriginalIndex();
//                                 String inspectedParagraph = inspectedLines.get(i);
//                                 InspectionRespDTO.InspectionResult complianceInspectionParagraph =
//                                     buildInspectResult(param, sink, eventCallbackParam, originalParagraphIndex,
//                                         needFixAll, inspectedParagraph, originalParagraph, sourceBORespList, INSPECTION_RULE_NAME,
//                                         summaryAll, finalParagraphIndexRangeForHuman);
//                                 inspectionResultIndexedMap.put(originalParagraphIndex, complianceInspectionParagraph);
//                             }
//                             // 少的，用"" 补齐
//                             for (int i = inspectedLineSize; i < mergedLinesSize; i++) {
//                                 int newIndex = mergedSegment.getStart() + i;
//                                 ListHandler.ElementMapping elementMappingByNewIndex =
//                                     DocumentUtils.findElementMappingByNewIndex(elementMappings, newIndex, mergedParagraphLines);
//                                 String originalParagraph =
//                                     elementMappingByNewIndex.getElement();
//                                 Integer originalParagraphIndex = elementMappingByNewIndex.getOriginalIndex();
//                                 String inspectedParagraph = "";
//                                 Boolean needFix = true;
//                                 InspectionRespDTO.InspectionResult complianceInspectionParagraph =
//                                     buildInspectResult(param, sink, eventCallbackParam, originalParagraphIndex,
//                                         needFix, inspectedParagraph, originalParagraph, sourceBORespList, INSPECTION_RULE_NAME,
//                                         summaryAll, finalParagraphIndexRangeForHuman);
//                                 inspectionResultIndexedMap.put(originalParagraphIndex, complianceInspectionParagraph);
//                             }
//                         }
    
    
//                     }).exceptionallyAsync(ex -> {
//                         // TODO traceId 为空，走不到这里
//                         ChatHelper.setCustomTraceId(traceID, finalParagraphIndexRangeForHuman);
//                         log.error("出现异常: {}", ex.getMessage(), ex);
    
//                         Throwable cause1 = ex.getCause();
//                         SmartInspectException smartInspectException = null;
//                         if (cause1 != null) {
//                             if (cause1 instanceof SmartInspectException) {
//                                 smartInspectException = (SmartInspectException) cause1;
//                             } else {
//                                 Throwable cause2 = cause1.getCause();
//                                 if (cause2 instanceof SmartInspectException) {
//                                     smartInspectException = (SmartInspectException) cause2;
//                                 }
//                             }
//                         }
    
//                         InspectionRespDTO.InspectionErrorResult inspectionErrorResult =
//                             new InspectionRespDTO.InspectionErrorResult();
//                         if (smartInspectException != null) {
//                             inspectionErrorResult = smartInspectException.getInspectionErrorResult();
//                             inspectionErrorResult
//                                 .setParagraphIndex(segmentOriginalStart)
//                                 .setStart(segmentOriginalStart)
//                                 .setEnd(segmentOriginalEnd)
//                                 .setOriginalParagraph(mergedParagraphLines);
//                         } else {
//                             inspectionErrorResult
//                                 .setErrorMsg(ex.getMessage())
//                                 .setErrorDisplayMsg("未知错误。以下内容无法审查：")
//                                 .setParagraphIndex(segmentOriginalStart)
//                                 .setStart(segmentOriginalStart)
//                                 .setEnd(segmentOriginalEnd)
//                                 .setOriginalParagraph(mergedParagraphLines)
//                                 .setErrorType(999)
//                                 .setHandleSuggestion("");
//                         }
//                         inspectionErrorResultIndexedMap.put(segmentOriginalStart, inspectionErrorResult);
//                         //                    chapterOutlineQuestionFuture.completeExceptionally(ex);
//                         return null;
//                     }, llmConfig.getCommonPoolExecutor());
    
//             taskList.add(complianceInspectCf);
//         }
//         CompletableFuture<Void> cf = CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0]));
//         cf.thenRun(() -> {
//             MDC.put(TRACE_ID, traceID);
//     //            log.debug("审查完成:\n{}", JSONUtils.getJacksonJsonString(inspectionResultIndexedMap, true));
//             // 生成审查结果
//             List<InspectionRespDTO.InspectionResult> inspectionResultList = new LinkedList<>();
//             for (int paragraphIndex = 0; paragraphIndex < originalParagraphs.size(); paragraphIndex++) {
//                 InspectionRespDTO.InspectionResult inspectionResult = inspectionResultIndexedMap.get(paragraphIndex);
//                 if (inspectionResult == null) {
//                     inspectionResult = new InspectionRespDTO.InspectionResult()
//                         .setNeedFix(false)
//                         .setOriginalParagraph("")
//                         .setInspectedParagraph("")
//                         .setParagraphIndex(paragraphIndex)
//                         .setRuleInfos(new ArrayList<>());
//                 }
//                 inspectionResultList.add(inspectionResult);
//             }
    
//             // 错误的结果
//             List<InspectionRespDTO.InspectionErrorResult> errorResultList = new LinkedList<>();
//             for (int paragraphIndex = 0; paragraphIndex < mergedParagraphSize; paragraphIndex++) {
//                 InspectionRespDTO.InspectionErrorResult errorResult = inspectionErrorResultIndexedMap.get(paragraphIndex);
//                 if (errorResult != null) {
//                     errorResultList.add(errorResult);
//                 }
//             }
    
//             InspectionRespDTO inspectionRespDTO =
//                 new InspectionRespDTO()
//                     .setInspectionResultList(inspectionResultList)
//                     .setErrorResultList(errorResultList);
    
//             List<JsonExtraContentChatRes.SourceBOResp.JsonContentExtraContentBO> extraContent =
//                 List.of(JsonExtraContentChatRes.SourceBOResp.buildComplianceInspectionExtraContentBO(inspectionRespDTO));
//             log.debug("inspectResult[extraContent]: {}", JSON.toJSONString(extraContent));
//             ChatHelper.doSinkNext(JsonExtraContentChatRes.buildCompleteAnswerWithExtraContent(
//                     param, null, System.currentTimeMillis(), new ArrayList<>(), "DEFAULT",
//                     eventCallbackParam, extraContent),
//                 sink, COMPLETE_ANSWER);
//             sink.complete();
//             StopWatch stopWatch = chatWatchRef.get().getStopWatch();
//             if (stopWatch != null) {
//                 if (stopWatch.isRunning()) {
//                     stopWatch.stop();
//                 }
//             }
//         }).exceptionallyAsync(ex -> {
//             MDC.put(TRACE_ID, traceID);
//             log.error("汇总结果出现异常: {}", ex.getMessage(), ex);
//             sink.error(ex);
//             return null;
//         })
//         ;
//         return eventSourceAR.get();
//     }
//     @NotNull
//     private static InspectionRespDTO.InspectionResult buildInspectResult(ChatParam param,
//                                                                          FluxSink<ServerSentEvent<String>> sink, SmartInspectEventCallbackParam eventCallbackParam, Integer originalParagraphIndex,
//                                                                          Boolean needFix, String inspectedParagraph, String originalParagraph,
//                                                                          List<ChatRes.SourceBOResp> sourceBORespList, String ruleName, String summaryAll,
//                                                                          String finalParagraphIndexRangeForHuman) {
//         InspectionRespDTO.InspectionResult complianceInspectionParagraph =
//             new InspectionRespDTO.InspectionResult()
//                 .setParagraphIndex(originalParagraphIndex)
//                 .setNeedFix(needFix)
//                 .setInspectedParagraph(inspectedParagraph)
//                 .setOriginalParagraph(originalParagraph);

//         // 目前只有一个 complianceInspectionRule
//         InspectionRespDTO.RuleInfo complianceInspectionRuleInfo = new InspectionRespDTO.RuleInfo()
//             .setSource(sourceBORespList)
//             .setRule(ruleName)
//             .setSummary(summaryAll);

//         ChatHelper.doSinkNext(ChatRes.buildStreamAnswer(
//             StrUtil.format("第{}段[{}]审查结束", finalParagraphIndexRangeForHuman, "合规性"),
//             System.currentTimeMillis(), param.getQuestion()), sink, PROCESS_STATUS);

//         // 可能是多个规则
//         ArrayList<InspectionRespDTO.RuleInfo> ruleInfos = new ArrayList<>();
//         ruleInfos.add(complianceInspectionRuleInfo);
//         complianceInspectionParagraph.setRuleInfos(ruleInfos);

//         // 运行过程中，吐出审查结果
//         InspectionRespDTO inspectionRespDTO = new InspectionRespDTO();
//         inspectionRespDTO.setInspectionResultList(List.of(complianceInspectionParagraph));
//         List<JsonExtraContentChatRes.SourceBOResp.JsonContentExtraContentBO> extraContent =
//             List.of(JsonExtraContentChatRes.SourceBOResp.buildComplianceInspectionExtraContentBO(inspectionRespDTO));
//         ChatHelper.doSinkNext(JsonExtraContentChatRes.buildCompleteAnswerWithExtraContent(param, null, System.currentTimeMillis(), new ArrayList<>(), null,
//             eventCallbackParam, extraContent), sink, EXTRA_CONTENT);
//         return complianceInspectionParagraph;
//     }
// }
