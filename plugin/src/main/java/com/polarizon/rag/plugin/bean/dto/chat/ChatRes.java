package com.polarizon.rag.plugin.bean.dto.chat;

import static com.polarizon.rag.plugin.bean.constant.Constants.OUTLINE_EXTRA_CONTENT_NAME;
import static com.polarizon.rag.plugin.bean.constant.Constants.OUTLINE_EXTRA_CONTENT_TYPE_NAME;
import static com.polarizon.rag.plugin.bean.constant.Constants.WARNING_EXTRA_CONTENT_TYPE_NAME;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.polarizon.rag.plugin.tool.llm.UsageDetail;
import com.polarizon.rag.plugin.tool.llm.Usage;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.rag.chat.ExtraContentBO;
import com.polarizon.rag.chat.KbTypeEnum;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;

import cn.hutool.core.lang.Assert;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ChatRes {
    // 本轮提问
    private String question;
    // 问题时间
    private Long questionTime;
    // 本轮回答
    @JSONField(ordinal = -1)
    private String response;
    // 本轮回答
    @JSONField(ordinal = -2)
    private String reasoningContent;
    // 本轮回答
    private String style;
    private Usage usage = new Usage();
    private UsageDetail usageDetail = new UsageDetail();
    // 历史问答
    private List<ChatHistory> history;
    // 文档出处
    private List<SourceBOResp> source;
    // 统计指标信息
    private Object evaluateInfo;
    // 重写信息
    // private QueryRewriterResult queryRewriterResult;
    // 额外内容
    public List<ExtraContentBO> extraContent;

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @EqualsAndHashCode(of = {"fileID", "kbChunkID"})
    public static class SourceBOResp {

        /**
         * 材料编号，从1开始，同给到大模型的编号
         */
        private Integer sourceNumber;

        /**
         * chunk来源类型
         */
        @Schema(description = "chunk来源类型")
        @JsonProperty("type")
        public KbTypeEnum kbType;

        /**
         * 文档id
         */
        @Schema(description = "文档id")
        @JsonProperty("fileID")
        public String fileID;

        /**
         * 知识库中的chunkID
         */
        @Schema(description = "知识库中的chunkID")
        @JsonProperty("kbChunkID")
        public String kbChunkID;

        /**
         * avro type: *schema.FloatField
         */
        @Schema(description = "可信度")
        @JsonView({AbstractBaseBO.EditView.class})
        @JsonProperty("score")
        public Float score;

        @ApiModelProperty(value = "网页展示URL", example = "example.com/article")
        private String displayUrl;

        /**
         * kbType 为 WEB_SEARCH_CHUNK （网页搜索）内容在 summary
         */
        @ApiModelProperty(value = "网页详细摘要（当summary=true时返回）")
        private String summary;


        /**
         * kbType 为 WEB_SEARCH_CHUNK （网页搜索） 网页标题
         */
        @ApiModelProperty(value = "网页标题")
        private String webName;

        /**
         *  kbType 为 TMP_KB_CHUNK 或 KB_CHUNK 的时候返回内容在 content
         */
        private String content;

        public static SourceBOResp fromChunk(Collection<Chunk> chunks, Chunk chunk, KbTypeEnum kbTypeEnum) {
            Assert.notNull(chunk, "chunk is null");
            return new SourceBOResp()
                .setContent(chunk.getContent())
                .setKbType(kbTypeEnum)
                .setFileID(chunk.getFileID())
                .setKbChunkID(chunk.getChunkID())
                .setScore(
                    chunks.stream()
                        .filter(chunk1 -> chunk1.getChunkID().equals(chunk.getChunkID())).findAny()
                        .orElseThrow(() -> 
                            new BusinessExecutionException("chunk 在rerank 结果里面找不到：chunkID:" + chunk.getChunkID()))
                        .getScore());
        }

    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class SourceOld {
        // 业务这边的chunkID
        private String kbChunkID;
        // 知识库ID
        private String kbID;
        // 向量数据库的片段ID
        private String chunkID;
        // 文档id
        private String fileID;
        // 文档名称
        private String fileName;
        // 章节ID
        private String chapterID;
        // 章节名称
        private String chapterName;
        // 原文内容
        private String content;
        // 可信度
        private Float score;
        // 页码
        private Integer pageNumber;
//        // 文件存储S3地址
//        public S3File fileS3;
    }


    /**
     * 构建知识来源
     * @param chunks 知识来源
     * @param kbTypeEnum 知识来源类型
     * @return 知识来源
     */
    public static List<SourceBOResp> buildSources(Collection<Chunk> chunks, KbTypeEnum kbTypeEnum) {
        Assert.notNull(kbTypeEnum, "kbTypeEnum is null");
        if (CollectionUtils.isEmpty(chunks)) {
            return new ArrayList<>();
        }

        List<SourceBOResp> kbSourceBORespList = chunks.stream().map(
            chunk -> SourceBOResp.fromChunk(chunks, chunk, kbTypeEnum)
        ).toList();
        return new ArrayList<>(kbSourceBORespList);
    }

    /**
     * 构建流式回答
     * @param resp 回答内容
     * @param questionTime 问题时间
     * @param question 问题
     * @return 流式回答
     */
    @NotNull
    public static ChatRes buildStreamAnswer(String resp,  String question) {
        ChatRes object = new ChatRes();
        object.setQuestion(question);
        object.setQuestionTime(System.currentTimeMillis());
        object.setEvaluateInfo(null);
        object.setHistory(new ArrayList<>());
        object.setSource(new ArrayList<>());
        object.setResponse(resp);
        return object;
    }

    /**
     * 构建流式推理内容
     * @param reasoningContent 推理内容
     * @param questionTime 问题时间
     * @param question 问题
     * @return 流式推理内容
     */
    @NotNull
    public static ChatRes buildStreamReasonContent(String reasoningContent, String question) {
        ChatRes object = new ChatRes();
        object.setQuestion(question);
        object.setQuestionTime(System.currentTimeMillis());
        object.setEvaluateInfo(null);
        object.setHistory(new ArrayList<>());
        object.setSource(new ArrayList<>());
        object.setReasoningContent(reasoningContent);
        return object;
    }

    /**
     * 构建大纲答案
     * @param resp 回答内容
     * @param outline 大纲
     * @param questionTime 问题时间
     * @param question 问题
     * @return 大纲答案
     */
    @NotNull
    public static ChatRes buildOutLineAnswer(String resp, String outline, String question) {
        ExtraContentBO extraContentBO = buildOutlineExtraContentBO(outline);

        ChatRes object = new ChatRes();
        object.setQuestion(question);
        object.setQuestionTime(System.currentTimeMillis());
        object.setEvaluateInfo(null);
        object.setHistory(new ArrayList<>());
        object.setSource(new ArrayList<>());
        object.setResponse(resp);
        object.setExtraContent(List.of(extraContentBO));
        return object;
    }

    /**
     * 构建警告答案
     * @param content 警告内容
     * @param questionTime 问题时间
     * @param name 警告名称
     * @return 警告答案
     */
    @NotNull
    public static ChatRes buildWarningAnswer(String content, Long questionTime, String name) {
        ExtraContentBO extraContentBO = ExtraContentBO.builder()
                .type(WARNING_EXTRA_CONTENT_TYPE_NAME)
                .name(name)
                .sort(0)
                .content(content)
                .build();

        ChatRes object = new ChatRes();
        object.setQuestion("");
        object.setQuestionTime(questionTime);
        object.setEvaluateInfo(null);
        object.setHistory(new ArrayList<>());
        object.setSource(new ArrayList<>());
        object.setResponse("");
        object.setExtraContent(List.of(extraContentBO));
        return object;
    }

    /**
     * 构建大纲额外内容
     * @param outline 大纲
     * @return 大纲额外内容
     */
    public static ExtraContentBO buildOutlineExtraContentBO(String outline) {
        if (outline == null) {
            return ExtraContentBO.builder().build();
        }
        return ExtraContentBO.builder()
                .type(OUTLINE_EXTRA_CONTENT_TYPE_NAME)
                .name(OUTLINE_EXTRA_CONTENT_NAME)
                .sort(0)
                .content(outline)
                .build();
    }
}