package com.polarizon.rag.plugin.faas;

import com.polarizon.rag.plugin.bean.dto.aiagent.AgentChatParam;
import com.polarizon.rag.plugin.bean.dto.aiagent.RagChatParam;
import com.polarizon.rag.plugin.service.agent.AgentChatService;
import com.polarizon.rag.plugin.service.ragchat.RagChatService;
import com.polarizon.rag.plugin.service.instruct.InstructChatService;
import com.polarizon.rag.plugin.util.JSONUtils;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.polarizon.rag.plugin.bean.constant.Constants.COMPLETE_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.ERROR_ANSWER;
import static com.polarizon.rag.plugin.bean.constant.Constants.X_ACCOUNT_HEADER_NAME;

@Slf4j
@Component
//@Validated
@RestController
public class ChatFaas {
    @Autowired
    private RagChatService ragChatService;
    @Autowired
    private InstructChatService instructChatService;
    @Autowired
    private AgentChatService agentChatService;

    @PostMapping(path = "/ragChat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public Object ragChat(@RequestHeader(X_ACCOUNT_HEADER_NAME) String userAccount,
                          @RequestBody @Valid RagChatParam ragChatParam) {
        log.info("ragChatParam:\n{}", JSONUtils.getJacksonJsonString(ragChatParam));
        return handleStream(ragChatParam.isStream(), ragChatService.ragChat(userAccount, ragChatParam));
    }
    
    @PostMapping(path = "/agentChat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public Object agentChat(@RequestHeader(X_ACCOUNT_HEADER_NAME) String userAccount,
                            @RequestBody @Valid AgentChatParam agentChatParam) {
        log.info("agentChatParam:\n{}", JSONUtils.getJacksonJsonString(agentChatParam));
        return handleStream(agentChatParam.isStream(), agentChatService.agentChat(userAccount, agentChatParam));
    }

    /**
     * 处理流式和非流式响应
     *
     * @param isStream 是否需要流式响应
     * @param flux     服务器发送事件流
     * @return 根据请求类型返回流式或非流式响应
     */
    public Object handleStream(Boolean isStream, Flux<ServerSentEvent<String>> flux) {
        // 流式响应直接返回flux
        if (isStream == null || isStream) {
            return flux;
        }

        // 非流式响应,返回json
        return flux
            .filter(event -> COMPLETE_ANSWER.equals(event.event()) || ERROR_ANSWER.equals(event.event()))
            .map(ServerSentEvent::data)
            .reduce((acc, data) -> acc + data)
            .switchIfEmpty(Mono.just(""))  // 处理空流情况
            .map(result ->
                ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(result))
            .onErrorResume(e -> {
                log.error("非流式响应处理失败: {}", e.getMessage(), e);
                return Mono.just(ResponseEntity.status(500)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(e.getMessage()));
            })
//                .timeout(java.time.Duration.ofMinutes(5),
//                    Mono.just(ResponseEntity.status(504)
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .body("响应超时，请稍后重试")))
            ;
    }

}

    