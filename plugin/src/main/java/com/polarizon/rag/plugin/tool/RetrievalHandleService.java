package com.polarizon.rag.plugin.tool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.ModelInfoDO;
import com.polarizon.rag.ModelTypeEnum;
import com.polarizon.rag.chat.KbTypeEnum;
import com.polarizon.rag.chat.RetrievalConfig;
import com.polarizon.rag.kb.KbEnableEnum;
import com.polarizon.rag.kb.KnowledgeBaseDO;
import com.polarizon.rag.plugin.bean.constant.Constants;
import com.polarizon.rag.plugin.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.bean.dto.chat.Chunk;
import com.polarizon.rag.plugin.bean.dto.chat.GenerateParam;
import com.polarizon.rag.plugin.bean.dto.chat.KbInfo;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterResp;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterResult;
import com.polarizon.rag.plugin.bean.dto.chat.QueryRewriterV2Param;
import com.polarizon.rag.plugin.bean.dto.chat.RerankParam;
import com.polarizon.rag.plugin.bean.dto.chat.RerankResp;
import com.polarizon.rag.plugin.bean.dto.chat.SearchChunkParam;
import com.polarizon.rag.plugin.bean.dto.chat.SearchChunksRep;
import com.polarizon.rag.plugin.bean.dto.websearch.WebSearchResponse;
import com.polarizon.rag.plugin.config.ModelIdentityConfig;
import com.polarizon.rag.plugin.config.LlmConfig;
import com.polarizon.rag.plugin.config.ModelConfig;
import com.polarizon.rag.plugin.exception.BusinessExecutionException;
import com.polarizon.rag.plugin.exception.LowBalanceRequestsException;
import com.polarizon.rag.plugin.exception.TooManyRequestsException;
import com.polarizon.rag.plugin.service.BaseChatContext;
import com.polarizon.rag.plugin.service.ChatHelper;
import com.polarizon.rag.plugin.util.FeignUtil;
import com.polarizon.rag.plugin.util.JSONUtils;
import com.polarizon.rag.plugin.util.StopWatchUtils;
import io.github.futures4j.ExtendedFuture;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.polarizon.rag.plugin.bean.constant.Constants.ENABLE_PARENT_INDEX_DEFAULT;
import static com.polarizon.rag.plugin.config.TraceIdFilter.TRACE_ID;

@Service
@Slf4j
public class RetrievalHandleService {
    @Autowired
    private LlmConfig llmConfig;
    @Autowired
    private BochaWebTool bochaWebTool;
    @Autowired
    private ModelConfig modelConfig;
    @Autowired
    private ModelIdentityConfig modelIdentityConfig;

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class RetrievalParam {
        @NotNull
        private RetrievalConfig retrievalConfig;
        @NotNull
        private List<String> questions;
        @NotNull
        private ModelInfoDO llmModelInfoDO;

        private String traceIndex = "0";
        private Boolean needRewriter = false;
        private List<ChatHistory> history = null;
        private Float scoreThreshold = null;
        private Float sparseScoreThreshold= null;

        public RetrievalParam(RetrievalConfig retrievalConfig, List<String> questions, ModelInfoDO llmModelInfoDO) {
            this.retrievalConfig = retrievalConfig;
            this.questions = questions;
            this.llmModelInfoDO = llmModelInfoDO;
        }
    }
    
    /**
     * TODO 使用 Supplier<String> jobNameSupplier，取消 chapterNumForHuman 和  finalQuestionIndex 参数
     * 检索知识，包含知识库和临时知识库，联网检索
     * 
     * @return
     */
    public Pair<QueryRewriterResult, GenerateParam.Sources> multiRetrieve(BaseChatContext  chatContext,
                                                                          Boolean needRewriter, Integer finalQuestionIndex) {
        RetrievalParam retrievalParam = 
            new RetrievalParam(chatContext.getRetrievalConfig(), List.of(chatContext.getChatParam().getQuestion()),
                chatContext.getLlmModelInfoDO())
                .setNeedRewriter(needRewriter)
                .setHistory(chatContext.getChatParam().getHistory());
        QueryRewriterResult queryRewriterResult = 
            new QueryRewriterResult().setRewritten(false);
        List<String> questions = List.of(chatContext.getChatParam().getQuestion());
        queryRewriterResult.setRewrittenQuestion(questions.get(0));
        // TODO 处理 kbList ，过滤掉无效的

        List<CompletableFuture<ImmutablePair<List<String>,List<Chunk>>>> kbRetrievetasklist = new CopyOnWriteArrayList<>();
        List<CompletableFuture<List<String>>> websearchCfList = new CopyOnWriteArrayList<>();
        String traceID = MDC.get(TRACE_ID);
        if (CollectionUtils.isEmpty(questions)) {
            throw new IllegalArgumentException("questions is empty");
        }

        List<String> validList = questions.stream().filter(StringUtils::isNotBlank).toList();
        if (CollectionUtils.isEmpty(validList)) {
            throw new IllegalArgumentException("questions is empty");
        }

        GenerateParam.Sources sources = new GenerateParam.Sources();
        RetrievalConfig retrievalConfig = chatContext.getRetrievalConfig();
        
        // 记录使用默认知识库的情况，仅在有默认知识库时记录一次
        if (StringUtils.isNotBlank(modelIdentityConfig.getDefaultKbId())) {
            log.debug("当前配置的默认知识库ID: {}", modelIdentityConfig.getDefaultKbId());
        }
        
        List<KnowledgeBaseDO> validKnowledgeBaseDOS = getValidKnowledgeBaseDOS(getKbIdList(retrievalConfig));
        List<KnowledgeBaseDO> validTmpKnowledgeBaseDOS = getValidKnowledgeBaseDOS(getTmpKbIdList(retrievalConfig));
        boolean noKb = CollectionUtils.isEmpty(validKnowledgeBaseDOS) && CollectionUtils.isEmpty(validTmpKnowledgeBaseDOS);
        boolean needWebSearch = null != retrievalConfig 
            && null != retrievalConfig.getWebSearchConfig() 
            && retrievalConfig.getWebSearchConfig().getIsSearchWeb()!=null
            && retrievalConfig.getWebSearchConfig().getIsSearchWeb();
        if (noKb && !needWebSearch) {
            log.debug("no kb and no web search");
            return Pair.of(queryRewriterResult, sources);
        }

        // 逐个问题检索,使用 llmConfig.getLlmPoolExecutor()
        for (int i = 0; i < validList.size(); i++) {
            String question = validList.get(i);
            String rewrittenQuestion;
            if (needRewriter) {
                // queryRewriter 改写问题
                queryRewriterResult = 
                    queryRewriterV2(question, chatContext.getChatParam().getHistory(), chatContext.getLlmModelInfoDO());
                rewrittenQuestion =
                    queryRewriterResult.getRewritten() ? queryRewriterResult.getRewrittenQuestion(): question;
            } else {
                rewrittenQuestion = question;
            }

            // 获取知识库信息
            String kbJobName = buildJobName(KbTypeEnum.KB_CHUNK, finalQuestionIndex, question);
            List<ChatRes.SourceBOResp> kbSourceBORespList = 
                handleKbRetrieve(chatContext, validKnowledgeBaseDOS, question, traceID, rewrittenQuestion, 
                    kbRetrievetasklist, KbTypeEnum.KB_CHUNK, kbJobName);
            sources.setKbSources(kbSourceBORespList);

            // 获取临时知识库信息
            String tmpKbJobName = buildJobName(KbTypeEnum.TMP_KB_CHUNK, finalQuestionIndex, question);
            List<ChatRes.SourceBOResp> tmpKbSourceBORespList = 
                handleKbRetrieve(chatContext, validTmpKnowledgeBaseDOS, question, traceID, rewrittenQuestion, 
                    kbRetrievetasklist, KbTypeEnum.TMP_KB_CHUNK, tmpKbJobName);

            sources.setTempKbSources(tmpKbSourceBORespList);

            // 联网搜索
            if (needWebSearch) {
                String webSearchJobName = buildJobName(KbTypeEnum.WEB_SEARCH_CHUNK, finalQuestionIndex, question);
                String newTraceId = StrUtil.format("{}|{}", traceID, webSearchJobName);
                List<ChatRes.SourceBOResp> webSearchSources =
                    handleWebSearch(chatContext, newTraceId, rewrittenQuestion, webSearchJobName, websearchCfList);
                sources.setWebSearchSources(webSearchSources);
            }
           
        }
        
        if (CollectionUtils.isEmpty(kbRetrievetasklist) && CollectionUtils.isEmpty(websearchCfList)) {
            return new Pair<>(queryRewriterResult, sources);
        }

        CompletableFuture<Void> cf = CompletableFuture
            .allOf(CollectionUtils.union(kbRetrievetasklist, websearchCfList).toArray(new CompletableFuture[0]));
        // 由于这个方法是异步的，所以这里需要等待所有的检索任务完成
        // 注意：这里必须在异步中运行，不阻塞主线程，且需要阻塞异步线程，等待结果，;
        chatContext.addFuture("main-multiRetrieve-allOf", cf, true);
        cf.join();
        // TODO 所有的检索任务提前结束的处理
//        kbRetrievetasklist.forEach(retrieveCF -> {
//            try {
//                ImmutablePair<List<String>,List<Chunk>> chunksList = retrieveCF.get();
//                totalChunkTuples.add(chunksList);
//            } catch (Exception e) {
//                //                log.error("检索章节内容出错:{}", e.getMessage(), e);
//                throw new BusinessExecutionException("检索内容出错: " + e.getMessage(), e);
//            }
//        });
        return new Pair<>(queryRewriterResult, sources);
    }

    private static String buildJobName(KbTypeEnum kbTypeEnum, Integer questionIndex,  String question) {
        return StrUtil.format("{}-q({})-({})", kbTypeEnum, questionIndex, question);
    }

    private List<ChatRes.SourceBOResp> handleWebSearch(BaseChatContext  chatContext, String newTraceId,
                                                       String rewrittenQuestion, String jobName,
                                                       List<CompletableFuture<List<String>>> websearchCfList) {
        List<ChatRes.SourceBOResp> webSearchSources = new CopyOnWriteArrayList<>();
        CompletableFuture<List<String>> websearchCf = CompletableFuture.supplyAsync(
            () -> {
                try {
                    Integer rerankTopN = chatContext.getRetrievalConfig().getWebSearchConfig().getRerankTopN();

                    MDC.put(TRACE_ID, newTraceId);
                    List<WebSearchResponse.WebPageValue> webPageValues = 
                        bochaWebTool.search(rerankTopN, rewrittenQuestion);
                    if (rerankTopN != null && webPageValues.size() > rerankTopN) {
                        webPageValues = webPageValues.subList(0, rerankTopN);
                    }
                    List<String> webSearchSummery = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(webPageValues)) {
                        webPageValues.forEach(value -> {
                            ChatRes.SourceBOResp sourceBOResp = new ChatRes.SourceBOResp();
                            sourceBOResp.setKbType(KbTypeEnum.WEB_SEARCH_CHUNK);
                            sourceBOResp.setSummary(value.getSummary());
                            sourceBOResp.setDisplayUrl(value.getDisplayUrl());
                            sourceBOResp.setWebName(value.getName());
                            webSearchSources.add(sourceBOResp);
                        });
                        webSearchSummery = 
                            webPageValues.stream()
                                .map(WebSearchResponse.WebPageValue::getSummary)
                                .collect(Collectors.toList());
                    }
                    log.debug("webSearchResult:{}", JSONUtils.getJacksonJsonString(webSearchSources));
                    return webSearchSummery;
                }catch ( TooManyRequestsException | LowBalanceRequestsException e) {
                    throw e;
                }
                catch (Exception e) {
                    throw new BusinessExecutionException("联网检索失败, 请稍后重试: " + e.getMessage(), e);
                }
            },
            llmConfig.getLlmPoolExecutor());
        websearchCfList.add(websearchCf);
        chatContext.addFuture(jobName, websearchCf, true);
        return webSearchSources;
    }

    private List<ChatRes.SourceBOResp> handleKbRetrieve(BaseChatContext  chatContext,
                                                        List<KnowledgeBaseDO> validKnowledgeBaseDOS, 
                                                        String question, String traceID, String rewrittenQuestion, 
                                                        List<CompletableFuture<ImmutablePair<List<String>, List<Chunk>>>> kbRetrievetasklist,
                                                        KbTypeEnum kbTypeEnum, String jobName) {
        RetrievalParam retrievalParam = new RetrievalParam();
        List<ChatRes.SourceBOResp> kbSources = new CopyOnWriteArrayList<>();
        List<KbInfo> kbInfos = buildKbInfos(validKnowledgeBaseDOS, question);
        if (CollectionUtils.isNotEmpty(kbInfos)) {
            CompletableFuture<ImmutablePair<List<String>,List<Chunk>>> retrieveCF = ExtendedFuture.supplyAsync(
                () -> {
                    MDC.put(TRACE_ID, traceID+"|"+jobName);
                    return retrieveChunkTuples(question, rewrittenQuestion, kbInfos, chatContext.getRetrievalConfig(), kbSources, retrievalParam.getScoreThreshold(),
                        retrievalParam.getSparseScoreThreshold(), kbTypeEnum);
                },
                llmConfig.getRetrivalPoolExecutor());
            kbRetrievetasklist.add(retrieveCF);
            chatContext.addFuture(jobName, retrieveCF, true);
        }
        return kbSources;
    }

    private static Integer getRerankTopKOrDefault(Integer defaultRerankTopK, RetrievalConfig retrievalConfig) {
        if (retrievalConfig != null && retrievalConfig.getKbSearchConfig() != null) {
            Integer rerankTopN = retrievalConfig.getKbSearchConfig().getRerankTopN();
            if (rerankTopN != null) {
                return rerankTopN;
            }
        }
        return defaultRerankTopK;
    }

    private static Integer getSearchTopKOrDefault(Integer defaultRerankTopK, RetrievalConfig retrievalConfig) {
        return getRerankTopKOrDefault(defaultRerankTopK, retrievalConfig) + 10;
    }

    /**
     * 获取知识库ID列表，如果配置了默认知识库ID且当前列表中不包含该ID，则添加默认知识库
     * @param retrievalConfig 检索配置
     * @return 知识库ID列表
     */
    private List<String> getKbIdList(RetrievalConfig retrievalConfig) {
        List<String> kbIdList = new ArrayList<>();
        
        // 获取配置中的知识库列表
        if (retrievalConfig != null
                && retrievalConfig.getKbSearchConfig() != null
                && retrievalConfig.getKbSearchConfig().getKbIDList() != null) {
            kbIdList.addAll(retrievalConfig.getKbSearchConfig().getKbIDList());
        }
        
        // 如果配置了默认知识库ID且当前列表中不包含该ID，则添加默认知识库
        if (modelIdentityConfig != null && StringUtils.isNotBlank(modelIdentityConfig.getDefaultKbId())) {
            if (!kbIdList.contains(modelIdentityConfig.getDefaultKbId())) {
                log.debug("添加默认知识库ID: {}", modelIdentityConfig.getDefaultKbId());
                kbIdList.add(modelIdentityConfig.getDefaultKbId());
            }
        }
        
        return kbIdList;
    }

    private static List<String> getTmpKbIdList(RetrievalConfig retrievalConfig) {
        if (retrievalConfig == null
                || retrievalConfig.getKbSearchConfig() == null
                || retrievalConfig.getKbSearchConfig().getTmpKbIDList() == null) {
            return new ArrayList<>();
        }
        return retrievalConfig.getKbSearchConfig().getTmpKbIDList();
    }

    @NotNull
    private static List<KbInfo> buildKbInfos(List<KnowledgeBaseDO> knowledgeBaseDOList, String question) {
        if (CollectionUtils.isEmpty(knowledgeBaseDOList)) {
            return Collections.emptyList();
        }
        // TODO：每个知识库用不同的模型
        ModelInfoDO modelInfoDO = ChatHelper.getDefaultModel(ModelTypeEnum.EMBEDDING);

        List<KbInfo> list = knowledgeBaseDOList.stream().map(
                        knowledgeBaseDO -> new KbInfo()
                                .setKbID(knowledgeBaseDO.getId())
                                .setKbName(knowledgeBaseDO.getName())
                                .setQuestion(question)
                                .setEnableParentIndex(ENABLE_PARENT_INDEX_DEFAULT)
                                .setEmbeddingModel(modelInfoDO.getName())
                                .setEmbeddingApiKey(modelInfoDO.getApiKey())
                                .setEmbeddingUrl(modelInfoDO.getEndpointUrl()))
                .toList();
        log.debug("获取知识库信息:{}", JSONUtils.getJacksonJsonString(list));
        return list;
    }

    @NotNull
    public static List<KnowledgeBaseDO> getValidKnowledgeBaseDOS(List<String> kbIds) {
        if (CollUtil.isEmpty(kbIds)) {
            return List.of();
        }
        // 过滤kbId为空的值
        List<String> cleanKbIds = kbIds.stream().filter(StringUtils::isNotBlank).toList();
        if (CollUtil.isEmpty(cleanKbIds)) {
            return List.of();
        }
        
        ResultDTO<List<KnowledgeBaseDO>> listResultDTO = 
            FeignUtil.getKnowledgeBaseApi().listAll(null, null, cleanKbIds);
        List<KnowledgeBaseDO> knowledgeBaseDOList = FeignUtil.getListDataNonEmpty(listResultDTO, "知识库不存在");
        // 检查是否有被禁用的知识库
        List<KnowledgeBaseDO> disableList = knowledgeBaseDOList.stream()
                .filter(knowledgeBaseDO -> KbEnableEnum.DISABLE.equals(knowledgeBaseDO.getEnable())).toList();
        if (CollectionUtils.isNotEmpty(disableList)) {
            String kbNames = disableList.stream().map(KnowledgeBaseDO::getName).collect(Collectors.joining(","));
            throw new BusinessExecutionException("知识库已被禁用:" + kbNames);
        }
        return knowledgeBaseDOList;
    }

    private static boolean isNoChunks(List<SearchChunksRep.KbResult> searchChunks) {
        if (CollectionUtils.isEmpty(searchChunks)) {
            return true;
        }
        for (SearchChunksRep.KbResult searchChunk : searchChunks) {
            if (CollectionUtils.isNotEmpty(searchChunk.getSparseChunks())) {
                return false;
            }
            if (CollectionUtils.isNotEmpty(searchChunk.getDenseChunks())) {
                return false;
            }
        }
        return true;
    }

    /**
     * TODO 方法返回 allSourceBOResp，而不是作为入参处理
     * @param question
     * @param kbInfos
     * @return
     */
    private ImmutablePair<List<String>,List<Chunk>> retrieveChunkTuples(String question, String rewrittenQuestion, 
                                                                        List<KbInfo> kbInfos,
                                                                        RetrievalConfig retrievalConfig,
                                                                        List<ChatRes.SourceBOResp> allSourceBOResp, 
                                                                        Float scoreThreshold, Float sparseScoreThreshold,
                                                                        KbTypeEnum kbTypeEnum)  {
        // TODO StopWatchUtils 参数传入
        StopWatchUtils watchUtils = new StopWatchUtils("instructChat");
        
        // 回答问题用 originalQuestion , searchChunk 和 rerank 用 rewrittenQuestion
        List<KbInfo> kbInfosForSearch =  KbInfo.buildQuestionInfoWithRewrittenQuestion(kbInfos, question, rewrittenQuestion);

        // retrieval 搜索相似的文档片段
        // TODO retrievalConfig传入进来 RerankTopK
        List<SearchChunksRep.KbResult> searchChunks = watchUtils.timeCall("retrieveChunks",
            () -> searchChunks(kbInfosForSearch,
                getSearchTopKOrDefault(ChatHelper.defaultRerankTopK, new RetrievalConfig()), scoreThreshold,
                sparseScoreThreshold)
                .getChunks());

        boolean noChunks = isNoChunks(searchChunks);
        ImmutablePair<List<String>,List<Chunk>> rerankChunkTuple = new ImmutablePair(new ArrayList<String>(), new ArrayList<Chunk>());
        // 搜索的结果不为空
        if (!noChunks) {
            // rerankChunks
            rerankChunkTuple = handleRerankChunks(retrievalConfig, searchChunks, rewrittenQuestion);
            List<ChatRes.SourceBOResp> newSourceBORespList = ChatRes.buildSources(rerankChunkTuple.getRight(), kbTypeEnum);
            allSourceBOResp.addAll(newSourceBORespList);
        }

        // 回答问题
        // 回答问题用原问题
        KbInfo.resetQuestionInfo(kbInfos);

        return rerankChunkTuple;
    }

    /**
     * searchChunk 和 rerank 用 rewrittenQuestion
     * 返回结果，rerank 会内部去重之后给出
     *
     * @param searchChunks
     * @param question 如果有rewriter，则使用rewriterQuestion
     * @param topK
     * @return
     */
    private RerankResp rerankChunks(List<SearchChunksRep.KbResult> searchChunks, String question, Integer topK) {
        if (CollectionUtils.isEmpty(searchChunks)) {
            return new RerankResp();
        }

        // 获取模型配置
        ModelInfoDO rerankModelInfo = ChatHelper.getDefaultModel(ModelTypeEnum.RERANK);

        // searchChunks rerank会内部去重
        RerankParam param =
            new RerankParam()
                .setQuestion(question)
                .setChunks(searchChunks)
                .setTopK(topK)
                .setUrl(rerankModelInfo.getEndpointUrl())
                .setModel(rerankModelInfo.getName())
                .setApiKey(rerankModelInfo.getApiKey())

                // shede 默认不使用模型重排
                .setUseReranker(llmConfig.getUseReranker());
        param.setTopK(2);
        log.debug("rerankParam:{}", JSONUtils.getJacksonJsonString(param));
        ResultDTO<RerankResp> rerankResult = FeignUtil.getRerankClient().rerank(param);
        RerankResp rerankResp = FeignUtil.getDataNonNull(rerankResult, "rerank 结果为空");
        log.debug("rerankResp:{}", JSONUtils.getJacksonJsonString(rerankResp));
        return rerankResp;
    }

    private SearchChunksRep searchChunks(List<KbInfo> kbInfos, Integer searchChunksTopK, Float scoreThreshold,
                                         Float sparseScoreThreshold) {
        SearchChunkParam searchChunkParam = new SearchChunkParam()
            .setTopK(searchChunksTopK)
            .setScoreThreshold(scoreThreshold)
            .setSparseScoreThreshold(sparseScoreThreshold)
            .setKbInfos(kbInfos);
        log.debug("searchChunksParm:{}", JSONUtils.getJacksonJsonString(searchChunkParam));
        ResultDTO<SearchChunksRep> searchChunksRepResultDTO =
            FeignUtil.getEmbeddingClient().searchChunks(searchChunkParam);
        SearchChunksRep searchChunk = FeignUtil.getDataNonNull(searchChunksRepResultDTO, "searchChunk响应结果为空");
        log.debug("searchChunksRep:{}", JSONUtils.getJacksonJsonString(searchChunk));
        return searchChunk;
    }

    
    /**
     * new Tuple(chunkStrs, newChunkList)
     * @param searchChunks
     * @param rewrittenQuestion
     * @return
     */
    private ImmutablePair<List<String>,List<Chunk>> handleRerankChunks(RetrievalConfig retrievalConfig,
                                                                       List<SearchChunksRep.KbResult> searchChunks,
                                                                       String rewrittenQuestion) {
        // TODO retrievalConfig传入进来 RerankTopK
        RerankResp rerankResp =
            this.rerankChunks(searchChunks, rewrittenQuestion,
                getRerankTopKOrDefault(ChatHelper.defaultRerankTopK, retrievalConfig));

        // rerankResp生成list,用 searchChunks补全信息
        List<Chunk> newChunkList = rewriteChunkScore(searchChunks, rerankResp);
        log.debug("rewriteChunkScore:{}", JSONUtils.getJacksonJsonString(newChunkList));

        // 获取答案
        List<String> chunkStrs = Chunk.buildChunkStrs(rerankResp, newChunkList);
        log.debug("chunkStrs:{}", JSONUtils.getJacksonJsonString(chunkStrs));
        return new ImmutablePair<>(chunkStrs, newChunkList);
    }


    private QueryRewriterResult queryRewriterV2(String question, List<ChatHistory> history, ModelInfoDO modelInfoDO) {
        Assert.notNull(modelInfoDO,  "modelInfoDO is null");
        
        // 使用大模型自我认知提示词（zhiyan）
        String cognitionPrompt = buildCognitionPrompt(question, modelInfoDO.getDisplayName());

        QueryRewriterV2Param queryRewriterV2Param =
            new QueryRewriterV2Param().setQuestion(cognitionPrompt).setHistory(history)
                .setModelInfo(modelInfoDO)
                .setChatMode(ChatHelper.getValidChatModeConfig(null));
        log.debug("queryRewriterV2Param:{}", JSONUtils.getJacksonJsonString(queryRewriterV2Param));

        QueryRewriterResult queryRewriterResult = new QueryRewriterResult();
        try {
            ResultDTO<QueryRewriterResp> queryRewriterRespResultDTO =
                FeignUtil.getQueryRewriterClient().queryRewriteV2(queryRewriterV2Param);
            QueryRewriterResp rewriterResp =
                FeignUtil.getDataNonNull(queryRewriterRespResultDTO, "queryRewriter响应结果为空");
            String rewrittenQuestion = rewriterResp.getRewrittenQuestion();
            // 兼容带推理的情况
            Pattern pattern = Pattern.compile(Constants.THINK_REASONING_REGEX);
            Matcher matcher = pattern.matcher(rewrittenQuestion);
            if (matcher.find() && matcher.groupCount() == 2) {
                String matchStr = matcher.group(2);
                log.debug("queryRewriter reasoning handled. originalResponse:{}, matchStr:{}", rewrittenQuestion, matchStr);
                rewrittenQuestion = matchStr.trim();
            }
            
            // 设置查询结果
            queryRewriterResult.setRewritten(true);
            queryRewriterResult.setRewrittenQuestion(rewrittenQuestion.trim());
            
            // 检查是否为"查询模型身份"
            if (Constants.MODEL_IDENTITY_QUERY.equals(rewrittenQuestion.trim())) {
                log.debug("检测到模型身份查询: {}", rewrittenQuestion.trim());
                queryRewriterResult.setModelIdentityQuery(true);
            }
            
            log.debug("queryRewriter success.originalQuery:{}, Result:{}", question, JSONUtils.getJacksonJsonString(queryRewriterResult));
        } catch (Exception e) {
            log.warn("queryRewriter failed", e);
            queryRewriterResult.setRewritten(false);
        }
        return queryRewriterResult;
    }

    @Nullable
    private String buildCognitionPrompt(String question, String modelDisplayName) {
        String zhiyanPrompt = "";
        try {
            if (modelConfig != null && modelConfig.getRecognition() != null &&
                StringUtils.isNotBlank(modelConfig.getRecognition().getZhiyan())) {
                // 将用户问题替换到提示词模板中的{{question}}
                zhiyanPrompt = modelConfig.getRecognition().getZhiyan();
                zhiyanPrompt = zhiyanPrompt.replace("{{question}}", question);
                zhiyanPrompt = zhiyanPrompt.replace("{{modelDisplayName}}", modelDisplayName);
            }
        } catch (Exception e) {
            log.warn("获取模型认知提示词失败", e);
        }
        return zhiyanPrompt;
    }

    @NotNull
    private static List<Chunk> rewriteChunkScore(List<SearchChunksRep.KbResult> searchChunks, RerankResp rerankResp) {
        if (CollectionUtils.isEmpty(searchChunks)) {
            return new ArrayList<>();
        }
        List<Chunk> denseChunks =
            searchChunks.stream().flatMap(searchChunk -> searchChunk.getDenseChunks().stream()).toList();
        List<Chunk> sparseChunks =
            searchChunks.stream().flatMap(searchChunk -> searchChunk.getSparseChunks().stream()).toList();
        Collection<Chunk> union = CollectionUtils.union(denseChunks, sparseChunks);
        List<Chunk> newChunkList = rerankResp.getChunks().stream().map(rerankChunk -> union.stream().filter(
                    oldChunk -> oldChunk.getChunkID().equals(rerankChunk.getChunkID()) && oldChunk.getKbID()
                        .equals(rerankChunk.getKbID())).findAny()
                .orElseThrow(() -> new BusinessExecutionException("researchChunk不存在")).setScore(rerankChunk.getScore()))
            .toList();
        return newChunkList;
    }
}
