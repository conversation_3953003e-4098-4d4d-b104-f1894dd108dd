package com.polarizon.rag.plugin.common.service.chat;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.rag.chat.*;
import com.polarizon.rag.kb.KnowledgeFileDO;
import com.polarizon.rag.kb.SplitStatusEnum;
import com.polarizon.rag.kb.params.KnowledgeFileDOParams;
import com.polarizon.rag.plugin.common.bean.dto.NewResultDTO;
import com.polarizon.rag.plugin.common.bean.dto.RagChatStreamReq;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatHistory;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatParam;
import com.polarizon.rag.plugin.common.bean.dto.chat.ChatRes;
import com.polarizon.rag.plugin.common.service.ConversationConfigService;
import com.polarizon.rag.plugin.common.service.RetrievalConfigService;
import com.polarizon.rag.plugin.common.util.Constants;
import com.polarizon.rag.plugin.common.util.FeignUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.polarizon.rag.plugin.common.configs.TraceIdFilter.TRACE_ID;
import static com.polarizon.rag.plugin.common.util.Constants.MESSAGE_CHANGE;

@Service
@Slf4j
@Getter
public class ChatService {


    @NotNull
    public Flux<ServerSentEvent<String>> chatStream(String userAccount, ChatParam param, ConversationDO conversationDO) {
        // 获取知识库列表
        List<String> kbIdList = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getKbSearchConfig)
                .map(KBSearchConfig::getKbIDList).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList());

        // 获取临时知识库列表
        List<String> temkbIdList = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getKbSearchConfig)
                .map(KBSearchConfig::getTmpKbIDList).filter(CollectionUtils::isNotEmpty).orElse(Lists.newArrayList());

        // 取知识库和临时知识库ID并集（仅用于后续逻辑，文件解析状态检查只针对temkbIdList）
        List<String> combinedList = new ArrayList<>(new LinkedHashSet<>(Stream.concat(kbIdList.stream(), temkbIdList.stream()).collect(Collectors.toSet())));

        // 保存问题
        MessageDO saveQuestionMessage = ChatHelper.saveQuestionMessage(userAccount, param.getQuestion(), conversationDO, combinedList);

        // 生成问题，方便前端及时渲染
        String traceId = MDC.get(TRACE_ID);
        ChatRes chatRes = new ChatRes();
        chatRes.setMessages(Lists.newArrayList(saveQuestionMessage));

        return Flux.create(sink -> {
            // 立即发送初始消息
            ChatHelper.doSinkNext(chatRes, sink, MESSAGE_CHANGE, traceId);

            // 创建一个新的Flux来处理后续操作
            Flux<ServerSentEvent<String>> processingFlux = Flux.defer(() -> {
                MDC.put(TRACE_ID, traceId);

                // 知识库列表为空、非联网检索、必须有参考依据
                boolean isNotWebSearch = Optional.ofNullable(conversationDO.getRetrievalConfig()).map(RetrievalConfig::getWebSearchConfig)
                        .map(WebSearchConfig::getIsSearchWeb).map(BooleanUtils::isFalse).orElse(true);
                boolean mustContainsReference = Optional.ofNullable(conversationDO.getConversationConfig()).map(ConversationConfig::getAnswerMode)
                        .map(AnswerModeEnum.MUST_CONTAINS_REFERENCE::equals).orElse(true);
                boolean existingContentReference = CollectionUtils.isEmpty(kbIdList) && isNotWebSearch && mustContainsReference;

                // 只要临时知识库列表不为空，就检查文件解析状态
                if (CollectionUtils.isNotEmpty(temkbIdList)) {
                    KnowledgeFileDOParams kbFileParams = KnowledgeFileDOParams.builder().knowledgeBaseID(temkbIdList).build();

                    // 检查文件解析状态，最多等待3分钟
                    long startTime = System.currentTimeMillis();
                    long timeout = 3 * 60 * 1000; // 3分钟超时

                    while (true) {
                        try {
                            List<KnowledgeFileDO> files = FeignUtil.getKnowledgeFileApi().conditions(null, null, kbFileParams).getData();

                            // 检查是否所有文件都解析失败并且没有其他参考
                            boolean allFilesFailed = CollectionUtils.isNotEmpty(files) && files.stream().allMatch(item -> item.getStatus().equals(SplitStatusEnum.FAILED));
                            if (allFilesFailed && existingContentReference) {
                                return handleFileParseFailure(userAccount, param, conversationDO, kbIdList);
                            }

                            // 检查是否所有文件都处理完成
                            boolean allFilesProcessed = files.stream().noneMatch(item -> item.getStatus().equals(SplitStatusEnum.INIT) || item.getStatus().equals(SplitStatusEnum.RUNNING));
                            if (allFilesProcessed) {
                                break;
                            }

                            // 检查是否超时
                            if (System.currentTimeMillis() - startTime > timeout) {
                                return handleTimeout(userAccount, param, conversationDO, kbIdList);
                            }

                            // 等待5秒后重试
                            Thread.sleep(5000);
                        } catch (Exception e) {
                            log.error("Failed to check knowledge base file status: {}", e.getMessage(), e);
                            return handleTimeout(userAccount, param, conversationDO, kbIdList);
                        }
                    }
                }

                // 执行聊天逻辑
                return proceedWithChat(userAccount, param, conversationDO, kbIdList, temkbIdList, saveQuestionMessage, traceId);
            });

            // 订阅处理Flux并将结果发送到sink
            processingFlux.subscribeOn(Schedulers.boundedElastic()).subscribe(
                    sink::next,
                    sink::error,
                    sink::complete
            );
        });
    }

    /**
     * 处理文件解析失败情况
     *
     * @param userAccount    用户账号
     * @param param          聊天参数
     * @param conversationDO 会话信息
     * @param kbIdList       知识库ID列表
     * @return SSE消息流
     */
    private Flux<ServerSentEvent<String>> handleFileParseFailure(String userAccount, ChatParam param,
                                                                 ConversationDO conversationDO, List<String> kbIdList) {
        // 保存问题消息
        MessageDO messageDO = ChatHelper.saveQuestionMessage(userAccount, param.getQuestion(), conversationDO, kbIdList);

        // 构建解析失败响应
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageDO.getId());
        chatRes.setQuestion(param.getQuestion());
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setResponse(Constants.FILE_PARSE_FAILURE_ANSWER);
        chatRes.setSource(Lists.newArrayList());
        chatRes.setStyle(Constants.MESSAGE_TYPE_ERROR);

        // 保存答案消息
        ChatHelper.saveAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), chatRes.getResponse(), null);

        // 返回SSE消息
        return Flux.just(ServerSentEvent.<String>builder()
                .event(Constants.COMPLETE_ANSWER)
                .data(JSON.toJSONString(NewResultDTO.ok(ResultDTO.Constants.SUCCESSFUL, "success", chatRes, MDC.get(TRACE_ID))))
                .build());
    }

    /**
     * 处理超时情况
     *
     * @param userAccount    用户账号
     * @param param          聊天参数
     * @param conversationDO 会话信息
     * @param kbIdList       知识库ID列表
     * @return SSE消息流
     */
    private Flux<ServerSentEvent<String>> handleTimeout(String userAccount, ChatParam param,
                                                        ConversationDO conversationDO, List<String> kbIdList) {
        // 保存问题消息
        MessageDO messageDO = ChatHelper.saveQuestionMessage(userAccount, param.getQuestion(), conversationDO, kbIdList);

        // 构建超时响应
        ChatRes chatRes = new ChatRes();
        chatRes.setId(messageDO.getId());
        chatRes.setQuestion(param.getQuestion());
        chatRes.setQuestionTime(System.currentTimeMillis());
        chatRes.setResponse("文件解析超时，请稍后重试");
        chatRes.setSource(Lists.newArrayList());
        chatRes.setStyle(Constants.MESSAGE_TYPE_ERROR);

        // 保存答案消息
        ChatHelper.saveAnswerMessage(conversationDO, kbIdList, Constants.MESSAGE_TYPE_ERROR, userAccount, Lists.newArrayList(), chatRes.getResponse(), null);

        // 返回SSE消息
        return Flux.just(ServerSentEvent.<String>builder()
                .event(Constants.ERROR_ANSWER)
                .data(JSON.toJSONString(NewResultDTO.error(ResultDTO.Constants.FAILURE, "文件解析超时，请稍后重试", chatRes, MDC.get(TRACE_ID))))
                .build());
    }

    // 抽取后续逻辑为单独方法，避免代码重复
    private Flux<ServerSentEvent<String>> proceedWithChat(String userAccount, ChatParam param, ConversationDO conversationDO,
                                                          List<String> kbIdList, List<String> temkbIdList, MessageDO saveQuestionMessage, String traceId) {
        // 获取对话配置、检索配置、用户提问
        ConversationConfig conversationConfig = ConversationConfigService.checkConversationConfig(conversationDO);
        RetrievalConfig retrievalConfig = Objects.isNull(conversationDO.getRetrievalConfig()) ? RetrievalConfigService.defaultRetrievalConfig(kbIdList, temkbIdList, conversationDO) : conversationDO.getRetrievalConfig();
        String question = param.getQuestion();
        Boolean stream = Objects.requireNonNullElse(param.getStream(), true);
        List<ChatHistory> history = CollectionUtils.isNotEmpty(param.getHistory()) ? param.getHistory() : Lists.newArrayList();

        // 历史消息去除sup标签避免干扰大模型
        history.forEach(item -> item.setContent(item.getContent().replaceAll("<sup>.*?</sup>", "")));

        // 请求参数配置
        RagChatStreamReq ragChatStreamReq = RagChatStreamReq.builder().conversationConfig(conversationConfig).retrievalConfig(retrievalConfig).question(question).stream(stream).history(history).messageId(saveQuestionMessage.getId()).build();

        // 获取大模型结果
        final AtomicReference<EventSource> realEventSourceAtomicRef = new AtomicReference<>();
        Flux<ServerSentEvent<String>> sseFlux = Flux.create(sink -> {
            EventSource realEventSource = ChatHelper.queryAgent(userAccount, ragChatStreamReq, traceId, kbIdList, conversationDO, sink);
            realEventSourceAtomicRef.set(realEventSource);
        });

        // 后处理
        return sseFlux.doOnComplete(() -> {
                    MDC.put(TRACE_ID, traceId);
                    log.debug("sseDoOnComplete");

                    // TODO 临时屏蔽 保存答案监测数据
                    // chatCommonService.saveAnswerTrace(param);
                })
                .doOnCancel(() -> {
                    MDC.put(TRACE_ID, traceId);
                    log.debug("sseDoOnCancel");
                    ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                })
                .onErrorResume(e -> {
                    MDC.put(TRACE_ID, traceId);
                    log.warn("onErrorResume: {}", e.getMessage(), e);
                    ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                    // TODO 临时屏蔽 保存答案监测数据
                    // chatCommonService.saveAnswerTrace(param);
                    return ChatHelper.buildErrorRespData(e);
                })
                .doOnTerminate(() -> {
                    MDC.put(TRACE_ID, traceId);
                    log.debug("sseDoOnTerminate");
                    ChatHelper.cancelRealEventSource(realEventSourceAtomicRef);
                });
    }

}

