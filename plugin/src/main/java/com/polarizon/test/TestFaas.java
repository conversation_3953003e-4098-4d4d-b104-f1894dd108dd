package com.polarizon.test;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@Slf4j
@Component
//@Validated
@RestController
public class TestFaas {
    @PostMapping(path = "/test")
    public String test() {
        return "aa";
    }

    @PostMapping(path = "/a")
    public String a() {
        return "aa";
    }
}
