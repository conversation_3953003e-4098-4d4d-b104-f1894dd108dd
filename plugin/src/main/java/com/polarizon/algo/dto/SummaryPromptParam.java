package com.polarizon.algo.dto;


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class SummaryPromptParam {
    private String previousSummaries;
    private String text;

    /**
     * 通过 previousSummaryList 设置 previousSummaries 字段
     * @param previousSummaryList
     */
    public SummaryPromptParam setPreviousSummaries(List<String> previousSummaryList) {
        this.previousSummaries = StringUtils.join(previousSummaryList, "\n");
        return this;
    }
}
