package com.polarizon.algo.dto.req;

import com.polarizon.algo.dto.resp.KnowledgeResult;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
@NoArgsConstructor
public class KnowledgeSingleChoiceGroupRequest {
    @NotEmpty
    private List<Knowledge> knowledges;
}
