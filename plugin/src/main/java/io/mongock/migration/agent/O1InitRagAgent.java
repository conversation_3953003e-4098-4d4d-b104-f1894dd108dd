package io.mongock.migration.agent;

import com.polarizon.gendo.common.config.namespace.NamespaceProvider;
import com.polarizon.rag.plugin.config.InitDataLoader;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;
import io.mongock.migration.MongoHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.io.IOException;

import static com.polarizon.rag.plugin.bean.constant.AgentConstants.RAG_COMMON_AGENT_FILE_NAME;

/**
 * wps 基本 初始化指令
 */
@Slf4j
@AllArgsConstructor
@ChangeUnit(id = "O1InitRagAgent", order = "0", runAlways = true)
public class O1InitRagAgent {
    private static final String JOB_NAME = "[rag智能问答] agent ";
    private final MongoTemplate mongoTemplate;
    private final NamespaceProvider namespaceProvider;
    private final InitDataLoader documentInstructLoader;

    /**
     * This is the method with the migration code
     **/
    @Execution
    public void changeSet() throws IOException {
        MongoHelper mongoHelper = new MongoHelper(mongoTemplate, namespaceProvider, documentInstructLoader);
        mongoHelper.initAgent(JOB_NAME, RAG_COMMON_AGENT_FILE_NAME);
    }


    @RollbackExecution
    public void rollback() {
    }
}
