project:
  namespace: 'com.polarizon.gendo3'
  name: 'ml-ops'
  description: 'Gendo3: MLOPS 数据管理微服务'
  site-home: 'https://www.polarizon.com'
  organization: '天海宸光 Polarizon'
  port: '20231'
  plugin: 'true'
  resources:
    limits:
      cpu: "4"
      memory: "4Gi"
    requests:
      cpu: "1"
      memory: "3Gi"
      
config:
  registry: polarizon-gendo-v3

hooks:
  before:
    - "{templatesDir}/scripts/version-before.sh"
  after:
    - "{templatesDir}/scripts/version-after.sh"
autocode:
  type: 'micro-service'
  output: 'target/ml-ops'
  templates: 'template/java-data-service'
  avro:
    - 'schema/commonenum.avsc'
    - 'schema/datafile.avsc'
    - 'schema/datasetgroup.avsc'
    - 'schema/datasetversion.avsc'
    - 'schema/labeltask.avsc'
    - 'schema/labeldata.avsc'
    - 'schema/labeljob.avsc'
    - 'schema/define_tags.avsc'
    - 'schema/define_attributes.avsc'
    - 'schema/define_values.avsc'
    - 'schema/project.avsc'
    - 'schema/workflow.avsc'
    - 'schema/algorithm.avsc'
    - 'schema/trainingjob.avsc'
    - 'schema/selectdatasetversion.avsc'
    - 'schema/evaluationjob.avsc'
    - 'schema/evaluationreport.avsc'
    - 'schema/detectionsample.avsc'
    - 'schema/verificationjob.avsc'
    - 'schema/workbench.avsc'
    - 'schema/image.avsc'
    - 'schema/s3File.avsc'
    - 'schema/detail.avsc'
    - 'schema/pr.avsc'
    - 'schema/box.avsc'
    - 'schema/collecttask.avsc'
    - 'schema/cleansingtask.avsc'
    - 'schema/enhancetask.avsc'
    - 'schema/importtask.avsc'
    - 'schema/exporttask.avsc'
    - 'schema/exportannotation.avsc'
    - 'schema/importerror.avsc'
    - 'schema/tailoring.avsc'
    - 'schema/datasource.avsc'
    - 'schema/point.avsc'
    - 'schema/labelconversiontask.avsc'
    - 'schema/annotation_attribute.avsc'
    - 'schema/annotation_shape.avsc'
    - 'schema/annotation_track.avsc'
    - 'schema/annotation_label.avsc'
    - 'schema/annotation_strackshape.avsc'
    - 'schema/hyperparam.avsc'
    - 'schema/gpuinfo.avsc'
    - 'schema/faastask.avsc'
    - 'schema/system_log.avsc'
    - 'schema/gpu_schedule.avsc'
    - 'schema/asset/gpu_asset.avsc'
    - 'schema/asset/asset_grade.avsc'
    - 'schema/asset/grade_quota.avsc'
    - 'schema/asset/asset_quota.avsc'
    - 'schema/asset/asset_process.avsc'
    - 'schema/asset/user_grade.avsc'
    - 'schema/asset/quota_record.avsc'
    - 'schema/temp/quota_queue_task.avsc'
    - 'schema/temp/delete_file.avsc'
    - 'schema/mlops/labeldata_count.avsc'
    - 'schema/mlops/gpu_usage.avsc'
