project:
  namespace: 'com.polarizon.gendo.gateway'
  name: 'graphql-gateway'
  version: '1.3.4-0'
  description: 'AutoCode: GraphQL聚合网关微服务'
  site-home: 'https://www.polarizon.com'
  organization: '天海宸光 Polarizon'
  port: '20230'

config:
  registry: polarizon-gendo-v3
  
hooks:
  before:
    - "{templatesDir}/scripts/version-before.sh"
  after:
    - "{templatesDir}/scripts/version-after.sh"
autocode:
  output: 'target/graphql'
  templates: 'template/graphql-gateway'
  remote-client:
    opa:
      addr: 'http://localhost:8181'
      path: '/v1/data'
    userapi:
      addr: 'http://**************:40002'
      path: '/user-authority/v1'
    deviceapi:
      addr: 'http://**************:40007'
      path: '/device-configuration/v1'
    mlopsapi:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'  
    cleansingtask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    collecttask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    enhancetask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    importtask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    exporttask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    importerrorrecord:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    tag:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    attributedefinition:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    attributevalue:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    datasetgroup:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    datafile:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    datasetversion:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'  
    labeleddata:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    labeltask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    labeljob:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    algorithm:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    image:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    workbench:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    evaluationjob:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    evaluationreport:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    detectionsample:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    verificationjob:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    trainingjob:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    project:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    workflow:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'  
    systemlog:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'    
    labelconversiontask:
      addr: 'http://localhost:20231'
      path: '/ml-ops/v1'
    arrangemodel:
      addr: 'http://localhost:20235'
      path: '/algo-arrange/v1'
    modelnode:
      addr: 'http://localhost:20235'
      path: '/algo-arrange/v1'
    operator:
      addr: 'http://localhost:20235'
      path: '/algo-arrange/v1'
    parammould:
      addr: 'http://localhost:20235'
      path: '/algo-arrange/v1'
    warehousealgorithm:
      addr: 'http://localhost:20232'
      path: '/algo-store/v1'
    supplier:
      addr: 'http://localhost:20232'
      path: '/algo-store/v1'
    applicationalgorithm:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'
    eventconfig:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'
    eventtask:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'
    eventresult:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'
    detecttime:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'
    seat:
      addr: 'http://localhost:20234'
      path: '/ai-app/v1'  
  avro:
    - 'schema/custom/user.avsc'
    - 'schema/custom/areas.avsc'
    - 'schema/custom/device.avsc'
    - 'schema/commonenum.avsc'
    - 'schema/datafile.avsc'
    - 'schema/datasetgroup.avsc'
    - 'schema/datasetversion.avsc'
    - 'schema/labeltask.avsc'
    - 'schema/labeldata.avsc'
    - 'schema/labeljob.avsc'
    - 'schema/define_tags.avsc'
    - 'schema/define_attributes.avsc'
    - 'schema/define_values.avsc'
    - 'schema/project.avsc'
    - 'schema/workflow.avsc'
    - 'schema/algorithm.avsc'
    - 'schema/trainingjob.avsc'
    - 'schema/selectdatasetversion.avsc'
    - 'schema/evaluationjob.avsc'
    - 'schema/evaluationreport.avsc'
    - 'schema/detectionsample.avsc'
    - 'schema/verificationjob.avsc'
    - 'schema/workbench.avsc'
    - 'schema/image.avsc'
    - 'schema/s3File.avsc'
    - 'schema/detail.avsc'
    - 'schema/pr.avsc'
    - 'schema/box.avsc'
    - 'schema/collecttask.avsc'
    - 'schema/cleansingtask.avsc'
    - 'schema/enhancetask.avsc'
    - 'schema/importtask.avsc'
    - 'schema/exporttask.avsc'
    - 'schema/exportannotation.avsc'
    - 'schema/importerror.avsc'
    - 'schema/tailoring.avsc'
    - 'schema/datasource.avsc'
    - 'schema/point.avsc'
    - 'schema/labelconversiontask.avsc'
    - 'schema/annotation_attribute.avsc'
    - 'schema/annotation_shape.avsc'
    - 'schema/annotation_track.avsc'
    - 'schema/annotation_label.avsc'
    - 'schema/annotation_strackshape.avsc'
    - 'schema/hyperparam.avsc'
    - 'schema/arrange/arrangemodel.avsc'
    - 'schema/arrange/commonenum.avsc'
    - 'schema/arrange/modelnode.avsc'
    - 'schema/arrange/nodebody.avsc'
    - 'schema/arrange/operator.avsc'
    - 'schema/arrange/parammould.avsc'
    - 'schema/gpuinfo.avsc'
    - 'schema/faastask.avsc'
    - 'schema/system_log.avsc'
    - 'schema/store/warehouse_algorithm.avsc'
    - 'schema/store/supplier.avsc'
    - 'schema/store/warehouse_algorithm_enums.avsc'
    - 'schema/app/application_algorithm.avsc'
    - 'schema/app/event_task.avsc'
    - 'schema/app/detect_time.avsc'
    - 'schema/app/event_config.avsc'
    - 'schema/app/line.avsc'
    - 'schema/app/event.avsc'
    - 'schema/app/extra_data.avsc'
    - 'schema/app/box_count.avsc'
    - 'schema/app/seat.avsc'
    - 'schema/gpu_schedule.avsc'
