spring:
  data:
    mongodb:
      # 使用嵌入式MongoDB进行测试
      port: 27017
      database: test-authority
      # 禁用自动配置
      auto-index-creation: false
  
  # 测试环境配置
  test:
    # 允许测试上下文bean定义覆盖
    allow-bean-definition-overriding: true

# 日志配置
logging:
  level:
    org.springframework.data.mongodb: DEBUG
    com.polarizon.gendo.sg.authority: DEBUG

#自定义配置
com:
  polarizon:
    feign:
      user-api-url: http://192.168.20.230:32080/workbench
      device-api-url: ${DEVICE_API_URL:http://192.168.20.230:32080/workbench}
      push-message-api-url: ${PUSH_MESSAGE_API_URL:http://192.168.20.230:32080/workbench}
      constant-collection-api-url: ${CONSTANT_COLLECTION_API_URL:http://192.168.20.230:32080/rag-v2}