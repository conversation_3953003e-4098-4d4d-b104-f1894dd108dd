{"roles": {"superAdmin": {"description": "超级管理员", "resourcePermissions": {"KnowledgeBase": ["read", "create", "update", "delete"], "KnowledgeFile": ["read", "create", "update", "delete"]}}, "admin": {"description": "管理员", "resourcePermissions": {"KnowledgeBase": ["read", "create", "update", "delete"], "KnowledgeFile": ["read", "create", "update", "delete"]}}, "internalUser": {"description": "内部用户", "resourcePermissions": {"KnowledgeBase": ["read", "create", "update", "delete"], "KnowledgeFile": ["read", "create", "update", "delete"]}}, "externalUser": {"description": "外部用户", "resourcePermissions": {"KnowledgeBase": ["read", "create", "update", "delete"], "KnowledgeFile": ["read", "create", "update", "delete"]}}}, "resources": {"KnowledgeBase": {"type": "MONGO", "collection": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "operations": {"read": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"tenantId": "${x-tenant-id}"}, "externalUser": {"tenantId": "${x-tenant-id}"}, "default": {"tenantId": "${x-tenant-id}"}}}, "create": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"isPublic": false, "tenantId": "${x-tenant-id}"}, "externalUser": {"isPublic": false, "tenantId": "${x-tenant-id}"}, "default": {"tenantId": "${x-tenant-id}"}}}, "update": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"isPublic": false, "tenantId": "${x-tenant-id}", "createdBy": "${x-user-id}"}, "externalUser": {"isPublic": false, "tenantId": "${x-tenant-id}", "createdBy": "${x-user-id}"}, "default": {"tenantId": "${x-tenant-id}"}}}, "delete": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"isPublic": false, "tenantId": "${x-tenant-id}", "createdBy": "${x-user-id}"}, "externalUser": {"isPublic": false, "tenantId": "${x-tenant-id}", "createdBy": "${x-user-id}"}, "default": {"tenantId": "${x-tenant-id}"}}}}}, "KnowledgeFile": {"type": "MONGO", "collection": "default_com.polarizon.rag.kb.KnowledgeFileDO", "operations": {"read": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$or": [{"$and": [{"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}, {"$eq": ["$$kb.isPublic", true]}]}]}}}}, "externalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}}}}, "default": null}}, "create": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$or": [{"$and": [{"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}, {"$and": [{"$eq": ["$$kb.isPublic", true]}, {"$or": [{"$eq": ["$role", "superAdmin"]}, {"$eq": ["$role", "admin"]}]}]}]}]}}}}, "externalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}}}}, "default": null}}, "update": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}}}}, "externalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}, {"$eq": ["$createdBy", "${x-user-id}"]}]}}}}, "default": null}}, "delete": {"dataRules": {"superAdmin": null, "admin": null, "internalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]}}}}, "externalUser": {"$expr": {"$let": {"vars": {"kb": {"$arrayElemAt": [{"$lookup": {"from": "default_com.polarizon.rag.kb.KnowledgeBaseDO", "localField": "knowledgeBaseId", "foreignField": "_id", "as": "kb"}}, 0]}}, "in": {"$and": [{"$eq": ["$$kb.tenantId", "${x-tenant-id}"]}, {"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}, {"$eq": ["$createdBy", "${x-user-id}"]}]}}}}, "default": null}}}}}}