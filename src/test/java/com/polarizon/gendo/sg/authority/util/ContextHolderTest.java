package com.polarizon.gendo.sg.authority.util;

import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.model.UserContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * ContextHolder单元测试
 */
class ContextHolderTest {

    @AfterEach
    void tearDown() {
        // 每个测试后清理上下文
        ContextHolder.clearCurrentUser();
    }

    @Test
    void testSetAndGetCurrentUser() {
        // 创建测试用户上下文
        UserContext userContext = UserContext.builder()
            .userId("test-user-123")
            .account("testuser")
            .tenantId("tenant-123")
            .roleEntities(Collections.singletonList(new RoleEntityExtend("1", null, "管理员", "admin", true, true)))
            .build();

        // 设置上下文
        ContextHolder.setCurrentUser(userContext);

        // 验证能否正确获取
        UserContext retrievedContext = ContextHolder.getCurrentUser();
        assertNotNull(retrievedContext);
        assertEquals("test-user-123", retrievedContext.getUserId());
        assertEquals("testuser", retrievedContext.getAccount());
        assertEquals("tenant-123", retrievedContext.getTenantId());
        assertEquals(1, retrievedContext.getRoleEntities().size());
        assertEquals("admin", retrievedContext.getRoleEntities().get(0).getAbbreviation());
    }

    @Test
    void testClearCurrentUser() {
        // 创建测试用户上下文
        UserContext userContext = UserContext.builder()
            .userId("test-user-123")
            .build();

        // 设置上下文
        ContextHolder.setCurrentUser(userContext);

        // 验证设置成功
        assertNotNull(ContextHolder.getCurrentUser());

        // 清理上下文
        ContextHolder.clearCurrentUser();

        // 验证清理成功
        assertNull(ContextHolder.getCurrentUser());
    }

    @Test
    void testThreadIsolation() throws InterruptedException {
        // 创建主线程用户上下文
        UserContext mainThreadContext = UserContext.builder()
            .userId("main-thread-user")
            .build();

        // 设置主线程上下文
        ContextHolder.setCurrentUser(mainThreadContext);

        // 用于等待子线程完成的计数器
        CountDownLatch latch = new CountDownLatch(1);

        // 用于从子线程获取结果的引用，分别存储初始和最终上下文
        AtomicReference<UserContext> childThreadInitialContext = new AtomicReference<>();
        AtomicReference<UserContext> childThreadFinalContext = new AtomicReference<>();

        // 在子线程中验证上下文隔离
        Thread thread = new Thread(() -> {
            try {
                // 子线程应该没有上下文
                childThreadInitialContext.set(ContextHolder.getCurrentUser());

                // 创建子线程用户上下文
                UserContext childThreadContext = UserContext.builder()
                    .userId("child-thread-user")
                    .build();

                // 设置子线程上下文
                ContextHolder.setCurrentUser(childThreadContext);

                // 再次获取并存储上下文以供验证
                childThreadFinalContext.set(ContextHolder.getCurrentUser());
            } finally {
                latch.countDown();
            }
        });

        thread.start();
        latch.await(); // 等待子线程完成

        // 验证子线程初始没有上下文
        assertNull(childThreadInitialContext.get());

        // 验证子线程最终设置了正确的上下文
        assertNotNull(childThreadFinalContext.get());
        assertEquals("child-thread-user", childThreadFinalContext.get().getUserId());

        // 验证主线程上下文未受影响
        UserContext retrievedContext = ContextHolder.getCurrentUser();
        assertNotNull(retrievedContext);
        assertEquals("main-thread-user", retrievedContext.getUserId());
    }

    @Test
    void testNullContext() {
        // 初始状态应为null
        assertNull(ContextHolder.getCurrentUser());

        // 设置null值
        ContextHolder.setCurrentUser(null);

        // 应仍然返回null
        assertNull(ContextHolder.getCurrentUser());
    }
} 