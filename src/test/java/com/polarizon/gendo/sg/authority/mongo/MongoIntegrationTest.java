package com.polarizon.gendo.sg.authority.mongo;

import com.polarizon.gendo.sg.authority.config.FeignTestConfig;
import com.polarizon.gendo.sg.authority.config.MongoTestConfig;
import org.bson.Document;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Date;
import java.util.List;

/**
 * MongoDB集成测试基类
 * <p>
 * 提供MongoDB测试环境和通用的测试方法，所有需要MongoDB的测试类可以继承此类
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {MongoIntegrationTest.class}, properties = "spring.cloud.openfeign.enabled=false")
@Import({MongoTestConfig.class, FeignTestConfig.class})
@ActiveProfiles("test")
public abstract class MongoIntegrationTest {

    @Autowired
    protected MongoTemplate mongoTemplate;
    
    /**
     * 测试前清空所有集合
     */
    @BeforeEach
    void setUpMongoCollections() {
        cleanCollections();
    }
    
    /**
     * 测试后清空所有集合
     */
    @AfterEach
    void tearDownMongoCollections() {
        cleanCollections();
    }
    
    /**
     * 清空所有测试集合
     */
    protected void cleanCollections() {
        mongoTemplate.getCollectionNames().forEach(collectionName -> 
            mongoTemplate.getCollection(collectionName).drop()
        );
    }
    
    /**
     * 插入测试文档到指定集合
     * 
     * @param document 文档对象
     * @param collectionName 集合名称
     */
    protected void insertDocument(Document document, String collectionName) {
        mongoTemplate.insert(document, collectionName);
    }
    
    /**
     * 获取指定集合的所有文档
     * 
     * @param collectionName 集合名称
     * @return 文档列表
     */
    protected List<Document> findAllDocuments(String collectionName) {
        return mongoTemplate.findAll(Document.class, collectionName);
    }
    
    /**
     * 创建知识库测试文档
     * 
     * @param id 文档ID
     * @param name 知识库名称
     * @param tenantId 租户ID
     * @param createdBy 创建者ID
     * @param isPublic 是否公开
     * @return 知识库文档
     */
    protected Document createKnowledgeBaseDocument(String id, String name, String tenantId, String createdBy, boolean isPublic) {
        Document document = new Document();
        document.append("_id", id);
        document.append("name", name);
        document.append("tenantId", tenantId);
        document.append("createdBy", createdBy);
        document.append("isPublic", isPublic);
        document.append("createTime", new Date());
        document.append("updateTime", new Date());
        return document;
    }
    
    /**
     * 创建知识库文件测试文档
     * 
     * @param id 文档ID
     * @param knowledgeBaseId 知识库ID
     * @param fileName 文件名
     * @param tenantId 租户ID
     * @param createdBy 创建者ID
     * @return 知识库文件文档
     */
    protected Document createKnowledgeFileDocument(String id, String knowledgeBaseId, String fileName, String tenantId, String createdBy) {
        Document document = new Document();
        document.append("_id", id);
        document.append("knowledgeBaseId", knowledgeBaseId);
        document.append("fileName", fileName);
        document.append("tenantId", tenantId);
        document.append("createdBy", createdBy);
        document.append("createTime", new Date());
        document.append("updateTime", new Date());
        return document;
    }
} 