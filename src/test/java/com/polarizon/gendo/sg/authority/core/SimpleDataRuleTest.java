package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.Document;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单测试类，验证JSON到Document的转换
 */
public class SimpleDataRuleTest {

    @Test
    public void testJacksonToDocumentConversion() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        String jsonArray = """
            [
                {
                    "$lookup": {
                        "from": "knowledgeBase",
                        "localField": "knowledgeBaseId",
                        "foreignField": "_id",
                        "as": "kb"
                    }
                },
                {
                    "$unwind": "$kb"
                },
                {
                    "$match": {
                        "$and": [
                            {"kb.isPublic": false},
                            {"kb.createdBy": "testUserId"}
                        ]
                    }
                }
            ]
            """;

        // 使用我们修复后的逻辑
        @SuppressWarnings("unchecked")
        List<Object> rawPipeline = objectMapper.readValue(jsonArray, new TypeReference<List<Object>>() {});

        List<Document> pipeline = new ArrayList<>();
        for (Object stage : rawPipeline) {
            if (stage instanceof Map<?, ?> map) {
                // 将Map转换为Document
                Document stageDoc = new Document();
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    if (entry.getKey() instanceof String key) {
                        stageDoc.put(key, convertToDocumentRecursively(entry.getValue()));
                    }
                }
                pipeline.add(stageDoc);
            } else if (stage instanceof Document doc) {
                pipeline.add(doc);
            }
        }

        // 验证转换结果
        assertEquals(3, pipeline.size());

        // 验证第一个阶段
        Document lookupStage = pipeline.get(0);
        assertTrue(lookupStage.containsKey("$lookup"));
        assertTrue(lookupStage.get("$lookup") instanceof Document);

        // 验证第三个阶段（包含嵌套结构）
        Document matchStage = pipeline.get(2);
        assertTrue(matchStage.containsKey("$match"));
        Document matchExpr = (Document) matchStage.get("$match");
        assertTrue(matchExpr.containsKey("$and"));

        @SuppressWarnings("unchecked")
        List<Document> andConditions = (List<Document>) matchExpr.get("$and");
        assertEquals(2, andConditions.size());
        assertEquals("testUserId", andConditions.get(1).get("kb.createdBy"));
    }

    /**
     * 递归地将对象转换为Document兼容的格式
     */
    @SuppressWarnings("unchecked")
    private Object convertToDocumentRecursively(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Map<?, ?> map) {
            // 将Map转换为Document
            Document document = new Document();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                if (entry.getKey() instanceof String key) {
                    document.put(key, convertToDocumentRecursively(entry.getValue()));
                }
            }
            return document;
        } else if (value instanceof List<?> list) {
            // 递归处理List中的元素
            List<Object> convertedList = new ArrayList<>();
            for (Object item : list) {
                convertedList.add(convertToDocumentRecursively(item));
            }
            return convertedList;
        } else {
            // 基本类型直接返回
            return value;
        }
    }
}