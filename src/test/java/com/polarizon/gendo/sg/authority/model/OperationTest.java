package com.polarizon.gendo.sg.authority.model;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Operation单元测试
 */
class OperationTest {

    @Test
    void testGetterAndSetter() {
        // 创建操作实例
        Operation operation = new Operation();
        
        // 初始状态，dataRules应该是空Map但不为null
        assertNotNull(operation.getDataRules());
        assertTrue(operation.getDataRules().isEmpty());
        
        // 添加数据规则
        Map<String, Object> adminRule = new HashMap<>();
        adminRule.put("status", "active");
        
        Map<String, Object> userRule = new HashMap<>();
        userRule.put("createdBy", "${x-user-id}");
        
        operation.getDataRules().put("admin", adminRule);
        operation.getDataRules().put("user", userRule);
        
        // 验证规则已添加
        assertEquals(2, operation.getDataRules().size());
        assertTrue(operation.getDataRules().containsKey("admin"));
        assertTrue(operation.getDataRules().containsKey("user"));
        
        // 验证规则内容
        assertEquals(adminRule, operation.getDataRules().get("admin"));
        assertEquals(userRule, operation.getDataRules().get("user"));
    }

    @Test
    void testSetDataRules() {
        // 创建操作实例
        Operation operation = new Operation();
        
        // 创建新的规则Map
        Map<String, Object> newRules = new HashMap<>();
        Map<String, Object> managerRule = new HashMap<>();
        managerRule.put("department", "HR");
        newRules.put("manager", managerRule);
        
        // 设置新规则
        operation.setDataRules(newRules);
        
        // 验证规则已更新
        assertEquals(1, operation.getDataRules().size());
        assertTrue(operation.getDataRules().containsKey("manager"));
        assertEquals(managerRule, operation.getDataRules().get("manager"));
    }

    @Test
    void testToString() {
        // 创建操作实例
        Operation operation = new Operation();
        
        // 添加数据规则
        Map<String, Object> adminRule = new HashMap<>();
        adminRule.put("status", "active");
        operation.getDataRules().put("admin", adminRule);
        
        // 验证toString包含关键信息
        String toString = operation.toString();
        assertTrue(toString.contains("dataRules"));
        assertTrue(toString.contains("admin"));
    }
} 