package com.polarizon.gendo.sg.authority.mongo;

import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.core.PermissionManager;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MongoPermissionAspect集成测试
 * <p>
 * 测试MongoDB权限切面在实际环境中的行为
 */
public class MongoPermissionAspectIntegrationTest extends MongoIntegrationTest {

    // 定义测试用户和租户ID，与MongoTestConfig中的测试数据对应
    private static final String ADMIN_USER_ID = "admin-001";
    private static final String SUPER_ADMIN_USER_ID = "super-admin-001";
    private static final String INTERNAL_USER_ID = "internal-001";
    private static final String EXTERNAL_USER_ID = "external-001";
    private static final String OTHER_USER_ID = "user-002";
    
    private static final String TEST_TENANT_ID = "tenant-001";
    private static final String OTHER_TENANT_ID = "tenant-002";
    
    private static final String KB_COLLECTION = "default_com.polarizon.rag.kb.KnowledgeBaseDO";
    private static final String KB_FILE_COLLECTION = "default_com.polarizon.rag.kb.KnowledgeFileDO";
    
    @Autowired
    private PermissionManager permissionManager;
    
    @Autowired
    private MongoConverter mongoConverter;
    
    private MongoPermissionAspect mongoPermissionAspect;
    
    @BeforeEach
    void setUp() {
        mongoPermissionAspect = new MongoPermissionAspect(permissionManager, mongoTemplate, mongoConverter);
        
        // 设置默认用户上下文
        setAdminUserContext();
    }
    
    private void setSuperAdminUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(SUPER_ADMIN_USER_ID)
                .account("superadmin")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "超级管理员", "superAdmin", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setAdminUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(ADMIN_USER_ID)
                .account("admin")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "管理员", "admin", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setInternalUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(INTERNAL_USER_ID)
                .account("internal")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "内部用户", "internalUser", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setExternalUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(EXTERNAL_USER_ID)
                .account("external")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "外部用户", "externalUser", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    @Test
    @DisplayName("测试转换对象到Document")
    void testConvertToDocument() throws Exception {
        // 创建测试对象
        TestEntity entity = new TestEntity();
        entity.setId("test-id-123");
        entity.setName("测试实体");
        entity.setActive(true);
        entity.setCount(42);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals("test-id-123", result.get("id"));
        assertEquals("测试实体", result.get("name"));
        assertEquals(true, result.get("active"));
        assertEquals(42, result.get("count"));
    }
    
    @Test
    @DisplayName("测试管理员查询知识库 - 无权限限制")
    void testAdminQueryKnowledgeBase() {
        // 设置管理员用户上下文
        setAdminUserContext();
        
        // 创建一个查询对象
        Query query = new Query();
        
        // 使用权限切面处理查询
        try {
            java.lang.reflect.Method method = MongoPermissionAspect.class.getDeclaredMethod("applyQueryFilter", Query.class, String.class);
            method.setAccessible(true);
            method.invoke(mongoPermissionAspect, query, KB_COLLECTION);
            
            // 执行查询
            List<Document> results = mongoTemplate.find(query, Document.class, KB_COLLECTION);
            
            // 验证查询结果 - 管理员应该能看到所有知识库
            assertEquals(8, results.size());
            
            // 验证查询条件 - 管理员查询应该没有额外限制
            assertTrue(query.getQueryObject().isEmpty() || !query.getQueryObject().containsKey("tenantId"));
        } catch (Exception e) {
            fail("执行方法失败: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("测试内部用户查询知识库 - 有租户限制")
    void testInternalUserQueryKnowledgeBase() {
        // 设置内部用户上下文
        setInternalUserContext();
        
        // 创建一个查询对象
        Query query = new Query();
        
        // 使用权限切面处理查询
        try {
            java.lang.reflect.Method method = MongoPermissionAspect.class.getDeclaredMethod("applyQueryFilter", Query.class, String.class);
            method.setAccessible(true);
            method.invoke(mongoPermissionAspect, query, KB_COLLECTION);
            
            // 执行查询
            List<Document> results = mongoTemplate.find(query, Document.class, KB_COLLECTION);
            
            // 验证查询结果 - 内部用户应该只能看到自己租户的知识库
            assertEquals(7, results.size());
            results.forEach(doc -> assertEquals(TEST_TENANT_ID, doc.get("tenantId")));
            
            // 验证查询条件 - 内部用户查询应该有租户限制
            Document queryObj = new Document(query.getQueryObject());
            assertTrue(queryObj.containsKey("tenantId"));
            assertEquals(TEST_TENANT_ID, queryObj.get("tenantId"));
        } catch (Exception e) {
            fail("执行方法失败: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("测试文档权限检查")
    void testCheckDocument() throws Exception {
        // 设置内部用户上下文
        setInternalUserContext();
        
        // 获取内部用户创建的私有知识库
        Document testDoc = mongoTemplate.findById("kb-internal-private-1", Document.class, KB_COLLECTION);
        assertNotNull(testDoc);
        
        // 使用反射调用私有方法检查文档权限
        java.lang.reflect.Method processMethod = MongoPermissionAspect.class.getDeclaredMethod("processQueryWithPermission", 
                org.aspectj.lang.ProceedingJoinPoint.class, String.class);
        processMethod.setAccessible(true);
        
        // 无法直接测试processQueryWithPermission，因为它需要ProceedingJoinPoint
        // 但我们可以测试convertToDocument和permissionManager.checkDocument
        
        // 测试直接调用permissionManager检查文档
        boolean hasReadPermission = permissionManager.checkDocument("KnowledgeBase", "read", testDoc);
        assertTrue(hasReadPermission, "内部用户应该有权读取自己租户的知识库");
        
        // 测试修改权限 - 应该只能修改自己创建的私有知识库
        boolean hasUpdatePermission = permissionManager.checkDocument("KnowledgeBase", "update", testDoc);
        assertTrue(hasUpdatePermission, "内部用户应该有权更新自己创建的私有知识库");
        
        // 获取其他用户创建的私有知识库
        Document otherUserDoc = mongoTemplate.findById("kb-admin-private-1", Document.class, KB_COLLECTION);
        assertNotNull(otherUserDoc);
        
        // 测试修改权限 - 不应该能修改其他用户创建的私有知识库
        boolean hasUpdatePermissionOther = permissionManager.checkDocument("KnowledgeBase", "update", otherUserDoc);
        assertFalse(hasUpdatePermissionOther, "内部用户不应该有权更新他人创建的知识库");
    }
    
    @Test
    @DisplayName("测试外部用户权限")
    void testExternalUserPermissions() {
        // 设置外部用户上下文
        setExternalUserContext();
        
        // 创建一个查询对象
        Query query = new Query();
        
        // 使用权限切面处理查询
        try {
            java.lang.reflect.Method method = MongoPermissionAspect.class.getDeclaredMethod("applyQueryFilter", Query.class, String.class);
            method.setAccessible(true);
            method.invoke(mongoPermissionAspect, query, KB_COLLECTION);
            
            // 执行查询
            List<Document> results = mongoTemplate.find(query, Document.class, KB_COLLECTION);
            
            // 验证查询结果 - 外部用户应该只能看到自己租户的知识库
            assertFalse(results.isEmpty());
            results.forEach(doc -> assertEquals(TEST_TENANT_ID, doc.get("tenantId")));
            
            // 获取外部用户创建的私有知识库
            Document externalDoc = mongoTemplate.findById("kb-external-private-1", Document.class, KB_COLLECTION);
            assertNotNull(externalDoc);
            
            // 测试修改权限 - 应该能修改自己创建的私有知识库
            boolean hasUpdatePermission = permissionManager.checkDocument("KnowledgeBase", "update", externalDoc);
            assertTrue(hasUpdatePermission, "外部用户应该有权更新自己创建的私有知识库");
            
            // 获取内部用户创建的私有知识库
            Document internalDoc = mongoTemplate.findById("kb-internal-private-1", Document.class, KB_COLLECTION);
            assertNotNull(internalDoc);
            
            // 测试修改权限 - 不应该能修改其他用户创建的私有知识库
            boolean hasUpdatePermissionOther = permissionManager.checkDocument("KnowledgeBase", "update", internalDoc);
            assertFalse(hasUpdatePermissionOther, "外部用户不应该有权更新他人创建的知识库");
        } catch (Exception e) {
            fail("执行方法失败: " + e.getMessage());
        }
    }
    
    // 测试实体类
    public static class TestEntity {
        private String id;
        private String name;
        private boolean active;
        private int count;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public boolean isActive() {
            return active;
        }
        
        public void setActive(boolean active) {
            this.active = active;
        }
        
        public int getCount() {
            return count;
        }
        
        public void setCount(int count) {
            this.count = count;
        }
    }
} 