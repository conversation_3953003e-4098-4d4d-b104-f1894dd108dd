package com.polarizon.gendo.sg.authority.model;

import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UserContext单元测试
 */
class UserContextTest {

    @Test
    void testBuilderAndGetters() {
        // 使用Builder创建UserContext
        UserContext userContext = UserContext.builder()
                .userId("test-user-123")
                .account("testuser")
                .tenantId("tenant-123")
                .roleEntities(Collections.singletonList(new RoleEntityExtend("1",null, "管理员", "admin", true, true)))
                .build();
        
        // 验证各字段值
        assertEquals("test-user-123", userContext.getUserId());
        assertEquals("testuser", userContext.getAccount());
        assertEquals("tenant-123", userContext.getTenantId());
        assertEquals(1, userContext.getRoleEntities().size());
        assertEquals("admin", userContext.getRoleEntities().get(0).getAbbreviation());
    }

    @Test
    void testGetPrimaryRole() {
        // 测试有多个角色时的主要角色
        UserContext userContext = UserContext.builder()
                .roleEntities(Arrays.asList(
                    new RoleEntityExtend("1",null, "管理员", "admin", true, true),
                    new RoleEntityExtend("1",null, "外部普通用户", "externalUser", true, true)
                ))
                .build();
        
        // 应返回第一个角色
        assertNotNull(userContext.getPrimaryRole());
        assertEquals("admin", userContext.getPrimaryRole().getAbbreviation());
    }

    @Test
    void testGetPrimaryRoleWithNoRoles() {
        // 测试没有角色时的主要角色
        UserContext userContext = UserContext.builder()
                .roleEntities(Collections.emptyList())
                .build();
        
        // 应返回null
        assertNull(userContext.getPrimaryRole());
    }

    @Test
    void testGetPrimaryRoleWithNullRoles() {
        // 测试角色列表为null时的主要角色
        UserContext userContext = UserContext.builder()
                .roleEntities(null)
                .build();
        
        // 应返回null
        assertNull(userContext.getPrimaryRole());
    }

    @Test
    void testDefaultEmptyCollections() {
        // 测试不设置集合时的默认值
        UserContext userContext = UserContext.builder()
                .userId("test-user")
                .build();
        
        // 应使用空集合作为默认值
        assertNotNull(userContext.getRoleEntities());
        assertTrue(userContext.getRoleEntities().isEmpty());
    }

    @Test
    void testToString() {
        // 测试toString方法
        UserContext userContext = UserContext.builder()
                .userId("test-user-123")
                .account("testuser")
                .build();
        
        String toString = userContext.toString();
        
        // 验证toString包含关键字段
        assertTrue(toString.contains("test-user-123"));
        assertTrue(toString.contains("testuser"));
    }
} 