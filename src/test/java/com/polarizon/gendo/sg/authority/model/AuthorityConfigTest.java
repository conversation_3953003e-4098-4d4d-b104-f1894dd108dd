package com.polarizon.gendo.sg.authority.model;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AuthorityConfig单元测试
 */
class AuthorityConfigTest {

    @Test
    void testGetterAndSetter() {
        // 创建AuthorityConfig实例
        AuthorityConfig config = new AuthorityConfig();
        
        // 初始状态，maps应该是空但不为null
        assertNotNull(config.getRoles());
        assertTrue(config.getRoles().isEmpty());
        assertNotNull(config.getResources());
        assertTrue(config.getResources().isEmpty());
        
        // 创建角色
        Role adminRole = new Role();
        adminRole.setDescription("管理员角色");
        adminRole.getResourcePermissions().put("users", Arrays.asList("read", "create", "update", "delete"));
        
        Role userRole = new Role();
        userRole.setDescription("普通用户角色");
        userRole.getResourcePermissions().put("documents", Arrays.asList("read", "create"));
        
        // 创建角色映射
        Map<String, Role> roles = new HashMap<>();
        roles.put("admin", adminRole);
        roles.put("user", userRole);
        
        // 设置角色
        config.setRoles(roles);
        
        // 验证角色已设置
        assertEquals(2, config.getRoles().size());
        assertTrue(config.getRoles().containsKey("admin"));
        assertTrue(config.getRoles().containsKey("user"));
        assertEquals(adminRole, config.getRoles().get("admin"));
        assertEquals(userRole, config.getRoles().get("user"));
        
        // 创建资源
        Resource userResource = new Resource();
        userResource.setType("MONGO");
        userResource.setCollection("users");
        
        Resource documentResource = new Resource();
        documentResource.setType("MONGO");
        documentResource.setCollection("documents");
        
        // 创建资源映射
        Map<String, Resource> resources = new HashMap<>();
        resources.put("users", userResource);
        resources.put("documents", documentResource);
        
        // 设置资源
        config.setResources(resources);
        
        // 验证资源已设置
        assertEquals(2, config.getResources().size());
        assertTrue(config.getResources().containsKey("users"));
        assertTrue(config.getResources().containsKey("documents"));
        assertEquals(userResource, config.getResources().get("users"));
        assertEquals(documentResource, config.getResources().get("documents"));
    }

    @Test
    void testAddRoleAndResource() {
        // 创建AuthorityConfig实例
        AuthorityConfig config = new AuthorityConfig();
        
        // 添加角色
        Role adminRole = new Role();
        adminRole.setDescription("管理员角色");
        config.getRoles().put("admin", adminRole);
        
        // 验证角色已添加
        assertEquals(1, config.getRoles().size());
        assertTrue(config.getRoles().containsKey("admin"));
        
        // 添加资源
        Resource userResource = new Resource();
        userResource.setType("MONGO");
        config.getResources().put("users", userResource);
        
        // 验证资源已添加
        assertEquals(1, config.getResources().size());
        assertTrue(config.getResources().containsKey("users"));
    }

    @Test
    void testToString() {
        // 创建AuthorityConfig实例
        AuthorityConfig config = new AuthorityConfig();
        
        // 添加角色和资源
        Role adminRole = new Role();
        adminRole.setDescription("管理员角色");
        config.getRoles().put("admin", adminRole);
        
        Resource userResource = new Resource();
        userResource.setType("MONGO");
        config.getResources().put("users", userResource);
        
        // 验证toString包含关键信息
        String toString = config.toString();
        assertTrue(toString.contains("roles"));
        assertTrue(toString.contains("resources"));
    }
} 