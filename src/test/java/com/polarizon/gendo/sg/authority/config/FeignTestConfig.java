package com.polarizon.gendo.sg.authority.config;

import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cloud.openfeign.FeignContext;
import org.springframework.context.annotation.Bean;

/**
 * Feign测试配置类
 * <p>
 * 提供测试环境所需的Feign相关bean
 */
@TestConfiguration
public class FeignTestConfig {

    /**
     * 提供FeignContext的模拟实现
     * 
     * @return FeignContext模拟对象
     */
    @Bean
    public FeignContext feignContext() {
        return Mockito.mock(FeignContext.class);
    }
} 