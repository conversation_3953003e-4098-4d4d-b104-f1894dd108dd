package com.polarizon.gendo.sg.authority.core;

import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import org.bson.Document;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * DataRuleEvaluator单元测试
 */
class DataRuleEvaluatorTest {

    private DataRuleEvaluator evaluator;
    private UserContext testUserContext;

    @BeforeEach
    void setUp() {
        evaluator = new DataRuleEvaluator();
        
        // 设置测试用户上下文
        testUserContext = new UserContext();
        testUserContext.setUserId("testUserId");
        testUserContext.setTenantId("testTenantId");
        ContextHolder.setCurrentUser(testUserContext);
    }

    @AfterEach
    void tearDown() {
        ContextHolder.clearCurrentUser();
    }

    @Test
    void testEvaluateRule_NullRule() {
        Object result = evaluator.evaluateRule(null);
        assertInstanceOf(CriteriaDefinition.class, result);
        assertTrue(((CriteriaDefinition) result).getCriteriaObject().isEmpty());
    }

    @Test
    void testEvaluateRule_SimpleCriteria() {
        // 测试简单的查询条件
        Document rule = new Document("status", "active");
        
        Object result = evaluator.evaluateRule(rule);
        
        assertInstanceOf(CriteriaDefinition.class, result);
        CriteriaDefinition criteria = (CriteriaDefinition) result;
        assertEquals("active", criteria.getCriteriaObject().get("status"));
    }

    @Test
    void testEvaluateRule_CriteriaWithVariable() {
        // 测试包含变量的查询条件
        Document rule = new Document("createdBy", "${x-user-id}");
        
        Object result = evaluator.evaluateRule(rule);
        
        assertInstanceOf(CriteriaDefinition.class, result);
        CriteriaDefinition criteria = (CriteriaDefinition) result;
        assertEquals("testUserId", criteria.getCriteriaObject().get("createdBy"));
    }

    @Test
    void testEvaluateRule_AggregationPipeline() {
        // 测试聚合管道规则
        List<Document> pipeline = List.of(
            new Document("$lookup", new Document()
                .append("from", "knowledgeBase")
                .append("localField", "knowledgeBaseId")
                .append("foreignField", "_id")
                .append("as", "kb")),
            new Document("$unwind", "$kb"),
            new Document("$match", new Document("kb.isPublic", true))
        );
        
        Object result = evaluator.evaluateRule(pipeline);
        
        assertInstanceOf(List.class, result);
        @SuppressWarnings("unchecked")
        List<Document> resultPipeline = (List<Document>) result;
        assertEquals(3, resultPipeline.size());
        assertTrue(resultPipeline.get(0).containsKey("$lookup"));
        assertTrue(resultPipeline.get(1).containsKey("$unwind"));
        assertTrue(resultPipeline.get(2).containsKey("$match"));
    }

    @Test
    void testEvaluateRule_AggregationPipelineWithVariable() {
        // 测试包含变量的聚合管道规则 - 注意：这里我们直接使用Map结构来模拟JSON解析的结果
        Map<String, Object> lookupStage = Map.of(
            "$lookup", Map.of(
                "from", "knowledgeBase",
                "localField", "knowledgeBaseId",
                "foreignField", "_id",
                "as", "kb"
            )
        );
        
        Map<String, Object> unwindStage = Map.of("$unwind", "$kb");
        
        Map<String, Object> matchStage = Map.of(
            "$match", Map.of(
                "$and", List.of(
                    Map.of("kb.isPublic", false),
                    Map.of("kb.createdBy", "${x-user-id}")
                )
            )
        );
        
        List<Map<String, Object>> pipeline = List.of(lookupStage, unwindStage, matchStage);
        
        Object result = evaluator.evaluateRule(pipeline);
        
        assertInstanceOf(List.class, result);
        @SuppressWarnings("unchecked")
        List<Document> resultPipeline = (List<Document>) result;
        assertEquals(3, resultPipeline.size());
        
        // 验证第一个阶段是lookup
        Document firstStage = resultPipeline.get(0);
        assertTrue(firstStage.containsKey("$lookup"));
        
        // 验证变量替换
        Document lastStage = resultPipeline.get(2);
        Document matchExpr = (Document) lastStage.get("$match");
        @SuppressWarnings("unchecked")
        List<Document> andConditions = (List<Document>) matchExpr.get("$and");
        assertEquals("testUserId", andConditions.get(1).get("kb.createdBy"));
    }

    @Test
    void testEvaluateRule_ComplexExpression() {
        // 测试复杂表达式
        Document rule = new Document("$expr", new Document("$eq", List.of("$status", "active")));
        
        Object result = evaluator.evaluateRule(rule);
        
        assertInstanceOf(CriteriaDefinition.class, result);
        CriteriaDefinition criteria = (CriteriaDefinition) result;
        assertTrue(criteria.getCriteriaObject().containsKey("$expr"));
    }

    @Test
    void testEvaluateRule_EmptyDocument() {
        // 测试空文档
        Document rule = new Document();
        
        Object result = evaluator.evaluateRule(rule);
        
        assertInstanceOf(CriteriaDefinition.class, result);
        assertTrue(((CriteriaDefinition) result).getCriteriaObject().isEmpty());
    }

    @Test
    void testEvaluateRule_InvalidJsonShouldReturnEmptyCriteria() {
        // 这个测试验证当规则解析失败时应该返回空的Criteria
        // 由于我们无法直接创建无效的JSON，我们测试一个可能导致解析问题的情况
        String invalidRule = "{ invalid json }";
        
        // 注意：这个测试可能需要根据实际的错误处理逻辑进行调整
        Object result = evaluator.evaluateRule(invalidRule);
        
        // 应该返回一个空的Criteria而不是抛出异常
        assertInstanceOf(CriteriaDefinition.class, result);
    }
}