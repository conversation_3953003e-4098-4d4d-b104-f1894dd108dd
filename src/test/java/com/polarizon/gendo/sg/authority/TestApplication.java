package com.polarizon.gendo.sg.authority;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试用的Spring Boot应用类
 * <p>
 * 用于启动测试环境的Spring上下文
 */
@EnableFeignClients(basePackages = "com.polarizon.gendo.sg.authority.api")
@SpringBootApplication(exclude = {MongoAutoConfiguration.class})
public class TestApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
} 