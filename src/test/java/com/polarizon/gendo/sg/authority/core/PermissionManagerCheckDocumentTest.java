package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import org.bson.Document;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PermissionManager的checkDocument方法详细测试
 */
@ExtendWith(MockitoExtension.class)
public class PermissionManagerCheckDocumentTest {

    @Mock
    private RuleLoader ruleLoader;
    
    @Mock
    private DataRuleEvaluator ruleEvaluator;
    
    private PermissionManager permissionManager;
    private ObjectMapper objectMapper;
    private AuthorityConfig authorityConfig;
    
    private static final String TEST_USER_ID = "test-user-123";
    private static final String TEST_TENANT_ID = "tenant-123";
    private static final String ADMIN_ROLE = "admin";
    private static final String INTERNAL_USER_ROLE = "internalUser";
    private static final String EXTERNAL_USER_ROLE = "externalUser";

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        
        // 从测试资源目录加载权限配置
        File configFile = ResourceUtils.getFile("classpath:test-authority-config.json");
        String content = new String(Files.readAllBytes(Paths.get(configFile.getPath())));
        authorityConfig = objectMapper.readValue(content, AuthorityConfig.class);
        
        // 配置模拟对象
        when(ruleLoader.getAuthorityConfig()).thenReturn(authorityConfig);
        
        // 创建测试对象
        permissionManager = new PermissionManager(ruleLoader, ruleEvaluator);
        
        // 设置默认用户上下文
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, ADMIN_ROLE);
    }
    
    @AfterEach
    void tearDown() {
        ContextHolder.clearCurrentUser();
    }
    
    private void setUserContext(String userId, String tenantId, String... roles) {
        UserContext.UserContextBuilder builder = UserContext.builder()
                .userId(userId)
                .account("testuser")
                .tenantId(tenantId);
                
        if (roles != null && roles.length > 0) {
            builder.roleEntities(Arrays.stream(roles)
                    .map(role -> new RoleEntityExtend("1", null, "角色" + role, role, true, true))
                    .toList());
        } else {
            builder.roleEntities(Collections.emptyList());
        }
        
        ContextHolder.setCurrentUser(builder.build());
    }

//    @Test
//    @DisplayName("测试超级管理员对KnowledgeBase的读权限 - 无数据限制")
//    void testSuperAdminReadKnowledgeBase() {
//        // 设置超级管理员角色
//        setUserContext(TEST_USER_ID, TEST_TENANT_ID, "superAdmin");
//
//        // 超级管理员读取权限的数据规则为null，意味着没有限制
//        when(ruleEvaluator.evaluateRule(null)).thenReturn(new Criteria());
//
//        // 创建测试文档
//        Document document = new Document();
//        document.append("_id", "kb-123");
//        document.append("name", "测试知识库");
//        document.append("tenantId", "other-tenant"); // 故意设置为其他租户
//        document.append("createdBy", "other-user"); // 故意设置为其他用户
//
//        // 超级管理员应该可以访问任何知识库
//        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document));
//    }
    
    @Test
    @DisplayName("测试内部用户对KnowledgeBase的读权限 - 租户限制")
    void testInternalUserReadKnowledgeBase() {
        // 设置内部用户角色
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 内部用户读取权限的数据规则为 {"tenantId": "${x-tenant-id}"}
        Criteria tenantCriteria = new Criteria("tenantId").is(TEST_TENANT_ID);
        when(ruleEvaluator.evaluateRule(any())).thenReturn(tenantCriteria);
        
        // 创建测试文档 - 相同租户
        Document document1 = new Document();
        document1.append("_id", "kb-123");
        document1.append("name", "测试知识库");
        document1.append("tenantId", TEST_TENANT_ID);
        document1.append("createdBy", "other-user");
        
        // 创建测试文档 - 不同租户
        Document document2 = new Document();
        document2.append("_id", "kb-456");
        document2.append("name", "其他租户知识库");
        document2.append("tenantId", "other-tenant");
        document2.append("createdBy", "other-user");
        
        // 同一租户的文档应该允许访问
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document1));
        
        // 不同租户的文档应该拒绝访问
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document2));
    }
    
    @Test
    @DisplayName("测试内部用户对KnowledgeBase的更新权限 - 租户和创建者限制")
    void testInternalUserUpdateKnowledgeBase() {
        // 设置内部用户角色
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 内部用户更新权限的数据规则为 {"isPublic": false, "tenantId": "${x-tenant-id}", "createdBy": "${x-user-id}"}
        Criteria complexCriteria = new Criteria().andOperator(
                Criteria.where("tenantId").is(TEST_TENANT_ID),
                Criteria.where("isPublic").is(false),
                Criteria.where("createdBy").is(TEST_USER_ID)
        );
        when(ruleEvaluator.evaluateRule(any())).thenReturn(complexCriteria);
        
        // 创建测试文档 - 满足所有条件
        Document document1 = new Document();
        document1.append("_id", "kb-123");
        document1.append("name", "测试知识库");
        document1.append("tenantId", TEST_TENANT_ID);
        document1.append("isPublic", false);
        document1.append("createdBy", TEST_USER_ID);
        
        // 创建测试文档 - 不满足createdBy条件
        Document document2 = new Document();
        document2.append("_id", "kb-456");
        document2.append("name", "他人创建的知识库");
        document2.append("tenantId", TEST_TENANT_ID);
        document2.append("isPublic", false);
        document2.append("createdBy", "other-user");
        
        // 创建测试文档 - 不满足isPublic条件
        Document document3 = new Document();
        document3.append("_id", "kb-789");
        document3.append("name", "公开知识库");
        document3.append("tenantId", TEST_TENANT_ID);
        document3.append("isPublic", true);
        document3.append("createdBy", TEST_USER_ID);
        
        // 满足所有条件的文档应该允许更新
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", document1));
        
        // 不满足createdBy条件的文档应该拒绝更新
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", document2));
        
        // 不满足isPublic条件的文档应该拒绝更新
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", document3));
    }
    
    @Test
    @DisplayName("测试复杂嵌套条件的检查 - $and操作符")
    void testComplexNestedConditionWithAnd() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, EXTERNAL_USER_ROLE);
        
        // 创建一个复杂的嵌套$and条件
        Document andCondition = Document.parse(
                "{ \"$and\": [" +
                "    { \"tenantId\": \"" + TEST_TENANT_ID + "\" }," +
                "    { \"isPublic\": false }," +
                "    { \"createdBy\": \"" + TEST_USER_ID + "\" }" +
                "  ]" +
                "}"
        );
        Criteria andCriteria = new Criteria(andCondition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(andCriteria);
        
        // 创建测试文档 - 满足所有条件
        Document document1 = new Document();
        document1.append("tenantId", TEST_TENANT_ID);
        document1.append("isPublic", false);
        document1.append("createdBy", TEST_USER_ID);
        
        // 创建测试文档 - 不满足其中一个条件
        Document document2 = new Document();
        document2.append("tenantId", TEST_TENANT_ID);
        document2.append("isPublic", false);
        document2.append("createdBy", "other-user");
        
        // 满足所有$and条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", document1));
        
        // 不满足其中一个$and条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", document2));
    }
    
    @Test
    @DisplayName("测试复杂嵌套条件的检查 - $or操作符")
    void testComplexNestedConditionWithOr() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建一个复杂的嵌套$or条件
        Document orCondition = Document.parse(
                "{ \"$or\": [" +
                "    { \"createdBy\": \"" + TEST_USER_ID + "\" }," +
                "    { \"sharedWith\": \"" + TEST_USER_ID + "\" }" +
                "  ]" +
                "}"
        );
        Criteria orCriteria = new Criteria(orCondition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(orCriteria);
        
        // 创建测试文档 - 满足第一个条件
        Document document1 = new Document();
        document1.append("createdBy", TEST_USER_ID);
        
        // 创建测试文档 - 满足第二个条件
        Document document2 = new Document();
        document2.append("sharedWith", TEST_USER_ID);
        
        // 创建测试文档 - 不满足任何条件
        Document document3 = new Document();
        document3.append("createdBy", "other-user");
        document3.append("sharedWith", "other-user");
        
        // 满足第一个$or条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeFile", "read", document1));
        
        // 满足第二个$or条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeFile", "read", document2));
        
        // 不满足任何$or条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeFile", "read", document3));
    }
    
    @Test
    @DisplayName("测试复杂嵌套条件的检查 - 混合$and和$or操作符")
    void testComplexNestedConditionWithAndOr() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建一个复杂的混合嵌套条件
        Document complexCondition = Document.parse(
                "{ \"$and\": [" +
                "    { \"tenantId\": \"" + TEST_TENANT_ID + "\" }," +
                "    { \"$or\": [" +
                "        { \"createdBy\": \"" + TEST_USER_ID + "\" }," +
                "        { \"isPublic\": true }" +
                "      ]" +
                "    }" +
                "  ]" +
                "}"
        );
        Criteria complexCriteria = new Criteria(complexCondition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(complexCriteria);
        
        // 创建测试文档 - 满足tenantId和createdBy条件
        Document document1 = new Document();
        document1.append("tenantId", TEST_TENANT_ID);
        document1.append("createdBy", TEST_USER_ID);
        document1.append("isPublic", false);
        
        // 创建测试文档 - 满足tenantId和isPublic条件
        Document document2 = new Document();
        document2.append("tenantId", TEST_TENANT_ID);
        document2.append("createdBy", "other-user");
        document2.append("isPublic", true);
        
        // 创建测试文档 - 只满足tenantId条件，不满足or条件
        Document document3 = new Document();
        document3.append("tenantId", TEST_TENANT_ID);
        document3.append("createdBy", "other-user");
        document3.append("isPublic", false);
        
        // 创建测试文档 - 不满足tenantId条件
        Document document4 = new Document();
        document4.append("tenantId", "other-tenant");
        document4.append("createdBy", TEST_USER_ID);
        document4.append("isPublic", true);
        
        // 满足tenantId和createdBy条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document1));
        
        // 满足tenantId和isPublic条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document2));
        
        // 只满足tenantId条件，不满足or条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document3));
        
        // 不满足tenantId条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document4));
    }
    
    @Test
    @DisplayName("测试比较操作符 - $gt, $lt, $gte, $lte")
    void testComparisonOperators() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建包含比较操作符的条件
        Document comparisonCondition = Document.parse(
                "{ \"$and\": [" +
                "    { \"accessLevel\": { \"$gte\": 5 } }," +
                "    { \"version\": { \"$lt\": 10 } }" +
                "  ]" +
                "}"
        );
        Criteria comparisonCriteria = new Criteria(comparisonCondition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(comparisonCriteria);
        
        // 创建测试文档 - 满足所有比较条件
        Document document1 = new Document();
        document1.append("accessLevel", 7);
        document1.append("version", 5);
        
        // 创建测试文档 - 不满足$gte条件
        Document document2 = new Document();
        document2.append("accessLevel", 3);
        document2.append("version", 5);
        
        // 创建测试文档 - 不满足$lt条件
        Document document3 = new Document();
        document3.append("accessLevel", 7);
        document3.append("version", 15);
        
        // 满足所有比较条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document1));
        
        // 不满足$gte条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document2));
        
        // 不满足$lt条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document3));
    }
    
    @Test
    @DisplayName("测试数组操作符 - $in, $nin")
    void testArrayOperators() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建包含数组操作符的条件
        Document arrayCondition = Document.parse(
                "{ \"$and\": [" +
                "    { \"category\": { \"$in\": [\"A\", \"B\", \"C\"] } }," +
                "    { \"tag\": { \"$nin\": [\"restricted\", \"confidential\"] } }" +
                "  ]" +
                "}"
        );
        Criteria arrayCriteria = new Criteria(arrayCondition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(arrayCriteria);
        
        // 创建测试文档 - 满足所有条件
        Document document1 = new Document();
        document1.append("category", "B");
        document1.append("tag", "public");
        
        // 创建测试文档 - 不满足$in条件
        Document document2 = new Document();
        document2.append("category", "D");
        document2.append("tag", "public");
        
        // 创建测试文档 - 不满足$nin条件
        Document document3 = new Document();
        document3.append("category", "A");
        document3.append("tag", "confidential");
        
        // 满足所有数组条件的文档应该通过
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document1));
        
        // 不满足$in条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document2));
        
        // 不满足$nin条件的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document3));
    }
    
    @Test
    @DisplayName("测试字段不存在的情况")
    void testFieldNotExists() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建要求字段存在的条件
        Document condition = Document.parse("{ \"requiredField\": { \"$exists\": true } }");
        Criteria existsCriteria = new Criteria(condition.toJson());
        when(ruleEvaluator.evaluateRule(any())).thenReturn(existsCriteria);
        
        // 创建包含必需字段的文档
        Document document1 = new Document();
        document1.append("requiredField", "someValue");
        
        // 创建不包含必需字段的文档
        Document document2 = new Document();
        document2.append("otherField", "someValue");
        
        // 包含必需字段的文档应该通过
        // 注意：由于checkDocument实现的限制，当前可能无法直接支持$exists操作符
        // 但是可以通过字段是否存在来间接判断
        when(ruleEvaluator.evaluateRule(any())).thenReturn(new Criteria("requiredField").exists(true));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", document1));
        
        // 不包含必需字段的文档应该失败
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", document2));
    }
    
    @Test
    @DisplayName("测试嵌套文档匹配")
    void testNestedDocumentMatching() {
        setUserContext(TEST_USER_ID, TEST_TENANT_ID, INTERNAL_USER_ROLE);
        
        // 创建嵌套文档匹配条件
        Document nestedCondition = Document.parse("{ \"metadata.owner\": \"" + TEST_USER_ID + "\" }");
        Criteria nestedCriteria = new Criteria(nestedCondition.toJson());
//        when(ruleEvaluator.evaluateRule(any())).thenReturn(nestedCriteria);
        
        // 创建带有嵌套文档的测试文档 - 匹配条件
        Document metadata1 = new Document();
        metadata1.append("owner", TEST_USER_ID);
        Document document1 = new Document();
        document1.append("metadata", metadata1);
        
        // 创建带有嵌套文档的测试文档 - 不匹配条件
        Document metadata2 = new Document();
        metadata2.append("owner", "other-user");
        Document document2 = new Document();
        document2.append("metadata", metadata2);
        
        // 匹配嵌套条件的文档应该通过
        // 注意：由于checkDocument的实现限制，当前可能无法直接支持嵌套文档匹配
        // 但这是一个重要的测试场景，需要在实现中考虑
        // 此处仅作为参考
    }
} 