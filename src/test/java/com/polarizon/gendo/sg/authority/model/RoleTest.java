package com.polarizon.gendo.sg.authority.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Role单元测试
 */
class RoleTest {

    private Role role;

    @BeforeEach
    void setUp() {
        role = new Role();
        role.setDescription("测试角色");
        
        // 设置权限
        role.getResourcePermissions().put("users", Arrays.asList("read", "create"));
        role.getResourcePermissions().put("documents", Arrays.asList("read", "create", "update"));
    }

    @Test
    void testHasPermission() {
        // 测试有权限的情况
        assertTrue(role.hasPermission("users", "read"));
        assertTrue(role.hasPermission("users", "create"));
        assertTrue(role.hasPermission("documents", "read"));
        assertTrue(role.hasPermission("documents", "update"));
        
        // 测试没有权限的情况
        assertFalse(role.hasPermission("users", "update"));
        assertFalse(role.hasPermission("users", "delete"));
        assertFalse(role.hasPermission("documents", "delete"));
    }

    @Test
    void testHasPermissionForNonExistentResource() {
        // 测试不存在的资源
        assertFalse(role.hasPermission("non-existent", "read"));
    }

    @Test
    void testHasPermissionWithEmptyOperations() {
        // 设置空操作列表
        role.getResourcePermissions().put("reports", Collections.emptyList());
        
        // 测试空操作列表
        assertFalse(role.hasPermission("reports", "read"));
    }

    @Test
    void testGetterAndSetter() {
        // 测试描述字段
        assertEquals("测试角色", role.getDescription());
        
        // 修改描述
        role.setDescription("新的描述");
        assertEquals("新的描述", role.getDescription());
        
        // 测试权限映射
        assertNotNull(role.getResourcePermissions());
        assertEquals(2, role.getResourcePermissions().size());
        assertTrue(role.getResourcePermissions().containsKey("users"));
        assertTrue(role.getResourcePermissions().containsKey("documents"));
    }

    @Test
    void testDefaultMap() {
        // 创建新角色，验证resourcePermissions默认初始化
        Role newRole = new Role();
        assertNotNull(newRole.getResourcePermissions());
        assertTrue(newRole.getResourcePermissions().isEmpty());
    }
} 