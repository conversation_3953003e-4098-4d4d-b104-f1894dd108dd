package com.polarizon.gendo.sg.authority.core;

import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import com.polarizon.gendo.sg.authority.model.Operation;
import com.polarizon.gendo.sg.authority.model.Resource;
import com.polarizon.gendo.sg.authority.model.Role;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PermissionManager单元测试
 */
@ExtendWith(MockitoExtension.class)
class PermissionManagerTest {

    @Mock
    private RuleLoader ruleLoader;
    
    @Mock
    private DataRuleEvaluator ruleEvaluator;
    
    private PermissionManager permissionManager;
    
    private AuthorityConfig authorityConfig;

    @BeforeEach
    void setUp() {
        // 创建测试配置
        authorityConfig = createTestConfig();
        
        // 配置模拟对象
        when(ruleLoader.getAuthorityConfig()).thenReturn(authorityConfig);
//        when(ruleEvaluator.evaluateRule(any())).thenReturn(new Criteria());
        
        // 创建测试对象
        permissionManager = new PermissionManager(ruleLoader, ruleEvaluator);
        
        // 设置测试用户上下文
        UserContext userContext = UserContext.builder()
                .userId("test-user-123")
                .account("testuser")
                .tenantId("tenant-123")
                .roleEntities(Arrays.asList(
                        new RoleEntityExtend("1",null, "管理员", "admin", true, true),
                        new RoleEntityExtend("1",null, "外部普通用户", "externalUser", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文
        ContextHolder.clearCurrentUser();
    }

    @Test
    void testInitCollectionMapping() {
        // 测试初始化时是否正确映射集合名称到资源ID
        assertEquals("users", permissionManager.getResourceIdByCollection("users"));
        assertEquals("documents", permissionManager.getResourceIdByCollection("documents"));
        assertNull(permissionManager.getResourceIdByCollection("non-existent"));
    }

    @Test
    void testHasOperationPermissionAdmin() {
        // 测试管理员角色是否有正确的权限
        assertTrue(permissionManager.hasOperationPermission("users", "read"));
        assertTrue(permissionManager.hasOperationPermission("users", "create"));
        assertTrue(permissionManager.hasOperationPermission("users", "update"));
        assertTrue(permissionManager.hasOperationPermission("users", "delete"));
        
        assertTrue(permissionManager.hasOperationPermission("documents", "read"));
    }

    @Test
    void testHasOperationPermissionLimitedAccess() {
        // 设置只有user角色的用户上下文
        UserContext limitedUser = UserContext.builder()
                .userId("limited-user")
                .roleEntities(Collections.singletonList(new RoleEntityExtend("1",null, "外部普通用户", "externalUser", true, true)))
                .build();
                
        ContextHolder.setCurrentUser(limitedUser);
        
        // 测试普通用户角色的权限限制
        assertFalse(permissionManager.hasOperationPermission("users", "read"));
        assertTrue(permissionManager.hasOperationPermission("documents", "read"));
        assertTrue(permissionManager.hasOperationPermission("documents", "create"));
        assertFalse(permissionManager.hasOperationPermission("documents", "update"));
        assertFalse(permissionManager.hasOperationPermission("documents", "delete"));
    }

    @Test
    void testHasOperationPermissionNoUserContext() {
        // 清除用户上下文
        ContextHolder.clearCurrentUser();
        
        // 没有用户上下文时应返回false
        assertFalse(permissionManager.hasOperationPermission("users", "read"));
        assertFalse(permissionManager.hasOperationPermission("documents", "read"));
    }

//    @Test
//    void testGetDataFilter() {
//        // 设置数据规则评估结果
//        Map<String, Object> expectedRule = new HashMap<>();
//        expectedRule.put("createdBy", "test-user-123");
//
//        Criteria expectedCriteria = new Criteria();
//        when(ruleEvaluator.evaluateRule(Mockito.any())).thenReturn(expectedCriteria);
//
//        // 获取数据过滤条件
//        CriteriaDefinition criteria = permissionManager.getDataFilter("documents", "read");
//
//        // 验证是否调用了evaluateRule方法
//        Mockito.verify(ruleEvaluator).evaluateRule(Mockito.any());
//
//        // 验证返回的条件是否正确
//        assertNotNull(criteria);
//        assertEquals(expectedCriteria, criteria);
//    }

//    @Test
//    void testGetDataFilterForNonExistentResource() {
//        // 获取不存在资源的数据过滤条件
//        CriteriaDefinition criteria = permissionManager.getDataFilter("non-existent", "read");
//
//        // 应返回空条件
//        assertNotNull(criteria);
//        assertEquals(new Criteria().getCriteriaObject(), criteria.getCriteriaObject());
//    }

    @Test
    void testCheckDocument() {
        // 创建测试文档
        Document document = new Document();
        document.put("createdBy", "test-user-123");
        document.put("status", "active");
        
        boolean result = permissionManager.checkDocument("documents", "read", document);
        assertTrue(result); // 当前默认返回true
    }

    /**
     * 创建测试配置
     */
    @NotNull
    private AuthorityConfig createTestConfig() {
        AuthorityConfig config = new AuthorityConfig();
        
        // 创建角色
        Role adminRole = new Role();
        adminRole.setDescription("管理员");
        adminRole.getResourcePermissions().put("users", Arrays.asList("read", "create", "update", "delete"));
        adminRole.getResourcePermissions().put("documents", Arrays.asList("read", "create", "update", "delete"));
        
        Role userRole = new Role();
        userRole.setDescription("外部用户");
        userRole.getResourcePermissions().put("documents", Arrays.asList("read", "create"));
        
        config.getRoles().put("admin", adminRole);
        config.getRoles().put("externalUser", userRole);
        
        // 创建资源
        Resource userResource = new Resource();
        userResource.setType("MONGO");
        userResource.setCollection("users");
        
        Operation userReadOp = new Operation();
        Map<String, Object> adminRule = new HashMap<>();
        adminRule.put("status", "active");
        userReadOp.getDataRules().put("admin", adminRule);
        userResource.getOperations().put("read", userReadOp);
        
        Resource documentResource = new Resource();
        documentResource.setType("MONGO");
        documentResource.setCollection("documents");
        
        Operation docReadOp = new Operation();
        Map<String, Object> userRule = new HashMap<>();
        userRule.put("createdBy", "${x-user-id}");
        docReadOp.getDataRules().put("user", userRule);
        docReadOp.getDataRules().put("default", Collections.emptyMap());
        documentResource.getOperations().put("read", docReadOp);
        
        config.getResources().put("users", userResource);
        config.getResources().put("documents", documentResource);
        
        return config;
    }
} 