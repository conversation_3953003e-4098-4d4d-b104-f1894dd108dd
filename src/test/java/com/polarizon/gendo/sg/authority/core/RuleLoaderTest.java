package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import com.polarizon.gendo.sg.authority.model.Resource;
import com.polarizon.gendo.sg.authority.model.Role;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RuleLoader单元测试
 */
class RuleLoaderTest {

    private RuleLoader ruleLoader;
    private ObjectMapper objectMapper;

    @TempDir
    Path tempDir;

    // 创建一个测试用的ResourceLoader实现
    private static class MockResourceLoader implements ResourceLoader {
        private final Map<String, org.springframework.core.io.Resource> resources = new HashMap<>();

        public void addResource(String location, org.springframework.core.io.Resource resource) {
            resources.put(location, resource);
        }

        @Override
        public org.springframework.core.io.Resource getResource(String location) {
            if (location.startsWith("file:")) {
                return new FileSystemResource(location.substring(5));
            }
            return resources.getOrDefault(location, new ClassPathResource(location.replace("classpath:", "")));
        }

        @Override
        public ClassLoader getClassLoader() {
            return getClass().getClassLoader();
        }
    }

    private MockResourceLoader mockResourceLoader;

    @BeforeEach
    void setUp() {
        mockResourceLoader = new MockResourceLoader();
        ruleLoader = new RuleLoader(mockResourceLoader);
        objectMapper = new ObjectMapper();
    }

    @Test
    void testInitLoadsConfig() throws IOException {
        // 创建测试配置文件
        AuthorityConfig config = createTestConfig();
        File configFile = createTempConfigFile(config);

        // 设置配置文件路径
        ReflectionTestUtils.setField(ruleLoader, "configPath", "file:" + configFile.getAbsolutePath());

        // 初始化加载配置
        ruleLoader.init();

        // 验证配置已正确加载
        AuthorityConfig loadedConfig = ruleLoader.getAuthorityConfig();
        assertNotNull(loadedConfig);
        assertEquals(2, loadedConfig.getRoles().size());
        assertEquals(2, loadedConfig.getResources().size());

        // 验证角色配置
        assertTrue(loadedConfig.getRoles().containsKey("admin"));
        assertTrue(loadedConfig.getRoles().containsKey("user"));

        // 验证资源配置
        assertTrue(loadedConfig.getResources().containsKey("users"));
        assertTrue(loadedConfig.getResources().containsKey("documents"));
    }

    @Test
    void testReloadConfig() throws IOException {
        // 创建测试配置文件
        AuthorityConfig config = createTestConfig();
        File configFile = createTempConfigFile(config);

        // 设置配置文件路径
        ReflectionTestUtils.setField(ruleLoader, "configPath", "file:" + configFile.getAbsolutePath());

        // 初始化加载配置
        ruleLoader.init();

        // 修改配置并重新写入文件
        AuthorityConfig newConfig = createUpdatedTestConfig();
        Files.write(configFile.toPath(), objectMapper.writeValueAsBytes(newConfig));

        // 重新加载配置
        ruleLoader.reloadConfig();

        // 验证新配置已加载
        AuthorityConfig loadedConfig = ruleLoader.getAuthorityConfig();
        assertNotNull(loadedConfig);
        assertEquals(3, loadedConfig.getRoles().size());
        assertTrue(loadedConfig.getRoles().containsKey("manager"));
    }

    /**
     * 创建临时配置文件
     */
    private File createTempConfigFile(AuthorityConfig config) throws IOException {
        File configFile = tempDir.resolve("test-authority-config.json").toFile();
        Files.write(configFile.toPath(), objectMapper.writeValueAsBytes(config));
        return configFile;
    }

    /**
     * 创建测试配置
     */
    private AuthorityConfig createTestConfig() {
        AuthorityConfig config = new AuthorityConfig();

        // 创建角色
        Role adminRole = new Role();
        adminRole.setDescription("管理员角色");
        adminRole.getResourcePermissions().put("users", Arrays.asList("read", "create", "update", "delete"));
        adminRole.getResourcePermissions().put("documents", Arrays.asList("read", "create", "update", "delete"));

        Role userRole = new Role();
        userRole.setDescription("普通用户角色");
        userRole.getResourcePermissions().put("documents", Arrays.asList("read", "create"));

        config.getRoles().put("admin", adminRole);
        config.getRoles().put("user", userRole);

        // 创建资源
        Resource userResource = new Resource();
        userResource.setType("MONGO");
        userResource.setCollection("users");

        Resource documentResource = new Resource();
        documentResource.setType("MONGO");
        documentResource.setCollection("documents");

        config.getResources().put("users", userResource);
        config.getResources().put("documents", documentResource);

        return config;
    }

    /**
     * 创建更新后的测试配置
     */
    private AuthorityConfig createUpdatedTestConfig() {
        AuthorityConfig config = createTestConfig();

        // 添加新角色
        Role managerRole = new Role();
        managerRole.setDescription("经理角色");
        managerRole.getResourcePermissions().put("users", List.of("read"));
        managerRole.getResourcePermissions().put("documents", Arrays.asList("read", "create", "update"));

        config.getRoles().put("manager", managerRole);

        return config;
    }
} 