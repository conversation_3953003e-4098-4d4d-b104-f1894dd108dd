package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import org.bson.Document;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 权限系统集成测试
 * <p>
 * 此测试使用真实的对象（非Mock），测试整个权限系统的功能
 */
@ExtendWith(MockitoExtension.class)
public class IntegrationPermissionTest {

    private RuleLoader ruleLoader;
    private DataRuleEvaluator ruleEvaluator;
    private PermissionManager permissionManager;
    
    private static final String TEST_USER_ID = "test-user-123";
    private static final String TEST_TENANT_ID = "tenant-123";
    private static final String OTHER_USER_ID = "other-user-456";
    private static final String OTHER_TENANT_ID = "other-tenant-456";

    // 创建一个简单的ResourceLoader实现用于测试
    private static class MockResourceLoader implements ResourceLoader {
        @Override
        public Resource getResource(String location) {
            return new ClassPathResource(location.replace("classpath:", ""));
        }

        @Override
        public ClassLoader getClassLoader() {
            return getClass().getClassLoader();
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        // 创建真实对象，使用MockResourceLoader
        ruleLoader = new RuleLoader(new MockResourceLoader()) {
            @Override
            public AuthorityConfig getAuthorityConfig() {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ClassPathResource resource = new ClassPathResource("test-authority-config.json");
                    try (InputStream inputStream = resource.getInputStream()) {
                        return objectMapper.readValue(inputStream, AuthorityConfig.class);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to load test config", e);
                }
            }
        };
        
        ruleEvaluator = new DataRuleEvaluator();
        permissionManager = new PermissionManager(ruleLoader, ruleEvaluator);
        
        // 默认设置管理员用户上下文
        setAdminUserContext();
    }

    @AfterEach
    void tearDown() {
        ContextHolder.clearCurrentUser();
    }
    
    private void setAdminUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(TEST_USER_ID)
                .account("admin")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "管理员", "admin", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setSuperAdminUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(TEST_USER_ID)
                .account("superadmin")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "超级管理员", "superAdmin", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setInternalUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(TEST_USER_ID)
                .account("internal")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "内部用户", "internalUser", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }
    
    private void setExternalUserContext() {
        UserContext userContext = UserContext.builder()
                .userId(TEST_USER_ID)
                .account("external")
                .tenantId(TEST_TENANT_ID)
                .roleEntities(Collections.singletonList(
                        new RoleEntityExtend("1", null, "外部用户", "externalUser", true, true)
                ))
                .build();
                
        ContextHolder.setCurrentUser(userContext);
    }

    @Test
    @DisplayName("测试资源操作权限检查")
    void testHasOperationPermission() {
        // 1. 测试超级管理员权限
        setSuperAdminUserContext();
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "delete"));
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "delete"));
        
        // 2. 测试管理员权限
        setAdminUserContext();
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "delete"));
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "delete"));
        
        // 3. 测试内部用户权限
        setInternalUserContext();
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "delete"));
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "delete"));
        
        // 4. 测试外部用户权限
        setExternalUserContext();
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeBase", "delete"));
        
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "read"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "create"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "update"));
        assertTrue(permissionManager.hasOperationPermission("KnowledgeFile", "delete"));
    }

//    @Test
//    @DisplayName("测试数据过滤条件生成")
//    void testGetDataFilter() {
//        // 1. 测试超级管理员的数据规则
//        setSuperAdminUserContext();
//
//        CriteriaDefinition superAdminReadCriteria = permissionManager.getDataFilter("KnowledgeBase", "read");
//        assertTrue(superAdminReadCriteria.getCriteriaObject().isEmpty(), "超级管理员读取应无限制");
//
//        // 2. 测试管理员的数据规则
//        setAdminUserContext();
//
//        CriteriaDefinition adminReadCriteria = permissionManager.getDataFilter("KnowledgeBase", "read");
//        assertTrue(adminReadCriteria.getCriteriaObject().isEmpty(), "管理员读取应无限制");
//
//        // 3. 测试内部用户的数据规则
//        setInternalUserContext();
//
//        CriteriaDefinition internalReadCriteria = permissionManager.getDataFilter("KnowledgeBase", "read");
//        assertFalse(internalReadCriteria.getCriteriaObject().isEmpty(), "内部用户读取应有限制");
//        String internalCriteriaJson = internalReadCriteria.getCriteriaObject().toJson();
//        assertTrue(internalCriteriaJson.contains(TEST_TENANT_ID), "内部用户读取限制应包含租户ID");
//
//        CriteriaDefinition internalUpdateCriteria = permissionManager.getDataFilter("KnowledgeBase", "update");
//        assertFalse(internalUpdateCriteria.getCriteriaObject().isEmpty(), "内部用户更新应有限制");
//        String internalUpdateJson = internalUpdateCriteria.getCriteriaObject().toJson();
//        assertTrue(internalUpdateJson.contains(TEST_TENANT_ID), "内部用户更新限制应包含租户ID");
//        assertTrue(internalUpdateJson.contains(TEST_USER_ID), "内部用户更新限制应包含用户ID");
//        assertTrue(internalUpdateJson.contains("isPublic"), "内部用户更新限制应包含isPublic");
//
//        // 4. 测试外部用户的数据规则
//        setExternalUserContext();
//
//        CriteriaDefinition externalReadCriteria = permissionManager.getDataFilter("KnowledgeBase", "read");
//        assertFalse(externalReadCriteria.getCriteriaObject().isEmpty(), "外部用户读取应有限制");
//        String externalCriteriaJson = externalReadCriteria.getCriteriaObject().toJson();
//        assertTrue(externalCriteriaJson.contains(TEST_TENANT_ID), "外部用户读取限制应包含租户ID");
//
//        // 5. 测试KnowledgeFile资源的复杂数据规则
//        CriteriaDefinition fileReadCriteria = permissionManager.getDataFilter("KnowledgeFile", "read");
//        assertFalse(fileReadCriteria.getCriteriaObject().isEmpty(), "文件读取应有限制");
//        String fileReadJson = fileReadCriteria.getCriteriaObject().toJson();
//        assertTrue(fileReadJson.contains("$expr") || fileReadJson.contains("$lookup"),
//                "文件读取应包含复杂表达式");
//    }

    @Test
    @DisplayName("测试文档权限检查 - KnowledgeBase资源")
    void testKnowledgeBaseDocumentCheck() {
        // 创建测试文档 - 当前租户公开文档
        Document publicDoc = new Document();
        publicDoc.append("_id", "kb-public-123");
        publicDoc.append("name", "公开知识库");
        publicDoc.append("tenantId", TEST_TENANT_ID);
        publicDoc.append("isPublic", true);
        publicDoc.append("createdBy", OTHER_USER_ID);
        
        // 创建测试文档 - 当前租户当前用户私有文档
        Document privateOwnDoc = new Document();
        privateOwnDoc.append("_id", "kb-private-own-123");
        privateOwnDoc.append("name", "私有知识库(自己的)");
        privateOwnDoc.append("tenantId", TEST_TENANT_ID);
        privateOwnDoc.append("isPublic", false);
        privateOwnDoc.append("createdBy", TEST_USER_ID);
        
        // 创建测试文档 - 当前租户其他用户私有文档
        Document privateOtherDoc = new Document();
        privateOtherDoc.append("_id", "kb-private-other-123");
        privateOtherDoc.append("name", "私有知识库(他人的)");
        privateOtherDoc.append("tenantId", TEST_TENANT_ID);
        privateOtherDoc.append("isPublic", false);
        privateOtherDoc.append("createdBy", OTHER_USER_ID);
        
        // 创建测试文档 - 其他租户公开文档
        Document otherTenantPublicDoc = new Document();
        otherTenantPublicDoc.append("_id", "kb-other-tenant-public-123");
        otherTenantPublicDoc.append("name", "其他租户公开知识库");
        otherTenantPublicDoc.append("tenantId", OTHER_TENANT_ID);
        otherTenantPublicDoc.append("isPublic", true);
        otherTenantPublicDoc.append("createdBy", OTHER_USER_ID);
        
        // 测试超级管理员
        setSuperAdminUserContext();
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOtherDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", otherTenantPublicDoc));
        
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOtherDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", otherTenantPublicDoc));
        
        // 测试管理员
        setAdminUserContext();
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOtherDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", otherTenantPublicDoc));
        
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOtherDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", otherTenantPublicDoc));
        
        // 测试内部用户
        setInternalUserContext();
        
        // 读取权限测试 - 只需要符合tenantId
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOtherDoc));
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", otherTenantPublicDoc));
        
        // 更新权限测试 - 需要符合tenantId、isPublic=false、createdBy=当前用户
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", publicDoc)); // isPublic=true不满足
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOwnDoc));
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", privateOtherDoc)); // createdBy不满足
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", otherTenantPublicDoc)); // 多重条件不满足
        
        // 测试外部用户
        setExternalUserContext();
        
        // 读取权限测试 - 只需要符合tenantId
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", publicDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOwnDoc));
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "read", privateOtherDoc));
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "read", otherTenantPublicDoc));
        
        // 更新权限测试 - 需要符合tenantId、isPublic=false、createdBy=当前用户
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", publicDoc)); // isPublic=true不满足
        assertTrue(permissionManager.checkDocument("KnowledgeBase", "update", privateOwnDoc));
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", privateOtherDoc)); // createdBy不满足
        assertFalse(permissionManager.checkDocument("KnowledgeBase", "update", otherTenantPublicDoc)); // 多重条件不满足
    }

//    @Test
//    @DisplayName("测试文档权限检查 - 带有$or条件")
//    void testDocumentCheckWithOrCondition() {
//        // 创建一个可以匹配$or条件的文档
//        Document doc1 = new Document();
//        doc1.append("tenantId", TEST_TENANT_ID);
//        doc1.append("createdBy", TEST_USER_ID);
//        doc1.append("isPublic", false);
//
//        Document doc2 = new Document();
//        doc2.append("tenantId", TEST_TENANT_ID);
//        doc2.append("createdBy", OTHER_USER_ID);
//        doc2.append("isPublic", true);
//
//        Document doc3 = new Document();
//        doc3.append("tenantId", TEST_TENANT_ID);
//        doc3.append("createdBy", OTHER_USER_ID);
//        doc3.append("isPublic", false);
//
//        Document doc4 = new Document();
//        doc4.append("tenantId", OTHER_TENANT_ID);
//        doc4.append("createdBy", TEST_USER_ID);
//        doc4.append("isPublic", true);
//
//        // 设置内部用户角色，其在KnowledgeBase资源上的读取规则是tenant_id匹配，且无其他限制
//        setInternalUserContext();
//
//        // 为了测试$or条件，我们手动构造一个带有$or的条件
//        Document orCondition = Document.parse(
//                "{ \"$and\": [" +
//                "    { \"tenantId\": \"" + TEST_TENANT_ID + "\" }," +
//                "    { \"$or\": [" +
//                "        { \"createdBy\": \"" + TEST_USER_ID + "\" }," +
//                "        { \"isPublic\": true }" +
//                "      ]" +
//                "    }" +
//                "  ]" +
//                "}"
//        );
//        Criteria orCriteria = new Criteria(orCondition.toJson());
//
//        // 重新实现一个PermissionManager，用于测试自定义条件
//        PermissionManager testManager = new PermissionManager(ruleLoader, new DataRuleEvaluator() {
//            @Override
//            public Criteria evaluateRule(Object rule) {
//                return orCriteria;
//            }
//        });
//
//        // 测试文档匹配
//        assertTrue(testManager.checkDocument("KnowledgeBase", "read", doc1)); // 满足tenantId和createdBy条件
//        assertTrue(testManager.checkDocument("KnowledgeBase", "read", doc2)); // 满足tenantId和isPublic条件
//        assertFalse(testManager.checkDocument("KnowledgeBase", "read", doc3)); // 只满足tenantId，不满足or内的任何条件
//        assertFalse(testManager.checkDocument("KnowledgeBase", "read", doc4)); // 不满足tenantId条件
//    }

    @Test
    @DisplayName("测试特殊集合名称映射")
    void testCollectionMapping() {
        assertEquals("KnowledgeBase", permissionManager.getResourceIdByCollection("default_com.polarizon.rag.kb.KnowledgeBaseDO"));
        assertEquals("KnowledgeFile", permissionManager.getResourceIdByCollection("default_com.polarizon.rag.kb.KnowledgeFileDO"));
        assertNull(permissionManager.getResourceIdByCollection("non_existent_collection"));
    }

//    @Test
//    @DisplayName("测试KnowledgeFile权限规则不包含聚合操作符")
//    void testKnowledgeFilePermissionWithoutAggregation() {
//        // 设置内部用户上下文
//        setInternalUserContext();
//
//        // 获取KnowledgeFile的数据过滤条件
//        CriteriaDefinition fileReadCriteria = permissionManager.getDataFilter("KnowledgeFile", "read");
//        assertNotNull(fileReadCriteria, "应该返回非空的过滤条件");
//
//        Document criteriaDoc = fileReadCriteria.getCriteriaObject();
//        assertNotNull(criteriaDoc, "过滤条件文档应该非空");
//
//        // 验证权限规则仍包含聚合操作符（这是预期的，因为我们在MongoPermissionAspect中处理它们）
//        String criteriaJson = criteriaDoc.toJson();
//        System.out.println("KnowledgeFile read criteria for internalUser: " + criteriaJson);
//
//        // 这个测试主要验证权限规则可以正常生成，不会抛出异常
//        assertTrue(true, "能够成功获取包含聚合操作符的权限规则");
//
//        // 测试外部用户
//        setExternalUserContext();
//        CriteriaDefinition externalFileReadCriteria = permissionManager.getDataFilter("KnowledgeFile", "read");
//        assertNotNull(externalFileReadCriteria, "外部用户应该返回非空的过滤条件");
//
//        Document externalCriteriaDoc = externalFileReadCriteria.getCriteriaObject();
//        assertNotNull(externalCriteriaDoc, "外部用户过滤条件文档应该非空");
//
//        String externalCriteriaJson = externalCriteriaDoc.toJson();
//        System.out.println("KnowledgeFile read criteria for externalUser: " + externalCriteriaJson);
//    }

//    @Test
//    @DisplayName("测试通用聚合查询权限处理")
//    void testUniversalAggregationPermissionHandling() {
//        // 设置内部用户上下文
//        setInternalUserContext();
//
//        // 获取包含聚合操作符的权限规则
//        CriteriaDefinition fileReadCriteria = permissionManager.getDataFilter("KnowledgeFile", "read");
//        assertNotNull(fileReadCriteria, "应该返回非空的过滤条件");
//
//        Document criteriaDoc = fileReadCriteria.getCriteriaObject();
//
//        // 验证包含聚合操作符
//        boolean containsAggregation = containsAggregationOperatorsInDocument(criteriaDoc);
//        assertTrue(containsAggregation, "KnowledgeFile权限规则应该包含聚合操作符");
//
//        // 验证聚合操作符检测逻辑
//        assertTrue(isAggregationOperatorTest("$lookup"), "$lookup应该被识别为聚合操作符");
//        assertTrue(isAggregationOperatorTest("$expr"), "$expr应该被识别为聚合操作符");
//        assertTrue(isAggregationOperatorTest("$let"), "$let应该被识别为聚合操作符");
//        assertFalse(isAggregationOperatorTest("$eq"), "$eq不应该被识别为聚合操作符");
//        assertFalse(isAggregationOperatorTest("$and"), "$and不应该被识别为聚合操作符");
//
//        System.out.println("✅ 通用聚合查询权限处理测试通过");
//
//        // 测试外部用户也应该能正常处理
//        setExternalUserContext();
//        CriteriaDefinition externalFileReadCriteria = permissionManager.getDataFilter("KnowledgeFile", "read");
//        assertNotNull(externalFileReadCriteria, "外部用户应该返回非空的过滤条件");
//
//        Document externalCriteriaDoc = externalFileReadCriteria.getCriteriaObject();
//        boolean externalContainsAggregation = containsAggregationOperatorsInDocument(externalCriteriaDoc);
//        assertTrue(externalContainsAggregation, "外部用户的KnowledgeFile权限规则也应该包含聚合操作符");
//    }

    // 辅助方法：检测文档是否包含聚合操作符
    private boolean containsAggregationOperatorsInDocument(Object obj) {
        if (obj instanceof Document document) {
            for (String key : document.keySet()) {
                if (isAggregationOperatorTest(key)) {
                    return true;
                }
                if (containsAggregationOperatorsInDocument(document.get(key))) {
                    return true;
                }
            }
        } else if (obj instanceof List<?> list) {
            for (Object item : list) {
                if (containsAggregationOperatorsInDocument(item)) {
                    return true;
                }
            }
        }
        return false;
    }

    // 辅助方法：测试聚合操作符识别
    private boolean isAggregationOperatorTest(String operator) {
        return operator.equals("$lookup") || 
               operator.equals("$expr") || 
               operator.equals("$let") || 
               operator.equals("$arrayElemAt") ||
               operator.equals("$facet") ||
               operator.equals("$graphLookup") ||
               operator.equals("$unwind") ||
               operator.equals("$group") ||
               operator.equals("$project") ||
               operator.equals("$addFields") ||
               operator.equals("$replaceRoot") ||
               operator.equals("$redact") ||
               operator.equals("$sort") ||
               operator.equals("$limit") ||
               operator.equals("$skip") ||
               operator.equals("$sample") ||
               operator.equals("$bucket") ||
               operator.equals("$bucketAuto") ||
               operator.equals("$sortByCount") ||
               operator.equals("$collStats") ||
               operator.equals("$indexStats") ||
               operator.equals("$out") ||
               operator.equals("$merge");
    }
} 