package com.polarizon.gendo.sg.authority.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import de.flapdoodle.embed.mongo.MongodExecutable;
import de.flapdoodle.embed.mongo.MongodStarter;
import de.flapdoodle.embed.mongo.config.ImmutableMongodConfig;
import de.flapdoodle.embed.mongo.config.MongodConfig;
import de.flapdoodle.embed.mongo.config.Net;
import de.flapdoodle.embed.mongo.distribution.Version;
import de.flapdoodle.embed.process.runtime.Network;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.io.IOException;

/**
 * MongoDB测试配置类
 * <p>
 * 用于配置嵌入式MongoDB和相关Bean用于测试
 */
@TestConfiguration
public class MongoTestConfig {

    private static final String MONGO_DB_URL = "mongodb://%s:%d";
    private static final String HOST = "localhost";
    
    @Value("${spring.data.mongodb.port}")
    private int port;
    
    @Value("${spring.data.mongodb.database}")
    private String database;
    
    private MongodExecutable mongodExecutable;
    
    @PostConstruct
    void startMongo() throws IOException {
        // 使用de.flapdoodle.embed.mongo启动嵌入式MongoDB
        MongodStarter starter = MongodStarter.getDefaultInstance();
        ImmutableMongodConfig mongodConfig = MongodConfig.builder()
                .version(Version.Main.V4_0)
                .net(new Net(HOST, port, Network.localhostIsIPv6()))
                .build();
        
        mongodExecutable = starter.prepare(mongodConfig);
        mongodExecutable.start();
    }
    
    @PreDestroy
    void stopMongo() {
        if (mongodExecutable != null) {
            mongodExecutable.stop();
        }
    }
    
    @Bean
    public MongoClient mongo() {
        String connectionString = String.format(MONGO_DB_URL, HOST, port);
        return MongoClients.create(connectionString);
    }
    
    @Bean
    public MongoDatabaseFactory mongoDatabaseFactory(MongoClient mongoClient) {
        return new SimpleMongoClientDatabaseFactory(mongoClient, database);
    }
    
    @Bean
    @DependsOn("mongoDatabaseFactory")
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory) {
        // 创建自定义的MongoConverter，移除_class字段
        MongoMappingContext mappingContext = new MongoMappingContext();
        mappingContext.setAutoIndexCreation(false);
        
        MappingMongoConverter converter = new MappingMongoConverter(
                new DefaultDbRefResolver(mongoDatabaseFactory), 
                mappingContext
        );
        
        // 设置类型映射为null，避免添加_class字段
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        
        return new MongoTemplate(mongoDatabaseFactory, converter);
    }
    
    /**
     * 暴露MongoConverter为独立的Bean
     * 用于测试中直接注入MongoConverter
     */
    @Bean
    @DependsOn("mongoTemplate")
    public MappingMongoConverter mongoConverter(MongoTemplate mongoTemplate) {
        return (MappingMongoConverter) mongoTemplate.getConverter();
    }
    
    /**
     * 初始化测试数据
     */
    @Bean
    @DependsOn("mongoTemplate")
    public TestDataInitializer testDataInitializer(MongoTemplate mongoTemplate) {
        return new TestDataInitializer(mongoTemplate);
    }
    
    /**
     * 测试数据初始化器
     */
    public static class TestDataInitializer {
        private final MongoTemplate mongoTemplate;
        
        public TestDataInitializer(MongoTemplate mongoTemplate) {
            this.mongoTemplate = mongoTemplate;
        }
        
        @PostConstruct
        public void init() {
            // 清空所有集合
            mongoTemplate.getCollectionNames().forEach(collectionName -> 
                mongoTemplate.getCollection(collectionName).drop()
            );
            
            // 初始化测试数据
            initTestData();
        }
        
        /**
         * 初始化测试数据
         */
        private void initTestData() {
            // 初始化知识库测试数据
            initKnowledgeBaseData();
            
            // 初始化知识库文件测试数据
            initKnowledgeFileData();
        }
        
        /**
         * 初始化知识库测试数据
         */
        private void initKnowledgeBaseData() {
            String collectionName = "default_com.polarizon.rag.kb.KnowledgeBaseDO";
            
            // 超级管理员创建的公开知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-super-public-1", 
                "超管公开知识库1", 
                "tenant-001", 
                "super-admin-001", 
                true
            ), collectionName);
            
            // 超级管理员创建的私有知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-super-private-1", 
                "超管私有知识库1", 
                "tenant-001", 
                "super-admin-001", 
                false
            ), collectionName);
            
            // 普通管理员创建的公开知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-admin-public-1", 
                "管理员公开知识库1", 
                "tenant-001", 
                "admin-001", 
                true
            ), collectionName);
            
            // 普通管理员创建的私有知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-admin-private-1", 
                "管理员私有知识库1", 
                "tenant-001", 
                "admin-001", 
                false
            ), collectionName);
            
            // 内部用户创建的公开知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-internal-public-1", 
                "内部用户公开知识库1", 
                "tenant-001", 
                "internal-001", 
                true
            ), collectionName);
            
            // 内部用户创建的私有知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-internal-private-1", 
                "内部用户私有知识库1", 
                "tenant-001", 
                "internal-001", 
                false
            ), collectionName);
            
            // 外部用户创建的私有知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-external-private-1", 
                "外部用户私有知识库1", 
                "tenant-001", 
                "external-001", 
                false
            ), collectionName);
            
            // 其他租户的知识库
            mongoTemplate.insert(createKnowledgeBase(
                "kb-other-tenant-1", 
                "其他租户知识库1", 
                "tenant-002", 
                "user-002", 
                true
            ), collectionName);
        }
        
        /**
         * 初始化知识库文件测试数据
         */
        private void initKnowledgeFileData() {
            String collectionName = "default_com.polarizon.rag.kb.KnowledgeFileDO";
            
            // 超级管理员公开知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-super-public-1",
                "kb-super-public-1",
                "超管公开知识库文件1.txt",
                "tenant-001",
                "super-admin-001"
            ), collectionName);
            
            // 超级管理员私有知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-super-private-1",
                "kb-super-private-1",
                "超管私有知识库文件1.txt",
                "tenant-001",
                "super-admin-001"
            ), collectionName);
            
            // 普通管理员公开知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-admin-public-1",
                "kb-admin-public-1",
                "管理员公开知识库文件1.txt",
                "tenant-001",
                "admin-001"
            ), collectionName);
            
            // 普通管理员私有知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-admin-private-1",
                "kb-admin-private-1",
                "管理员私有知识库文件1.txt",
                "tenant-001",
                "admin-001"
            ), collectionName);
            
            // 内部用户公开知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-internal-public-1",
                "kb-internal-public-1",
                "内部用户公开知识库文件1.txt",
                "tenant-001",
                "internal-001"
            ), collectionName);
            
            // 内部用户私有知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-internal-private-1",
                "kb-internal-private-1",
                "内部用户私有知识库文件1.txt",
                "tenant-001",
                "internal-001"
            ), collectionName);
            
            // 外部用户私有知识库的文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-external-private-1",
                "kb-external-private-1",
                "外部用户私有知识库文件1.txt",
                "tenant-001",
                "external-001"
            ), collectionName);
            
            // 其他租户的知识库文件
            mongoTemplate.insert(createKnowledgeFile(
                "file-other-tenant-1",
                "kb-other-tenant-1",
                "其他租户知识库文件1.txt",
                "tenant-002",
                "user-002"
            ), collectionName);
        }
        
        /**
         * 创建知识库文档
         */
        private org.bson.Document createKnowledgeBase(String id, String name, String tenantId, String createdBy, boolean isPublic) {
            org.bson.Document document = new org.bson.Document();
            document.append("_id", id);
            document.append("name", name);
            document.append("tenantId", tenantId);
            document.append("createdBy", createdBy);
            document.append("isPublic", isPublic);
            document.append("createTime", new java.util.Date());
            document.append("updateTime", new java.util.Date());
            return document;
        }
        
        /**
         * 创建知识库文件文档
         */
        private org.bson.Document createKnowledgeFile(String id, String knowledgeBaseId, String fileName, String tenantId, String createdBy) {
            org.bson.Document document = new org.bson.Document();
            document.append("_id", id);
            document.append("knowledgeBaseId", knowledgeBaseId);
            document.append("fileName", fileName);
            document.append("tenantId", tenantId);
            document.append("createdBy", createdBy);
            document.append("createTime", new java.util.Date());
            document.append("updateTime", new java.util.Date());
            return document;
        }
    }
} 