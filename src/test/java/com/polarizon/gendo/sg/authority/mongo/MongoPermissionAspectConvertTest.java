package com.polarizon.gendo.sg.authority.mongo;

import com.polarizon.gendo.sg.authority.core.PermissionManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * MongoPermissionAspect的convertToDocument方法详细测试
 */
@ExtendWith(MockitoExtension.class)
public class MongoPermissionAspectConvertTest {

    @Mock
    private PermissionManager permissionManager;
    
    @Mock
    private MongoTemplate mongoTemplate;
    
    @Mock
    private MongoConverter mongoConverter;

    private MongoPermissionAspect mongoPermissionAspect;

    @BeforeEach
    void setUp() {
        mongoPermissionAspect = new MongoPermissionAspect(permissionManager, mongoTemplate, mongoConverter);
    }

    @Test
    @DisplayName("测试转换简单POJO对象")
    void testConvertSimplePojo() throws Exception {
        // 创建一个简单的测试对象
        SimpleTestEntity entity = new SimpleTestEntity();
        entity.setId("test-123");
        entity.setName("测试名称");
        entity.setActive(true);
        entity.setCount(42);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals("test-123", result.get("id"));
        assertEquals("测试名称", result.get("name"));
        assertEquals(true, result.get("active"));
        assertEquals(42, result.get("count"));
    }
    
    @Test
    @DisplayName("测试转换具有嵌套对象的POJO")
    void testConvertWithNestedObject() throws Exception {
        // 创建嵌套对象
        NestedObject nested = new NestedObject();
        nested.setDescription("嵌套描述");
        nested.setValue(123.45);
        
        // 创建包含嵌套对象的测试对象
        ComplexTestEntity entity = new ComplexTestEntity();
        entity.setId("complex-123");
        entity.setTitle("复杂对象");
        entity.setNestedObject(nested);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals("complex-123", result.get("id"));
        assertEquals("复杂对象", result.get("title"));
        assertNotNull(result.get("nestedObject"));
        
        // 注意：由于实现方式的不同，嵌套对象可能是Document或原始对象
        Object nestedResult = result.get("nestedObject");
        if (nestedResult instanceof Document) {
            Document nestedDoc = (Document) nestedResult;
            assertEquals("嵌套描述", nestedDoc.get("description"));
            assertEquals(123.45, nestedDoc.get("value"));
        } else if (nestedResult instanceof NestedObject) {
            NestedObject nestedObj = (NestedObject) nestedResult;
            assertEquals("嵌套描述", nestedObj.getDescription());
            assertEquals(123.45, nestedObj.getValue());
        } else {
            fail("嵌套对象转换失败");
        }
    }
    
    @Test
    @DisplayName("测试转换包含集合的POJO")
    void testConvertWithCollections() throws Exception {
        // 创建带有集合的测试对象
        CollectionTestEntity entity = new CollectionTestEntity();
        entity.setId("collection-123");
        entity.setTags(Arrays.asList("标签1", "标签2", "标签3"));
        
        Map<String, Integer> scores = new HashMap<>();
        scores.put("math", 95);
        scores.put("science", 87);
        entity.setScores(scores);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals("collection-123", result.get("id"));
        
        // 验证列表
        Object tagsResult = result.get("tags");
        assertNotNull(tagsResult);
        if (tagsResult instanceof List) {
            List<?> tagsList = (List<?>) tagsResult;
            assertEquals(3, tagsList.size());
            assertTrue(tagsList.contains("标签1"));
            assertTrue(tagsList.contains("标签2"));
            assertTrue(tagsList.contains("标签3"));
        } else {
            fail("标签列表转换失败");
        }
        
        // 验证Map
        Object scoresResult = result.get("scores");
        assertNotNull(scoresResult);
        if (scoresResult instanceof Map) {
            Map<?, ?> scoresMap = (Map<?, ?>) scoresResult;
            assertEquals(2, scoresMap.size());
            assertEquals(95, scoresMap.get("math"));
            assertEquals(87, scoresMap.get("science"));
        } else {
            fail("分数Map转换失败");
        }
    }
    
    @Test
    @DisplayName("测试转换有继承关系的POJO")
    void testConvertWithInheritance() throws Exception {
        // 创建子类对象
        ChildEntity entity = new ChildEntity();
        entity.setId("child-123");
        entity.setName("父类名称");
        entity.setAge(30);
        entity.setChildField("子类字段");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals("child-123", result.get("id"));
        assertEquals("父类名称", result.get("name"));
        assertEquals(30, result.get("age"));
        assertEquals("子类字段", result.get("childField"));
    }
    
    @Test
    @DisplayName("测试转换带有注解的POJO")
    void testConvertWithAnnotations() throws Exception {
        // 创建带有注解的测试对象
        AnnotatedEntity entity = new AnnotatedEntity();
        entity.setEntityId("anno-123");
        entity.setEntityName("注解实体");
        entity.setInternalField("内部字段");
        entity.setIgnoredField("应该被忽略");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, entity);
        
        // 验证转换结果
        assertNotNull(result);
        
        // 根据具体实现的不同，可能会忽略或保留某些字段
        // 此处仅作为参考
        if (result.containsKey("_id")) {
            assertEquals("anno-123", result.get("_id"));
        } else if (result.containsKey("entityId")) {
            assertEquals("anno-123", result.get("entityId"));
        }
        
        if (result.containsKey("custom_name")) {
            assertEquals("注解实体", result.get("custom_name"));
        } else if (result.containsKey("entityName")) {
            assertEquals("注解实体", result.get("entityName"));
        }
    }
    
    @Test
    @DisplayName("测试转换null对象")
    void testConvertNull() throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, (Object) null);
        
        // 验证转换结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    @DisplayName("测试转换已经是Document的对象")
    void testConvertDocument() throws Exception {
        // 创建一个Document对象
        Document original = new Document();
        original.append("key1", "value1");
        original.append("key2", 123);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method convertMethod = MongoPermissionAspect.class.getDeclaredMethod("convertToDocument", Object.class);
        convertMethod.setAccessible(true);
        Document result = (Document) convertMethod.invoke(mongoPermissionAspect, original);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals(original, result);
        assertEquals("value1", result.get("key1"));
        assertEquals(123, result.get("key2"));
    }
    
    // 测试实体类
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class SimpleTestEntity {
        private String id;
        private String name;
        private boolean active;
        private int count;
    }
    
    @Data
    @NoArgsConstructor
    static class NestedObject {
        private String description;
        private double value;
    }
    
    @Data
    @NoArgsConstructor
    static class ComplexTestEntity {
        private String id;
        private String title;
        private NestedObject nestedObject;
    }
    
    @Data
    @NoArgsConstructor
    static class CollectionTestEntity {
        private String id;
        private List<String> tags;
        private Map<String, Integer> scores;
    }
    
    @Data
    @NoArgsConstructor
    static class ParentEntity {
        private String id;
        private String name;
        private int age;
    }
    
    @Data
    @NoArgsConstructor
    static class ChildEntity extends ParentEntity {
        private String childField;
    }
    
    @Data
    @NoArgsConstructor
    static class AnnotatedEntity {
        @Id
        @MongoId
        private String entityId;
        
        @Field("custom_name")
        private String entityName;
        
        private String internalField;
        
        private transient String ignoredField;
    }
} 