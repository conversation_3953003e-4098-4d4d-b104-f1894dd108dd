package com.polarizon.gendo.sg.authority.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Resource单元测试
 */
class ResourceTest {

    private Resource resource;
    private Operation readOperation;
    private Operation createOperation;

    @BeforeEach
    void setUp() {
        resource = new Resource();
        resource.setType("MONGO");
        resource.setCollection("users");
        
        // 设置读操作
        readOperation = new Operation();
        Map<String, Object> adminReadRule = new HashMap<>();
        adminReadRule.put("status", "active");
        
        Map<String, Object> userReadRule = new HashMap<>();
        userReadRule.put("createdBy", "${x-user-id}");
        
        readOperation.getDataRules().put("admin", adminReadRule);
        readOperation.getDataRules().put("user", userReadRule);
        readOperation.getDataRules().put("default", null);
        
        // 设置创建操作
        createOperation = new Operation();
        Map<String, Object> adminCreateRule = new HashMap<>();
        adminCreateRule.put("tenantId", "${x-tenant-id}");
        
        createOperation.getDataRules().put("admin", adminCreateRule);
        
        // 添加操作到资源
        resource.getOperations().put("read", readOperation);
        resource.getOperations().put("create", createOperation);
    }

    @Test
    void testGetDataRule() {
        // 测试获取数据规则
        Object adminReadRule = resource.getDataRule("read", "admin");
        assertNotNull(adminReadRule);
        assertTrue(adminReadRule instanceof Map);
        assertEquals("active", ((Map<?, ?>) adminReadRule).get("status"));
        
        Object userReadRule = resource.getDataRule("read", "user");
        assertNotNull(userReadRule);
        assertTrue(userReadRule instanceof Map);
        assertEquals("${x-user-id}", ((Map<?, ?>) userReadRule).get("createdBy"));
        
        Object defaultReadRule = resource.getDataRule("read", "default");
        assertNull(defaultReadRule);
    }

    @Test
    void testGetDataRuleForNonExistentOperation() {
        // 测试获取不存在操作的数据规则
        Object rule = resource.getDataRule("non-existent", "admin");
        assertNull(rule);
    }

    @Test
    void testGetDataRuleForNonExistentRole() {
        // 测试获取不存在角色的数据规则
        Object rule = resource.getDataRule("read", "non-existent");
        assertNull(rule);
    }

    @Test
    void testGetterAndSetter() {
        // 测试类型字段
        assertEquals("MONGO", resource.getType());
        
        // 修改类型
        resource.setType("SQL");
        assertEquals("SQL", resource.getType());
        
        // 测试集合字段
        assertEquals("users", resource.getCollection());
        
        // 修改集合
        resource.setCollection("customers");
        assertEquals("customers", resource.getCollection());
        
        // 测试操作映射
        assertNotNull(resource.getOperations());
        assertEquals(2, resource.getOperations().size());
        assertTrue(resource.getOperations().containsKey("read"));
        assertTrue(resource.getOperations().containsKey("create"));
    }

    @Test
    void testDefaultMap() {
        // 创建新资源，验证operations默认初始化
        Resource newResource = new Resource();
        assertNotNull(newResource.getOperations());
        assertTrue(newResource.getOperations().isEmpty());
    }
} 