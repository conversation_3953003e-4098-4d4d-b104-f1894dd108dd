package com.polarizon.gendo.sg.authority.mongo;

import com.polarizon.gendo.sg.authority.core.PermissionManager;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MongoDB权限切面ID转换功能测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MongoDB权限切面ID转换测试")
class MongoPermissionAspectIdConversionTest {

    @Mock
    private PermissionManager permissionManager;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoConverter mongoConverter;

    private MongoPermissionAspect aspect;

    @BeforeEach
    void setUp() {
        aspect = new MongoPermissionAspect(permissionManager, mongoTemplate, mongoConverter);
    }

    @Test
    @DisplayName("测试有效ObjectId字符串识别")
    void testIsValidObjectIdString() throws Exception {
        // 使用反射访问私有方法
        Method method = MongoPermissionAspect.class.getDeclaredMethod("isValidObjectIdString", String.class);
        method.setAccessible(true);

        // 有效的ObjectId字符串
        assertTrue((Boolean) method.invoke(aspect, "507f1f77bcf86cd799439011"));
        assertTrue((Boolean) method.invoke(aspect, "5f8f8c44b54764421b7156c1"));

        // 无效的ObjectId字符串
        assertFalse((Boolean) method.invoke(aspect, "invalid"));
        assertFalse((Boolean) method.invoke(aspect, "507f1f77bcf86cd79943901")); // 23位
        assertFalse((Boolean) method.invoke(aspect, "507f1f77bcf86cd7994390111")); // 25位
        assertFalse((Boolean) method.invoke(aspect, "507f1f77bcf86cd79943901g")); // 包含非十六进制字符
    }

    @Test
    @DisplayName("测试ID字段识别")
    void testIsIdField() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("isIdField", String.class);
        method.setAccessible(true);

        // 应该识别为ID字段
        assertTrue((Boolean) method.invoke(aspect, "_id"));
        assertTrue((Boolean) method.invoke(aspect, "id"));
        assertTrue((Boolean) method.invoke(aspect, "userId"));
        assertTrue((Boolean) method.invoke(aspect, "user_id"));
        assertTrue((Boolean) method.invoke(aspect, "parent.id"));
        assertTrue((Boolean) method.invoke(aspect, "parent._id"));

        // 不应该识别为ID字段
        assertFalse((Boolean) method.invoke(aspect, "name"));
        assertFalse((Boolean) method.invoke(aspect, "title"));
        assertFalse((Boolean) method.invoke(aspect, "content"));
    }

    @Test
    @DisplayName("测试String到ObjectId转换")
    void testConvertToObjectIdIfNeeded() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertToObjectIdIfNeeded", Object.class);
        method.setAccessible(true);

        // 有效的ObjectId字符串应该被转换
        String validIdString = "507f1f77bcf86cd799439011";
        Object result = method.invoke(aspect, validIdString);
        assertInstanceOf(ObjectId.class, result);
        assertEquals(validIdString, result.toString());

        // 无效的字符串不应该被转换
        String invalidString = "invalid";
        result = method.invoke(aspect, invalidString);
        assertEquals(invalidString, result);

        // 非字符串值不应该被转换
        Integer number = 123;
        result = method.invoke(aspect, number);
        assertEquals(number, result);

        // ObjectId列表转换测试
        List<String> idList = Arrays.asList("507f1f77bcf86cd799439011", "5f8f8c44b54764421b7156c1", "invalid");
        result = method.invoke(aspect, idList);
        assertInstanceOf(List.class, result);
        
        @SuppressWarnings("unchecked")
        List<Object> convertedList = (List<Object>) result;
        assertEquals(3, convertedList.size());
        assertInstanceOf(ObjectId.class, convertedList.get(0));
        assertInstanceOf(ObjectId.class, convertedList.get(1));
        assertEquals("invalid", convertedList.get(2)); // 无效的应该保持原样
    }

    @Test
    @DisplayName("测试Document中的ID转换")
    void testConvertStringIdsInDocument() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertStringIdsInDocument", Document.class);
        method.setAccessible(true);

        // 创建包含String ID的Document
        Document document = new Document();
        document.put("_id", "507f1f77bcf86cd799439011");
        document.put("userId", "5f8f8c44b54764421b7156c1");
        document.put("name", "test");
        document.put("age", 25);

        // 执行转换
        method.invoke(aspect, document);

        // 验证ID字段被转换为ObjectId
        assertInstanceOf(ObjectId.class, document.get("_id"));
        assertInstanceOf(ObjectId.class, document.get("userId"));
        assertEquals("test", document.get("name")); // 非ID字段保持不变
        assertEquals(25, document.get("age")); // 非ID字段保持不变
    }

    @Test
    @DisplayName("测试嵌套Document中的ID转换")
    void testConvertStringIdsInNestedDocument() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertStringIdsInDocument", Document.class);
        method.setAccessible(true);

        // 创建嵌套Document
        Document nestedDoc = new Document();
        nestedDoc.put("parentId", "507f1f77bcf86cd799439011");
        nestedDoc.put("name", "nested");

        Document document = new Document();
        document.put("_id", "5f8f8c44b54764421b7156c1");
        document.put("nested", nestedDoc);
        document.put("title", "main");

        // 执行转换
        method.invoke(aspect, document);

        // 验证转换结果
        assertInstanceOf(ObjectId.class, document.get("_id"));
        assertEquals("main", document.get("title"));
        
        Document convertedNested = (Document) document.get("nested");
        assertInstanceOf(ObjectId.class, convertedNested.get("parentId"));
        assertEquals("nested", convertedNested.get("name"));
    }

    @Test
    @DisplayName("测试Query对象中的ID转换")
    void testConvertStringIdsInQuery() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertStringIdsInQuery", Query.class);
        method.setAccessible(true);

        // 创建包含String ID的Query
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is("507f1f77bcf86cd799439011"));
        query.addCriteria(Criteria.where("userId").in(Arrays.asList("5f8f8c44b54764421b7156c1", "507f191e810c19729de860ea")));

        // 执行转换
        method.invoke(aspect, query);

        // 验证查询条件中的ID被转换
        Document queryDoc = query.getQueryObject();
        assertNotNull(queryDoc);
        
        // 注意：由于Criteria的内部实现复杂，这里主要验证方法调用不抛异常
        // 实际的转换验证可以通过集成测试进行
    }

    @Test
    @DisplayName("测试复杂查询条件中的ID转换")
    void testComplexQueryIdConversion() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertStringIdsRecursive", Object.class);
        method.setAccessible(true);

        // 创建复杂的查询条件Document
        Document complexQuery = new Document();
        
        // $or条件
        Document orCondition1 = new Document("_id", "507f1f77bcf86cd799439011");
        Document orCondition2 = new Document("userId", "5f8f8c44b54764421b7156c1");
        complexQuery.put("$or", Arrays.asList(orCondition1, orCondition2));
        
        // $in条件
        complexQuery.put("categoryId", new Document("$in", Arrays.asList("507f191e810c19729de860ea", "5f8f8c44b54764421b7156c2")));

        // 执行转换
        method.invoke(aspect, complexQuery);

        // 验证转换结果
        @SuppressWarnings("unchecked")
        List<Document> orList = (List<Document>) complexQuery.get("$or");
        assertInstanceOf(ObjectId.class, orList.get(0).get("_id"));
        assertInstanceOf(ObjectId.class, orList.get(1).get("userId"));

        Document inCondition = (Document) complexQuery.get("categoryId");
        @SuppressWarnings("unchecked")
        List<Object> inList = (List<Object>) inCondition.get("$in");
        assertInstanceOf(ObjectId.class, inList.get(0));
        assertInstanceOf(ObjectId.class, inList.get(1));
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() throws Exception {
        Method method = MongoPermissionAspect.class.getDeclaredMethod("convertToObjectIdIfNeeded", Object.class);
        method.setAccessible(true);

        // 测试null值
        Object result = method.invoke(aspect, (Object) null);
        assertNull(result);

        // 测试格式正确但无效的ObjectId字符串
        String invalidObjectId = "000000000000000000000000"; // 技术上有效但可能无效的ObjectId
        result = method.invoke(aspect, invalidObjectId);
        // 应该被转换为ObjectId（因为格式正确）
        assertInstanceOf(ObjectId.class, result);
    }
} 