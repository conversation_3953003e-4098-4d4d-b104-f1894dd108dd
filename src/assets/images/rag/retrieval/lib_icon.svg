<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-3" y="43" width="62" height="18"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_1558_31524_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_ii_1558_31524)" data-figma-bg-blur-radius="6">
<path d="M50.826 49H5.17384C3.97234 49 3 50.3419 3 52C3 53.6554 3.97234 55 5.17384 55H50.826C52.0253 55 53 53.6554 53 52C53 50.3418 52.0252 49 50.826 49Z" fill="#D5D4FF" fill-opacity="0.65"/>
</g>
<path d="M53.826 51H8.17384C6.97234 51 6 52.3419 6 54C6 55.6554 6.97234 57 8.17384 57H53.826C55.0253 57 56 55.6554 56 54C56 52.3418 55.0252 51 53.826 51Z" fill="url(#paint0_linear_1558_31524)"/>
<foreignObject x="0" y="-3" width="56" height="53"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_1_1558_31524_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_ii_1558_31524)" data-figma-bg-blur-radius="6">
<path d="M24.5264 44H31.4736C32.7512 44 33.7895 42.9788 33.7895 41.7221V5.27764C33.7895 4.02085 32.7512 3 31.4736 3H24.5264C23.2462 3 22.2104 4.02085 22.2104 5.27764V41.7221C22.2104 42.9788 23.2462 44 24.5264 44ZM40.7367 44H47.6842C48.9618 44 50 42.9788 50 41.7221V12.1108C50 10.854 48.9617 9.83286 47.6842 9.83286H40.7367C39.4566 9.83286 38.4208 10.854 38.4208 12.1108V41.7221C38.4208 42.9788 39.4566 44 40.7367 44ZM8.31572 44H15.2632C16.5407 44 17.5789 42.9788 17.5789 41.7221V16.666C17.5789 15.4092 16.5407 14.3883 15.2632 14.3883H8.31572C7.0358 14.3883 6 15.4092 6 16.666V41.7221C6 42.9788 7.0358 44 8.31572 44Z" fill="#D5D4FF" fill-opacity="0.65"/>
</g>
<path d="M27.5264 47H34.4736C35.7512 47 36.7895 45.9788 36.7895 44.7221V8.27764C36.7895 7.02085 35.7512 6 34.4736 6H27.5264C26.2462 6 25.2104 7.02085 25.2104 8.27764V44.7221C25.2104 45.9788 26.2462 47 27.5264 47ZM43.7367 47H50.6842C51.9618 47 53 45.9788 53 44.7221V15.1108C53 13.854 51.9617 12.8329 50.6842 12.8329H43.7367C42.4566 12.8329 41.4208 13.854 41.4208 15.1108V44.7221C41.4208 45.9788 42.4566 47 43.7367 47ZM11.3157 47H18.2632C19.5407 47 20.5789 45.9788 20.5789 44.7221V19.666C20.5789 18.4092 19.5407 17.3883 18.2632 17.3883H11.3157C10.0358 17.3883 9 18.4092 9 19.666V44.7221C9 45.9788 10.0358 47 11.3157 47Z" fill="url(#paint1_linear_1558_31524)"/>
<g filter="url(#filter2_d_1558_31524)">
<circle cx="15" cy="40" r="3" fill="white"/>
</g>
<g filter="url(#filter3_d_1558_31524)">
<circle cx="31" cy="40" r="3" fill="white"/>
</g>
<g filter="url(#filter4_d_1558_31524)">
<circle cx="47" cy="40" r="3" fill="white"/>
</g>
<defs>
<filter id="filter0_ii_1558_31524" x="-3" y="43" width="62" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1558_31524"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1558_31524" result="effect2_innerShadow_1558_31524"/>
</filter>
<clipPath id="bgblur_0_1558_31524_clip_path" transform="translate(3 -43)"><path d="M50.826 49H5.17384C3.97234 49 3 50.3419 3 52C3 53.6554 3.97234 55 5.17384 55H50.826C52.0253 55 53 53.6554 53 52C53 50.3418 52.0252 49 50.826 49Z"/>
</clipPath><filter id="filter1_ii_1558_31524" x="0" y="-3" width="56" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1558_31524"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-0.5"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1558_31524" result="effect2_innerShadow_1558_31524"/>
</filter>
<clipPath id="bgblur_1_1558_31524_clip_path" transform="translate(0 3)"><path d="M24.5264 44H31.4736C32.7512 44 33.7895 42.9788 33.7895 41.7221V5.27764C33.7895 4.02085 32.7512 3 31.4736 3H24.5264C23.2462 3 22.2104 4.02085 22.2104 5.27764V41.7221C22.2104 42.9788 23.2462 44 24.5264 44ZM40.7367 44H47.6842C48.9618 44 50 42.9788 50 41.7221V12.1108C50 10.854 48.9617 9.83286 47.6842 9.83286H40.7367C39.4566 9.83286 38.4208 10.854 38.4208 12.1108V41.7221C38.4208 42.9788 39.4566 44 40.7367 44ZM8.31572 44H15.2632C16.5407 44 17.5789 42.9788 17.5789 41.7221V16.666C17.5789 15.4092 16.5407 14.3883 15.2632 14.3883H8.31572C7.0358 14.3883 6 15.4092 6 16.666V41.7221C6 42.9788 7.0358 44 8.31572 44Z"/>
</clipPath><filter id="filter2_d_1558_31524" x="10" y="36" width="10" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.26038 0 0 0 0 0.320711 0 0 0 0 0.877608 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1558_31524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1558_31524" result="shape"/>
</filter>
<filter id="filter3_d_1558_31524" x="26" y="36" width="10" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.26038 0 0 0 0 0.320711 0 0 0 0 0.877608 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1558_31524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1558_31524" result="shape"/>
</filter>
<filter id="filter4_d_1558_31524" x="42" y="36" width="10" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.26038 0 0 0 0 0.320711 0 0 0 0 0.877608 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1558_31524"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1558_31524" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1558_31524" x1="10.4286" y1="51.42" x2="11.2984" y2="61.0505" gradientUnits="userSpaceOnUse">
<stop stop-color="#95A0FF"/>
<stop offset="1" stop-color="#5667FA"/>
</linearGradient>
<linearGradient id="paint1_linear_1558_31524" x1="12.8971" y1="8.87" x2="44.0868" y2="53.3396" gradientUnits="userSpaceOnUse">
<stop stop-color="#95A0FF"/>
<stop offset="1" stop-color="#5667FA"/>
</linearGradient>
</defs>
</svg>
