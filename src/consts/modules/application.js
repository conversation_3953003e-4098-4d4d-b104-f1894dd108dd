import { col24, col12, labelCol5, wrapperCol19 } from '@/consts'
import { Image } from 'ant-design-vue'
import { useApplicationStore } from '@/stores/modules/application.js'
import appAPI from '@/api/application'
import { categoryAPI } from '@/api/knowledge.js'
import { useKnowledgeStore } from '@/stores/modules/knowledge'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const knowledgeStore = useKnowledgeStore()
import { useStatusLists } from './knowledge'
import { regex, utils, constants } from 'gendo-shared'

export const appTypeLists = [
  { value: 'WPS', label: 'WPS插件智能写作助手' },
  { value: 'INSPECT', label: 'WPS插件智能审查' }
]

export const pageData = {
  designScope: 'application-template',
  tableProps: {
    api: appAPI.appPage,
    defSort: {
      sort: 'createTime,desc'
    },
    bordered: false,
    tableSetting: {
      cacheKey: 'application'
    },
    notShowTable: true,
    useFormPageCache: true,
    beforeFetch: (params) => {
      // console.log('查询应用参数', params)
      params.visible = [userStore.getUserInfo.userId, 'public']
      return params
    },
    afterFetch: (list) => {
      list.forEach((item) => {
        item.status = item.enable === 'ENABLE'
      })
    },
    showTableSetting: false,
    useSettingSearchForm: true,
    formConfig: {
      labelAlign: 'right',
      autoSearch: true,
      // labelWidth: 80,
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 12,
        lg: 8,
        xl: 8,
        xxl: 8
      },
      baseRowStyle: {
        width: '1200px',
        justifyContent: 'end'
      },
      schemas: [
        {
          field: 'type',
          component: 'Select',
          label: '应用类型',
          componentProps: {
            options: appTypeLists
          }
        },
        {
          field: 'nameLike',
          component: 'Input',
          label: '应用名称',
          componentProps: {
            placeholder: '请输入关键字搜索'
          }
        },
        {
          field: 'enable',
          component: 'Select',
          label: '使用状态',
          componentProps: {
            mode: 'multiple',
            placeholder: '请选择',
            options: useStatusLists
          }
        }
      ]
    },
    // useSearchForm: true,
    showAdvancedButton: false,
    labelWidth: 'max-content'
  }
}
export const titleCfg = [
  {
    value: 'temperature',
    label: '溫度'
  },
  {
    value: 'topP',
    label: 'Top P'
  },
  {
    value: 'presencePenalty',
    label: '存在惩罚'
  },
  {
    value: 'frequencyPenalty',
    label: '频率惩罚'
  }
]

export const formSchema = [
  {
    field: 'basicInfo',
    component: 'FormTitle',
    label: '基本信息'
  },
  {
    field: 'logoUrlS3',
    component: 'Input',
    label: '图标',
    slot: 'logo'
  },
  {
    field: 'type',
    component: 'Text',
    label: '应用类型',
    // slot: 'span',
    render: ({ model, field }) => h('span', {}, appTypeLists.find((item) => item.value === model[field])?.label || '--')
  },
  {
    field: 'name',
    component: 'Text',
    label: '标题',
    slot: 'span'
  },
  {
    field: 'description',
    component: 'Text',
    label: '描述',
    slot: 'span'
  },
  {
    field: 'modelSetting',
    component: 'FormTitle',
    label: '模型设置',
    slot: 'span'
  },
  {
    field: 'modelDisplayName',
    component: 'Text',
    label: '推理模型',
    slot: 'span'
  },
  {
    field: 'modeName',
    component: 'modeRadio',
    label: '模式',
    componentProps: {
      isOnlyRead: true,
      values: []
    }
  },
  {
    field: 'searchSetting',
    component: 'FormTitle',
    label: '检索设置'
  },
  {
    field: 'rerankTopN',
    component: 'Text',
    label: '引用分段Top N',
    slot: 'span'
  },
  {
    field: 'knowledgeLists',
    component: 'Text',
    label: '可用知识库',
    slot: 'knowledgeLists'
  }
]
/**测试数据 */
export const inferenceModelLists = [
  {
    label: 'Qwen1.5-32B',
    value: '1'
  },
  {
    label: 'GPT-3.5-Turbo',
    value: '2'
  }
]
/**新增 */
export const addFormSchema = [
  {
    field: 'basicInfo',
    component: 'FormTitle',
    label: '基本信息'
  },
  {
    field: 'logoUrlS3',
    component: 'Input',
    label: '图标',
    slot: 'logo',
    required: true
  },
  {
    field: 'type',
    component: 'RadioGroup',
    label: '应用类型',
    componentProps: {
      options: appTypeLists
    },
    defaultValue: 'WPS',
    required: true
  },
  {
    field: 'name',
    component: 'Input',
    label: '标题',
    componentProps: {
      placeholder: '请输入1-50长度的中英文、数字、中划线、下划线',
      maxlength: 50
    },
    rules: [
      {
        required: true,
        pattern: regex.ChineseEnglishNumberUnderlineReg,
        message: '请输入1-50长度的中英文、数字、中划线、下划线',
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'description',
    component: 'InputTextArea',
    label: '描述',
    componentProps: {
      placeholder: '请输入描述',
      maxlength: 100,
      showCount: true,
      autosize: {
        minRows: 4,
        maxRows: 4
      }
    },
    rules: [
      {
        required: true,
        message: '请输入描述',
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'modelSetting',
    component: 'FormTitle',
    label: '模型设置'
  },
  {
    field: 'modelInfoID',
    component: 'Select',
    label: '推理模型',
    componentProps: {
      options: [],
      showSearch: true,
      optionFilterProp: 'label'
    },
    required: true
  },
  {
    field: 'modeName',
    component: 'modeRadio',
    label: '模式',
    componentProps: {
      values: []
    },
    required: true
  },
  {
    field: 'searchSetting',
    component: 'FormTitle',
    label: '检索设置'
  },
  {
    field: 'rerankTopN',
    component: 'Input',
    label: '引用分段Top N',
    defaultValue: '4',
    required: true,
    componentProps: {
      placeholder: '请输入1-20的正整数,推荐4'
    },
    rules: [
      {
        validator: async (_, value) => {
          if (!value || !/^(1[0-9]|20|[1-9])$/.test(value)) return Promise.reject('请输入1-20的正整数,推荐4')
          return Promise.resolve()
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'knowledgeLists',
    component: 'Text',
    label: '可用知识库',
    slot: 'knowledgeLists'
  }
]

/**选择知识库 */
export const formSchemas = (knowledgeStore) => {
  return [
    {
      field: 'nameLike',
      component: 'Input',
      label: '知识库名称',
      componentProps: {
        placeholder: '请输入关键字搜索'
      },
      colProps: col12,
      itemProps: {
        labelCol: {
          style: {
            'min-width': 'max-content'
          }
        },

        wrapperCol: {
          // ...col24,
          style: {
            width: '250px'
          }
        }
      }
    },
    {
      label: '所属分类',
      field: 'categoryIDs',
      component: 'ApiTreeSelect',
      // colProps: {
      //   style: {
      //     width: '100%'
      //   }
      // },
      componentProps: (option) => {
        return {
          class: 'tree-select-scroll',
          showCheckedStrategy: 'SHOW_ALL',
          treeCheckable: true,
          showSearch: true,
          treeNodeFilterProp: 'name',
          // allowClear:true
          api: categoryAPI.list,
          params: {},
          resultField: 'result',
          fieldNames: { label: 'name', value: 'id', children: 'subBOs', key: 'id' }
        }
      },
      colProps: col12,
      itemProps: {
        // labelCol: {
        //   style: {
        //     'min-width': 'max-content'
        //   }
        // },

        wrapperCol: {
          ...col24,
          style: {
            width: '250px'
          }
        }
      }
    }
  ]
}
