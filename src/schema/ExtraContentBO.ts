export const extracontent = `#gateway
# Comments in GraphQL strings (such as this one) start with the hash (#) symbol.

""" 查询结果类型:  """
type ExtraContentVO {
    """ 主键ObjectID """
    id: ID
    """ 备注信息 """
    remark: String
    """ 类型名称 """
    selfTypeName: String
    """ 数据标签 """
    dataTag: [String]
    """ 领域标签 """
    domainLabel: JSON
    """ 创建者 """
    createBy: String
    """ 创建者BO """
    createByBO: UserVO
    """ 更新者 """
    updateBy: String
    """ 更新者BO """
    updateByBO: UserVO
    """ 创建时间 """
    createTime: Long
    """ 更新时间 """
    updateTime: Long
    """ 软删除标志: LIVE (存活),DEAD (死亡) """
    statusFlag: StatusFlag
    """ 内容 """
    content: String
    """ 类型 """
    type: String
    """ 名称 """
    name: String
    """ 排序 """
    sort: Int
}



""" 输入类型:  """
input ExtraContentForm {
    """ 主键ObjectID """
    id: ID
    """ 备注信息 """
    remark: String
    """ 数据标签 """
    dataTag: [String]
    """ 领域标签 """
    domainLabel: JSON
     """ 需要删除的字段 """
    unsetFields: [String]
    """ 软删除标志: LIVE (存活),DEAD (死亡) """
    statusFlag: StatusFlag
    """ 内容 """
    content: String

    """ 类型 """
    type: String

    """ 名称 """
    name: String

    """ 排序 """
    sort: Int

}

""" 输入类型: 视图:  """
input ExtraContentViewForm {
    content: [String]
    contentLike: String
    type: [String]
    typeLike: String
    name: [String]
    nameLike: String
    sort: [Int]
    createBy: [String]
    createByLike: String
    updateBy: [String]
    updateByLike: String
    startCreateTime: Long
    endCreateTime: Long
    startUpdateTime: Long
    endUpdateTime: Long
    remark: [String]
    remarkLike: String
    statusFlag: [StatusFlag]
    orderBy: String
}


"""  查询类型: 视图:  """
input ExtraContentParams {
    
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
}




`;export const extracontentQuery = `
    extraContent_one(id: ID!): ExtraContentVO
    extraContent_more(ids: [ID!]!): [ExtraContentVO]
    extraContent_more_page(ids: [ID!]!, pageable: Pageable!): ExtraContentPage
    extraContent_all: [ExtraContentVO]
    extraContent_all_page(pageable: Pageable!): ExtraContentPage
    extraContent_custom(mongoQuery: String!): [ExtraContentVO]
	extraContent_count(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
): Int
    extraContent_custom_page(mongoQuery: String!, pageable: Pageable!): ExtraContentPage
    extraContent_some(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        orderBy: String
    ): [ExtraContentVO]
    extraContent_some_page(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
       orderBy: String
 pageable: Pageable!
    ): ExtraContentPage

    extraContent_admin(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag]
    ): [ExtraContentVO]
    extraContent_admin_page(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        pageable: Pageable!
    ): ExtraContentPage
    extraContent_distinct_field(fieldName: String!,
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
): [String]
    extraContent_count_value(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        fields: [String]!,
 sort: String,
 queryJson: String): ExtraContentVOCount
    extraContent_group_value(
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        fields: [String]!,
 sort: String,
 queryJson: String): ExtraContentVOGroup
    extraContent_count_page(
        
        """ 数组精确查询:  内容  """
        content: [String],
        """ 模糊查询:  内容  """
        contentLike: String,
        """ 数组精确查询:  类型  """
        type: [String],
        """ 模糊查询:  类型  """
        typeLike: String,
        """ 数组精确查询:  名称  """
        name: [String],
        """ 模糊查询:  名称  """
        nameLike: String,
        """ 数组精确查询:  排序  """
        sort: [Int],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        countField: String!,
        groupField: String!,
        filter: String,
        pageable: Pageable!
    ): GroupCountPage


`;
