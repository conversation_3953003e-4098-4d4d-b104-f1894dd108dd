export const modelinfo = `#gateway
# Comments in GraphQL strings (such as this one) start with the hash (#) symbol.

""" 查询结果类型: 推理模型信息 """
type ModelInfoVO {
    """ 主键ObjectID """
    id: ID
    """ 备注信息 """
    remark: String
    """ 类型名称 """
    selfTypeName: String
    """ 数据标签 """
    dataTag: [String]
    """ 领域标签 """
    domainLabel: JSON
    """ 创建者 """
    createBy: String
    """ 创建者BO """
    createByBO: UserVO
    """ 更新者 """
    updateBy: String
    """ 更新者BO """
    updateByBO: UserVO
    """ 创建时间 """
    createTime: Long
    """ 更新时间 """
    updateTime: Long
    """ 软删除标志: LIVE (存活),DEAD (死亡) """
    statusFlag: StatusFlag
    """ 图标网址 """
    logoUrlS3: String
    """ 模型类型 """
    type: ModelTypeEnum
    """ 推理模型展示的名称 """
    displayName: String
    """ 推理模型名称，新版，直接用于展示和模型调用 """
    name: String
    """ 描述 """
    description: String
    """ apiKey """
    apiKey: String
    """ endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/ """
    endpointUrl: String
    """ 最大输出token数(过时的配置) """
    maxTokens: Int
    """ 最大输出token数，必填 """
    maxOutputTokens: Int
    """ 模型最大上下文长度，必填 """
    contextLength: Int
    """ 模型最大输入长度 """
    maxInputLength: Int
    """ 模型来源的类型 """
    sourceType: ModelSourceTypeEnum
    """ 模型使能: ENABLE-启用, DISABLE-停用 """
    enable: ModelEnableEnum
    """ 是否是默认的 """
    isDefault: Boolean
    """ 否是推理模型 """
    isReasoning: Boolean
}



""" 输入类型: 推理模型信息 """
input ModelInfoForm {
    """ 主键ObjectID """
    id: ID
    """ 备注信息 """
    remark: String
    """ 数据标签 """
    dataTag: [String]
    """ 领域标签 """
    domainLabel: JSON
     """ 需要删除的字段 """
    unsetFields: [String]
    """ 软删除标志: LIVE (存活),DEAD (死亡) """
    statusFlag: StatusFlag
    """ 图标网址 """
    logoUrlS3: String

    """ 模型类型 """
    type: ModelTypeEnum

    """ 推理模型展示的名称 """
    displayName: String

    """ 推理模型名称，新版，直接用于展示和模型调用 """
    name: String

    """ 描述 """
    description: String

    """ apiKey """
    apiKey: String

    """ endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/ """
    endpointUrl: String

    """ 最大输出token数(过时的配置) """
    maxTokens: Int

    """ 最大输出token数，必填 """
    maxOutputTokens: Int

    """ 模型最大上下文长度，必填 """
    contextLength: Int

    """ 模型最大输入长度 """
    maxInputLength: Int

    """ 模型来源的类型 """
    sourceType: ModelSourceTypeEnum

    """ 模型使能: ENABLE-启用, DISABLE-停用 """
    enable: ModelEnableEnum

    """ 是否是默认的 """
    isDefault: Boolean

    """ 否是推理模型 """
    isReasoning: Boolean

}

""" 输入类型: 视图: 推理模型信息 """
input ModelInfoViewForm {
    logoUrlS3: [String]
    logoUrlS3Like: String
    type: [ModelTypeEnum]
    displayName: [String]
    displayNameLike: String
    name: [String]
    nameLike: String
    description: [String]
    descriptionLike: String
    apiKey: [String]
    apiKeyLike: String
    endpointUrl: [String]
    endpointUrlLike: String
    maxTokens: [Int]
    maxOutputTokens: [Int]
    contextLength: [Int]
    maxInputLength: [Int]
    sourceType: [ModelSourceTypeEnum]
    enable: [ModelEnableEnum]
    isDefault: [Boolean]
    isReasoning: [Boolean]
    createBy: [String]
    createByLike: String
    updateBy: [String]
    updateByLike: String
    startCreateTime: Long
    endCreateTime: Long
    startUpdateTime: Long
    endUpdateTime: Long
    remark: [String]
    remarkLike: String
    statusFlag: [StatusFlag]
    orderBy: String
}


"""  查询类型: 视图: 推理模型信息 """
input ModelInfoParams {
    
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
}


""" 属性统计类型: 推理模型信息 """
type ModelInfoVOCount {
    """ 计数 图标网址 """
    logoUrlS3: [CountItem]
    """ 计数 模型类型 """
    type: [CountItem]
    """ 计数 推理模型展示的名称 """
    displayName: [CountItem]
    """ 计数 推理模型名称，新版，直接用于展示和模型调用 """
    name: [CountItem]
    """ 计数 描述 """
    description: [CountItem]
    """ 计数 apiKey """
    apiKey: [CountItem]
    """ 计数 endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/ """
    endpointUrl: [CountItem]
    """ 计数 最大输出token数(过时的配置) """
    maxTokens: [CountItem]
    """ 计数 最大输出token数，必填 """
    maxOutputTokens: [CountItem]
    """ 计数 模型最大上下文长度，必填 """
    contextLength: [CountItem]
    """ 计数 模型最大输入长度 """
    maxInputLength: [CountItem]
    """ 计数 模型来源的类型 """
    sourceType: [CountItem]
    """ 计数 模型使能: ENABLE-启用, DISABLE-停用 """
    enable: [CountItem]
    """ 计数 是否是默认的 """
    isDefault: [CountItem]
    """ 计数 否是推理模型 """
    isReasoning: [CountItem]
}

"""属性分组item"""
type ModelInfoVOGroupItem{
   value: String
   objects: [ModelInfoVO]
}

""" 属性分组类型: 推理模型信息 """
type ModelInfoVOGroup {
    """ 计数 图标网址 """
    logoUrlS3: [ModelInfoVOGroupItem]
    """ 计数 模型类型 """
    type: [ModelInfoVOGroupItem]
    """ 计数 推理模型展示的名称 """
    displayName: [ModelInfoVOGroupItem]
    """ 计数 推理模型名称，新版，直接用于展示和模型调用 """
    name: [ModelInfoVOGroupItem]
    """ 计数 描述 """
    description: [ModelInfoVOGroupItem]
    """ 计数 apiKey """
    apiKey: [ModelInfoVOGroupItem]
    """ 计数 endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/ """
    endpointUrl: [ModelInfoVOGroupItem]
    """ 计数 最大输出token数(过时的配置) """
    maxTokens: [ModelInfoVOGroupItem]
    """ 计数 最大输出token数，必填 """
    maxOutputTokens: [ModelInfoVOGroupItem]
    """ 计数 模型最大上下文长度，必填 """
    contextLength: [ModelInfoVOGroupItem]
    """ 计数 模型最大输入长度 """
    maxInputLength: [ModelInfoVOGroupItem]
    """ 计数 模型来源的类型 """
    sourceType: [ModelInfoVOGroupItem]
    """ 计数 模型使能: ENABLE-启用, DISABLE-停用 """
    enable: [ModelInfoVOGroupItem]
    """ 计数 是否是默认的 """
    isDefault: [ModelInfoVOGroupItem]
    """ 计数 否是推理模型 """
    isReasoning: [ModelInfoVOGroupItem]
}

""" 分页类型: 推理模型信息 """
type ModelInfoPage {
    """ 是否有前一页？ """
    hasNext: Boolean
    """ 是否有后一页？ """
    hasPrevious: Boolean
    """ 当前页序号 """
    pageNumber: Int
    """ 页大小 """
    pageSize: Int
    """ 总页数 """
    pages: Int
    """ 总数据量 """
    total: Int
    """ 返回结果列表 """
    result: [ModelInfoVO]
}

""" 单个ResultDTO<ObjectVO>类型: 推理模型信息 """
type ModelInfoVOResult {
    """ "状态码: 0成功, -1失败, -2数据错误, -3数据已禁用, -4数据不存在" """
    status: Int
    """ 返回提示消息 """
    msg: String
    """ 返回业务数据 """
    data: ModelInfoVO
}

""" 多个ResultDTO<List<ObjectVO>>类型: 推理模型信息 """
type ModelInfoVOResultList {
    """ "状态码: 0成功, -1失败, -2数据错误, -3数据已禁用, -4数据不存在" """
    status: Int
    """ 返回提示消息 """
    msg: String
    """ 返回业务数据 """
    data: [ModelInfoVO]
}

""" 分页ResultDTO<Page<ObjectVO>>类型: 推理模型信息 """
type ModelInfoVOResultPage {
    """ "状态码: 0成功, -1失败, -2数据错误, -3数据已禁用, -4数据不存在" """
    status: Int
    """ 返回提示消息 """
    msg: String
    """ 返回业务数据 """
    data: ModelInfoPage
}

`;export const modelinfoQuery = `
    modelInfo_one(id: ID!): ModelInfoVO
    modelInfo_more(ids: [ID!]!): [ModelInfoVO]
    modelInfo_more_page(ids: [ID!]!, pageable: Pageable!): ModelInfoPage
    modelInfo_all: [ModelInfoVO]
    modelInfo_all_page(pageable: Pageable!): ModelInfoPage
    modelInfo_custom(mongoQuery: String!): [ModelInfoVO]
	modelInfo_count(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
): Int
    modelInfo_custom_page(mongoQuery: String!, pageable: Pageable!): ModelInfoPage
    modelInfo_some(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        orderBy: String
    ): [ModelInfoVO]
    modelInfo_some_page(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
       orderBy: String
 pageable: Pageable!
    ): ModelInfoPage

    modelInfo_admin(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag]
    ): [ModelInfoVO]
    modelInfo_admin_page(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        pageable: Pageable!
    ): ModelInfoPage
    modelInfo_distinct_field(fieldName: String!,
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
): [String]
    modelInfo_count_value(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        fields: [String]!,
 sort: String,
 queryJson: String): ModelInfoVOCount
    modelInfo_group_value(
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        fields: [String]!,
 sort: String,
 queryJson: String): ModelInfoVOGroup
    modelInfo_count_page(
        
        """ 数组精确查询:  图标网址  """
        logoUrlS3: [String],
        """ 模糊查询:  图标网址  """
        logoUrlS3Like: String,
        """ 数组精确查询:  模型类型  """
        type: [ModelTypeEnum],
        """ 数组精确查询:  推理模型展示的名称  """
        displayName: [String],
        """ 模糊查询:  推理模型展示的名称  """
        displayNameLike: String,
        """ 数组精确查询:  推理模型名称，新版，直接用于展示和模型调用  """
        name: [String],
        """ 模糊查询:  推理模型名称，新版，直接用于展示和模型调用  """
        nameLike: String,
        """ 数组精确查询:  描述  """
        description: [String],
        """ 模糊查询:  描述  """
        descriptionLike: String,
        """ 数组精确查询:  apiKey  """
        apiKey: [String],
        """ 模糊查询:  apiKey  """
        apiKeyLike: String,
        """ 数组精确查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrl: [String],
        """ 模糊查询:  endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/  """
        endpointUrlLike: String,
        """ 数组精确查询:  最大输出token数(过时的配置)  """
        maxTokens: [Int],
        """ 数组精确查询:  最大输出token数，必填  """
        maxOutputTokens: [Int],
        """ 数组精确查询:  模型最大上下文长度，必填  """
        contextLength: [Int],
        """ 数组精确查询:  模型最大输入长度  """
        maxInputLength: [Int],
        """ 数组精确查询:  模型来源的类型  """
        sourceType: [ModelSourceTypeEnum],
        """ 数组精确查询:  模型使能: ENABLE-启用, DISABLE-停用  """
        enable: [ModelEnableEnum],
        """ 数组精确查询:  是否是默认的  """
        isDefault: [Boolean],
        """ 数组精确查询:  否是推理模型  """
        isReasoning: [Boolean],
        """ 自定义参数 """
        params: JSON,
        """ 数组精确查询: 创建者 """
        createBy: [String],
        """ 模糊查询: 创建者 """
        createByLike: String,
        """ 数组精确查询: 更新者 """
        updateBy: [String],
        """ 模糊查询: 更新者 """
        updateByLike: String,
        """ 区间查询开始时间: 创建时间 """
        startCreateTime: Long,
        """ 区间查询结束时间: 创建时间 """
        endCreateTime: Long,
        """ 区间查询开始时间: 更新时间 """
        startUpdateTime: Long,
        """ 区间查询结束时间: 更新时间 """
        endUpdateTime: Long,
        """ 数组精确查询: 备注 """
        remark: [String],
        """ 模糊查询: 备注 """
        remarkLike: String,
        """ 数组精确查询: 软删除标志 """
        statusFlag: [StatusFlag],
        countField: String!,
        groupField: String!,
        filter: String,
        pageable: Pageable!
    ): GroupCountPage


`;
export const modelinfoMutation = `
    modelInfo_insert_one(bean: ModelInfoForm!): ModelInfoVO
    modelInfo_update_one(bean: ModelInfoForm!,unsetFields: [String]): ModelInfoVO
    modelInfo_save_one(bean: ModelInfoForm!,unsetFields: [String]): ModelInfoVO
    modelInfo_insert_all(beans: [ModelInfoForm!]!): [ModelInfoVO]    
    modelInfo_update_all(beans: [ModelInfoForm!]!,unsetFields: [String]): [ModelInfoVO]
    modelInfo_save_all(beans: [ModelInfoForm!]!,unsetFields: [String]): [ModelInfoVO]
    modelInfo_delete_custom(mongoQuery: String!): [ModelInfoVO]
    modelInfo_delete_cascade(ids: [ID!]!): [ModelInfoVO]
    modelInfo_delete_one(id: ID!): ModelInfoVO
    modelInfo_delete_all(ids: [ID!]!): [ModelInfoVO] 


`;
