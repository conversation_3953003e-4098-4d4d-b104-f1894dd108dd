import { HeadBucketCommand, CreateBucketCommand, GetBucketCorsCommand, PutBucketCorsCommand } from '@aws-sdk/client-s3'
import { to } from './index'
import { CEPH_ENDPOINT, CEPH_PORT } from '../config/index'
import logger from './logger'

const CEPH_HOST = `http://${CEPH_ENDPOINT}${(CEPH_PORT as unknown) == 80 ? '' : ':' + CEPH_PORT}`

export function replaceHost(url?: string) {
  if (!url) return ''
  return url.replace(CEPH_HOST, '')
}
/**
 * 检查存储桶是否存在，是否有cors，没有时自动创建并设置cors，存在时设置cors
 * @param client v3
 * @param s3 v2
 * @param Bucket 桶名
 * @returns
 */
export async function bucketCheckCreate(client: any, s3: any, Bucket: string) {
  const input = { Bucket }
  //查询存储桶是否存在
  const [err, data] = await to(client.send(new HeadBucketCommand(input)))
  //如果存储桶不存在或者异常
  if (err || !data) {
    err && logger.error('检查存储桶是否存在异常', err)
    const [createErr, createData] = await to(client.send(new CreateBucketCommand(input)))
    if (createErr) {
      logger.error('创建存储桶错误', createErr, createData)
    }
    const result = await setBucketCors(client, s3, Bucket, true)
    return result
  }
  const result = await setBucketCors(client, s3, Bucket)
  return result
}

/**
 * 设置存储桶的cors
 * @param client v3
 * @param s3 v2
 * @param Bucket 桶名
 * @param noCheck
 * @returns
 */
export async function setBucketCors(client: any, s3: any, Bucket: string, noCheck?: boolean) {
  //如果需要检查通是否存在且已有跨域
  if (!noCheck) {
    const [err, data] = await to<any, Error>(client.send(new GetBucketCorsCommand({ Bucket })))
    //如果异常，且属于NoSuchCORSConfiguration，就不再处理
    if (err && (err as any).Code === 'NoSuchCORSConfiguration') {
      return true
    }
    err && logger.error('获取存储桶cors错误', err)
    //如果存在存储桶，并且有cors
    if (data?.CORSRules) return true
  }
  const params = {
    Bucket: Bucket,
    CORSConfiguration: {
      CORSRules: [
        {
          AllowedHeaders: ['*'],
          AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE'],
          AllowedOrigins: ['*'],
        },
      ],
    },
  }

  const [error, putCors] = await to(
    // client.send(new PutBucketCorsCommand(params))
    new Promise((resolve, reject) => {
      s3.putBucketCors(params, (err: any, data: any) => {
        if (err) reject(err)
        resolve(data)
      })
    })
  )
  if (error) {
    logger.error('存储桶cors设置错误', error)
  }
  return !error
}
