import { chatRole, MAX_HISTORY_DISK_NUM } from '@/consts/chat'
import { scrollToDom, scrollToBottom } from '@gendo/utils'
import Util from '@/utils/util'
import { Marked } from 'marked'

const marked = new Marked()

export function getHistoryQueryAnswer(list) {
  const history = []
  const len = list?.length ?? 0
  for (let index = len - 1; index >= 0; index--) {
    //计算大小
    // const blob = new Blob([JSON.stringify(history)])
    // // const blob = new Blob(history)
    // if (blob.size > MAX_HISTORY_BYTE_SIZE) break
    //用户和系统的都为3以上
    if (
      history.filter((m) => m.role == chatRole.user).length >= MAX_HISTORY_DISK_NUM &&
      history.filter((m) => m.role == chatRole.robot).length >= MAX_HISTORY_DISK_NUM
    )
      break
    const item = list[index]
    const { role, requestId, content } = item
    //非空，且属于用户和模型回复，并且第一条必须是模型，否则丢弃
    if (
      !content ||
      ![chatRole.user, chatRole.robot].includes(role) ||
      (!history.length && role !== chatRole.robot)
    )
      continue
    const prev = history[0]
    //如果上一条数据和当前是同一个类型，类型不同时不是一个请求， 都丢弃
    if (prev && prev.role === role) {
      continue
    }
    //如果requestId不相同，且不成对，移除
    if (
      prev &&
      prev.requestId !== requestId &&
      !history.some((m, i) => m.requestId == prev.requestId && i != 0)
    ) {
      history.splice(0, 1)
      continue
    }
    history.unshift({
      requestId,
      role,
      content
    })
  }
  return history
}

function getChatBody() {
  return document.querySelector('.chat-center-content')
}

/**
 * 滚动到message位置
 * @param {*} _id
 * @param {*} immediate
 */
export function scrollChatMessage(_id, immediate) {
  const chatBody = getChatBody()
  const chatMessage = document.querySelector(`.chat-message-${_id}`)
  scrollToDom(chatBody, chatMessage, immediate ? 'instant' : undefined)
}

export function scrollChatBottom(immediate) {
  scrollToBottom(getChatBody(), immediate ? 'instant' : undefined)
}

export function isEmptyText(text) {
  if (!text) return true
  return text.trim().replace(/\r/g, '').replace(/\n/g, '').length == 0
}

export function getSelectInfo(selection) {
  let isSelectText = [
    Util.WPS_Enum.Selection.wdSelectionNormal,
    Util.WPS_Enum.Selection.wdSelectionColumn,
    Util.WPS_Enum.Selection.wdSelectionRow
  ].includes(selection.Type)
  // console.log(
  //   'WindowSelectionChange',
  //   selection.Type,
  //   isSelectText,
  //   selection.Type,
  //   selection.Text,
  //   selection
  // )
  let selectLength = selection.Characters?.Count ?? 0 //isSelectText?selection.Characters?.Count ?? 0:0
  // eslint-disable-next-line no-control-regex
  let selectText = selection.Text.replace(/[\r\x01]/g, '') ? selection.Text : '' //isSelectText?selection.Text:'', .replace(/\r/g,'\r\n')
  //如果只有换行符和图片（x01）则不算选中
  if (!selectText) {
    selectLength = 0
    isSelectText = false
  }

  return {
    isSelectText,
    selectText,
    selectLength
  }
}

export function bindSelectEvent(isBind, fn) {
  if (!window.Application?.ApiEvent) return
  if (!isBind) {
    window.Application.ApiEvent.RemoveApiEventListener('WindowSelectionChange')
    return
  }
  window.Application.ApiEvent.AddApiEventListener('WindowSelectionChange', fn)
  const selection = window.wps.WpsApplication().ActiveWindow.Selection
  return getSelectInfo(selection)
}

export function createHtml(content) {
  let htmlContent = marked.parse(content || '')
  // 第一步：移除所有 <li> 标签内的 <p> 标签
  htmlContent = htmlContent.replace(/<li>([\s\S]*?)<\/li>/g, function (match, innerContent) {
    return `<li>${innerContent.replace(/<\/?p>/g, '')}</li>`
  })

  // 第二步：处理<li>嵌套<ul> 结构
  htmlContent = htmlContent.replace(/<li>\s*<p>([\s\S]*?)<\/p>\s*<ul>/g, '<li>$1<ul>')

  htmlContent = htmlContent.replace(/<li>\s*<p>([\s\S]*?)<\/p>\s*<p>/g, '<li>$1<p>')
  const html = `<!DOCTYPE html><html><body>${htmlContent}</body></html>`
  const path = window.wps.Env.GetTempPath() + '/tempHtml.html'
  window.wps.FileSystem.WriteFile(path, html) //生成html文件
  return path
}

/**
 * 插入或替换内容
 * @param {*} isSelect 是否选中内容
 * @param {*} content 插入和替换的内容
 * @param {*} insert 只插入
 * @returns
 */
export function insertReplaceContent(isInsert, content) {
  if (!window.wps?.ActiveWindow || !content) return
  const path = createHtml(content)
  insertContent(isInsert, path)
}

/**
 * 有选中插入到选中的后面，没有选中插入到文档末尾
 * @param {boolean} isSelect 是否有选中
 * @param {*} content 插入的内容
 * @returns
 */
export function insertContent(isInsert, path) {
  const { Application } = window
  if (!Application?.ActiveDocument) return
  if (isInsert) {
    const end = Application.Selection.End
    Application.Selection.Start = end
    Application.Selection.End = end
  }
  /**
   * 5. 光标位置
   * wps.Application.ActiveWindow.Selection.Start //光标的起始位置
   * wps.Application.ActiveWindow.Selection.End //光标的结束位置
   */
  // window.wps.ActiveWindow.Selection.InsertAfter(content)
  Application.ActiveWindow.Selection.InsertFile(path)
  Application.FileSystem.Remove(path)
}
