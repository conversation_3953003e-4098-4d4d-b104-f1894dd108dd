import asyncio

from loguru import logger
from typing import Optional
from functools import partial
from fastapi import BackgroundTasks
from concurrent.futures import ProcessPoolExecutor
from starlette.concurrency import run_in_threadpool

from common.context.logging_context import get_request_id, set_request_id

from application.service import api_service
from application.dto.result import ResultDTO
from application.req.rerank_req import RerankRequest
from application.req.vector_search_req import VectorSearchReq
from application.req.document_embedding_req import DocumentEmbeddingReq
from application.resp.document_embedding_resp import DocumentEmbeddingResp
from application.req.query_rewrite_req import SimpleQueryRewriteReq, ModelInfoQueryRewriteReq


def document_embedding_with_request_id(req: DocumentEmbeddingReq, request_id: str) -> DocumentEmbeddingResp:
    """
    设置请求ID
    :params:
        req: 文档向量化请求
        request_id: 请求ID
    :return:
        DocumentEmbeddingResp: 文档向量化响应
    """
    set_request_id(request_id)
    return api_service.document_embedding(req)


async def document_embedding(req: DocumentEmbeddingReq) -> ResultDTO:
    """文档向量化"""
    logger.info(f"document_embedding req: {req.model_dump_json()}")

    # 子进程执行避免内存溢出
    request_id = get_request_id()
    with ProcessPoolExecutor(max_workers=1) as executor:
        loop = asyncio.get_running_loop()
        func = partial(document_embedding_with_request_id, req, request_id)
        res = await loop.run_in_executor(executor, func)
        return ResultDTO(status=0, code=0, msg="ok", data=res)


async def search_chunks(vectorSearchReq: VectorSearchReq) -> ResultDTO:
    """向量检索"""
    logger.info(f"search_chunks req: {vectorSearchReq.model_dump_json()}")
    resps = []
    for req in vectorSearchReq.kbInfos:
        res = await run_in_threadpool(
            api_service.search_vector,
            req.question,
            req.kbID,
            req.embeddingUrl,
            req.embeddingApiKey,
            req.embeddingModel,
            vectorSearchReq.topK,
            vectorSearchReq.scoreThreshold,
            vectorSearchReq.sparseScoreThreshold
        )
        resps.append(res)

    return ResultDTO(status=0, code=0, msg="ok", data={"chunks": resps})


async def rerank(req: RerankRequest) -> ResultDTO:
    """重排序"""
    logger.debug(f"rerank req: {req.model_dump_json()}")
    res = await run_in_threadpool(
        api_service.rerank,
        question=req.question,
        mix_chunks=req.chunks,
        api_key=req.apiKey,
        model=req.model,
        url=req.url,
        topK=req.topK,
        useReranker=req.useReranker
    )
    return ResultDTO(status=0, code=0, msg="ok", data={"chunks": res})


async def query_rewrite(req: SimpleQueryRewriteReq) -> ResultDTO:
    """简单模式查询改写"""
    logger.debug(f"query_rewrite req: {req.model_dump_json()}")
    response = await run_in_threadpool(
        api_service.simple_rewrite,
        question=req.question,
        prompt=req.prompt,
        history=req.history,
        model_name=req.model_name,
        temperature=req.temperature,
        max_tokens=req.max_tokens
    )
    return ResultDTO(status=0, code=0, msg="ok", data=response)


async def query_rewrite_with_model_info(req: ModelInfoQueryRewriteReq) -> ResultDTO:
    """带模型信息的查询改写"""
    logger.debug(f"query_rewrite_with_model_info req: {req.model_dump_json()}")
    response = await run_in_threadpool(
        api_service.query_rewrite_with_model_info,
        question=req.question,
        prompt=req.prompt,
        history=req.history,
        model_info=req.model_info,
        chat_mode=req.chat_mode
    )
    return ResultDTO(status=0, code=0, msg="ok", data=response)


async def del_file(knowledgeBaseID: str, fileID: str, background_tasks: BackgroundTasks, embeddingModelName: Optional[str] = None) -> ResultDTO:
    """删除文件"""
    logger.info(f"del_file: knowledgeBaseID={knowledgeBaseID}, fileID={fileID}")
    background_tasks.add_task(api_service.delete_file, knowledgeBaseID, fileID)
    return ResultDTO(status=0, code=0, msg="ok")


async def del_db(knowledgeBaseID: str, background_tasks: BackgroundTasks, embeddingModelName: Optional[str] = None) -> ResultDTO:
    """删除知识库"""
    logger.info(f"del_db: knowledgeBaseID={knowledgeBaseID}")
    background_tasks.add_task(api_service.delete_knowledge_base, knowledgeBaseID)
    return ResultDTO(status=0, code=0, msg="ok")

async def update_kbid_by_fileid(knowledgeBaseID: str, fileID: str, newKbID: str, background_tasks: BackgroundTasks) -> ResultDTO:
    """根据 fileID 更新 kbID"""
    logger.info(f"update_kbid_by_fileid: fileID={fileID}, newKbID={newKbID}")
    background_tasks.add_task(api_service.update_kbid_by_fileid, fileID, newKbID)
    return ResultDTO(status=0, code=0, msg="ok")