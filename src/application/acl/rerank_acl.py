from typing import List, Dict
from model.rerank.service import rerank_service
from model.rerank.req.rerank_request import RerankRequest
from application.req.rerank_req import MixChunk, Chunk


def rerank_documents(
    question: str,
    mix_chunks: List[MixChunk],
    api_key: str,
    model: str,
    url: str,
    topK: int = 4,
    k: int = 2,
    useReranker: bool = True
) -> List[Dict]:
    """重排序文档"""
    # 处理dense和sparse结果
    if useReranker:
        _rerank_mix_chunks(mix_chunks, question, model, api_key, url)

    # 执行RRF融合
    response = _reciprocal_rank_fusion(mix_chunks, k=k)
    return response[:topK] if len(response) > topK else response


def _rerank_mix_chunks(mix_chunks: List[MixChunk], question: str, model: str, api_key: str, url: str):
    """重排序混合文档块（内部私有方法）"""
    for chunk in mix_chunks:
        # 处理dense文档
        if chunk.denseChunks:
            _rerank_chunks(chunk.denseChunks, question, model, api_key, url)

        # 处理sparse文档
        if chunk.sparseChunks:
            _rerank_chunks(chunk.sparseChunks, question, model, api_key, url)


def _rerank_chunks(chunks: List[Chunk], question: str, model: str, api_key: str, url: str):
    """重排序文档块（内部私有方法）"""
    if not chunks:
        return

    # 重排序
    docs = [item.content for item in chunks]
    request = RerankRequest(model=model, query=question, documents=docs)
    response = rerank_service.rerank(request, api_key=api_key, url=url)

    # 更新得分
    for chunk in chunks:
        for result in response.results:
            if chunk.content == result.document:
                chunk.score = result.relevance_score
                break


def _reciprocal_rank_fusion(mix_chunks: List[MixChunk], k: int = 10) -> List[Dict]:
    """RRF融合（内部私有方法）"""
    combined_scores = {}
    for mix_chunk in mix_chunks:
        # 稠密向量
        dense_data = sorted(mix_chunk.denseChunks,
                            key=lambda x: x.score, reverse=True)
        # 稀疏向量
        sparse_data = sorted(mix_chunk.sparseChunks,
                             key=lambda x: x.score, reverse=True)
        # 权重
        weight = 1

        # 计算dense和sparse向量得分
        for data_list in [dense_data, sparse_data]:
            for item_rank, item in enumerate(data_list):
                score = weight / (item_rank + k)
                id = f"{item.chunkID}--{item.kbID}"
                if id not in combined_scores:
                    combined_scores[id] = (score, item)
                else:
                    combined_scores[id] = (
                        combined_scores[id][0] + score, item)

    # 按得分排序
    sorted_results = sorted(combined_scores.values(),
                            key=lambda x: x[0], reverse=True)
    return [
        {
            'score': score,
            'kbID': result.kbID,
            'chunkID': result.chunkID
        }
        for score, result in sorted_results
    ]
