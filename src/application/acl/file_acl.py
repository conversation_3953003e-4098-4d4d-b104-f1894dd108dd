import os
import gc
import time
import fitz
import requests
import tempfile
import subprocess

from loguru import logger
from typing import Union, List
from tempfile import NamedTemporaryFile
from common.utils.common_manage_util import generate_random_string


def download_file(file_uri: str, file_name: str, timeout: int = 30) -> Union[str, bytes]:
    """
    下载文件
    
    Args:
        file_uri: 文件URI
        timeout: 超时时间(秒)
    
    Returns:
        str: 临时文件路径
    """
    try:
        # 发送请求
        response = requests.get(
            file_uri,
            timeout=timeout,
            stream=True
        )
        
        # 检查响应状态
        if not response.ok:
            http_error_msg = f'下载失败: {response.status_code} {response.reason} for url: {file_uri}'
            raise requests.HTTPError(http_error_msg, response=response)
        
        # 创建临时文件
        temp_file = create_temp_file(file_name)
        
        # 下载文件内容
        download_content(response, temp_file)
        
        return temp_file.name

    except requests.Timeout as e:
        logger.error(f"文件下载超时: {str(e)}")
        raise
    except requests.RequestException as e:
        logger.error(f"文件下载请求错误: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"文件下载过程中发生错误: {str(e)}")
        raise



def create_temp_file(filename: str) -> NamedTemporaryFile:
    def get_filename_suffix(full_path: str):
        basename = os.path.basename(full_path)
        
        if basename in ('.', '..'):
            return ""

        if basename.startswith('.') and '.' not in basename[1:]:
            return basename

        last_dot_index = basename.rfind('.')
        if last_dot_index == -1 or last_dot_index == len(basename) - 1:
            return ""
        
        return basename[last_dot_index:]
    
    suffix = get_filename_suffix(full_path=filename)
    result = NamedTemporaryFile(prefix="tmp", suffix=suffix, delete=False, dir="/tmp")
    return result


def write_temp_file(filename: str, content: str) -> str:
    """
    将内容写入临时文件
    
    Args:
        filename: 临时文件的名称
        content: 要写入的内容
    
    Returns:
        str: 临时文件路径
    """
    temp_file = None
    try:
        # 创建临时文件
        temp_file = create_temp_file(filename)
        
        # 写入内容
        temp_file.write(content.encode('utf-8'))
        return temp_file.name
    except Exception as e:
        logger.error(f"写入临时文件时发生错误: {str(e)}")
        raise
    finally:
        if temp_file:
            temp_file.close()


def download_content(response: requests.Response, temp_file: NamedTemporaryFile, chunk_size: int = 8192):
    """下载文件内容到临时文件"""
    try:
        # 分块下载文件
        for chunk in response.iter_content(chunk_size=chunk_size):
            if chunk:
                temp_file.write(chunk)
    except Exception as e:
        # 发生错误时删除临时文件
        try:
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
        except:
            pass
        raise e
    finally:
        # 确保文件被关闭
        temp_file.close()


def convert2pdf(file_path: str, timeout: int = 900) -> str:
    """
    将文件转换为PDF
    :params:
        file_path: 文件路径
        timeout: 超时时间(单位秒)
    :return:
        str: 文件路径
    """
    try:
        # 创建锁文件
        lock_file = os.path.join(tempfile.gettempdir(), 'libreoffice.lock')
        with open(lock_file, 'w') as f:
            try:
                # 获取文件锁
                import fcntl
                fcntl.flock(f, fcntl.LOCK_EX)

                # 构建输出路径
                path_out = ".".join(file_path.split('.')[:-1]) + '.pdf'
                dir_out = os.path.dirname(file_path)

                # 确保输出目录存在
                os.makedirs(dir_out, exist_ok=True)

                # 构建命令
                cmd = [
                    'timeout',
                    '-s', '9',
                    f'{timeout + 1}s',
                    'libreoffice7.6',
                    '--headless',
                    '--convert-to', 'pdf',
                    file_path,
                    '--outdir', dir_out
                ]

                # 使用 with 语句确保进程资源被正确释放
                with subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                ) as process:
                    try:
                        stdout, stderr = process.communicate(timeout=timeout)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        raise TimeoutError(f"libreoffice timeout at pdf conversion for {file_path}")

                    if process.returncode != 0:
                        error_msg = f"Failed to convert {file_path} to PDF: {stderr}"
                        logger.error(error_msg)
                        raise RuntimeError(error_msg)

                # 验证输出文件
                if not os.path.exists(path_out):
                    error_msg = f"PDF file was not created: {path_out} file_path: {file_path}"
                    logger.error(error_msg)
                    raise FileNotFoundError(error_msg)

                # 验证文件大小
                if os.path.getsize(path_out) == 0:
                    error_msg = f"Created PDF file is empty: {path_out} file_path: {file_path}"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)

                return path_out
            
            finally:
                # 释放文件锁
                fcntl.flock(f, fcntl.LOCK_UN)
            
    except Exception as e:
        logger.error(f"Error converting {file_path} to PDF: {str(e)}")
        raise



def pdf_to_screenshots(pdf_path: str, zoom: float = 2.0) -> List[str]:
    """
    高性能、低内存消耗的PDF转截图工具
    
    Args:
        pdf_path: PDF文件路径
        zoom: 缩放因子 (默认2.0=150dpi)
    
    Returns:
        List[str]: 截图文件路径列表
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
    # 使用临时目录
    temp_dir = "/tmp/pdf_screenshots"
    os.makedirs(temp_dir, exist_ok=True)
    
    screenshot_paths = []
    doc = None
    
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        
        # 批量处理页面
        batch_size = 5
        for batch_start in range(0, total_pages, batch_size):
            batch_end = min(batch_start + batch_size, total_pages)
            
            for page_num in range(batch_start, batch_end):
                try:
                    # 生成唯一的文件名
                    screenshot_name = f"{int(time.time())}_{generate_random_string(4)}.png"
                    screenshot_path = os.path.join(temp_dir, screenshot_name)
                    
                    # 按需加载单页
                    page = doc.load_page(page_num)
                    
                    # 设置渲染参数
                    matrix = fitz.Matrix(zoom, zoom)
                    
                    # 渲染并保存截图
                    pix = page.get_pixmap(matrix=matrix, alpha=False)
                    pix.save(screenshot_path, output="png")
                    
                    # 添加到结果列表
                    screenshot_paths.append(screenshot_path)
                    
                    # 显式释放资源
                    del pix
                    del page
                    
                except Exception as e:
                    logger.error(f"Error processing page {page_num} pdf_path {pdf_path}: {str(e)}")
                    if os.path.exists(screenshot_path):
                        os.remove(screenshot_path)
            
            # 每批处理完成后强制回收内存
            gc.collect()
            
    except Exception as e:
        logger.error(f"Error processing PDF {pdf_path}: {str(e)}")

        for path in screenshot_paths:
            if os.path.exists(path):
                os.remove(path)
        
        return screenshot_paths
        
    finally:
        # 确保PDF文档被关闭
        if doc:
            doc.close()
            
        # 强制垃圾回收
        gc.collect()
    
    return screenshot_paths
