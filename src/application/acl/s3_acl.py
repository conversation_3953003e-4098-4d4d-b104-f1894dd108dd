import os

from loguru import logger
from typing import Optional
from s3.service import s3_service


def file_to_s3path(file_path: str, file_name: Optional[str] = None) -> str:
    """
    将文件上传到S3并返回访问路径
    
    Args:
        file_path: 本地文件路径
        file_name: 自定义文件名，如果不提供则使用原文件名
    
    Returns:
        str: S3访问路径
    
    Raises:
        FileNotFoundError: 文件不存在
        RuntimeError: 上传失败
    """
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        # 如果没有提供文件名，使用原文件名
        if not file_name:
            file_name = os.path.basename(file_path)
            
        # 上传文件到S3
        s3_path = s3_service.put_obj_from_file(file_name, file_path)
        
        return s3_path
        
    except Exception as e:
        logger.error(f"Failed to upload file to S3: {str(e)}")
        raise



def bytes_to_s3path(data: bytes, file_name: str) -> str:
    """
    将字节数据上传到S3并返回访问路径
    
    Args:
        data: 字节数据
        file_name: 文件名
    
    Returns:
        str: S3访问路径
    
    Raises:
        ValueError: 数据为空
        RuntimeError: 上传失败
    """
    try:
        if not data:
            raise ValueError("Empty data provided")
            
        # 上传字节数据到S3
        s3_path = s3_service.put_obj(file_name, data)
        
        return s3_path
        
    except Exception as e:
        logger.error(f"Failed to upload bytes to S3: {str(e)}")
        raise