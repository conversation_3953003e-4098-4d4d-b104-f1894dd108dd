import difflib

from loguru import logger
from typing import List, Tuple
from langchain.docstore.document import Document
from langchain.text_splitter import MarkdownHeaderTextSplitter
from langchain.text_splitter import RecursiveCharacterTextSplitter

from model.ocr.resp.ocr_response import OCRResponse
from application.acl import ocr_acl, file_acl, s3_acl
from application.resp.document_embedding_resp import Capture
from application.req.document_embedding_req import DocumentEmbeddingReq
from application.resp.document_embedding_resp import DocumentEmbeddingResp
from application.strategy.abstract.document_embedding_strategy import DocumentEmbeddingStrategy


class MineruModelDocumentEmbeddingStrategy(DocumentEmbeddingStrategy):
    """模型文档向量化处理策略"""

    def __init__(self, req: DocumentEmbeddingReq, file_path: str):
        super().__init__(req, file_path)

    
    def __find_best_page_window(self, doc_content, page_texts, start_page=0, window=5):
        best_ratio = 0
        best_page = None
        end_page = min(len(page_texts), start_page + window)
        for idx in range(start_page, end_page):
            page_text = page_texts[idx]
            ratio = difflib.SequenceMatcher(None, doc_content, page_text).ratio()
            if ratio > best_ratio:
                best_ratio = ratio
                best_page = idx
        return best_page, best_ratio

    
    def __assign_metadatas_to_docs(self, docs, page_texts, header_levels, window=5, ratio=0.2):
        """
        为分割后的 docs 分配页码和章节名称，未能高置信度匹配的内容跟随下一个匹配到的页码。
        """
        current_page = 0
        pending_docs = [] # 待补全页码的文档

        for doc in docs:
            content = doc.page_content.strip()

            # 跳过过短内容，等待后续补全
            if len(content) < 20:
                pending_docs.append(doc)
                continue

            # 文本页码匹配
            best_page, best_ratio = self.__find_best_page_window(content, page_texts, start_page=current_page, window=window)

            if best_page is not None and best_ratio > ratio:
                page_number = best_page
                doc.metadata["pageNumber"] = page_number
                current_page = best_page

                # 将之前未分配页码的文档全部补齐为当前页码
                for pd in pending_docs:
                    pd.metadata["pageNumber"] = page_number
                pending_docs.clear()
            else:
                # 暂存未分配页码的文档，等待后续补全
                pending_docs.append(doc)
            
            # 章节名称处理
            for header in reversed(header_levels):
                if header in doc.metadata:
                    doc.metadata["chapterName"] = doc.metadata.pop(header)
                    break
                
        # 结尾兜底：如果最后还有没补全的，赋值为最后匹配到页码的下一页
        if pending_docs:
            fallback_page = min(current_page + 1, len(page_texts) - 1)
            for pd in pending_docs:
                pd.metadata["pageNumber"] = fallback_page


    def pre_process(self) -> Tuple[List[Document], List[Capture]]:
        # 文件格式转换
        pdf_path: str = file_acl.convert2pdf(self.file_path) if not self.file_path.endswith('.pdf') else self.file_path
        super()._add_temp_file(pdf_path)
        logger.info(f"[MineruModelStrategy] File converted to PDF: {pdf_path}")

        # 获取 OCR 识别结果
        ocr_response: OCRResponse = ocr_acl.document_recognize(self.req.ocrUrl, self.req.ocrApiKey, self.req.ocrModel, pdf_path, aggregated=self.req.aggregated)
        logger.info(f"[MineruModelStrategy] OCR recognition completed. Found {len(ocr_response.data)} pages")

        # 构建每页纯文本
        page_texts = [" ".join([item.text for item in page.result]) for page in ocr_response.data]

        # Markdown 章节分割
        header_levels = ["Header 1", "Header 2", "Header 3"]
        splitter: MarkdownHeaderTextSplitter = MarkdownHeaderTextSplitter(headers_to_split_on=[("#", header_levels[0]), ("##", header_levels[1]), ("###", header_levels[2])])
        md_header_splits: List[Document] = splitter.split_text(ocr_response.text)
        logger.info(f"[MineruModelStrategy] MarkdownHeaderTextSplitter Split into {len(md_header_splits)} documents")

        # Character 字符分割
        splitter: RecursiveCharacterTextSplitter = RecursiveCharacterTextSplitter(chunk_size=2048, chunk_overlap=256)
        docs: List[Document] = splitter.split_documents(md_header_splits)
        logger.info(f"[MineruModelStrategy] RecursiveCharacterTextSplitter Split into {len(docs)} documents")

        # 文档元数据更新
        self.__assign_metadatas_to_docs(docs, page_texts, header_levels)

        # 获取文档截图
        screenshots: List[str] = file_acl.pdf_to_screenshots(pdf_path)
        s3_paths: List[str] = [s3_acl.file_to_s3path(screenshot) for screenshot in screenshots]
        captures: List[Capture] = [Capture(pageNumber=n, path=s3_path) for n, s3_path in enumerate(s3_paths)]
        super()._add_temp_file(*screenshots)

        logger.info(f"[MineruModelStrategy] Generated {len(captures)} document captures")
        return docs, captures


    def process(self) -> DocumentEmbeddingResp:
        return super().process()


    def post_process(self):
        super().post_process()
