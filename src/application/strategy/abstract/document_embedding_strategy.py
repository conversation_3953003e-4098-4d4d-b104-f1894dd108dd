import os
import gc

from abc import ABC
from loguru import logger
from typing import List, <PERSON><PERSON>
from langchain.docstore.document import Document
from application.acl import embedding_acl, milvus_acl, document_acl
from application.req.document_embedding_req import DocumentEmbeddingReq
from application.resp.document_embedding_resp import DocumentEmbeddingResp, Chunk, Capture


class DocumentEmbeddingStrategy(ABC):
    """文档向量化处理策略"""

    def __init__(self, req: DocumentEmbeddingReq, file_path: str):
        self.req = req
        self.file_path = file_path

        # 前置处理结果
        self.pre_process_result = None

        # 临时文件跟踪
        self.temp_files = [file_path]  


    def pre_process(self) -> Tuple[List[Document], List[Capture]]:
        pass


    def process(self) -> DocumentEmbeddingResp:
        """
        文档向量化
        :return:
            DocumentEmbeddingResp: 文档向量化响应
        """
        # 1. 获取预处理结果
        docs, captures = self.pre_process_result
        logger.info(f"[DocumentEmbeddingStrategy] Retrieved pre-process results: {len(docs)} documents and {len(captures)} captures")
        
        # 2. 转换为具体 chunks 分片
        chunks: List[Chunk] = document_acl.documents2chunks(docs, self.req.kbID)
        logger.info(f"[DocumentEmbeddingStrategy] Converted documents to {len(chunks)} chunks")
        
        # 3. 向量化存储
        chunks = self.__chunks_store(chunks)
        logger.info(f"[DocumentEmbeddingStrategy] Completed vector storage for {len(chunks)} chunks")
        
        # 4. 获取文档截图
        return DocumentEmbeddingResp(fileID=self.req.fileID, chunks=chunks, captures=captures if self.req.includeCapture else [])
    

    def post_process(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        self.temp_files.clear()
        gc.collect()


    def run(self) -> DocumentEmbeddingResp:
        """统一调度入口"""
        try:
            # 前置处理
            self.pre_process_result = self.pre_process()
            
            # 主处理流程
            resp = self.process()
            
            return resp
        finally:
            # 确保后置处理总是执行
            self.post_process()
    
    
    def _add_temp_file(self, *file_paths: str):
        """
        添加临时文件到跟踪列表
        
        Args:
            *file_paths: 可变数量的文件路径参数
        """
        if not file_paths:
            return
        
        for file_path in file_paths:
            if file_path and os.path.exists(file_path):
                self.temp_files.append(file_path)


    def __chunks_store(self, chunks: List[Chunk]) -> List[Chunk]:
        """
        存储文档
        """
        # 如果设置了跳过向量化，则直接返回原始chunks
        if self.req.skipEmbedding:
            logger.info("[DocumentEmbeddingStrategy] Skipping embeddings generation as skipEmbedding is True")
            return chunks
            
        # 1. 生成文档块的向量表示
        contents: List[str] = [chunk.content for chunk in chunks]
        embeddings = embedding_acl.generate_embeddings(
            self.req.embeddingUrl,
            self.req.embeddingApiKey,
            self.req.embeddingModel,
            contents,
            dimensions=self.req.dimensions,
            encoding_format=self.req.encodingFormat
        )
        logger.info(f"[DocumentEmbeddingStrategy] Generated {len(embeddings)} embeddings")

        # 2. 存储文档向量
        if self.req.isAddMilvus:
            milvus_acl.delete_file(self.req.kbID, self.req.fileID)
            chunks = milvus_acl.store_document_vectors(
                chunks,
                embeddings,
                self.req.fileID,
                self.req.kbID,
                self.req.fileName
            )
            logger.info(f"[DocumentEmbeddingStrategy] Successfully stored {len(chunks)} vectors in Milvus")
        else:
            logger.info("[DocumentEmbeddingStrategy] Skipping Milvus storage as isAddMilvus is False")

        return chunks
