from loguru import logger

from application.req.document_embedding_req import DocumentEmbeddingReq
from application.strategy.abstract.document_embedding_strategy import DocumentEmbeddingStrategy
from application.strategy.default.default_strategy import DefaultDocumentEmbeddingStrategy
from application.strategy.model.ppocr_model_strategy import PPOCRModelDocumentEmbeddingStrategy
from application.strategy.model.mineru_model_strategy import MineruModelDocumentEmbeddingStrategy


class StrategyFactory:
    _extension_mappings = {}
    _strategy_mappings = {}

    @classmethod
    def register_strategy(cls, strategy_class: type[DocumentEmbeddingStrategy], strategy_type: str, file_extensions: list[str] = None):
        """注册策略类"""
        cls._strategy_mappings[strategy_type] = strategy_class
        if file_extensions:
            for ext in file_extensions:
                if ext.lower() not in cls._extension_mappings:
                    cls._extension_mappings[ext.lower()] = []
                cls._extension_mappings[ext.lower()].append((strategy_type, strategy_class))

    @classmethod
    def _get_strategies_by_extension(cls, extension: str) -> list:
        """根据文件扩展名获取匹配的策略列表"""
        return cls._extension_mappings.get(extension.lower(), [])

    @classmethod
    def _get_strategy_by_type(cls, strategies: list, strategy_type: str) -> DocumentEmbeddingStrategy:
        """根据策略类型从策略列表中获取对应的策略"""
        for st_type, strategy_class in strategies:
            if st_type == strategy_type:
                return strategy_class
        return None

    @classmethod
    def _get_strategy_type_from_model(cls, model_name: str) -> str:
        """从模型名称中获取策略类型"""
        if not model_name:
            return None
        model_name = model_name.lower()
        for strategy_type in cls._strategy_mappings.keys():
            if strategy_type in model_name:
                return strategy_type
        return None

    @classmethod
    def get_strategy(cls, req: DocumentEmbeddingReq, file_path: str) -> DocumentEmbeddingStrategy:
        """获取文档向量化策略实例"""
        if not req.fileName:
            logger.info(f"[StrategyFactory] Use DefaultDocumentEmbeddingStrategy (no fileName)")
            return DefaultDocumentEmbeddingStrategy(req, file_path)

        # 1. 通过文件后缀名获取策略列表
        extension = req.fileName.rsplit('.', 1)[-1].lower() if '.' in req.fileName else ''
        matching_strategies = cls._get_strategies_by_extension(extension)

        # 2. 没有匹配到后缀名，直接用 default 策略
        if not matching_strategies:
            logger.info(f"[StrategyFactory] Use DefaultDocumentEmbeddingStrategy (no extension match)")
            return cls._strategy_mappings["default"](req, file_path)

        # 3. 只匹配到一个策略，直接用
        if len(matching_strategies) == 1:
            logger.info(f"[StrategyFactory] Use {matching_strategies[0][1].__name__} (single match)")
            return matching_strategies[0][1](req, file_path)

        # 4. 匹配到多个策略，优先用 strategy_type 匹配
        if req.ocrModel:
            strategy_type = cls._get_strategy_type_from_model(req.ocrModel)
            if strategy_type:
                strategy_class = cls._get_strategy_by_type(matching_strategies, strategy_type)
                if strategy_class:
                    logger.info(f"[StrategyFactory] Use {strategy_class.__name__} (matched by ocrModel: {req.ocrModel})")
                    return strategy_class(req, file_path)
        
        # 5. 没有匹配到 strategy_type，直接用 ppocr
        logger.info(f"[StrategyFactory] Use PPOCRModelDocumentEmbeddingStrategy (fallback)")
        return cls._strategy_mappings["ppocr"](req, file_path) 
        

# 注册策略类
StrategyFactory.register_strategy(DefaultDocumentEmbeddingStrategy, "default")
StrategyFactory.register_strategy(PPOCRModelDocumentEmbeddingStrategy, "ppocr", ['pdf', 'doc', 'docx', "ppt", "pptx"])
StrategyFactory.register_strategy(MineruModelDocumentEmbeddingStrategy, "mineru", ['pdf', 'doc', 'docx', "ppt", "pptx"])