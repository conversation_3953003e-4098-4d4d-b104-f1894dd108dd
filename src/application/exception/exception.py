# 官方推荐注册异常处理器时，应该注册到来自 Starlette 的 HTTPException
from loguru import logger
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException
from fastapi.exceptions import RequestValidationError
from common.context.logging_context import get_request_id


class BusinessError(Exception):
    def __init__(self, msg):
        self.msg = msg

    def __str__(self):
        return self.msg


class OtherException(Exception):
    def __init__(self, msg):
        self.msg = msg

    def __str__(self):
        return self.msg


class BaseAPIException(HTTPException):
    status_code = 400
    detail = 'api error'

    def __init__(self, detail: str = None, status_code: int = None):
        self.detail = detail or self.detail
        self.status_code = status_code or self.status_code


class NoMsgException(Exception):
    pass


# 全局异常
async def http_exception_handler(request, e):
    return JSONResponse({
        'code': e.status_code,
        'status': e.status_code,
        'msg': str(e.detail)
    })

# 请求数据无效时的错误处理


async def validate_exception_handler(request, e):
    msg = ''
    if hasattr(e, 'msg'):
        msg = e.msg
    info = f'error={e.__class__.__name__}, msg:{msg}, args={e.args}'
    logger.error(info)
    return JSONResponse({
        'code': 400,
        'status': 400,
        'msg': info
    })

# 请求数据无效时的错误处理


async def business_error_handler(request, e):
    msg = ''
    if hasattr(e, 'msg'):
        msg = e.msg
    info = f'error={e.__class__.__name__}, msg:{msg}, args={e.args}'
    logger.error(info)
    return JSONResponse({
        'code': 400,
        'status': 400,
        'msg': info
    })


async def catch_exceptions_middleware(request, call_next):
    try:
        return await call_next(request)
    except Exception as e:
        msg = ''
        if hasattr(e, 'msg'):
            msg = e.msg
        
        if e is HTTPException:
            info = f'error={e.__class__.__name__}, msg:{msg}, args={e.args}, detail={e.detail}'
        else:
            info = f'error={e.__class__.__name__}, msg:{msg}, args={e.args}'

        
        body = getattr(request.state, "body", b"<cannot read body>")
        
        try:
            body_str = body.decode("utf-8")
        except Exception:
            body_str = str(body)
        logger.error(
            f"error={e.__class__.__name__}, request_id={get_request_id()}, path={request.url.path}, body={body_str}, error={e}"
        )

        import traceback
        traceback.print_exc()

        return JSONResponse({
            'code': 500,
            'status': 500,
            'msg': info,
        })

golbal_exception_handlers = {
    HTTPException: http_exception_handler,
    RequestValidationError: validate_exception_handler,
    BusinessError: business_error_handler,
    # Exception: unhandled_exception_handler
}
