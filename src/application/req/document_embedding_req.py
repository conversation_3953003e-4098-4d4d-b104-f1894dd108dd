from pydantic import BaseModel
from typing import Optional


class DocumentEmbeddingReq(BaseModel):
    # OCR URL
    ocrUrl: str
    # OCR 模型名
    ocrModel: str
    # OCR ApiKey
    ocrApiKey: str
    # 文件URI
    fileURI: str
    # 文件名称
    fileName: str
    # 是否包含页面截图
    includeCapture: Optional[bool] = True
    # 是否入库到milvus
    isAddMilvus: Optional[bool] = True
    # 是否跳过向量化处理
    skipEmbedding: Optional[bool] = False
    # 文本块聚合方式，可选：line, paragraph
    aggregated: Optional[str] = "paragraph"

    # 文件ID
    fileID: str
    # 知识库ID
    kbID: str
    # Embedding URL
    embeddingUrl: str
    # Embedding apiKey
    embeddingApiKey: str
    # Embedding 模型名
    embeddingModel: str
    # 指定返回embedding的输出向量维度，默认1024
    dimensions: Optional[int] = None
    # 用于控制返回的embedding格式
    encodingFormat: Optional[str] = None
