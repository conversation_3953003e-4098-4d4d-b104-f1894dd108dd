import uuid
import uvicorn
import argparse

from loguru import logger
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from common.configs.basic_config import setup_logger
from common.context.logging_context import set_request_id

from application.api.run_api import (
    document_embedding,
    search_chunks,
    del_file,
    del_db,
    rerank,
    query_rewrite,
    query_rewrite_with_model_info,
    update_kbid_by_fileid
)

from application.exception.exception import golbal_exception_handlers
from application.exception.exception import catch_exceptions_middleware


async def cache_body_middleware(request: Request, call_next):
    if request.method in ("POST", "PUT", "PATCH", "DELETE"):
        request.state.body = await request.body()
    else:
        request.state.body = b""
    response = await call_next(request)
    return response


def create_app(run_mode: str = None):
    app = FastAPI(
        title="RAG-Embedding Server",
        version=1.0,
        debug=True,
        exception_handlers=golbal_exception_handlers,
    )
    # 允许跨域
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.middleware("http")(cache_body_middleware)
    app.middleware("http")(catch_exceptions_middleware)
    mount_app_routes(app, run_mode=run_mode)
    return app


def mount_app_routes(app: FastAPI, run_mode: str = None):
    @app.middleware("http")
    async def add_request_id_middleware(request: Request, call_next):
        request_id = request.headers.get("X-Request-ID") or str(uuid.uuid4())
        set_request_id(request_id)
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        logger.info(f'{request.client.host}:{request.client.port} - "{request.method} {request.url.path} {request.scope.get("http_version", "")}"')
        return response

    app.post(
        "/rest/v1/document_embedding",
        tags=["document_embedding"],
        summary="文档嵌入",
    )(document_embedding)

    app.post(
        "/rest/v1/searchChunks",
        tags=["searchChunks"],
        summary="获取相似的片段",
    )(search_chunks)

    app.post(
        "/rest/v1/rerank",
        tags=["rerank"],
        summary="重排序",
    )(rerank)

    app.post("/rest/v1/queryRewrite",
             response_model=None,
             tags=["queryRewrite"],
             summary="查询改写v1"
             )(query_rewrite)

    app.post("/rest/v2/queryRewrite",
             response_model=None,
             tags=["queryRewrite"],
             summary="查询改写v2"
             )(query_rewrite_with_model_info)

    app.delete(
        "/rest/v1/knowledgeBase/{knowledgeBaseID}",
        tags=["delDB"],
        summary="删除db",
    )(del_db)

    app.delete(
        "/rest/v1/knowledgeBase/{knowledgeBaseID}/file/{fileID}",
        tags=["delFile"],
        summary="删除文档",
    )(del_file)

    app.put(
        "/rest/v1/knowledgeBase/{knowledgeBaseID}/file/{fileID}/updateKbID",
        tags=["updateKbID"],
        summary="根据 fileID 更新 kbID",
    )(update_kbid_by_fileid)


if __name__ == "__main__":
    # 初始化日志
    setup_logger()

    # 初始化参数
    parser = argparse.ArgumentParser(
        prog="llm-embedding",
        description="About local knowledge based ChatGLM ｜ 基于本地知识库的 ChatGLM 问答",
    )
    parser.add_argument("--host", type=str, default="0.0.0.0")
    parser.add_argument("--port", type=int, default=20521)

    # 初始化消息
    args = parser.parse_args()

    # 启动服务
    uvicorn.run(create_app(), host=args.host, port=args.port)
