<template>
  <div class="auth-body">
    <div class="auth-content">
      <div class="auth-box">
        <div class="auth-box-title p-color">{{ title }}</div>
        <a-tabs v-model:activeKey="formState.factorType">
          <a-tab-pane :key="LoginFactorType.ACCOUNT" tab="密码登录">
            <a-form ref="formRef" class="auth-form" hideRequiredMark scrollToFirstError :model="formState" :rules="loginRules" v-bind="layout" name="basic" autocomplete="off" :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }" @finish="onLoginFinish" @finishFailed="onLoginFinishFailed">
              <a-form-item name="account">
                <a-input v-model:value.trim="formState.account" :maxlength="200" placeholder="请输入账号名称"> </a-input>
              </a-form-item>
              <a-form-item name="password">
                <a-input-password v-model:value.trim="formState.password" :maxlength="200" placeholder="请输入密码" autocomplete="off"> </a-input-password>
              </a-form-item>
              <a-form-item>
                <a-button block type="primary" class="auth-submit" :loading="loading" html-type="submit">登录</a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
          <a-tab-pane :key="LoginFactorType.SMS" tab="验证码登录"
            ><a-form ref="formRef" class="auth-form" hideRequiredMark scrollToFirstError :model="formState" :rules="loginRules" v-bind="layout" name="basic" autocomplete="off" :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }" @finish="onLoginFinish" @finishFailed="onLoginFinishFailed">
              <a-form-item name="phone">
                <a-input v-model:value.trim="formState.phone" :maxlength="20" placeholder="请输入手机号"> </a-input>
              </a-form-item>
              <a-form-item name="verifyCode">
                <a-input v-model:value.trim="formState.verifyCode" :maxlength="10" placeholder="请输入验证码" autocomplete="off" class="auth-input-identifying-code">
                  <template #suffix>
                    <a-button type="primary" :loading="phoneVerifyCodeLoading" :disabled="isDisabled" @click="getVerificationCode(formState.phone)">{{ codeButtonText }}</a-button>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-button block type="primary" class="auth-submit" :loading="loading" html-type="submit">登录</a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
        <div class="flex-space-between mt12 pl24 pr24">
          <span class="p-color cp" @click="openUserRegisterModal">注册</span>
          <span class="cp" @click="openForgotPasswordModal">忘记密码？</span>
        </div>
        <div class="tc mt30">
          <a-checkbox v-model:checked="checkedProtocols">
            <span>阅读并接受</span>
            <span class="p-color ml4 mr4" @click.prevent="openUserAgreementModal">{{ VITE_GLOB_APP_TITLE }}用户协议</span>
            <span>和</span>
            <span class="p-color ml4 mr4" @click.prevent="openUserPrivacyPolicyModal">隐私政策</span>
          </a-checkbox>
        </div>
      </div>
    </div>
  </div>
  <UserForgotPassword @register="registerForgotPasswordModal" @goRegister="onGoRegister" />
  <UserRegister @register="registerUserRegisterModal" @userAgreementModal="openUserAgreementModal" @userPrivacyPolicyModal="openUserPrivacyPolicyModal" />
  <UserAgreement @register="registerUserAgreementModal" />
  <UserPrivacyPolicy @register="registerUserPrivacyPolicyModal" />
</template>
<script setup>
import { useUserStoreWithOut } from '@/stores/modules/user'
import { message } from 'ant-design-vue'
import { getAppEnvConfig } from '@/utils/envUtil'
import { useLogin } from '@/hooks/component/useLogin'
import { useModal } from 'gendo-ui-vue3'
import { LoginFactorType } from '@/consts'
import { usePhoneVerifyCode } from '@/hooks/component/usePhoneVerifyCode'
import { useUserValidator } from '@/hooks/component/useRule'
const userStore = useUserStoreWithOut()
const router = useRouter()
const { VITE_GLOB_APP_WHOLE_TITLE, VITE_GLOB_APP_TITLE } = getAppEnvConfig()

const { codeButtonText, isDisabled, getVerificationCode, loading: phoneVerifyCodeLoading } = usePhoneVerifyCode()

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 }
}
const formRef = ref(null)
// 登录处理
const title = ref(VITE_GLOB_APP_WHOLE_TITLE)
const formState = ref({
  account: undefined,
  password: undefined,
  phone: undefined,
  verifyCode: undefined,
  factorType: LoginFactorType.ACCOUNT
})
const { phone, verifyCode } = useUserValidator()
const loginRules = ref({
  account: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
  phone,
  verifyCode
})
// 校验成功
const { login, loading } = useLogin()
const onLoginFinish = async (values) => {
  if (!checkedProtocols.value) {
    message.warning('请阅读并同意用户协议和隐私政策')
    return
  }
  values.factorType = formState.value.factorType
  const data = await login(values)
  if (!data) return
  userStore.setToken(data.token)
  userStore.setTenant(data.tenantId)
  userStore.setUserInfo(data)
  await userStore.getUserInfoAction()
  message.success('登录成功,即将跳转', 1)
  setTimeout(() => {
    loading.value = false
  }, 500)
  router.push({ path: '/' })
}
// 校验失败
const onLoginFinishFailed = (errorInfo) => {
  // console.log('Failed:', errorInfo)
  message.error(errorInfo.errorFields?.[0]?.errors?.[0] || '请输入正确的信息')
}

const checkedProtocols = ref(false)

const [registerForgotPasswordModal, { openModal: openForgotPasswordModal }] = useModal()
const [registerUserRegisterModal, { openModal: openUserRegisterModal }] = useModal()
const [registerUserAgreementModal, { openModal: openUserAgreementModal }] = useModal()
const [registerUserPrivacyPolicyModal, { openModal: openUserPrivacyPolicyModal }] = useModal()
const onGoRegister = () => {
  openUserRegisterModal()
}
</script>

<style lang="less">
.auth-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.login-logo {
  position: absolute;
  left: 50px;
  top: 40px;
  width: auto;
  max-width: 140px;
  height: 50px;
}
.auth-body {
  height: 100vh;
  overflow: auto;
  display: flex;
  width: 100%;
  background: url('../../assets/images/login/login-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.auth-header {
  padding: 1.5vh 0;
  display: flex;
  align-items: center;
  padding-left: 32px;
}
.auth-logo {
  width: @logo-width;
  height: 48px;
  object-fit: contain;
}

.auth-content {
  position: relative;
  flex-grow: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-height: 700px;
}
.auth-left {
  width: 43.8%;
  height: 88%;
  background: url('../../assets/images/login/login-left.png') no-repeat;
  margin: auto;
  background-size: contain;
  background-position: center;
  margin: 0 98px 0 68px;
}
.auth-box {
  min-width: 460px;
  z-index: 1;
  padding: 50px 40px 26px 40px;
  margin-right: 13.85vw;
  box-shadow: 0px 4px 4px 0px rgba(92, 157, 224, 0.3);
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 30px;
  border: 2px solid #fff;
  .ant-tabs-top > .ant-tabs-nav::before {
    border-bottom: none;
  }
  .ant-tabs .ant-tabs-tab {
    padding: 0 0 7px 0;
  }
  .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar-animated {
    height: 5px;
    border-radius: 6px;
    width: 22px !important;
  }
  .ant-tabs-top > .ant-tabs-nav {
    margin: 0 0 36px 0;
  }
  .ant-tabs-tab-active + .ant-tabs-tab + .ant-tabs-ink-bar-animated {
    transform: translateX(16px);
  }
  .ant-tabs-tab-active + .ant-tabs-ink-bar-animated {
    transform: translateX(24px);
  }
}
.auth-title {
  font-size: 36px;
  letter-spacing: 3.6px;
  font-weight: 700;
  color: rgba(34, 34, 34, 1);
  text-align: center;
  margin-bottom: 0;
  margin-left: 16px;
}
.auth-box-title {
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
}
.auth-content .ant-form-item-label > label {
  color: #fff;
  height: 46px;
}
.auth-content .ant-form-item-control-input {
  min-height: 46px;
  user-select: none;
}
// .auth-content .ant-input-affix-wrapper > input.ant-input {
//   padding: 0;
// }
.auth-content .ant-input-affix-wrapper.ant-input-password,
.auth-content .ant-input-affix-wrapper.auth-input-identifying-code {
  padding: 0 15px 0 0;
}
.auth-content .ant-input-password .ant-input,
.auth-content .ant-input-affix-wrapper.auth-input-identifying-code > input {
  padding: 18px 15px;
  border-radius: 8px;
  background-color: transparent;
  box-shadow: none;
}
.auth-content .ant-form-item-has-error .auth-input-identifying-code :not(.ant-input-disabled):not(.ant-input-borderless).ant-input {
  background-color: transparent;
  box-shadow: none;
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }
}
.auth-content .ant-input:focus,
.auth-content .ant-input-focused {
  border-color: transparent;
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
}
// .auth-content .ant-input-password .ant-input-suffix {
//   margin-right: 10px;
// }
.auth-content .ant-input,
.auth-content .ant-input-affix-wrapper,
.auth-content .ant-input-password {
  border-radius: 8px;
  color: @base-text-color;
  border: 1px solid transparent;
  padding: 18px 15px;
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
  background: @primary-bg-color;
}
.auth-content .ant-input:focus {
  border-color: @primary-color;
}

.auth-content {
  .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
  .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus,
  .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper,
  .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:focus,
  .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:focus,
  .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused,
  .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper-focused,
  .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper-focused {
    box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
    background-color: @primary-bg-color;
  }
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: transparent;
  }
  .ant-input-affix-wrapper-focused.ant-input-password,
  .ant-input-affix-wrapper-focused.auth-input-identifying-code {
    border-color: @primary-color !important;
  }

  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover,
  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
  }
}

.auth-content .ant-input-password .ant-input,
.auth-content .ant-input-password .ant-input:hover {
  background: none !important;
  box-shadow: none !important;
}

.auth-content .ant-input-password-icon {
  color: @base-text-color-light;
}
.auth-content .ant-input-password-icon:hover {
  color: rgba(0, 26, 68, 1);
}

.auth-content .ant-form .ant-form-item {
  margin-bottom: 42px;
  &:last-child {
    margin-bottom: 0;
  }
}
.auth-content .ant-form-item-explain {
  margin-bottom: -24px;
}

.auth-submit.ant-btn {
  height: 46px;
  border-radius: 8px;
}
.pwd-content {
  margin-top: -44px;
  margin-left: -40px;
}
.pwd-title {
  font-weight: 400;
  font-size: 36px;
  /* color: #222222; */
}
.pwd-tips {
  font-weight: 400;
  font-size: 20px;
  /* color: #222222; */
  padding-bottom: 23px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 54px;
}
/* 自动填充input样式 */
.auth-content form {
  input:-webkit-autofill,
  .ant-input-password .ant-input {
    -webkit-box-shadow: 0 0 20px 1000px #dce0fd inset !important;
    &:hover,
    &:focus,
    &:active {
      -webkit-box-shadow: 0 0 20px 1000px #dce0fd inset !important;
    }
  }
  :-webkit-autofill {
    -webkit-transition: background-color 5000s ease-in-out 0s !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }
  input:-internal-autofill-selected {
    background-color: @primary-bg-color !important;
  }
  ::placeholder {
    color: @base-text-color !important;
  }
}

/* 自动填充input样式 END */
</style>
