import { getMarkdown2Json, checkMarkdownJsonEmpty } from '@/utils/markdown'
import { message } from 'ant-design-vue'
export function useSelectTemplate(content) {
  if (!content) {
    message.error('请输入内容')
    return
  }
  const { result, message: msg } = getMarkdown2Json(content)
  if (msg) {
    message.error(msg)
    return
  }
  const [checkSuccess, emptyKey] = checkMarkdownJsonEmpty(result)
  if (!checkSuccess) {
    message.error(`${emptyKey}缺少正文或说明`)
    return
  }
  return result
}
