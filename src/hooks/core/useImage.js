/**
 * 图片使用 //TODO:打包之后使用图片生产环境会丢失，暂时不可用
 * @param {*} folder 图片文件夹，assets/images/下的文件夹
 * @param {*} suffix 图片后缀，默认svg
 * @example const getImageUrl = useImage()
 * @returns {Function} getImageUrl
 */
export function useImage(folder, suffix = 'svg') {
  function getImageUrl(image) {
    const locationStr = `../../assets/images/${folder || 'classification-block-select'}/${image}.${suffix}`
    const obj = new URL(locationStr, import.meta.url)
    return obj?.pathname
  }
  return getImageUrl
}
