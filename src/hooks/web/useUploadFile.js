import uploadApi from '@/api/upload'
import { useAppStore } from '@/stores/modules/app'
import { DEFAULT_BUCKET_NAME, LIST_FILE_UPLOAD_SUCCESS_CODE, LIST_FILE_UPLOAD_ERROR_CODE, putFile, getChunkFileName, getFileChunkList } from '@/utils/common/fileUtils'
import { utils } from 'gendo-shared'
import { isAsyncFunction, isFunction, isArray } from '@gendo/utils'
const CHUNK_SIZE = 15 * 1024 * 1024 //分片15M
const DELETE_STORE_TIME = 60 * 1000 //删除缓存时间

/**
 * 上传文件，配置信息
 * @param {Object} options 配置信息
 * @param {String} options.bucketName 存储桶名称 默认DEFAULT_BUCKET_NAME - font-end
 * @param {Number} options.chunkSize 分片大小 默认5M-5 * 1024 * 1024
 * @param {Boolean | Function} options.successRunTask 所有文件上传成功后执行任务
 * @param {Function} options.errorMsg 失败时的错误提示回调
 * @param {Function} options.callback 上传成功或失败的回调函数，如果是list上传，每个文件都会触发。参数 key - 当前文件的key, isSuccess - 是否成功, fileItem - 当前上传文件的item 包含桶名、文件名、id、key等数据, id - listId或者fileId, isLast - 是否是list最后一个文件(只有list才有)
 * @param {Boolean} options.isMultipartCallback 是否是分片上传处理回调函数
 * @param {Boolean} options.isForceMultipart 是否强制触发分片上传
 *
 */
export function useUploadFile(options) {
  const appStore = useAppStore()
  const { chunkSize = CHUNK_SIZE, bucketName: defBucketName = DEFAULT_BUCKET_NAME, successRunTask, errorMsg, callback, isMultipartCallback, isForceMultipart } = options
  // const refTaskData = ref()
  // function changeTaskData(data) {
  //   console.log('changeTaskData', data)
  //   const refTaskData = ref(data)
  //   console.log('refTaskData', refTaskData)
  // }
  //上传任务
  function addTask(key, data) {
    // appStore.uploadTask[key] = data || refTaskData.value
    appStore.uploadTask[key] = data
  }

  /**执行任务 */
  async function runTask(key, result) {
    const taskData = appStore.uploadTask[key]
    if (!taskData) {
      console.warn('找不到需要执行的任务,taskId:%s', key)
      return
    }
    let { api, data, beforeFetch, afterFetch } = taskData
    if (!api) return
    if (beforeFetch) {
      if (isAsyncFunction(beforeFetch)) {
        data = await beforeFetch(data, result)
      } else {
        data = beforeFetch(data, result)
      }
    }
    if (!data) return
    await api(data)
    afterFetch && afterFetch(data)
    //1分钟后自动删除
    setTimeout(() => {
      delete appStore.uploadTask[key]
    }, DELETE_STORE_TIME)
  }

  /**获取缓存数据 */
  function getData(key) {
    return appStore.uploadObj[key] || {}
  }
  /**设置缓存数据 */
  function setData(key, data) {
    appStore.uploadObj[key] = data
    return appStore.uploadObj[key]
  }

  /**初始化缓存数据 */
  function initData(data) {
    return setData(data.key, {
      ...data,
      progress: 0,
      status: 0,
      statusMsg: '上传中'
    })
  }

  /**
   * 上传成功
   * @param {*} key
   * @param {*} url
   * @param {*} data
   * @param {*} listId
   */
  function success(key, url, data, listId, callData) {
    const state = getData(key)
    setData(key, {
      ...state,
      progress: 100,
      status: 1,
      statusMsg: '上传成功',
      uploadUrl: url.substring(0, url.lastIndexOf('?')),
      resData: data
    })
    uploadListLast && updateListData(listId, LIST_FILE_UPLOAD_SUCCESS_CODE)
    callData && fileCallback(...callData, appStore.uploadObj[key])
  }

  /**
   * 上传失败
   * @param {*} key
   * @param {*} msg
   * @param {*} listId
   */
  function error(key, msg, listId, callData) {
    const state = getData(key)
    setData(key, {
      ...state,
      status: -1,
      statusMsg: '上传失败',
      msg
    })

    updateListData(listId, LIST_FILE_UPLOAD_ERROR_CODE, -1, msg)
    callData && fileCallback(...callData, appStore.uploadObj[key])
  }

  /**
   * 更新进度
   * @param {*} key
   * @param {*} _loaded
   */
  function updateProgress(key, _loaded, listId) {
    const state = getData(key)
    const loaded = (state.loaded || 0) + (_loaded || 0)
    setData(key, {
      ...state,
      loaded,
      progress: Number(((loaded / state.total) * 100).toFixed(2))
    })

    updateListData(listId, _loaded)
  }

  /**
   * 更新列表上传的数据
   * @param {*} id
   * @param {*} loaded
   * @param {*} status
   * @param {*} msg
   * @returns
   */
  function updateListData(id, loaded, status, msg, isInit) {
    if (!id) return
    if (!appStore.uploadListObj[id]) {
      appStore.uploadListObj[id] = {}
    }
    const state = appStore.uploadListObj[id]
    if (!state.loaded || isInit) {
      state.loaded = 0
    }
    if (loaded === LIST_FILE_UPLOAD_ERROR_CODE) {
      state.status = status ?? 1
      state.statusMsg = '上传失败'
      state.msg = msg
      return
    }
    if (loaded === LIST_FILE_UPLOAD_SUCCESS_CODE) {
      state.loaded = state.total
      state.progress = 100
    } else {
      state.loaded += loaded
      state.progress = state.total ? Number(((state.loaded / state.total) * 100).toFixed(2)) : 0
    }
    if (status != undefined) {
      state.status = status
    } else {
      state.status = state.progress >= 100 ? 1 : 0
    }
    if (state.progress >= 100) {
      state.status = 1
      state.statusMsg = '上传成功'
    }
    return state
  }

  /**
   * 单文件上传（小文件）
   * @param {*} file
   * @param {*} param1
   * @returns
   */
  async function uploadPutObject(file, { key, bucketName, fileName }, listId) {
    const res = await uploadApi.presignedPutObject({ bucketName, fileName })
    const { status, data: url } = res || {}
    if (status || !url) {
      error(key, '获取上传地址失败：' + res.msg)
      return false
    }
    try {
      await putFile(url, file)
      success(key, url, undefined, listId)
    } catch (err) {
      error(key, err, listId)
      return false
    }
    return true
  }

  /**
   * 获取文件分片信息，如果不存在则创建分片
   */
  async function getListMultipart({ key, bucketName, fileName }) {
    //如果文件大于切片的大小，分片断点续传
    const [, checkRes] = await utils.to(uploadApi.listMultipart({ bucketName, fileName }))
    const { Uploads = [] } = checkRes?.data || {}
    const uploaded = Uploads.find((m) => m.Key === fileName)
    let uploadId
    let Parts = []
    //如果存在上传的分片，直接取第一个
    if (uploaded) {
      uploadId = uploaded.UploadId
      //获取已上传的分片数据
      const res = await uploadApi.listParts({ bucketName, fileName, uploadId })
      Parts = res?.data?.Parts || []
    } else {
      const res = await uploadApi.createMultipartUpload({ bucketName, fileName })
      const { status = -1, data } = res || {}
      if (status || !data?.UploadId) {
        error(key, '创建分片失败')
        return [false]
      }
      uploadId = data.UploadId
    }
    return [uploadId, Parts]
  }

  function fileCallback(...args) {
    // console.log('fileCallback', ...args)
    callback && callback(...args)
  }

  async function _autoRunTask(key, result, autoRunTask, taskData) {
    if (!result) return
    //如果执行成功task是函数，就直接执行
    if (isFunction(successRunTask)) {
      const data = isArray(result) ? appStore.uploadListObj[key] : appStore.uploadObj[key]
      return successRunTask(data, result, taskData)
    }
    autoRunTask = successRunTask || autoRunTask
    //自动执行任务
    if (autoRunTask) {
      addTask(key, taskData)
      await runTask(key, result)
    }
  }

  let uploadListLast = false

  /**
   * 批量上传文件
   * @param {*} fileList
   * @param {*} id
   * @param {*} autoRunTask 上传完成之后自动执行任务
   * @returns
   */
  async function uploadFileList(fileList, id, autoRunTask = true, taskData) {
    let isSuccess = false
    const keys = []
    const total = fileList.reduce((total, cur) => total + cur.file.size, 0)

    let uploadData
    if (id) {
      uploadData = updateListData(id, 0, 0, undefined, true)
      uploadData.total = total
    }

    for (let i = 0; i < fileList.length; i++) {
      const item = fileList[i]
      const { file, ...other } = item
      uploadListLast = i === fileList.length - 1
      const successKey = await uploadFile(file, other, id)
      fileCallback(successKey, !!successKey, item, id, uploadListLast, appStore.uploadListObj[id])
      if (!successKey) {
        isSuccess = false
        break
      }
      //不分片的数据添加到已上传
      item.file.size <= CHUNK_SIZE && updateListData(id, item.file.size)
      keys.push(successKey)
      if (uploadListLast) {
        isSuccess = true
      }
    }
    await _autoRunTask(id, isSuccess ? keys : isSuccess, autoRunTask, taskData)
    !isSuccess && autoRunTask && errorMsg && errorMsg(appStore.uploadListObj[id])
    uploadListLast = false
    return isSuccess
  }

  /**
   * 上传文件
   * @param {*} file
   * @param {*} params
   * @param {*} listId
   * @returns
   */
  async function uploadFile(file, { bucketName, fileName, key, id }, listId, autoRunTask = true, taskData, customData) {
    if (!bucketName) bucketName = defBucketName
    if (!fileName) {
      fileName = await getChunkFileName(file, listId)
    }
    key = id || key || `s3://${bucketName}/${fileName}`
    initData({ key, bucketName, fileName, total: file.size, listId, customData })
    const fileItem = { key, bucketName, fileName, id }

    //如果文件小于分片大小，并且没有强制进行分片, 直接上传
    if (!isForceMultipart && file.size <= chunkSize) {
      const isPutObject = await uploadPutObject(file, { key, bucketName, fileName }, listId)

      //不是list时触发回调
      !listId && fileCallback(key, isPutObject, fileItem, id, false, appStore.uploadObj[key])
      if (!isPutObject) {
        autoRunTask && errorMsg && errorMsg(appStore.uploadObj[key])
        return false
      }
      if (!listId) {
        //单文件上传成功
        await _autoRunTask(key, key, autoRunTask, taskData)
      }
      return key
    }

    //分片上传
    const result = await multipartUpload(file, { id, key, bucketName, fileName }, listId)
    // 每次上传分片时都先判断当前上传的任务是否用户已经停止上传
    if (result === 'isStop') {
      const lastData = getData(key)
      setData(key, {
        ...lastData,
        isStop: true,
        status: 0,
        statusMsg: '上传失败',
        msg: '停止上传'
      })
      fileCallback(undefined, undefined, undefined, undefined, undefined, appStore.uploadObj[key])
      return false
    }
    if (!listId) {
      !isMultipartCallback && fileCallback(key, result, fileItem, id, false, appStore.uploadObj[key])
      await _autoRunTask(key, result, autoRunTask, taskData)
    }
    !result && !listId && errorMsg && errorMsg(appStore.uploadObj[key])
    return result
  }

  /**
   * 分片上传
   * @param {*} file
   * @param {*} param1
   * @param {*} listId
   * @returns
   */
  async function multipartUpload(file, itemData, listId) {
    const { id, key, bucketName, fileName } = itemData
    const [uploadId, Parts] = await getListMultipart(itemData)
    if (!uploadId) return

    //生成文件分片
    let chunkList = await getFileChunkList(file, chunkSize)
    let loaded = 0
    //存在分片的数据就过滤掉，并设置进度
    if (Parts.length) {
      chunkList = chunkList.filter((m) => {
        const part = Parts.find((p) => p.PartNumber === m.partNumber)
        if (part) {
          loaded += part.Size
          return false
        }
        return true
      })
    }
    const uploadData = getData(key)
    uploadData.loaded = loaded
    updateListData(listId, loaded)

    let isSuccess = true
    let isNeedStop
    for (let index = 0; index < chunkList.length; index++) {
      const item = chunkList[index]
      const { partNumber } = item
      //获取上传地址
      const partRes = await uploadApi.presignedUploadPart({ bucketName, fileName, uploadId, partNumber })
      const { data: url, msg } = partRes || {}
      if (!url) {
        error(key, msg || `获取分片${partNumber}上传地址失败`)
        isSuccess = false
        break
      }

      //上传分片
      try {
        await putFile(url, item.file)
        // 每次上传分片时都先判断当前上传的任务是否用户已经停止上传
        isNeedStop = getData(key)?.isStop
        if (isNeedStop) break
        updateProgress(key, item.file.size, listId)
        isMultipartCallback && fileCallback(key, true, itemData, id, false, appStore.uploadObj[key])
      } catch (err) {
        error(key, err)
        isSuccess = false
        break
      }
    }
    if (isNeedStop) return 'isStop'
    if (!isSuccess) return isSuccess
    const completeRes = await uploadApi.completeMultipartUpload({ bucketName, fileName, uploadId })
    let mulCall = [key, true, itemData, id, false] //  最后一个参数appStore.uploadObj[key]不从此处传进去, 而是从success/error方法中获取最新的appStore.uploadObj[key]数据
    if (completeRes?.status || !completeRes?.data) {
      error(key, completeRes.msg || '完成分片错误', mulCall)
      return false
    }
    success(key, completeRes.data.Location, completeRes.data, listId, mulCall)
    return key
  }

  return {
    uploadFileList,
    uploadFile,
    uploadPutObject,
    getData,
    setData,
    addTask,
    runTask
    // refTaskData
    // changeTaskData
  }
}
