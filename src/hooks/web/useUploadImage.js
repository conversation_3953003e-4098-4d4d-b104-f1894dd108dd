import { message } from 'ant-design-vue'
import { regexImgSize } from '@/hooks/web/useFile'
/**
 * 上传图片
 * @param {*} options
 * @param {*} options.maxSize 最大大小 没有则不限制，单位MB
 * @param {*} options.fileTypeList 文件类型，没有则不限制
 * @param {*} options.fileTypeMsg 文件类型提示，没有则根据fileTypeList生成
 * @param {*} options.sizeRange 文件分辨率范围，如果只有一个则表示小于，如果两个则表示在之间
 * @param {*} options.callback 上传后回调
 */
export function useUploadImage(options) {
  options = options || {}
  const fileList = ref([])
  const imageUrl = ref('')
  const loading = ref(false)

  function getBase64(img, callback) {
    const reader = new FileReader()
    reader.addEventListener('load', () => callback(reader.result))
    reader.readAsDataURL(img)
  }
  const beforeUpload = async (file) => {
    const { fileTypeList, fileTypeMsg, maxSize, sizeRange } = options
    if (fileTypeList?.length && !fileTypeList.includes(file.type)) {
      message.error(fileTypeMsg || '只能上传' + fileTypeList.map((m) => m.split('/').at(-1)).join('、') + '图片格式')
      return false
    }
    const fileMB = file.size / 1024 / 1024
    if (maxSize && fileMB > maxSize) {
      message.error('图片大小不能超过' + maxSize + 'MB')
      return false
    }
    if (sizeRange) {
      let [min, max] = sizeRange
      if (!min) min = 0
      const sizeCheck = await regexImgSize(file, min, max)
      if (!sizeCheck) {
        message.error('图片尺寸需要大于' + min + '*' + min + '，小于' + max + '*' + max)
        return false
      }
    }
    return true
  }
  const handleChange = (info) => {
    if (info.file.status === 'uploading') {
      loading.value = true
      return
    }
    if (info.file.status === 'error') {
      loading.value = false
      message.error('上传失败')
    }
  }

  function uploadImage(info) {
    getBase64(info.file, (base64Url) => {
      imageUrl.value = base64Url
      loading.value = false
      options.callback?.(base64Url)
    })
  }

  return {
    fileList,
    imageUrl,
    loading,
    beforeUpload,
    handleChange,
    uploadImage
  }
}
