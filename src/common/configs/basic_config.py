import sys
from pathlib import Path

from loguru import logger
from common.context.logging_context import get_request_id


"""
简单配置loguru日志

Args:
    log_dir: 日志文件目录
"""


def setup_logger(log_dir="logs"):
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    # 移除默认处理器
    logger.remove()

    # 日志格式，带 request_id 和线程名称
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "request_id={extra[request_id]} | "
        "thread={thread.name} | "
        "<cyan>{name}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
        "request_id={extra[request_id]} | "
        "thread={thread.name} | "
        "{name}:{line} - {message}"
    )

    # Patch loguru: 每条日志都自动加 request_id
    def patcher(record):
        record["extra"]["request_id"] = get_request_id() or "-"

    # 全局 patcher
    logger.configure(patcher=patcher)

    # 控制台日志
    logger.add(
        sys.stderr,
        format=log_format,
        level="INFO",
        colorize=True,
        enqueue=True
    )

    # 文件日志
    logger.add(
        f"{log_dir}/app.log",
        format=file_format,
        level="DEBUG",
        rotation="10 MB",
        retention="3 days",
        compression="zip",
        enqueue=True
    )

    # 错误日志
    logger.add(
        f"{log_dir}/error.log",
        format=file_format,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        enqueue=True
    )

    logger.info(f"日志系统初始化完成，日志目录: {Path(log_dir).absolute()}")
