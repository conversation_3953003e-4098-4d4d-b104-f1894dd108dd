# -*- coding: utf-8 -*-
import os
import pandas as pd
from langchain_community.document_loaders import UnstructuredFileLoader
from langchain_core.documents import Document
from typing import Any, Dict, List, Iterable
from unstructured.documents.elements import (
    Element,
    ElementMetadata,
    ListItem,
    NarrativeText,
    Table,
    Text,
    Title,
    process_metadata,
)
from lxml.html.soupparser import fromstring as soupparser_fromstring
import json

def data2docs(data, len_limit=512):
    """
    简单地合并字典，对于表头较长的表格比较浪费
    {"表头1":"表值1","表头2":"表值2"}
    """
    docs = []
    stack = ''
    for i in data:
        if len(i)>len_limit:
            if stack != '':
                docs.append(stack)
                stack = ''
            docs.append(i)
        elif len(stack) + len(i) > len_limit:
            stack += i
            docs.append(stack)
            stack = ''
        else:
            stack += i
    if stack != '':
        docs.append(stack)
    print('分割前',len(data))
    print('分割后',len(docs))
    return docs

def data2tables(data, len_limit=512):
    """
    将字典转为更可读和节省长度的表格格式
    |表头1|表头2|
    |表值1|表值2|
    """
    docs = []
    stack = ''
    row_num = 2 # excel中从1开始，第一行是表头，因此初始行为2
    row_count = 0
    for row in data:
        row = row.strip()
        if len(row) == 0:
            continue
        row_count += 1
        try:
            tbl_content = json.loads(row)
        except Exception as e:
            print(e, '\n', row)
        header = '|' + '|'.join([str(i) for i in tbl_content.keys()]) + '|'
        value_stack = '|' + '|'.join([str(i) for i in tbl_content.values()]) + '|'
        text_len = len(value_stack)
        # 第一个循环或空堆的情况
        if len(stack) == 0: 
            stack += header
            # 一行数据就超出上限的情况
            if len(stack) + text_len > len_limit: 
                # 严重超出限制的情况，虽然嵌入考虑的是token，但有必要针对这类数据做特殊处理，因此先报错
                if len(stack) + text_len > 4096: 
                    raise ValueError(f"Table content is too long to be processed: {len(stack) + text_len}.")
                stack += '\n' + value_stack
                docs.append((stack, row_num))
                row_num += row_count
                row_count = 0
                stack = ''
                continue
        if len(stack) + text_len > len_limit: # 新增数据会使字典超上限
            docs.append((stack, row_num))
            row_num += row_count - 1
            row_count = 0
            stack = header + '\n' + value_stack
        else:
            stack += '\n' + value_stack
    if stack != '':
        docs.append((stack, row_num))
    # print('分割前',len(data))
    # print('分割后',len(docs))
    return docs

class XLSPagedLoader(UnstructuredFileLoader):
    def __init__(self, file_path, filename, **unstructured_kwargs):
        self.file_path = file_path
        self.filename = filename
        super().__init__(file_path, **unstructured_kwargs)   

    def __init__(self, file_path, filename, **unstructured_kwargs):
        self.file_path = file_path
        self.filename = filename
        super().__init__(file_path, **unstructured_kwargs)    
    def load(self) -> Iterable[Document]:
        """
        Load file. Currently converting all doc files to pdf and using pdf loader to load.
        """
        sheets = pd.read_excel(self.file_path, sheet_name=None)
        filename = self.filename
        elements: List[Element] = []
        page_number = 0
        for sheet_name, sheet in sheets.items():
            page_number += 1
            # html_text = sheet.to_html(index=False, header=True, na_rep="")
            
            sheet = sheet.applymap(lambda x: str(x) if pd.notnull(x) else "")
            sheet_string = sheet.to_json(orient='records', lines=True, force_ascii=False).split('\n')
            # text = soupparser_fromstring(html_text).text_content()
            # temp = data2docs(sheet_string, len_limit=1024)
            # print(temp[0])
            data = data2tables(sheet_string, len_limit=1024)
            if len(data) == 0:
                continue

            table = [Document(page_content=f'文件名：{filename}；表名：{sheet_name}。内容：'+d[0], 
                              metadata={
                                'source':filename,
                                'chapterName':sheet_name,
                                'chapterID':str(page_number),
                                'parentChapterName':'',
                                'parentChapterID': str(-1),
                                'pageNumber': d[1],
                                }) for d in data]
            elements.extend(table)
            print(f"文件名：{filename}，表名：{sheet_name}，表头：{list(sheet.columns)}")
            # print(len(html_text))
            # print(len(text))
            # print(table[0])
            # print(len(table[0].page_content))
            # print("*"*50)
        return elements
    
if __name__ == "__main__":
    # ZIGBEE_TX. BOM 20240605xlsx-50套-明细报价及EQ备注（下单金额）(1).xlsx
    # 企业数据样本.xlsx
    loader = XLSPagedLoader("./docs/xlsx/ZIGBEE_TX. BOM 20240605xlsx-50套-明细报价及EQ备注（下单金额）(1).xlsx")
    docs = loader.load()
    with open("response_docs.txt", "w", encoding="utf-8") as f:
        for doc in docs:
            f.write(str(doc))
            f.write("\n")
        