import http from '@/utils/http'

const replaceCephUrl = (url) => {
  if (!url || url.startsWith('http')) return url
  return `${location.origin}/ceph${url.startsWith('/') ? '' : '/'}${url}`
}

const upload = {
  /**下载或显示文件预签名（生成s3临时下载地址) */
  presignedGetObject: (params) =>
    http
      .get(
        { url: `/ceph/presignedGetObject`, params },
        { isGateway: true, isTransformResponse: false }
      )
      .then((res) => {
        if (res?.data) {
          res.data = replaceCephUrl(res.data)
        }
        return res
      })
}
/**获取上传 */
export default upload
