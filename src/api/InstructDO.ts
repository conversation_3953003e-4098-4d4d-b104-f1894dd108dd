import DataLoader from 'dataloader';
import { RESTDataSource, AugmentedRequest } from '@apollo/datasource-rest';
import type { KeyValueCache } from '@apollo/utils.keyvaluecache';
import { GraphQLError } from 'graphql';

export class InstructAPI extends RESTDataSource {
    override baseURL = 'http://kb-chat:20501';
    private rootPath = '/kb-chat/v1/instruct';
    private clientHeaders: any
  
    constructor(options: { cache: KeyValueCache }, headers: any) {
        super(options); 
        this.clientHeaders = headers
    }

    override willSendRequest(_path: string, request: AugmentedRequest) {
        request.headers = { ...request.headers, ...this.clientHeaders };
    }
    private loadByIDs = new DataLoader(async (ids: Array<string>) => {
        let address = this.rootPath + '/all/many'
        const result = await this.post(address, {
            body: ids
        });
        const data = result.data;
        return ids.map((id) => data.find((bo) => bo.id === id));
    });



    async getOneByID(id: string) {
        return this.loadByIDs.load(id);
    }

    async getAllByIDs(ids: Array<string>) {
        return this.loadByIDs.loadMany(ids);
    }

    /**
    * 只有conditions这个接口才支持dataTag等数据查询，所以用conditions这个接口，而不使用load/many接口
    * 另外这个接口也方便拓展：加子查询，（但这种不支持list不为空过滤。)
    */
    private conditionByIDLoader = new DataLoader(async (jsons: Array<string>) => {
        let keys = jsons.map((query_string) => JSON.parse(query_string)["ids"])
        let other_query = JSON.parse(jsons[0])
        delete other_query["ids"]
        let ids = keys.flatMap(s => s.split(","))
        let params = {}
        if (other_query?.orderBy) {
            params["orderBy"] = other_query.orderBy
        }
        let address = this.rootPath + '/conditions'
        let result = await this.post(address, { body: { ["ids"]: ids }, params })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, { extensions: result })
        }
        const mapFunc = result.data.reduce((mapping, s) => (mapping[s["id"]] = s, mapping), {});
        const res = keys.map(key => key.split(",").reduce((acc, id) => (mapFunc[id] && acc.push(mapFunc[id]), acc), []));
        return res
    });

    /**
     * 从record中获取参数
     * @param record 
     * @param paramKeys 
     * @returns 
     */
    private getParamsFromRecord(record: any, paramKeys: Array<string>) {
        let res = {}
        let params = record?.params
        paramKeys.forEach((key) => {
            if (record[key] !== undefined) {
                res[key] = record[key]
            }
            if (params && params[key] !== undefined) {
                res[key] = params[key]
            }
        })
        return res
    }

    async conditionAllByIDs(json: string) {
        return this.conditionByIDLoader.load(json);
    }
    
    private load = {}

    private loadByKey(key) {
        if (!this.load[key]) {
            this.load[key] = new DataLoader(async (jsons: Array<string>) => {
                let keys = jsons.map((query_string) => JSON.parse(query_string)[key])
                let other_query = JSON.parse(jsons[0])
                delete other_query[key]
                let params = {}
                if (other_query?.orderBy) {
                    params["orderBy"] = other_query.orderBy
                }
                let address = this.rootPath + '/conditions'
                let result = await this.post(address, { body: { [key]: keys, ...other_query }, params })
                if (result.status !== 0) {
                    throw new GraphQLError(`get ${this.baseURL}${address} error!`, { extensions: result })
                }
                const mapFunc = result.data.reduce((mapping, s) => {
                    if (!mapping[s[key]]) {
                        mapping[s[key]] = [];
                    }
                    mapping[s[key]].push(s);
                    return mapping;
                }, {});

                return keys.map((s) => mapFunc[s] ? mapFunc[s] : [])
            });
        }
        return this.load[key]
    }

    /**
     * 对象解析为URLSearchParams
     * @param params  参数对象
     * @returns     URLSearchParams
     */
    private parseParamsFromRecord(params: Record<string, string | undefined> | undefined,) {
        const usp = new URLSearchParams();
        if (params) {
            Object.entries(params).forEach(([name, value]) => {
                if (value !== undefined && value !== null) {
                    if (Array.isArray(value)) {
                        value.forEach((v) => {
                            usp.append(name, v);
                        });
                    } else {
                        usp.set(name, value);
                    }
                }
            });
        }
        return usp;
    }

    async getByKey(key, query_string) {
        return this.loadByKey(key).load(query_string);
    }

    async getAll() {
        let address = this.rootPath + '/all'
        let result = await this.get(address)
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getAllWithPage(pageable: any) {
        let address = this.rootPath + '/all/page'
        let result = await this.get(address, { params: this.parseParamsFromRecord(pageable) })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result});
        }
        return result.data
    }

    async getAllWithPageByIDs(ids: Array<string>, pageable: any) {
        let address = this.rootPath + '/all/many/page'
        let result = await this.post(address, { 
            params: this.parseParamsFromRecord(pageable),
            body: ids
        })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getAllByCustomQuery(mongoQuery: string) {
        let address = this.rootPath + '/query'
        let result = await this.get(address, { params: {queryJson: mongoQuery} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

	async getCountByQuery(conditions: any) {
        let address = this.rootPath + '/count/field'
        let result = await this.get(address,{ params: {...conditions} });
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getAllWithPageByCustomQuery(mongoQuery: string, pageable: any) {
        let address = this.rootPath + '/query/page?' + this.parseParamsFromRecord(pageable).toString()
        let result = await this.get(address, { params: {queryJson: mongoQuery} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result});
        }
        return result.data
    }

    async getAllByConditions(conditions: any) {
        let params = this.getParamsFromRecord(conditions, ["orderBy","customField","customValue"])
        let address = this.rootPath + '/conditions'
        let result = await this.post(address, {params: params, body: this.handlerBody(conditions) })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getAllWithPageByConditions(conditions: any, pageable: any) {
        let address = this.rootPath + '/conditions/page?' + this.parseParamsFromRecord(pageable).toString()
        let result = await this.post(address, { body: this.handlerBody(conditions) })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }
    
    // 特殊处理，后续去掉，是为了前端现在还有部分接口，本该传数组的，传成了单个导致的问题（因为以前调用接口是用get，不区分数组，但现在用post了，区分数组形式）
    handlerBody(conditions: any) {
        for (const key in conditions) {
            if (conditions[key] && !key.endsWith('Like') && typeof conditions[key] == 'string' && !key.endsWith('excludeVisable')) {
                conditions[key] = [conditions[key]]
            }
            if (conditions[key] && !key.endsWith('NotEmpty') && typeof conditions[key] == 'boolean') {
                conditions[key] = [conditions[key]]
            }
        }
        return conditions
    }

    async getAllToAdmin(conditions: any) {
        let address = this.rootPath + '/admin'
        let result = await this.get(address, { params: {...conditions} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getAllWithPageToAdmin(conditions: any, pageable: any) {
        let address = this.rootPath + '/admin/page?' + this.parseParamsFromRecord(pageable).toString()
        let result = await this.get(address, { params: {...conditions} });
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getDistinctFields(fieldName: string,conditions: any) {
        let address = this.rootPath + `/distinct/${fieldName}`
        let result = await this.get(address,{ params: {...conditions} });
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async getValueCountFields(conditions: any) {
        let address = this.rootPath + '/count/enum'
        let { fields ,sort, queryJson } = conditions
        let result = await this.post(address, { params: { fields ,sort, queryJson} , body: this.handlerBody(conditions) })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

	async getValueGroupFields(conditions: any) {
			let address = this.rootPath + '/group/enum'
			let result = await this.get(address, { params: {...conditions} })
			if (result.status !== 0) {
				throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
			}
			return result.data
		}

    async getCountPageWithPageToCount(conditions: any, pageable: any) {
        let address = this.rootPath + '/count/page?' + this.parseParamsFromRecord(pageable).toString()
        let result = await this.get(address, { params: {...conditions} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async addOne(bean: any) {
        let address = this.rootPath
        let result = await this.post(address, { body: bean })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async addAll(beans: Array<any>) {
        let address = this.rootPath + '/all'
        let result = await this.post(address, { body: beans })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async putOne(bean: any,unsetFields: Array<string>) {
        let address = this.rootPath
        //判断unsetFields是否为空或者是否有传递
        if (unsetFields && unsetFields.length > 0) {
            address = address + `?unsetFields=${unsetFields}`
        }
        let result = await this.put(address, { body: bean })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async putAll(beans: Array<any>,unsetFields: Array<string>) {
        let address = this.rootPath + '/all'
         //判断unsetFields是否为空或者是否有传递
         if (unsetFields && unsetFields.length > 0) {
            address = address + `?unsetFields=${unsetFields}`
        }
        let result = await this.put(address, { body: beans })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async patchOne(bean: any,unsetFields: Array<string>) {
        let address = this.rootPath
        //判断unsetFields是否为空或者是否有传递
        if (unsetFields && unsetFields.length > 0) {
            address = address + `?unsetFields=${unsetFields}`
        }
        let result = await this.patch(address, { body: bean })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async patchAll(beans: Array<any>,unsetFields: Array<string>) {
        let address = this.rootPath + '/all'
         //判断unsetFields是否为空或者是否有传递
         if (unsetFields && unsetFields.length > 0) {
            address = address + `?unsetFields=${unsetFields}`
        }
        let result = await this.patch(address, { body: beans })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async delOne(id: string) {
        let address = this.rootPath + `/${id}`
        let result = await this.delete(address)
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async delAll(ids: Array<string>) {
        let address = this.rootPath + '/all'
        let result = await this.delete(address, { params: {ids: ids.join(',')} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async delAllByCustomQuery(mongoQuery: string) {
        let address = this.rootPath + '/query'
        let result = await this.delete(address, { params: {queryJson: mongoQuery} })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, {extensions: result})
        }
        return result.data
    }

    async delAllByCondition(conditions: any) {
        let address = this.rootPath + '/delete/condition'
        let result = await this.post(address, { body: { ...conditions } })
        if (result.status !== 0) {
            throw new GraphQLError(`get ${this.baseURL}${address} error!`, { extensions: result })
        }
        return result.data
    } 

}
