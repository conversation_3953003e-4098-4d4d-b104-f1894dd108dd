import { LAYOUT } from '@/router/constant'
import writingIcon from '@/assets/images/rag/menu/writing.svg'

export default {
  path: '/writing',
  name: 'Writing',
  component: LAYOUT,
  redirect: '/writing/index',
  showOrder: 2,
  meta: {
    title: '智能写作',
    icon: writingIcon,
    hideChildrenInMenu: true
  },
  children: [
    {
      path: 'index/:id?',
      name: 'writingIndex',
      component: () => import('@/views/writing/index.vue'),
      meta: {
        title: '智能写作',
        currentActiveMenu: '/writing/index',
        padding: false
      }
    },
    // {
    //   path: 'chat/:id?',
    //   name: 'WritingChat',
    //   component: () => import('@/views/writing/chat.vue'),
    //   meta: {
    //     title: '智能写作',
    //     currentActiveMenu: '/writing/index',
    //     padding: false,
    //     noScroll: true
    //   }
    // },
    {
      path: 'list',
      name: 'WritingList',
      component: () => import('@/views/writing/list.vue'),
      meta: {
        title: '文档列表',
        currentActiveMenu: '/writing/index',
        padding: false
      }
    }
  ]
}
