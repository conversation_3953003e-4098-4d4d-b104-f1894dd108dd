package com.polarizon.gendo.common.bean.vo;

import com.fasterxml.jackson.annotation.JsonGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * gpu监控数据封装
 */
@Data
@SuperBuilder
@Schema(description = "gpu监控数据")
@AllArgsConstructor
@NoArgsConstructor
public class GpuMonitorVO extends CommonMonitorVO{
    @Schema(description = "gpuID")
    private String id;

    @Schema(description = "gpu序号")
    private String gpuNumber;

    @Schema(description = "加速卡版本")
    private String gpuVersion;

    @Schema(description = "使用的显存，单位MB")
    private Integer usedMemoryMB;

    @Schema(description = "空闲的显存，单位MB")
    private Integer freeMemoryMB;

    @Schema(description = "所有的显存，单位MB")
    @JsonGetter("allMemoryMB")
    public Integer getAllMemoryMB() {
        return usedMemoryMB + freeMemoryMB;
    }

    @Schema(description = "gpu显存使用率")
    @JsonGetter("memoryUtil")
    public Double getMemoryUtil() {
        return (Double.valueOf(usedMemoryMB) / getAllMemoryMB()) * 100.0;
    }

    @Schema(description = "gpu显存使用率、使用率综合使用率（平均值）")
    @JsonGetter("comprehensiveUtil")
    public Double getComprehensiveUtil() {
        return (getUtil() + getMemoryUtil()) / 2;
    }

    @Override
    public String toString() {
        return "GpuMonitorVO{" +
            "id='" + id  +
            ", hostName=" + getHostName() +
            ", ip=" + getIp()+
            ", gpuNumber='" + gpuNumber +
            ", gpuVersion='" + gpuVersion +
            ", usedMemoryMB=" + usedMemoryMB +
            ", freeMemoryMB=" + freeMemoryMB +
            ", allMemoryMB=" + getAllMemoryMB() +
            ", util=" + getUtil() +
            ", memoryUtil=" + getMemoryUtil() +
            ", comprehensiveUtil=" + getComprehensiveUtil() +
            "}";
    }
}
