package com.polarizon.gendo.common.bean.vo;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 通用监控数据封装
 */
@Data
@SuperBuilder
@Schema(description = "通用监控数据")
@AllArgsConstructor
@NoArgsConstructor
public class CommonMonitorVO {
    @Schema(description = "主机名")
    private String hostName;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "使用率")
    private Double util;

    @Override
    public String toString() {
        return "CommonMonitorVO{" +
            "hostName='" + hostName +
            ", ip='" + ip +
            ", util=" + util +
            "}";
    }
}
