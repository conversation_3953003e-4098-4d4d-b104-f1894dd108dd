package com.polarizon.gendo.sg.authority.util;

import com.polarizon.gendo.sg.authority.model.UserContext;
import org.springframework.core.NamedThreadLocal;

/**
 * 上下文持有者
 * <p>
 * 使用ThreadLocal存储当前用户上下文
 */
public class ContextHolder {
    
    private static final ThreadLocal<UserContext> CURRENT_USER = new NamedThreadLocal<>("User Context");
    
    /**
     * 设置当前用户上下文
     *
     * @param userContext 用户上下文
     */
    public static void setCurrentUser(UserContext userContext) {
        CURRENT_USER.set(userContext);
    }
    
    /**
     * 获取当前用户上下文
     *
     * @return 用户上下文，如果不存在则返回null
     */
    public static UserContext getCurrentUser() {
        return CURRENT_USER.get();
    }
    
    /**
     * 清除当前用户上下文
     */
    public static void clearCurrentUser() {
        CURRENT_USER.remove();
    }
} 