package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.polarizon.gendo.sg.authority.constant.Constants.X_TENANT_ID;
import static com.polarizon.gendo.sg.authority.constant.Constants.X_USER_ID;

/**
 * 数据规则评估器
 * <p>
 * 负责解析和评估数据规则，将规则转换为MongoDB查询条件或聚合管道
 * <p>
 * 支持两种类型的规则：
 * <ul>
 *   <li><b>Criteria规则</b> - 简单的查询条件，返回CriteriaDefinition</li>
 *   <li><b>Pipeline规则</b> - 聚合管道，返回List&lt;Document&gt;</li>
 * </ul>
 */
@Slf4j
@Component
public class DataRuleEvaluator {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    private final ObjectMapper objectMapper;

    public DataRuleEvaluator() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 将数据规则转换为MongoDB查询条件或聚合管道
     *
     * @param rule 数据规则
     * @return CriteriaDefinition（简单查询条件）或 List&lt;Document&gt;（聚合管道）
     */
    public Object evaluateRule(Object rule) {
        if (rule == null) {
            return new Criteria(); // 无限制
        }

        try {
            // 将规则转换为JSON字符串
            String ruleJson = objectMapper.writeValueAsString(rule);

            // 替换变量
            String processedRule = replaceVariables(ruleJson);

            // 判断是聚合管道还是普通查询条件
            String trimmedRule = processedRule.trim();
            if (trimmedRule.startsWith("[")) {
                // 聚合管道：JSON数组格式
                try {
                    // Jackson会将JSON对象反序列化为LinkedHashMap，需要转换为Document
                    @SuppressWarnings("unchecked")
                    List<Object> rawPipeline = objectMapper.readValue(processedRule, new TypeReference<List<Object>>() {
                    });
                    
                    List<Document> pipeline = new ArrayList<>();
                    for (Object stage : rawPipeline) {
                        if (stage instanceof Map<?, ?> map) {
                            // 将Map转换为Document
                            Document stageDoc = new Document();
                            for (Map.Entry<?, ?> entry : map.entrySet()) {
                                if (entry.getKey() instanceof String key) {
                                    stageDoc.put(key, convertToDocumentRecursively(entry.getValue()));
                                }
                            }
                            pipeline.add(stageDoc);
                        } else if (stage instanceof Document doc) {
                            pipeline.add(doc);
                        }
                    }
                    return pipeline;
                } catch (Exception e) {
                    log.error("Failed to parse aggregation pipeline rule: {}", processedRule, e);
                    return new Criteria(); // 解析失败时返回无限制
                }
            } else {
                // 普通查询条件：JSON对象格式
                try {
                    Document document = Document.parse(processedRule);

                    if (document == null || document.isEmpty()) {
                        return new Criteria();
                    }

                    // 返回CriteriaDefinition
                    return new CriteriaDefinition() {
                        @NotNull
                        @Override
                        public Document getCriteriaObject() {
                            return document;
                        }

                        @NotNull
                        @Contract(pure = true)
                        @Override
                        public String getKey() {
                            return "";
                        }
                    };
                } catch (Exception e) {
                    log.error("Failed to parse criteria rule: {}", processedRule, e);
                    return new Criteria(); // 解析失败时返回无限制
                }
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to process rule", e);
            return new Criteria(); // 出错时返回无限制
        }
    }

    /**
     * 替换规则中的变量
     *
     * @param rule 规则字符串
     * @return 替换变量后的规则字符串
     */
    private String replaceVariables(String rule) {
        UserContext userContext = ContextHolder.getCurrentUser();
        if (userContext == null) {
            return rule;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(rule);
        StringBuilder sb = new StringBuilder();

        while (matcher.find()) {
            String variable = matcher.group(1);
            String value = getVariableValue(variable, userContext);
            matcher.appendReplacement(sb, value);
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 获取变量值
     *
     * @param variable    变量名
     * @param userContext 用户上下文
     * @return 变量值
     */
    private String getVariableValue(@NotNull String variable, UserContext userContext) {
        return switch (variable) {
            case X_USER_ID -> userContext.getUserId() != null ? userContext.getUserId() : "null";
            case X_TENANT_ID -> userContext.getTenantId() != null ? userContext.getTenantId() : "null";
            default -> "null";
        };
    }

    /**
     * 递归地将对象转换为Document兼容的格式
     * 
     * @param value 要转换的值
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    private Object convertToDocumentRecursively(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Map<?, ?> map) {
            // 将Map转换为Document
            Document document = new Document();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                if (entry.getKey() instanceof String key) {
                    document.put(key, convertToDocumentRecursively(entry.getValue()));
                }
            }
            return document;
        } else if (value instanceof List<?> list) {
            // 递归处理List中的元素
            List<Object> convertedList = new ArrayList<>();
            for (Object item : list) {
                convertedList.add(convertToDocumentRecursively(item));
            }
            return convertedList;
        } else {
            // 基本类型直接返回
            return value;
        }
    }
} 