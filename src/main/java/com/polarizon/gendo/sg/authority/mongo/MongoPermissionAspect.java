package com.polarizon.gendo.sg.authority.mongo;

import com.polarizon.gendo.sg.authority.core.PermissionManager;
import com.polarizon.gendo.sg.authority.util.MongoCollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.convert.QueryMapper;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MongoDB权限切面
 * <p>
 * 拦截MongoDB操作并应用权限规则，支持普通查询和聚合查询的权限过滤
 * <p>
 * 类结构：
 * <ul>
 *   <li><b>基础设施</b> - 依赖注入、上下文定义、异常类</li>
 *   <li><b>切入点定义</b> - AOP切入点配置</li>
 *   <li><b>拦截处理器</b> - 各种MongoDB操作的拦截方法</li>
 *   <li><b>权限验证</b> - 普通查询的权限处理逻辑</li>
 *   <li><b>聚合处理</b> - 聚合查询的权限处理逻辑</li>
 *   <li><b>工具方法</b> - 通用的辅助方法</li>
 * </ul>
 *
 * @see PermissionManager
 * @see MongoTemplate
 */
@Slf4j
@Aspect
@Component
public class MongoPermissionAspect {

    private final PermissionManager permissionManager;
    private final MongoTemplate mongoTemplate;
    private final MongoConverter mongoConverter;
    private final QueryMapper queryMapper;

    // ThreadLocal变量，用于标记当前线程是否正在执行权限过滤的聚合查询
    private final ThreadLocal<Boolean> inPermissionAggregation = ThreadLocal.withInitial(() -> false);

    public MongoPermissionAspect(PermissionManager permissionManager, MongoTemplate mongoTemplate, MongoConverter mongoConverter) {
        this.permissionManager = permissionManager;
        this.mongoTemplate = mongoTemplate;
        this.mongoConverter = mongoConverter;
        this.queryMapper = new QueryMapper(mongoConverter);
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 🏗️ 基础设施 - 依赖注入、上下文定义、异常类
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 权限上下文，封装权限检查所需的信息
     *
     * @param collectionName 集合名称
     * @param resourceId     资源ID
     * @param operation      操作类型
     */
    private record PermissionContext(String collectionName, String resourceId, String operation) {

        boolean hasResource() {
            return resourceId != null;
        }
    }

    /**
     * 权限过滤结果封装
     * 用于表示权限过滤的不同处理方式
     */
    private sealed interface PermissionFilterResult {
        /**
         * 简单的查询过滤条件
         */
        record SimpleFilter(CriteriaDefinition criteria) implements PermissionFilterResult {}

        /**
         * 需要聚合查询的过滤条件
         */
        record AggregationFilter(Document aggregationCriteria) implements PermissionFilterResult {}

        /**
         * 无过滤条件
         */
        record NoFilter() implements PermissionFilterResult {}
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 🎯 切入点定义 - AOP切入点配置 
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 定义切入点：捕获MongoTemplate的findById方法(两个参数版本)
     */
    @Pointcut(value = "execution(public * org.springframework.data.mongodb.core.MongoTemplate.findById(..)) && args(id, entityClass)",
        argNames = "id,entityClass")
    public void findByIdPointcut(Object id, Class<?> entityClass) {
    }

    /**
     * 定义切入点：捕获MongoTemplate的findById方法(三个参数版本)
     */
    @Pointcut(value = "execution(public * org.springframework.data.mongodb.core.MongoTemplate.findById(..)) && args(id, entityClass, collectionName)",
        argNames = "id,entityClass,collectionName")
    public void findByIdPointcutWithCollection(Object id, Class<?> entityClass, String collectionName) {
    }

    /**
     * 定义切入点：捕获MongoTemplate的insert方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.insert(..))")
    public void insertPointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate的insertAll方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.insertAll(..))")
    public void insertAllPointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate的save方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.save(..))")
    public void savePointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate的remove方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.remove(..))")
    public void removePointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate中Query作为第一个参数的方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(org.springframework.data.mongodb.core.query.Query, ..))")
    public void queryAsFirstParamPointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate中Query作为唯一参数的方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(org.springframework.data.mongodb.core.query.Query))")
    public void queryAsOnlyParamPointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate中Query作为最后一个参数（且不是唯一参数）的方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, org.springframework.data.mongodb.core.query.Query)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, *, org.springframework.data.mongodb.core.query.Query)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, *, *, org.springframework.data.mongodb.core.query.Query)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, *, *, *, org.springframework.data.mongodb.core.query.Query))")
    public void queryAsLastParamPointcut() {
    }

    /**
     * 定义切入点：捕获MongoTemplate中Aggregation作为参数的方法
     */
    @Pointcut("execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(org.springframework.data.mongodb.core.aggregation.Aggregation, ..)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, org.springframework.data.mongodb.core.aggregation.Aggregation, ..)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, *, org.springframework.data.mongodb.core.aggregation.Aggregation, ..)) || " +
        "execution(public * org.springframework.data.mongodb.core.MongoTemplate.*(*, *, *, org.springframework.data.mongodb.core.aggregation.Aggregation, ..))")
    public void aggregationPointcut() {
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 🛡️ 拦截处理器 - 各种MongoDB操作的拦截方法
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 拦截findById方法(两个参数版本)
     *
     * @param joinPoint   切点对象，包含被拦截的方法信息
     * @param id          实体ID
     * @param entityClass 实体类
     * @return 查询结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around(value = "findByIdPointcut(id, entityClass)", argNames = "joinPoint,id,entityClass")
    public Object aroundFindById(ProceedingJoinPoint joinPoint, Object id, Class<?> entityClass) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        return processQueryWithPermission(joinPoint, getCollectionName(joinPoint));
    }

    /**
     * 拦截findById方法(三个参数版本)
     *
     * @param joinPoint      切点对象，包含被拦截的方法信息
     * @param id             实体ID
     * @param entityClass    实体类
     * @param collectionName 集合名称
     * @return 查询结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around(value = "findByIdPointcutWithCollection(id, entityClass, collectionName)",
        argNames = "joinPoint,id,entityClass,collectionName")
    public Object aroundFindByIdWithCollection(ProceedingJoinPoint joinPoint, Object id, Class<?> entityClass, String collectionName) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        return processQueryWithPermission(joinPoint, collectionName);
    }

    /**
     * 拦截insert方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 插入结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("insertPointcut()")
    public Object aroundInsert(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        Object entity = args[0];

        if (entity instanceof Collection<?>) {
            // 如果是集合类型，就要调用拦截insertAll方法
            return aroundInsertAll(joinPoint);
        }

        PermissionContext context = createPermissionContext(joinPoint, "create");
        
        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed();
        }
        
        checkOperationPermission(context);
        checkDocumentPermission(context, entity);

        return joinPoint.proceed();
    }

    /**
     * 拦截insertAll方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 插入结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("insertAllPointcut()")
    public Object aroundInsertAll(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        Collection<?> entities = (Collection<?>) args[0];

        if (entities == null || entities.isEmpty()) {
            return joinPoint.proceed();
        }

        PermissionContext context = createPermissionContext(joinPoint, "create");
        
        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed();
        }
        
        checkOperationPermission(context);

        // 检查每个文档的权限
        for (Object entity : entities) {
            checkDocumentPermission(context, entity);
        }

        return joinPoint.proceed();
    }

    /**
     * 拦截save方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 保存结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("savePointcut()")
    public Object aroundSave(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        Object entity = args[0];

        // 判断是创建还是更新操作
        String operation = isNewEntity(entity) ? "create" : "update";

        PermissionContext context = createPermissionContext(joinPoint, operation);
        
        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed();
        }
        
        checkOperationPermission(context);
        checkDocumentPermission(context, entity);

        return joinPoint.proceed();
    }

    /**
     * 拦截remove方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 删除结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("removePointcut()")
    public Object aroundRemove(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        PermissionContext context = createPermissionContext(joinPoint, "delete");

        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed();
        }

        checkOperationPermission(context);

        if (args[0] instanceof Query query) {
            boolean needsAggregation = applyPermissionFilter(query, context);

            if (needsAggregation) {
                // 需要使用聚合查询，重新分析权限规则获取聚合条件
                PermissionFilterResult filterResult = analyzePermissionFilter(context);
                if (filterResult instanceof PermissionFilterResult.AggregationFilter aggregationFilter) {
                    checkDocumentPermissionWithAggregationQuery(query, context, aggregationFilter.aggregationCriteria());
                }
            } else {
                // 不需要聚合查询，先执行查询进行文档权限校验
                checkDocumentPermissionByQuery(query, context);
            }
        } else if (args[0] != null) {
            // 单个对象删除
            checkDocumentPermission(context, args[0]);
        }

        return joinPoint.proceed();
    }

    /**
     * 拦截Query作为第一个参数的方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 查询结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("queryAsFirstParamPointcut() && !queryAsOnlyParamPointcut()")
    public Object aroundQueryAsFirst(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        return processQueryMethod(joinPoint, 0);
    }

    /**
     * 拦截Query作为唯一参数的方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 查询结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("queryAsOnlyParamPointcut()")
    public Object aroundQueryAsOnly(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        return processQueryMethod(joinPoint, 0);
    }

    /**
     * 拦截Query作为最后一个参数的方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 查询结果，如果权限验证失败则抛出SecurityException
     * @throws Throwable 执行过程中可能抛出的异常
     */
    @Around("queryAsLastParamPointcut()")
    public Object aroundQueryAsLast(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 避免权限校验过程中的递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        return processQueryMethod(joinPoint, args.length - 1);
    }

    /**
     * 拦截包含Aggregation参数的聚合查询方法
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 应用过滤条件后的聚合对象
     * @throws Throwable         执行过程中可能抛出的异常
     * @throws SecurityException 权限验证失败时抛出的异常
     */
    @Around("aggregationPointcut()")
    public Object aroundAggregation(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查是否已经在权限聚合查询中，避免递归调用
        if (inPermissionAggregation.get()) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        // 聚合查询通常是读操作，但为了完整性也检查方法名
        String operation = determineOperationTypeFromMethod(joinPoint);
        PermissionContext context = createPermissionContext(joinPoint, operation);

        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed();
        }

        checkOperationPermission(context);

        // 查找并处理Aggregation参数
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof Aggregation originalAggregation) {
                Aggregation filteredAggregation = applyAggregationFilter(originalAggregation, context);

                if (filteredAggregation != null) {
                    // 普通处理成功
                    args[i] = filteredAggregation;
                } else {
                    // 需要特殊处理的聚合规则，进行合并
                    PermissionFilterResult filterResult = analyzePermissionFilter(context);
                    if (filterResult instanceof PermissionFilterResult.AggregationFilter aggregationFilter) {
                        args[i] = mergeAggregationWithPermissionFilter(originalAggregation, aggregationFilter.aggregationCriteria());
                    }
                }
                break;
            }
        }

        return joinPoint.proceed(args);
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 🔐 权限验证 - 普通查询的权限处理逻辑
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 创建权限上下文
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @param operation 操作类型
     * @return 权限上下文
     */
    @NotNull
    private PermissionContext createPermissionContext(ProceedingJoinPoint joinPoint, String operation) {
        String collectionName = getCollectionName(joinPoint);
        String resourceId = collectionName != null ? permissionManager.getResourceIdByCollection(collectionName) : null;
        return new PermissionContext(collectionName, resourceId, operation);
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 🔧 工具方法 - 通用的辅助方法
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 检查是否应该跳过权限验证
     * 采用默认拒绝策略：只有明确配置为无需权限的资源才能跳过检查
     *
     * @param context 权限上下文
     * @return true表示应该跳过权限验证，false表示需要继续进行权限验证
     * @throws SecurityException 当集合未配置权限管理且不在白名单中时抛出
     */
    private boolean shouldSkipPermissionCheck(@NotNull PermissionContext context) {
        if (context.hasResource()) {
            // 有资源配置，需要进行权限验证
            return false;
        }
        
        // 没有资源配置，检查是否在白名单中
        String collectionName = context.collectionName();
        if (collectionName != null && !permissionManager.isWhitelistedCollection(collectionName)) {
            String logMessage = String.format("权限拒绝：集合 %s 未授权访问", collectionName);
            log.warn(logMessage);
            throw new SecurityException("权限不足，无法执行此操作");
        }
        
        // 在白名单中，可以跳过权限验证
        return true;
    }

    /**
     * 获取集合名称
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 集合名称
     */
    private String getCollectionName(@NotNull ProceedingJoinPoint joinPoint) {
        return MongoCollectionUtil.getCollectionName(
            mongoTemplate,
            ((MethodSignature) joinPoint.getSignature()).getParameterNames(),
            joinPoint.getArgs()
        );
    }

    /**
     * 检查操作权限
     * <p>
     * 对于upsert操作进行特殊处理，因为它既可能是创建也可能是更新操作。
     * 用户至少需要拥有update权限或create权限中的一种才能执行upsert。
     * </p>
     *
     * @param context 权限上下文
     */
    private void checkOperationPermission(@NotNull PermissionContext context) {
        // 特殊处理upsert操作
        if ("update".equals(context.operation)) {
            // 检查是否是upsert方法调用
            if (isUpsertOperation(context)) {
                // upsert操作：用户需要有update或create权限中的至少一种
                boolean hasUpdatePermission = permissionManager.hasOperationPermission(context.resourceId, "update");
                boolean hasCreatePermission = permissionManager.hasOperationPermission(context.resourceId, "create");
                
                if (!hasUpdatePermission && !hasCreatePermission) {
                    String logMessage = String.format("权限拒绝：用户对资源 %s 执行 upsert 操作需要 update 或 create 权限", 
                        context.resourceId);
                    log.warn(logMessage);
                    throw new SecurityException("权限不足，无法执行此操作");
                }
                
                // 记录实际检查的权限类型
                if (hasUpdatePermission && hasCreatePermission) {
                    log.debug("权限校验通过：用户对资源 {} 同时拥有 update 和 create 权限，可执行 upsert 操作", 
                        context.resourceId);
                } else if (hasUpdatePermission) {
                    log.debug("权限校验通过：用户对资源 {} 拥有 update 权限，可执行 upsert 操作", 
                        context.resourceId);
                } else {
                    log.debug("权限校验通过：用户对资源 {} 拥有 create 权限，可执行 upsert 操作", 
                        context.resourceId);
                }
                return;
            }
        }
        
        // 常规权限检查
        if (!permissionManager.hasOperationPermission(context.resourceId, context.operation)) {
            String logMessage = String.format("权限拒绝：用户对资源 %s 执行 %s 操作权限不足", 
                context.resourceId, context.operation);
            log.warn(logMessage);
            throw new SecurityException("权限不足，无法执行此操作");
        }
    }

    /**
     * 判断当前操作是否为upsert操作
     * <p>
     * 这个方法通过检查线程调用栈来判断当前是否在执行upsert方法。
     * 这是必要的，因为upsert方法最终也会被标记为"update"操作类型，
     * 但它的权限检查逻辑与普通的update操作不同。
     * </p>
     *
     * @param context 权限上下文
     * @return 是否为upsert操作
     */
    private boolean isUpsertOperation(@NotNull PermissionContext context) {
        // 通过调用栈判断是否为upsert操作
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stackTrace) {
            if (element.getMethodName().equals("upsert") && 
                element.getClassName().contains("MongoTemplate")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文档权限
     * <p>
     * 对于upsert操作，会同时检查update和create权限的文档规则，
     * 只要满足其中一种权限的文档规则即可通过验证。
     * </p>
     *
     * @param context 权限上下文
     * @param entity  实体对象
     */
    private void checkDocumentPermission(@NotNull PermissionContext context, Object entity) {
        Document document = convertToDocument(entity);
        
        // 特殊处理upsert操作的文档权限
        if ("update".equals(context.operation) && isUpsertOperation(context)) {
            // upsert操作：检查用户是否有update或create权限的文档规则
            boolean hasUpdatePermission = permissionManager.hasOperationPermission(context.resourceId, "update");
            boolean hasCreatePermission = permissionManager.hasOperationPermission(context.resourceId, "create");
            
            boolean passUpdateCheck = false;
            boolean passCreateCheck = false;
            
            // 如果有update权限，检查update的文档规则
            if (hasUpdatePermission) {
                passUpdateCheck = permissionManager.checkDocument(context.resourceId, "update", document);
            }
            
            // 如果有create权限，检查create的文档规则
            if (hasCreatePermission) {
                passCreateCheck = permissionManager.checkDocument(context.resourceId, "create", document);
            }
            
            // 只要通过其中一种权限的文档检查即可
            if (!passUpdateCheck && !passCreateCheck) {
                String logMessage = String.format("权限拒绝：upsert操作文档权限校验失败，资源：%s，文档既不满足update规则也不满足create规则", 
                    context.resourceId);
                log.warn(logMessage);
                throw new SecurityException("权限不足，无法执行此操作");
            }
            
            // 记录通过的权限类型
            if (passUpdateCheck && passCreateCheck) {
                log.debug("权限校验通过：文档同时满足 update 和 create 规则，可执行 upsert 操作");
            } else if (passUpdateCheck) {
                log.debug("权限校验通过：文档满足 update 规则，可执行 upsert 操作");
            } else {
                log.debug("权限校验通过：文档满足 create 规则，可执行 upsert 操作");
            }
            
            return;
        }
        
        // 常规文档权限检查
        if (!permissionManager.checkDocument(context.resourceId, context.operation, document)) {
            String logMessage = String.format("权限拒绝：文档权限校验失败，资源：%s，操作：%s", 
                context.resourceId, context.operation);
            log.warn(logMessage);
            throw new SecurityException("权限不足，无法执行此操作");
        }
    }

    /**
     * 通过执行查询检查文档权限
     *
     * @param query   查询对象
     * @param context 权限上下文
     */
    private void checkDocumentPermissionByQuery(@NotNull Query query, @NotNull PermissionContext context) {
        try {
            // 设置标记避免递归拦截
            inPermissionAggregation.set(true);
            try {
                // 执行查询获取文档进行权限校验
                List<Document> docs = mongoTemplate.find(query, Document.class, context.collectionName);
                
                // 检查每个文档的权限
                for (Document doc : docs) {
                    if (!permissionManager.checkDocument(context.resourceId, context.operation, doc)) {
                        String logMessage = String.format("权限拒绝：查询文档权限校验失败，资源：%s，操作：%s",
                            context.resourceId, context.operation);
                        log.warn(logMessage);
                        throw new SecurityException("权限不足，无法执行此操作");
                    }
                }
            } finally {
                inPermissionAggregation.remove();
            }
        } catch (SecurityException e) {
            // 重新抛出安全异常
            throw e;
        } catch (Exception e) {
            log.error("文档权限校验过程中发生错误，资源：{}", context.resourceId, e);
            throw new RuntimeException("系统内部错误，请稍后重试");
        }
    }

    /**
     * 处理Query方法
     *
     * @param joinPoint       切点对象，包含被拦截的方法信息
     * @param queryParamIndex 查询参数索引
     * @return 处理后的查询结果
     * @throws Throwable 执行过程中可能抛出的异常
     */
    private Object processQueryMethod(@NotNull ProceedingJoinPoint joinPoint, int queryParamIndex) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Query query = (Query) args[queryParamIndex];

        // 根据方法名动态确定操作类型
        String operation = determineOperationTypeFromMethod(joinPoint);
        PermissionContext context = createPermissionContext(joinPoint, operation);
        
        // 检查是否应该跳过权限验证
        if (shouldSkipPermissionCheck(context)) {
            return joinPoint.proceed(args);
        }
        
        checkOperationPermission(context);

        // 检查是否需要使用聚合查询
        boolean needsAggregation = applyPermissionFilter(query, context);

        if (needsAggregation) {
            // 需要使用聚合查询，重新分析权限规则获取聚合条件
            PermissionFilterResult filterResult = analyzePermissionFilter(context);
            if (filterResult instanceof PermissionFilterResult.AggregationFilter aggregationFilter) {
                checkDocumentPermissionWithAggregationQuery(query, context, aggregationFilter.aggregationCriteria());
            }
        } else {
            // 不需要聚合查询，先执行查询进行文档权限校验
            checkDocumentPermissionByQuery(query, context);
        }

        // 权限校验通过，执行原始方法
        return joinPoint.proceed(args);
    }

    /**
     * 根据方法名确定操作类型
     * <p>
     * 这个方法解决了之前的安全漏洞：所有Query相关方法都被当作read操作，
     * 但实际上update、upsert等方法应该检查相应的写权限。
     * </p>
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 操作类型：read、create、update、delete
     */
    /**
     * 根据方法名确定操作类型
     * <p>
     * 基于Spring Data MongoDB MongoTemplate的完整方法列表进行精确匹配，
     * 确保不多拦截也不少拦截，准确识别出具体的操作类型。
     * </p>
     *
     * @param joinPoint 切点对象，包含被拦截的方法信息
     * @return 操作类型：read、create、update、delete
     */
    @NotNull
    private String determineOperationTypeFromMethod(@NotNull ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        
        // =============================================
        // CREATE 操作类型 - 插入和保存新实体
        // =============================================
        if (methodName.equals("insert") ||
            methodName.equals("insertAll") ||
            methodName.equals("doInsert") ||
            methodName.equals("doInsertAll") ||
            methodName.equals("doInsertBatch") ||
            methodName.equals("insertDocument") ||
            methodName.equals("insertDocumentList")) {
            return "create";
        }
        
        // Save 操作比较特殊：既可能是insert也可能是update
        // 我们将其归类为create，因为它主要用于保存新实体
        if (methodName.equals("save") ||
            methodName.equals("doSave") ||
            methodName.equals("saveDocument")) {
            return "create";
        }
        
        // =============================================
        // UPDATE 操作类型 - 修改现有文档
        // =============================================
        if (methodName.equals("updateFirst") ||
            methodName.equals("updateMulti") ||
            methodName.equals("doUpdate") ||
            methodName.equals("findAndModify") ||
            methodName.equals("doFindAndModify") ||
            methodName.equals("findAndReplace") ||
            methodName.equals("doFindAndReplace") ||
            methodName.equals("replace") ||
            methodName.equals("replaceFirst")) {
            return "update";
        }
        
        // Upsert 操作 - 既可能是创建也可能是更新
        // 我们将其归类为update，在权限检查中会特殊处理
        if (methodName.equals("upsert")) {
            return "update"; // 特殊处理：会检查update和create权限
        }
        
        // =============================================
        // DELETE 操作类型 - 删除文档
        // =============================================
        if (methodName.equals("remove") ||
            methodName.equals("doRemove") ||
            methodName.equals("findAndRemove") ||
            methodName.equals("doFindAndRemove") ||
            methodName.equals("findAllAndRemove") ||
            methodName.equals("doFindAndDelete")) {
            return "delete";
        }
        
        // =============================================
        // READ 操作类型 - 查询和读取文档
        // =============================================
        // 以下方法明确为读操作
        if (methodName.equals("find") ||
            methodName.equals("findOne") ||
            methodName.equals("findById") ||
            methodName.equals("findAll") ||
            methodName.equals("findDistinct") ||
            methodName.equals("doFind") ||
            methodName.equals("doFindOne") ||
            methodName.equals("exists") ||
            methodName.equals("count") ||
            methodName.equals("exactCount") ||
            methodName.equals("estimatedCount") ||
            methodName.equals("doCount") ||
            methodName.equals("doExactCount") ||
            methodName.equals("doEstimatedCount") ||
            methodName.equals("countCanBeEstimated") ||
            methodName.equals("geoNear") ||
            methodName.equals("stream") ||
            methodName.equals("doStream") ||
            methodName.equals("scroll") ||
            methodName.equals("aggregate") ||
            methodName.equals("aggregateStream") ||
            methodName.equals("doAggregate") ||
            methodName.equals("mapReduce") ||
            methodName.equals("executeQuery") ||
            methodName.equals("execute") ||
            methodName.equals("executeCommand")) {
            return "read";
        }
        
        // 集合和索引管理方法（通常不涉及业务数据权限，但归类为读操作以保证安全）
        if (methodName.equals("getCollection") ||
            methodName.equals("getCollectionName") ||
            methodName.equals("getCollectionNames") ||
            methodName.equals("collectionExists") ||
            methodName.equals("createCollection") ||
            methodName.equals("doCreateCollection") ||
            methodName.equals("dropCollection") ||
            methodName.equals("createView") ||
            methodName.equals("doCreateView") ||
            methodName.equals("indexOps") ||
            methodName.equals("searchIndexOps") ||
            methodName.equals("bulkOps") ||
            methodName.equals("scriptOps")) {
            return "read";
        }
        
        // 内部工具方法（通常是protected方法，归类为读操作）
        if (methodName.equals("prepareCollection") ||
            methodName.equals("prepareDatabase") ||
            methodName.equals("prepareWriteConcern") ||
            methodName.equals("convertToDocument") ||
            methodName.equals("ensureNotCollectionLike") ||
            methodName.equals("populateIdIfNecessary") ||
            methodName.equals("maybeCallBeforeConvert") ||
            methodName.equals("maybeCallAfterConvert") ||
            methodName.equals("maybeCallBeforeSave") ||
            methodName.equals("maybeCallAfterSave") ||
            methodName.equals("maybeEmitEvent") ||
            methodName.equals("withSession") ||
            methodName.equals("getConverter") ||
            methodName.equals("getDb") ||
            methodName.equals("getExceptionTranslator") ||
            methodName.equals("getMongoDatabaseFactory") ||
            methodName.equals("getReadPreference") ||
            methodName.equals("hasReadPreference")) {
            return "read";
        }
        
        // 默认情况：所有未明确分类的方法都视为读操作（保守安全策略）
        log.debug("未识别的方法：{}，默认归类为read操作", methodName);
        return "read";
    }

    // ████████████████████████████████████████████████████████████████████████████████████████
    // 📊 聚合处理 - 使用聚合查询检查文档许可
    // ████████████████████████████████████████████████████████████████████████████████████████

    /**
     * 使用聚合查询检查文档许可
     *
     * @param originalQuery       原始查询对象
     * @param context             权限上下文
     * @param aggregationCriteria 聚合权限条件
     */
    private void checkDocumentPermissionWithAggregationQuery(@NotNull Query originalQuery,
                                                             @NotNull PermissionContext context,
                                                             @NotNull Document aggregationCriteria) {
        // 1. 获取用户有权限操作的数据ID范围
        Set<Object> allowedIds = getPermittedDataIds(originalQuery, context, aggregationCriteria);

        // 2. 验证原始操作的目标数据是否都在权限范围内
        validateOperationPermission(originalQuery, context, allowedIds);
    }

    /**
     * 获取用户有权限操作的数据ID集合
     *
     * @param originalQuery      原始查询
     * @param context            权限上下文
     * @param aggregationCriteria 聚合权限条件
     * @return 允许操作的数据ID集合
     */
    @NotNull
    private Set<Object> getPermittedDataIds(@NotNull Query originalQuery,
                                           @NotNull PermissionContext context,
                                           @NotNull Document aggregationCriteria) {
        // 构建聚合管道
        List<AggregationOperation> operations = new ArrayList<>();

        // 1. 添加原始查询条件（映射后的）
        Document originalQueryDoc = originalQuery.getQueryObject();
        if (!originalQueryDoc.isEmpty()) {
            Document mappedQueryDoc = getMappedQueryDocument(originalQueryDoc, context.collectionName);
            operations.add(Aggregation.match(createCriteriaFromDocument(mappedQueryDoc)));
        }

        // 2. 添加权限过滤条件
        addPermissionFilterToOperations(operations, aggregationCriteria);

        // 3. 只投影ID字段
        operations.add(Aggregation.project("_id"));

        // 4. 执行聚合查询
        Aggregation aggregation = buildMappedAggregation(operations);
        List<Document> idDocs = mongoTemplate.aggregate(aggregation, context.collectionName, Document.class).getMappedResults();

        // 5. 提取ID集合
        return idDocs.stream()
                .map(doc -> doc.get("_id"))
                .collect(Collectors.toSet());
    }

    /**
     * 验证操作权限：检查原始操作要处理的数据是否都在权限范围内
     *
     * @param originalQuery 原始查询
     * @param context       权限上下文
     * @param allowedIds    允许操作的ID集合
     */
    private void validateOperationPermission(@NotNull Query originalQuery,
                                           @NotNull PermissionContext context,
                                           @NotNull Set<Object> allowedIds) {
        // 获取原始查询实际要操作的数据ID
        Set<Object> targetIds = getTargetDataIds(originalQuery, context);

        // 检查是否所有目标数据都在权限范围内
        for (Object targetId : targetIds) {
            if (!allowedIds.contains(targetId)) {
                String logMessage = String.format("权限拒绝：无权限操作数据 [%s]，资源：%s，操作：%s",
                    targetId, context.resourceId, context.operation);
                log.warn(logMessage);
                throw new SecurityException("权限不足，无法执行此操作");
            }
        }

        log.debug("权限校验通过：用户有权限对 {} 个数据执行 {} 操作", targetIds.size(), context.operation);
    }

    /**
     * 获取原始查询要操作的目标数据ID集合
     *
     * @param originalQuery 原始查询
     * @param context       权限上下文
     * @return 目标数据ID集合
     */
    @NotNull
    private Set<Object> getTargetDataIds(@NotNull Query originalQuery, @NotNull PermissionContext context) {
        try {
            // 执行查询获取目标数据的ID
            Query idQuery = new Query();
            idQuery.addCriteria(originalQuery.getQueryObject().isEmpty() ?
                new Criteria() :
                createCriteriaFromDocument(getMappedQueryDocument(originalQuery.getQueryObject(), context.collectionName)));

            // 只查询ID字段
            idQuery.fields().include("_id");

            // 设置标记避免递归拦截
            inPermissionAggregation.set(true);
            try {
                List<Document> docs = mongoTemplate.find(idQuery, Document.class, context.collectionName);
                return docs.stream()
                        .map(doc -> doc.get("_id"))
                        .collect(Collectors.toSet());
            } finally {
                inPermissionAggregation.remove();
            }
        } catch (Exception e) {
            log.error("获取目标数据ID时发生错误，资源：{}", context.resourceId, e);
            throw new RuntimeException("系统内部错误，请稍后重试");
        }
    }

    /**
     * 从Document创建Criteria
     *
     * @param document 文档对象
     * @return Criteria对象
     */
    @NotNull
    private Criteria createCriteriaFromDocument(@NotNull Document document) {
        if (document.isEmpty()) {
            return new Criteria();
        }

        // 对于复杂的聚合表达式，我们需要使用特殊方式处理
        return new Criteria() {
            @NotNull
            @Override
            public Document getCriteriaObject() {
                return document;
            }
        };
    }

    /**
     * 从Document创建AggregationOperation
     *
     * @param stageDocument 聚合阶段文档
     * @return AggregationOperation对象
     */
    @NotNull
    private AggregationOperation createAggregationOperationFromDocument(@NotNull Document stageDocument) {
        // 创建一个原生的聚合操作，直接使用传入的Document
        return context -> stageDocument;
    }

    /**
     * 将权限过滤条件添加到聚合操作列表中
     *
     * @param operations 聚合操作列表
     * @param permissionCriteria 权限过滤条件
     */
    private void addPermissionFilterToOperations(@NotNull List<AggregationOperation> operations,
                                                @NotNull Document permissionCriteria) {
        if (permissionCriteria.containsKey("$pipeline")) {
            // 如果是完整的聚合管道
            Object pipelineObj = permissionCriteria.get("$pipeline");
            if (pipelineObj instanceof List<?> pipeline) {
                // 添加权限管道的各个阶段
                for (Object stage : pipeline) {
                    if (stage instanceof Document stageDoc) {
                        operations.add(createAggregationOperationFromDocument(stageDoc));
                    }
                }
            }
        } else {
            // 单个条件，作为match阶段
            operations.add(Aggregation.match(createCriteriaFromDocument(permissionCriteria)));
        }
    }

    /**
     * 分析权限规则并返回过滤结果
     *
     * @param context 权限上下文
     * @return 权限过滤结果
     */
    @NotNull
    private PermissionFilterResult analyzePermissionFilter(@NotNull PermissionContext context) {
        Object permissionFilter = permissionManager.getDataFilter(context.resourceId, context.operation);

        if (permissionFilter == null) {
            return new PermissionFilterResult.NoFilter();
        }

        // 处理不同类型的权限规则
        if (permissionFilter instanceof CriteriaDefinition permissionCriteria) {
            // 普通查询条件
            Document criteriaDocument = permissionCriteria.getCriteriaObject();

            if (criteriaDocument.isEmpty()) {
                return new PermissionFilterResult.NoFilter();
            }

            // 检查是否包含聚合操作符（如 $lookup, $expr 等）
            if (containsAggregationOperators(criteriaDocument)) {
                // 对于包含聚合操作符的规则，返回聚合过滤结果
                return new PermissionFilterResult.AggregationFilter(criteriaDocument);
            }

            return new PermissionFilterResult.SimpleFilter(permissionCriteria);

        } else if (permissionFilter instanceof List<?> pipeline) {
            // 聚合管道规则
            return new PermissionFilterResult.AggregationFilter(new Document("$pipeline", pipeline));
        }

        return new PermissionFilterResult.NoFilter();
    }

    /**
     * 应用权限过滤条件到Query
     *
     * @param query   查询对象
     * @param context 权限上下文
     * @return 是否需要使用聚合查询
     */
    private boolean applyPermissionFilter(@NotNull Query query, @NotNull PermissionContext context) {
        PermissionFilterResult filterResult = analyzePermissionFilter(context);

        if (filterResult instanceof PermissionFilterResult.NoFilter) {
            return false;

        } else if (filterResult instanceof PermissionFilterResult.SimpleFilter simpleFilter) {
            CriteriaDefinition permissionCriteria = simpleFilter.criteria();

            // 处理普通的权限条件
            Document root = query.getQueryObject();
            boolean hasRootOp = root.containsKey("$or") || root.containsKey("$and");

            if (!hasRootOp) {
                // 根级尚无逻辑运算符，直接 AND 进去即可
                query.addCriteria(permissionCriteria);
            } else {
                // 有冲突：把旧条件与权限条件包装成一个新的 $and
                // 复制旧查询
                Document original = new Document(root);
                // 权限
                Document permDoc = new Document(permissionCriteria.getCriteriaObject());

                List<Document> andList = List.of(original, permDoc);

                // 清空再写入新的 $and
                root.clear();
                root.put("$and", andList);
            }
            return false;

        } else if (filterResult instanceof PermissionFilterResult.AggregationFilter) {
            // 需要使用聚合查询
            return true;

        } else {
            // 默认情况，不应该发生
            log.warn("未知的PermissionFilterResult类型：{}", filterResult.getClass());
            return false;
        }
    }

    /**
     * 检查条件文档是否包含聚合操作符
     *
     * @param document 条件文档
     * @return 是否包含聚合操作符
     */
    private boolean containsAggregationOperators(@NotNull Document document) {
        return containsAggregationOperatorsRecursive(document);
    }

    /**
     * 递归检查文档是否包含聚合操作符
     *
     * @param obj 要检查的对象
     * @return 是否包含聚合操作符
     */
    private boolean containsAggregationOperatorsRecursive(Object obj) {
        if (obj instanceof Document document) {
            for (String key : document.keySet()) {
                // 检查已知的聚合操作符
                if (isAggregationOperator(key)) {
                    return true;
                }
                // 递归检查嵌套值
                if (containsAggregationOperatorsRecursive(document.get(key))) {
                    return true;
                }
            }
        } else if (obj instanceof List<?> list) {
            for (Object item : list) {
                if (containsAggregationOperatorsRecursive(item)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断操作符是否为聚合操作符
     *
     * @param operator 操作符
     * @return 是否为聚合操作符
     */
    private boolean isAggregationOperator(@NotNull String operator) {
        return operator.equals("$lookup") ||
               operator.equals("$expr") ||
               operator.equals("$let") ||
               operator.equals("$arrayElemAt") ||
               operator.equals("$facet") ||
               operator.equals("$graphLookup") ||
               operator.equals("$unwind") ||
               operator.equals("$group") ||
               operator.equals("$project") ||
               operator.equals("$addFields") ||
               operator.equals("$replaceRoot") ||
               operator.equals("$redact") ||
               operator.equals("$sort") ||
               operator.equals("$limit") ||
               operator.equals("$skip") ||
               operator.equals("$sample") ||
               operator.equals("$bucket") ||
               operator.equals("$bucketAuto") ||
               operator.equals("$sortByCount") ||
               operator.equals("$collStats") ||
               operator.equals("$indexStats") ||
               operator.equals("$out") ||
               operator.equals("$merge");
    }

    /**
     * 应用聚合过滤条件
     *
     * @param aggregation 聚合对象
     * @param context     权限上下文
     * @return 应用过滤条件后的聚合对象，如果需要特殊处理则返回null
     */
    @Nullable
    private Aggregation applyAggregationFilter(@NotNull Aggregation aggregation, @NotNull PermissionContext context) {
        PermissionFilterResult filterResult = analyzePermissionFilter(context);

        if (filterResult instanceof PermissionFilterResult.NoFilter) {
            return aggregation;

        } else if (filterResult instanceof PermissionFilterResult.SimpleFilter simpleFilter) {
            CriteriaDefinition criteria = simpleFilter.criteria();
            AggregationOperation matchOperation = Aggregation.match(criteria);

            // 创建新的聚合管道，将权限过滤条件添加到原始管道之前
            List<AggregationOperation> operations = new ArrayList<>();
            operations.add(matchOperation);
            operations.addAll(aggregation.getPipeline().getOperations());

            return buildMappedAggregation(operations);

        } else if (filterResult instanceof PermissionFilterResult.AggregationFilter) {
            // 需要特殊处理的聚合规则，返回null表示需要调用方进行合并处理
            return null;

        } else {
            // 默认情况，不应该发生
            log.warn("未知的PermissionFilterResult类型：{}", filterResult.getClass());
            return aggregation;
        }
    }

    /**
     * 将权限过滤条件合并到聚合管道中
     *
     * @param originalAggregation 原始聚合对象
     * @param permissionCriteria  权限过滤条件
     * @return 合并后的聚合对象
     */
    @NotNull
    private Aggregation mergeAggregationWithPermissionFilter(@NotNull Aggregation originalAggregation,
                                                             @NotNull Document permissionCriteria) {
        try {
            // 创建新的聚合管道
            List<AggregationOperation> operations = new ArrayList<>();

            // 1. 添加权限过滤条件
            addPermissionFilterToOperations(operations, permissionCriteria);

            // 2. 添加原始聚合管道的所有操作
            operations.addAll(originalAggregation.getPipeline().getOperations());

            return buildMappedAggregation(operations);

        } catch (Exception e) {
            log.error("合并聚合查询与权限过滤条件时发生错误", e);
            // 出错时，只返回一个限制性的聚合（不返回任何结果）
            return Aggregation.newAggregation(
                Aggregation.match(Criteria.where("_nonExistentField").is("_restrictAll"))
            );
        }
    }

    /**
     * 处理查询操作的权限验证
     *
     * @param joinPoint      切点对象，包含被拦截的方法信息
     * @param collectionName 集合名称
     * @return 处理后的查询结果
     * @throws Throwable 执行过程中可能抛出的异常
     */
    private @Nullable Object processQueryWithPermission(@NotNull ProceedingJoinPoint joinPoint, String collectionName) throws Throwable {
        String resourceId = collectionName != null ? permissionManager.getResourceIdByCollection(collectionName) : null;

        if (resourceId != null && !permissionManager.hasOperationPermission(resourceId, "read")) {
            String logMessage = String.format("权限拒绝：用户对资源 %s 执行读取操作权限不足", resourceId);
            log.warn(logMessage);
            throw new SecurityException("权限不足，无法执行此操作");
        }

        Object result = joinPoint.proceed();

        // 如果找到了结果，验证是否有权限访问
        if (result != null && resourceId != null) {
            Document document = convertToDocument(result);
            if (!permissionManager.checkDocument(resourceId, "read", document)) {
                String logMessage = String.format("权限拒绝：文档读取权限校验失败，资源：%s", resourceId);
                log.warn(logMessage);
                throw new SecurityException("权限不足，无法执行此操作");
            }
        }

        return result;
    }

    // ===================== 辅助方法 =====================

    /**
     * 获取映射后的查询文档，自动处理字段类型转换
     *
     * @param queryDocument 原始查询文档
     * @param collectionName 集合名称
     * @return 映射后的查询文档
     */
    @NotNull
    private Document getMappedQueryDocument(@NotNull Document queryDocument, @NotNull String collectionName) {
        try {
            // 如果查询文档为空，直接返回
            if (queryDocument.isEmpty()) {
                return queryDocument;
            }

            // 获取映射转换器和映射上下文
            MappingMongoConverter mappingConverter = (MappingMongoConverter) mongoTemplate.getConverter();
            MongoMappingContext mappingContext = (MongoMappingContext) mappingConverter.getMappingContext();

            // 尝试通过集合名称获取对应的实体类
            MongoPersistentEntity<?> persistentEntity = findPersistentEntityByCollectionName(mappingContext, collectionName);

            if (persistentEntity != null) {
                // 使用QueryMapper进行字段映射和类型转换
                // 这会自动处理@MongoId(FieldType.OBJECT_ID)和@Field(targetType = OBJECT_ID)等注解
                return queryMapper.getMappedObject(queryDocument, persistentEntity);
            } else {
                // 如果找不到对应的实体类，使用Optional.empty()进行通用映射
                log.debug("未找到集合 {} 对应的持久化实体", collectionName);
                return queryMapper.getMappedObject(queryDocument, Optional.empty());
            }

        } catch (Exception e) {
            log.warn("查找集合 {} 对应的持久化实体时发生错误", collectionName, e);
            return queryDocument;
        }
    }

    /**
     * 通过集合名称查找对应的持久化实体
     *
     * @param mappingContext MongoDB映射上下文
     * @param collectionName 集合名称
     * @return 持久化实体，如果找不到则返回null
     */
    @Nullable
    private MongoPersistentEntity<?> findPersistentEntityByCollectionName(@NotNull MongoMappingContext mappingContext,
                                                                           @NotNull String collectionName) {
        try {
            // 遍历所有已知的持久化实体
            for (MongoPersistentEntity<?> entity : mappingContext.getPersistentEntities()) {
                // 检查实体对应的集合名称是否匹配
                String entityCollectionName = entity.getCollection();
                if (collectionName.equals(entityCollectionName)) {
                    return entity;
                }
            }

            log.debug("未找到集合 {} 对应的持久化实体", collectionName);
            return null;

        } catch (Exception e) {
            log.warn("查找集合 {} 对应的持久化实体时发生错误", collectionName, e);
            return null;
        }
    }

    /**
     * 判断实体是否为新实体
     *
     * @param entity 实体对象
     * @return 是否为新实体
     */
    private boolean isNewEntity(@NotNull Object entity) {
        // 查找ID字段
        Field idField = ReflectionUtils.findField(entity.getClass(), "id");
        if (idField == null) {
            idField = ReflectionUtils.findField(entity.getClass(), "_id");
        }

        if (idField != null) {
            ReflectionUtils.makeAccessible(idField);
            try {
                Object idValue = ReflectionUtils.getField(idField, entity);
                return idValue == null;
            } catch (Exception e) {
                log.error("访问ID字段时发生错误", e);
            }
        }

        // 默认假设为新实体
        return true;
    }

    /**
     * 将实体转换为Document
     * <p>
     * 这个方法的主要用途是为了权限检查，将各种类型的实体对象转换为MongoDB的Document格式。
     * 对于集合类型（Collection或数组），会取第一个元素进行转换，因为权限检查通常基于
     * 实体的结构和字段值，而不是集合本身。
     * </p>
     *
     * @param entity 实体对象，可以是单个对象、Collection或数组
     * @return 转换后的Document对象，永远不为null
     */
    @NotNull
    @Contract(value = "_ -> new", pure = true)
    private Document convertToDocument(Object entity) {
        if (entity == null) {
            return new Document();
        }

        // 如果实体已经是Document，直接返回副本以确保不可变性
        if (entity instanceof Document document) {
            return new Document(document);
        }

        try {
            // 使用mongoConverter将Java对象转换为BSON Document
            Document document = new Document();
            mongoConverter.write(entity, document);
            return document;
        } catch (ClassCastException e) {
            log.warn("使用MongoConverter转换类型 {} 时发生ClassCastException，回退到反射方式：{}",
                    entity.getClass().getSimpleName(), e.getMessage());
            return convertUsingReflection(entity);
        } catch (Exception e) {
            log.error("使用MongoConverter转换类型 {} 失败，回退到反射方式",
                    entity.getClass().getSimpleName(), e);
            throw e;
        }
    }

    /**
     * 反射降级：对用户自定义实体做"浅层次"字段拷贝，
     * 但遇到 JDK 内置类型时，直接跳过这些类的 private 字段，以免触发 InaccessibleObjectException。
     * <p>
     *
     * @param entity 实体对象
     * @return 转换后的 Document 对象
     */
    @NotNull
    private Document convertUsingReflection(@NotNull Object entity) {
        Document document = new Document();
        Class<?> currentClass = entity.getClass();

        while (currentClass != null && !currentClass.equals(Object.class)) {
            // "跳过所有 JDK 内置类"（只处理用户自定义类或第三方包里的类）
            if (currentClass.getName().startsWith("java.")) {
                break;
            }

            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields) {
                int modifiers = field.getModifiers();
                // 跳过 static 和 transient 字段
                if (Modifier.isStatic(modifiers) || Modifier.isTransient(modifiers)) {
                    continue;
                }

                field.setAccessible(true); // 这一行只针对 "自己写的实体类" 的字段
                String fieldName = field.getName();

                // 如果已经 put 过同名字段，就跳过（避免子类/父类字段重复）
                if (document.containsKey(fieldName)) {
                    continue;
                }

                try {
                    Object fieldValue = field.get(entity);
                    if (fieldValue == null) {
                        // 可以选择不放、也可以放一个 null。此处我们直接跳过 null
                        continue;
                    }

                    // 如果字段值本身是 Document，直接放入
                    if (fieldValue instanceof Document) {
                        document.put(fieldName, fieldValue);
                    }
                    // 如果字段值是集合或 Map，递归地再包装
                    else if (fieldValue instanceof Collection || fieldValue instanceof Map) {
                        document.put(fieldName, convertToDocument(fieldValue));
                    }
                    // 如果是常见"简单值类型"（基本类型、String、Number、Date、ObjectId 等），直接放
                    else if (isSimpleValueType(fieldValue.getClass())) {
                        document.put(fieldName, fieldValue);
                    }
                    // 其余情况，认为它是"用户自定义的嵌套实体"，递归调用
                    else {
                        document.put(fieldName, convertUsingReflection(fieldValue));
                    }
                } catch (IllegalAccessException ignore) {
                    // 如果访问失败，就跳过这个字段
                }
            }

            currentClass = currentClass.getSuperclass();
        }

        return document;
    }

    /**
     * 判断某个类型是否属于"简单值类型"：
     * 基本类型、字符序列、数字类型、日期、Boolean、ObjectId 等。
     */
    private boolean isSimpleValueType(@NotNull Class<?> clazz) {
        return clazz.isPrimitive()
            || CharSequence.class.isAssignableFrom(clazz)
            || Number.class.isAssignableFrom(clazz)
            || Date.class.isAssignableFrom(clazz)
            || Boolean.class.isAssignableFrom(clazz)
            || ObjectId.class.isAssignableFrom(clazz);
    }

    /**
     * Wrap an {@link AggregationOperation} so that **all** pipeline stages it emits are first
     * converted via {@link QueryMapper}. This uses the non‑deprecated {@link AggregationOperation#toPipelineStages}
     * method introduced in recent Spring‑Data‑MongoDB versions; the deprecated <code>toDocument</code>
     * is still overridden for backward compatibility.
     */
    @NotNull
    @Contract(pure = true)
    private AggregationOperation wrapOperationWithMapping(AggregationOperation original) {
        return new AggregationOperation() {
            @NotNull
            @Override
            public List<Document> toPipelineStages(@NotNull AggregationOperationContext ctx) {
                return original.toPipelineStages(ctx)
                    .stream()
                    .map(d -> queryMapper.getMappedObject(d, Optional.empty()))
                    .collect(Collectors.toList());
            }

            /**
             * @deprecated kept only for frameworks that still fall back to <code>toDocument</code>
             */
            @NotNull
            @Override
            @Deprecated
            public Document toDocument(@NotNull AggregationOperationContext ctx) {
                return queryMapper.getMappedObject(original.toDocument(ctx), Optional.empty());
            }
        };
    }

    /**
     * Build an Aggregation while ensuring every stage is mapped through {@link QueryMapper}.
     */
    @NotNull
    private Aggregation buildMappedAggregation(@NotNull java.util.List<AggregationOperation> operations) {
        java.util.List<AggregationOperation> mappedOps = operations.stream()
            .map(this::wrapOperationWithMapping)
            .collect(java.util.stream.Collectors.toList());
        return Aggregation.newAggregation(mappedOps);
    }
}
    
