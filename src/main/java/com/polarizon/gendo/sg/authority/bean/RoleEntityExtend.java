package com.polarizon.gendo.sg.authority.bean;

import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色实体扩展类
 */
@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("角色实体扩展类")
public class RoleEntityExtend extends RoleEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    public RoleEntityExtend(String tenantId, OrganizationEntity organization, String name, String abbreviation, Boolean isSystem, Boolean enable) {
        super(tenantId, organization, name, abbreviation, isSystem, enable);
    }

    /**
     * 角色常量类
     */
    public interface Constants extends RoleEntity.Constants, AbstractBaseBO.Constants {
    }
} 