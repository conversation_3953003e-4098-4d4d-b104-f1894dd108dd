package com.polarizon.gendo.sg.authority.core;

import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import com.polarizon.gendo.sg.authority.model.Resource;
import com.polarizon.gendo.sg.authority.model.Role;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class PermissionManager {

    private final RuleLoader ruleLoader;
    private final DataRuleEvaluator ruleEvaluator;

    /**
     * 集合到资源ID的映射
     * <p>
     * key: 集合名称
     * value: 资源ID
     */
    private final Map<String, String> collectionToResourceCache = new ConcurrentHashMap<>();

    public PermissionManager(RuleLoader ruleLoader, DataRuleEvaluator ruleEvaluator) {
        this.ruleLoader = ruleLoader;
        this.ruleEvaluator = ruleEvaluator;
        initCollectionMapping();
    }

    private void initCollectionMapping() {
        AuthorityConfig config = ruleLoader.getAuthorityConfig();
        for (Map.Entry<String, Resource> entry : config.getResources().entrySet()) {
            Resource resource = entry.getValue();
            if (resource.getCollection() != null) {
                collectionToResourceCache.put(resource.getCollection(), entry.getKey());
            }
        }
    }

    /**
     * 检查集合是否在权限管理白名单中
     * 白名单中的集合无需进行权限检查，可以直接访问
     * 
     * @param collectionName 集合名称
     * @return true表示在白名单中，false表示不在白名单中
     */
    public boolean isWhitelistedCollection(String collectionName) {
        if (collectionName == null) {
            return false;
        }
        
        // 系统内部集合，通常以"system."开头，无需权限检查
        if (collectionName.startsWith("system.")) {
            return true;
        }
        
        // Spring Session 相关集合，无需权限检查
        if (collectionName.startsWith("sessions") || collectionName.equals("spring_session")) {
            return true;
        }
        
        // MongoDB内部集合，无需权限检查
        if (collectionName.startsWith("__") || collectionName.endsWith("__")) {
            return true;
        }
        
        // 日志相关集合，无需权限检查
        if (collectionName.startsWith("log_") || collectionName.endsWith("_log")) {
            return true;
        }
        
        // 缓存相关集合，无需权限检查
        if (collectionName.startsWith("cache_") || collectionName.endsWith("_cache")) {
            return true;
        }
        
        // 临时集合，无需权限检查
        if (collectionName.startsWith("temp_") || collectionName.endsWith("_temp")) {
            return true;
        }
        
        // 其他集合需要进行权限检查
        return false;
    }

    public boolean hasOperationPermission(String resourceId, String operation) {
        UserContext userContext = ContextHolder.getCurrentUser();
        if (userContext == null) {
            // log.info("No user context found. 认为是内部调用。");
            return true;
        }

        AuthorityConfig config = ruleLoader.getAuthorityConfig();
        for (RoleEntity roleEntity : userContext.getRoleEntities()) {
            Role role = config.getRoles().get(roleEntity.getAbbreviation());
            if (role != null && role.hasPermission(resourceId, operation)) {
                return true;
            }
        }

        return false;
    }

    public String getResourceIdByCollection(String collectionName) {
        return collectionToResourceCache.get(collectionName);
    }

    /**
     * 获取数据过滤规则
     * 
     * @param resourceId 资源ID
     * @param operation 操作类型
     * @return CriteriaDefinition（简单查询条件）或 List&lt;Document&gt;（聚合管道）
     */
    public Object getDataFilter(String resourceId, String operation) {
        UserContext userContext = ContextHolder.getCurrentUser();
        if (userContext == null) {
            // log.info("No user context found. Returning unrestricted criteria.");
            return new Criteria();
        }

        AuthorityConfig config = ruleLoader.getAuthorityConfig();
        Resource resource = config.getResources().get(resourceId);
        if (resource == null) {
            log.warn("未找到资源：{}", resourceId);
            return new Criteria();
        }

        for (RoleEntity roleEntity : userContext.getRoleEntities()) {
            Object rule = resource.getDataRule(operation, roleEntity.getAbbreviation());
            if (rule != null) {
                return ruleEvaluator.evaluateRule(rule);
            }
        }

        Object defaultRule = resource.getDataRule(operation, "default");
        return ruleEvaluator.evaluateRule(defaultRule);
    }

    /**
     * 获取数据过滤条件（仅返回CriteriaDefinition，兼容旧接口）
     * 
     * @param resourceId 资源ID
     * @param operation 操作类型
     * @return CriteriaDefinition对象，如果是聚合管道则返回空条件
     * @deprecated 建议使用 getDataFilter 方法
     */
    @Deprecated
    public CriteriaDefinition getDataFilterCriteria(String resourceId, String operation) {
        Object filter = getDataFilter(resourceId, operation);
        if (filter instanceof CriteriaDefinition criteriaDefinition) {
            return criteriaDefinition;
        }
        // 如果是聚合管道，返回空条件（表示需要使用聚合查询）
        return new Criteria();
    }

    public boolean checkDocument(String resourceId, String operation, Document document) {
        Object filter = getDataFilter(resourceId, operation);

        // 处理不同类型的过滤规则
        if (filter instanceof CriteriaDefinition criteria) {
            // 如果没有条件限制，则允许访问
            if (criteria.getCriteriaObject().isEmpty()) {
                return true;
            }

            try {
                // 获取Criteria对应的Document
                Document criteriaDocument = criteria.getCriteriaObject();
                // 处理特殊格式 - 为了保持代码一致性，在此处一次性处理问题，而不是在多处解析JSON
                Document fixedCriteria = handleSpecialCriteriaFormat(criteriaDocument);

                // 如果条件为空，允许访问
                if (fixedCriteria.isEmpty()) {
                    return true;
                }

                // 匹配文档
                return matchDocument(document, fixedCriteria);
            } catch (Exception e) {
                log.error("检查文档权限时发生错误：{}", e.getMessage(), e);
                return false;
            }
        } else if (filter instanceof List<?>) {
            // 对于聚合管道规则，文档级权限检查比较复杂，需要实际执行聚合查询
            // 这里我们暂时返回true，让实际的查询操作来处理权限过滤
            log.debug("聚合管道规则的文档检查 - 委托给查询级别处理");
            return true;
        }

        // 未知类型，默认允许
        return true;
    }

    /**
     * 处理特殊格式的查询条件
     *
     * @param criteriaDocument 原始条件文档
     * @return 处理后的条件文档
     */
    private Document handleSpecialCriteriaFormat(Document criteriaDocument) {
        // 如果不是特殊格式，直接返回
        if (criteriaDocument == null || criteriaDocument.size() != 1) {
            return criteriaDocument;
        }

        String firstKey = criteriaDocument.keySet().iterator().next();
        // 如果键不是特殊格式，直接返回
        if (!firstKey.startsWith("{") || !firstKey.endsWith("}")) {
            return criteriaDocument;
        }

        // 这是唯一需要的JSON解析操作，用于处理Spring Data MongoDB生成的特殊查询格式
        try {
            Document actualQuery = Document.parse(firstKey);

            // 检查解析后的Document是否有效
            if (actualQuery.isEmpty()) {
                log.warn("解析后的文档为空，使用原始文档");
                return criteriaDocument;
            }

            // 确保解析结果包含了预期的操作符或字段
            boolean isValidQuery = false;
            for (String key : actualQuery.keySet()) {
                if (key.startsWith("$") || actualQuery.get(key) instanceof Document || actualQuery.get(key) instanceof List) {
                    isValidQuery = true;
                    break;
                }
            }

            if (!isValidQuery) {
                log.warn("解析后的文档不包含有效的查询操作符，使用原始文档");
                return criteriaDocument;
            }

            return actualQuery;
        } catch (Exception e) {
            log.warn("解析特殊格式条件失败", e);
            return criteriaDocument;
        }
    }

    /**
     * 检查文档是否匹配条件文档
     *
     * @param document 要检查的文档
     * @param criteria 条件文档
     * @return 是否匹配
     */
    private boolean matchDocument(Document document, @NotNull Document criteria) {
        for (String key : criteria.keySet()) {
            if (key.startsWith("$")) {
                // 处理顶级操作符
                if (!matchTopLevelOperator(document, key, criteria.get(key))) {
                    return false;
                }
            } else {
                // 处理字段匹配
                if (!document.containsKey(key)) {
                    return false;
                }

                Object documentValue = document.get(key);
                Object criteriaValue = criteria.get(key);

                if (criteriaValue instanceof Document nestedCriteria) {
                    // 处理嵌套操作符
                    for (String nestedKey : nestedCriteria.keySet()) {
                        if (!matchOperator(documentValue, nestedKey, nestedCriteria.get(nestedKey))) {
                            return false;
                        }
                    }
                } else if (!documentValue.equals(criteriaValue)) {
                    // 简单值匹配
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 匹配顶级操作符（$and, $or等）
     *
     * @param document 要检查的文档
     * @param operator 操作符
     * @param value    操作符值
     * @return 是否匹配
     */
    private boolean matchTopLevelOperator(Document document, String operator, Object value) {
        if (!(value instanceof List<?> conditions)) {
            return false;
        }

        return switch (operator) {
            case "$and" -> {
                for (Object condition : conditions) {
                    if (!(condition instanceof Document) || !matchDocument(document, (Document) condition)) {
                        yield false;
                    }
                }
                yield true;
            }
            case "$or" -> {
                for (Object condition : conditions) {
                    if (condition instanceof Document && matchDocument(document, (Document) condition)) {
                        yield true;
                    }
                }
                yield false;
            }
            default -> {
                log.warn("不支持的顶级操作符：{}", operator);
                yield false;
            }
        };
    }

    /**
     * 匹配字段操作符（$eq, $ne, $gt等）
     *
     * @param fieldValue   字段值
     * @param operator     操作符
     * @param operatorValue 操作符值
     * @return 是否匹配
     */
    private boolean matchOperator(Object fieldValue, String operator, Object operatorValue) {
        if (fieldValue == null) {
            return false;
        }

        return switch (operator) {
            case "$eq" -> fieldValue.equals(operatorValue);
            case "$ne" -> !fieldValue.equals(operatorValue);
            case "$gt" -> {
                if (fieldValue instanceof Comparable && operatorValue instanceof Comparable) {
                    yield ((Comparable) fieldValue).compareTo(operatorValue) > 0;
                }
                yield false;
            }
            case "$gte" -> {
                if (fieldValue instanceof Comparable && operatorValue instanceof Comparable) {
                    yield ((Comparable) fieldValue).compareTo(operatorValue) >= 0;
                }
                yield false;
            }
            case "$lt" -> {
                if (fieldValue instanceof Comparable && operatorValue instanceof Comparable) {
                    yield ((Comparable) fieldValue).compareTo(operatorValue) < 0;
                }
                yield false;
            }
            case "$lte" -> {
                if (fieldValue instanceof Comparable && operatorValue instanceof Comparable) {
                    yield ((Comparable) fieldValue).compareTo(operatorValue) <= 0;
                }
                yield false;
            }
            case "$in" -> {
                if (operatorValue instanceof List) {
                    yield ((List<?>) operatorValue).contains(fieldValue);
                }
                yield false;
            }
            case "$nin" -> {
                if (operatorValue instanceof List) {
                    yield !((List<?>) operatorValue).contains(fieldValue);
                }
                yield false;
            }
            default -> {
                log.warn("不支持的操作符：{}", operator);
                yield false;
            }
        };
    }
} 