package com.polarizon.gendo.sg.authority.util;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.Arrays;
import java.util.Collection;

/**
 * @Description MongoDB集合名称辅助工具类
 * @create 2025-05-28 下午6:12
 */
public class MongoCollectionUtil {

    /**
     * 辅助方法：从方法参数中提取集合名称
     * <p>
     * 通过四种方式获取集合名称：
     * 1. 从名为"collectionName"的参数中直接获取
     * 2. 从带有@Document注解的类参数中获取
     * 3. 从Class参数中获取
     * 4. 如果参数中包含Collection<?>类型，则取第一个元素的Class
     *
     * @param mongoTemplate  MongoTemplate实例，用于获取集合名称
     * @param parameterNames 参数名称数组
     * @param pointArgs      参数值数组
     * @return 集合名称，如果无法确定则返回null
     */
    @Nullable
    public static String getCollectionName(@NotNull MongoTemplate mongoTemplate, @NotNull String[] parameterNames, Object[] pointArgs) {
        // 1. 优先找名为"collectionName"的参数
        for (int i = 0; i < parameterNames.length; i++) {
            if ("collectionName".equals(parameterNames[i]) && pointArgs[i] instanceof String) {
                return (String) pointArgs[i];
            }
        }

        // 2. 尝试从带有@Document注解的类参数中获取
        for (Object pointArg : pointArgs) {
            if ((pointArg instanceof Class) && Arrays.stream(((Class<?>) pointArg).getAnnotations()).anyMatch(annotation -> annotation instanceof org.springframework.data.mongodb.core.mapping.Document)) {
                return mongoTemplate.getCollectionName((Class<?>) pointArg);
            }
        }

        // 3. 找Class参数
        for (Object arg : pointArgs) {
            if (arg instanceof Class<?>) {
                return mongoTemplate.getCollectionName((Class<?>) arg);
            }
        }

        // 4. 用要保存对象的Class推断
        for (Object arg : pointArgs) {
            if (arg != null) {
                if (arg instanceof Collection<?> col && !col.isEmpty()) {
                    Object first = col.iterator().next();
                    if (first != null) {
                        return mongoTemplate.getCollectionName(first.getClass());
                    }
                } else {
                    return mongoTemplate.getCollectionName(arg.getClass());
                }
            }
        }
        return null;
    }


}
