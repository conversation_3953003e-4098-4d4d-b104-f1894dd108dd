package com.polarizon.gendo.sg.authority.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 权限配置主类
 * <p>
 * 存储完整的权限配置，包括角色定义和资源定义
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthorityConfig {
    
    /**
     * 角色配置映射 - 角色ID到角色定义
     */
    private Map<String, Role> roles = new HashMap<>();
    
    /**
     * 资源配置映射 - 资源ID到资源定义
     */
    private Map<String, Resource> resources = new HashMap<>();
} 