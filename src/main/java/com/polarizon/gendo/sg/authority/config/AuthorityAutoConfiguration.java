package com.polarizon.gendo.sg.authority.config;

import com.polarizon.gendo.sg.authority.core.DataRuleEvaluator;
import com.polarizon.gendo.sg.authority.core.PermissionManager;
import com.polarizon.gendo.sg.authority.core.RuleLoader;
import com.polarizon.gendo.sg.authority.interceptor.WebMvcAuthConfig;
import com.polarizon.gendo.sg.authority.mongo.MongoPermissionAspect;
import com.polarizon.gendo.sg.authority.util.JwtUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;

@Configuration
@EnableConfigurationProperties(AuthorityProperties.class)
@Import(WebMvcAuthConfig.class)
@ConditionalOnProperty(prefix = "authority", name = "enabled", havingValue = "true", matchIfMissing = true)
public class AuthorityAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public JwtUtil jwtUtil() {
        return new JwtUtil();
    }

    @Bean
    @ConditionalOnMissingBean
    public RuleLoader ruleLoader(ResourceLoader resourceLoader) {
        return new RuleLoader(resourceLoader);
    }

    @Bean
    @ConditionalOnMissingBean
    public DataRuleEvaluator dataRuleEvaluator() {
        return new DataRuleEvaluator();
    }

    @Bean
    @ConditionalOnMissingBean
    public PermissionManager permissionManager(RuleLoader ruleLoader, DataRuleEvaluator dataRuleEvaluator) {
        return new PermissionManager(ruleLoader, dataRuleEvaluator);
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "authority", name = "mongo.enabled", havingValue = "true", matchIfMissing = true)
    public MongoPermissionAspect mongoPermissionAspect(PermissionManager permissionManager, MongoTemplate mongoTemplate, MongoConverter mongoConverter) {
        return new MongoPermissionAspect(permissionManager, mongoTemplate, mongoConverter);
    }
} 