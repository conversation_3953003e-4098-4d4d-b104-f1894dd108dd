package com.polarizon.gendo.sg.authority.interceptor;

import com.polarizon.gendo.constant.api.DataDictionaryControllerInterface;
import com.polarizon.gendo.sg.authority.api.PermissionsManagerControllerInterface;
import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
import com.polarizon.gendo.sg.authority.model.UserContext;
import com.polarizon.gendo.sg.authority.util.ContextHolder;
import com.polarizon.gendo.sg.authority.util.JwtUtil;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 权限认证拦截器
 * <p>
 * 负责处理HTTP请求的权限验证、用户信息解析和上下文管理
 * 实现以下功能：
 * 1. 验证JWT token并解析用户信息
 * 2. 设置当前用户上下文，便于后续业务逻辑使用
 * 3. 处理token刷新，延长用户会话有效期
 * 4. 请求完成后清理线程上下文
 */
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor {

    /**
     * 权限管理服务接口
     * 用于获取用户角色关联的权限信息
     */
    private final PermissionsManagerControllerInterface permissionsManagerControllerInterface;

    /**
     * 用户管理服务接口
     */
    private final UserManagerControllerInterface userManagerControllerInterface;

    /**
     * 数据字典服务接口
     * 用于获取权限规则配置
     */
    private final DataDictionaryControllerInterface dictionaryControllerInterface;

    /**
     * 构造函数
     *
     * @param permissionsManagerControllerInterface 权限管理服务接口
     * @param userManagerControllerInterface        用户管理服务接口
     * @param dictionaryControllerInterface         数据字典服务接口
     */
    public AuthenticationInterceptor(JwtUtil jwtUtil, PermissionsManagerControllerInterface permissionsManagerControllerInterface, UserManagerControllerInterface userManagerControllerInterface, DataDictionaryControllerInterface dictionaryControllerInterface) {
        this.jwtUtil = jwtUtil;
        this.permissionsManagerControllerInterface = permissionsManagerControllerInterface;
        this.userManagerControllerInterface = userManagerControllerInterface;
        this.dictionaryControllerInterface = dictionaryControllerInterface;
    }

    /**
     * Token请求头名称
     */
    private static final String HEADER_TOKEN_KEY = "x-token";

    /**
     * Token前缀
     */
    private static final String TOKEN_PREFIX = "";

    /**
     * 内部服务调用Token标识
     */
    private static final String INTERNAL_SERVICE_TOKEN = "shiny-plus-app";

    /**
     * JWT工具类
     */
    private final JwtUtil jwtUtil;

    /**
     * 请求处理前的拦截方法
     * <p>
     * 验证token、解析用户信息、设置上下文并处理token刷新
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  请求处理器
     * @return true表示允许请求继续处理，false表示拦截请求
     */
    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        String requestURI = request.getRequestURI();
        String referer = request.getHeader("Referer");
        String token = extractToken(request);

        // 不传token或使用内部服务token则认为是来自于feign调用，直接放行
        // 因为来自于前端的请求已经在gateway中校验过了
        if (StringUtils.isEmpty(token) || INTERNAL_SERVICE_TOKEN.equals(token)) {
            // log.debug("Internal service call or no token for request: {}", requestURI);
            return true;
        }

        // 解析token，得到claims
        Claims claims = jwtUtil.parseToken(token);
        if (claims == null) {
            log.error("Token验证失败 - RequestURI: {}; Referer: {}; Token: {}", requestURI, referer, token);
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            return false;
        }

        // 解析claims，得到用户上下文
        UserContext userContext = jwtUtil.extractUserContext(claims, userManagerControllerInterface);
        if (userContext == null) {
            log.error("用户信息解析失败 - RequestURI: {}; Referer: {}; Token: {}", requestURI, referer, token);
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            return false;
        }

        // 设置当前用户上下文
        ContextHolder.setCurrentUser(userContext);

        // 若token即将过期，则返回新的token
        String newToken = jwtUtil.getRefreshToken(claims);
        if (StringUtils.isNotEmpty(newToken)) {
            response.setHeader(HEADER_TOKEN_KEY, newToken);
        }

        return true;
    }

    /**
     * 请求完成后的清理方法
     * <p>
     * 清除当前用户上下文，防止内存泄漏和信息泄露
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  请求处理器
     * @param ex       异常信息(如果有)
     */
    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        ContextHolder.clearCurrentUser();
    }

    /**
     * 从请求中提取令牌
     *
     * @param request HTTP请求
     * @return 令牌字符串，如果没有则返回null
     */
    @Nullable
    private String extractToken(@NotNull HttpServletRequest request) {
        // 先尝试从HEADER_TOKEN_KEY头获取token
        String token = request.getHeader(HEADER_TOKEN_KEY);

        if (StringUtils.isNotEmpty(token)) {
            return token;
        }

        return null;
    }
}
 