package com.polarizon.gendo.sg.authority.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色定义类
 * <p>
 * 描述角色及其拥有的资源操作权限
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Role {
    
    /**
     * 角色描述
     */
    private String description;
    
    /**
     * 资源操作权限映射
     * <p>
     * 键：资源ID，值：允许的操作列表
     */
    private Map<String, List<String>> resourcePermissions = new HashMap<>();
    
    /**
     * 检查角色是否拥有对指定资源的指定操作权限
     *
     * @param resourceId 资源ID
     * @param operation  操作类型
     * @return 是否有权限
     */
    public boolean hasPermission(String resourceId, String operation) {
        List<String> operations = resourcePermissions.get(resourceId);
        return operations != null && operations.contains(operation);
    }
} 