package com.polarizon.gendo.sg.authority.util;

import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntityExtend;
import com.polarizon.gendo.sg.authority.model.UserContext;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * JWT工具类
 * <p>
 * 用于JWT令牌的解析和生成
 */
@Slf4j
@Component
public class JwtUtil {

    @Value("${authority.jwt.secret:aHR0cHM6Ly9teS5vc2NoaW5hLm5ldc91LzM2ODE4Njg=}")
    private String secret;

    @Value("${authority.jwt.expire:604800}")
    private long expire;

    @Value("${authority.jwt.refresh:86400}")
    private long refresh;

    /**
     * 解析JWT令牌
     *
     * @param token JWT令牌
     * @return 解析出的声明数据，解析失败则返回null
     */
    public Claims parseToken(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }

        try {
            return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
        } catch (Exception e) {
            log.error("Failed to parse JWT token", e);
            return null;
        }
    }

    /**
     * 从JWT声明中提取用户上下文
     *
     * @param claims                         JWT声明
     * @param userManagerControllerInterface 用户管理服务接口
     * @return 用户上下文，解析失败则返回null
     */
    public UserContext extractUserContext(Claims claims, UserManagerControllerInterface userManagerControllerInterface) {
        try {
            String userId = String.valueOf(claims.get(UserEntityExtend.Constants.FIELD_ID));
            String account = String.valueOf(claims.get(UserEntityExtend.Constants.FIELD_ACCOUNT));
            String tenantId = String.valueOf(claims.get(UserEntityExtend.Constants.FIELD_TENANT_ID));

            ResultDTO<UserEntity> userEntityResultDTO = userManagerControllerInterface.getSingle(userId);
            if (userEntityResultDTO == null || userEntityResultDTO.getData() == null) {
                log.error("Failed to retrieve user entity for userId: {}", userId);
                return null;
            }
            List<RoleEntity> roleList = userEntityResultDTO.getData().getRoleList();

            return UserContext.builder()
                .userId(userId)
                .account(account)
                .tenantId(tenantId)
                .roleEntities(roleList)
                .build();
        } catch (Exception e) {
            log.error("Failed to extract user context from JWT claims", e);
            return null;
        }
    }

    /**
     * 生成JWT令牌
     *
     * @param claims 要包含的声明数据
     * @return JWT令牌字符串
     */
    public String generateToken(Map<String, Object> claims) {
        Date expirationDate = new Date(System.currentTimeMillis() + expire * 1000);

        return Jwts.builder()
            .setHeaderParam("typ", "JWT")
            .setClaims(claims)
            .setExpiration(expirationDate)
            .signWith(SignatureAlgorithm.HS512, secret)
            .compact();
    }

    /**
     * 检查是否需要刷新令牌
     *
     * @param claims JWT声明
     * @return 新令牌，如果不需要刷新则返回null
     */
    public String getRefreshToken(Map<String, Object> claims) {
        Integer exp = (Integer) claims.get("exp");
        long currentTimeMillis = System.currentTimeMillis() / 1000;

        if (exp - currentTimeMillis < refresh) {
            claims.remove("exp"); // 移除过期时间，生成新令牌时会重新设置
            return generateToken(claims);
        }

        return null;
    }
}