package com.polarizon.gendo.sg.authority.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源定义类
 * <p>
 * 描述资源类型、对应的集合和操作规则
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Resource {
    
    /**
     * 资源类型，如 MONGO
     */
    private String type;
    
    /**
     * 资源对应的集合名称
     */
    private String collection;
    
    /**
     * 操作映射
     * <p>
     * 键：操作类型，如 read, create, update, delete
     * 值：操作定义，包含各角色的数据规则
     */
    private Map<String, Operation> operations = new HashMap<>();
    
    /**
     * 获取指定角色在特定操作下的数据规则
     *
     * @param operation 操作类型
     * @param roleId    角色ID
     * @return 数据规则，如果没有则返回null
     */
    public Object getDataRule(String operation, String roleId) {
        Operation op = operations.get(operation);
        if (op == null) {
            return null;
        }
        return op.getDataRules().get(roleId);
    }
} 