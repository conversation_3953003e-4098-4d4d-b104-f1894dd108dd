package com.polarizon.gendo.sg.authority.bean;

import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 组织实体扩展类
 */
@Data
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("组织实体扩展类")
public class OrganizationEntityExtend extends OrganizationEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 组织常量类
     */
    public interface Constants extends OrganizationEntity.Constants, AbstractBaseBO.Constants {
    }
}