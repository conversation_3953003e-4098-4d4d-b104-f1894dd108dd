//package com.polarizon.gendo.sg.authority.spring.config;
//
//import com.polarizon.gendo.constant.api.DataDictionaryControllerInterface;
//import com.polarizon.gendo.sg.authority.api.PermissionsManagerControllerInterface;
//import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
//import com.polarizon.gendo.sg.authority.interceptor.AuthenticatedInterceptor3;
//import jakarta.annotation.Resource;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * Spring MVC权限拦截器配置
// * <p>
// * 通过实现WebMvcConfigurer接口，向Spring MVC框架注册权限拦截器。
// * 该配置确保所有Web请求（除了明确排除的路径外）都经过权限验证。
// * 使用@Configuration注解，被Spring自动扫描和应用。
// */
//@Configuration
//public class WebMvcAuthConfig implements WebMvcConfigurer {
//
//    /**
//     * 权限管理服务接口
//     * 用于获取用户角色关联的权限信息
//     * 使用@Lazy注解延迟加载，避免可能的循环依赖问题
//     */
//    @Resource
//    @Lazy
//    PermissionsManagerControllerInterface permissionsManagerControllerInterface;
//
//    /**
//     * 数据字典服务接口
//     * 用于获取权限规则配置
//     * 使用@Lazy注解延迟加载，避免可能的循环依赖问题
//     */
//    @Resource
//    @Lazy
//    DataDictionaryControllerInterface dictionaryControllerInterface;
//
//    /**
//     * 用户管理服务接口
//     * 用于获取用户信息
//     * 使用@Lazy注解延迟加载，避免可能的循环依赖问题
//     */
//    @Resource
//    @Lazy
//    UserManagerControllerInterface userManagerControllerInterface;
//
//    /**
//     * 默认构造函数
//     */
//    public WebMvcAuthConfig() {
//    }
//
//    /**
//     * 注册权限拦截器（设置多租户拦截器：默认通过文档名前缀实现多租户数据隔离）
//     * <p>
//     * 向Spring MVC注册AuthenticatedInterceptor3拦截器，
//     * 对所有Web请求进行权限验证。
//     * 明确排除Swagger相关路径，确保API文档可以不受权限限制访问。
//     *
//     * @param registry Spring MVC拦截器注册表
//     */
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(new AuthenticatedInterceptor3(permissionsManagerControllerInterface, userManagerControllerInterface, dictionaryControllerInterface))
//            // 排除Swagger UI路径
//            .excludePathPatterns("/**/swagger-ui/**")
//            // 排除Swagger资源路径
//            .excludePathPatterns("/**/swagger-resources/**")
//            // 排除OpenAPI 3规范路径
//            .excludePathPatterns("/**/v3/**");
//    }
//}
//
