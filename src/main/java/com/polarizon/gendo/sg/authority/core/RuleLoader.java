package com.polarizon.gendo.sg.authority.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarizon.gendo.sg.authority.model.AuthorityConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * 权限规则加载器
 * <p>
 * 负责从JSON文件加载权限配置
 */
@Slf4j
@Component
public class RuleLoader {

    private static final String DEFAULT_CONFIG_PATH = "classpath:authority-config.json";

    @Value("${authority.config.path:" + DEFAULT_CONFIG_PATH + "}")
    private String configPath;

    private final ObjectMapper objectMapper;
    private final ResourceLoader resourceLoader;

    private AuthorityConfig authorityConfig;

    public RuleLoader(ResourceLoader resourceLoader) {
        this.objectMapper = new ObjectMapper();
        this.resourceLoader = resourceLoader;
    }

    /**
     * 初始化时加载配置
     */
    @PostConstruct
    public void init() {
        try {
            loadConfig();
        } catch (Exception e) {
            log.error("Failed to load authority config", e);
            // 初始化默认配置
            authorityConfig = new AuthorityConfig();
            // TODO 后续应该改成程序无法启动
        }
    }

    /**
     * 加载权限配置
     */
    private void loadConfig() throws IOException {
        Resource resource;
        try {
            resource = resourceLoader.getResource(configPath);
            if (!resource.exists()) {
                log.warn("Config file not found at {}, will use default config", configPath);
                resource = resourceLoader.getResource(DEFAULT_CONFIG_PATH);
            }
        } catch (Exception e) {
            log.warn("Error loading config from {}, will use default config", configPath, e);
            resource = new ClassPathResource("authority-config.json");
        }

        try (InputStream inputStream = resource.getInputStream()) {
            authorityConfig = objectMapper.readValue(inputStream, AuthorityConfig.class);
            log.info("Loaded authority config from {}", resource.getDescription());
        }
    }

    /**
     * 获取权限配置
     */
    public AuthorityConfig getAuthorityConfig() {
        return authorityConfig;
    }

    /**
     * 重新加载配置
     */
    public void reloadConfig() throws IOException {
        loadConfig();
    }
} 