package com.polarizon.gendo.sg.authority.model;

import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.bean.RoleEntityExtend;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 用户上下文类
 * <p>
 * 存储当前用户的信息，用于权限检查
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserContext {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 账号
     */
    private String account;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户角色扩展列表
     */
    @Builder.Default
    private List<? extends RoleEntity> roleEntities = Collections.emptyList();

    /**
     * 获取用户主要角色名称
     */
    public RoleEntityExtend getPrimaryRole() {
        return CollectionUtils.isEmpty(roleEntities) ? null : (RoleEntityExtend) roleEntities.get(0);
    }
}