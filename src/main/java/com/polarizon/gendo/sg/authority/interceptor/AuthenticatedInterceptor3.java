//package com.polarizon.gendo.sg.authority.interceptor;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.polarizon.common.bean.dto.ResultDTO;
//import com.polarizon.gendo.common.bo.AbstractBaseBO;
//import com.polarizon.gendo.constant.api.DataDictionaryControllerInterface;
//import com.polarizon.gendo.constant.bean.dto.DataDictionaryDTO;
//import com.polarizon.gendo.sg.authority.api.PermissionsManagerControllerInterface;
//import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
//import com.polarizon.gendo.sg.authority.bean.UserEntity;
//import com.polarizon.gendo.sg.authority.bean.dto.UserRoleOrganizationDTO;
//import com.polarizon.gendo.sg.authority.bean.form.RoleAssociationFormBean;
//import com.polarizon.gendo.sg.authority.enums.DataAuthEnum;
//import com.polarizon.gendo.sg.authority.utils.SystemUtil;
//import io.jsonwebtoken.Jwts;
//import io.jsonwebtoken.SignatureAlgorithm;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.jetbrains.annotations.NotNull;
//import org.springframework.lang.Nullable;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//import static org.apache.commons.lang3.StringUtils.isBlank;
//
///**
// * 权限认证拦截器
// * <p>
// * 负责处理HTTP请求的权限验证、用户信息解析和上下文管理
// * 实现以下功能：
// * 1. 验证JWT token并解析用户信息
// * 2. 加载用户的角色和权限配置
// * 3. 设置当前用户上下文，便于后续业务逻辑使用
// * 4. 处理token刷新，延长用户会话有效期
// * 5. 请求完成后清理线程上下文
// */
//@Slf4j
//public class AuthenticatedInterceptor3 implements HandlerInterceptor {
//
//    /**
//     * 权限管理服务接口
//     * 用于获取用户角色关联的权限信息
//     */
//    PermissionsManagerControllerInterface permissionsManagerControllerInterface;
////getUserRoleAndOrganization
//    /**
//     * 用户管理服务接口
//     */
//    UserManagerControllerInterface userManagerControllerInterface;
//
//    /**
//     * 数据字典服务接口
//     * 用于获取权限规则配置
//     */
//    DataDictionaryControllerInterface dictionaryControllerInterface;
//
//    /**
//     * JWT密钥
//     * 用于签名和验证token
//     */
//    private String secret = "aHR0cHM6Ly9teS5vc2NoaW5hLm5ldc91LzM2ODE4Njg=";
//
//    /**
//     * token过期时间(单位:秒)
//     * 默认604800秒(7天)
//     */
//    private long expire = 604800;
//
//    /**
//     * token刷新阈值(单位:秒)
//     * 当token剩余有效期小于此值时将自动刷新
//     * 默认86400秒(1天)
//     */
//    private long refresh = 86400;
//
//    /**
//     * 数据权限标识
//     * 用于过滤数据权限相关的权限ID
//     */
//    private String DATA_AUTH = "dataAuth";
//
//    /**
//     * Token请求头名称
//     */
//    private static final String HEADER_TOKEN_KEY = "x-token";
//
//    /**
//     * 构造函数
//     *
//     * @param permissionsManagerControllerInterface 权限管理服务接口
//     * @param userManagerControllerInterface        用户管理服务接口
//     * @param dictionaryControllerInterface         数据字典服务接口
//     */
//    public AuthenticatedInterceptor3(PermissionsManagerControllerInterface permissionsManagerControllerInterface, UserManagerControllerInterface userManagerControllerInterface, DataDictionaryControllerInterface dictionaryControllerInterface) {
//        this.permissionsManagerControllerInterface = permissionsManagerControllerInterface;
//        this.userManagerControllerInterface = userManagerControllerInterface;
//        this.dictionaryControllerInterface = dictionaryControllerInterface;
//    }
//
//    /**
//     * 请求处理前的拦截方法
//     * <p>
//     * 验证token、解析用户信息、设置上下文并处理token刷新
//     *
//     * @param request  HTTP请求对象
//     * @param response HTTP响应对象
//     * @param handler  请求处理器
//     * @return true表示允许请求继续处理，false表示拦截请求
//     */
//    @Override
//    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
//        String requestURI = request.getRequestURI();
//        String referer = request.getHeader("Referer");
//        String token = request.getHeader(HEADER_TOKEN_KEY);
//        // todo 这里有bug，这里gateway是没有校验的，直接就放过了。
//        // 不传token则认为是来自于feign，因为来自于前端的请求已经在geteway中校验过了
//        if (StringUtils.isEmpty(token) || token.equals("shiny-plus-app")) {
//            return true;
//        }
//        //解析token，得到claim
//        Map<String, Object> claim = parse(token);
//        if (claim == null) {
//            log.error("Token验证失败 - RequestURI：{}；Referer：{}；Token：{}", requestURI, referer, token);
//            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//            return false;
//        }
//        //解析claim，得到用户信息
//        UserEntity userEntity = parse(claim);
//        if (userEntity == null || StringUtils.isEmpty(userEntity.getId())) {
//            log.error("用户信息解析失败 - RequestURI：{}；Referer：{}；Token：{}", requestURI, referer, token);
//            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//            return false;
//        }
//        //防止空指针 userEntity.getRoleList()
//        if (userEntity.getRoleList() == null) {
//            userEntity.setRoleList(new ArrayList<>());
//        }
//        SystemUtil.setCurrentUser(userEntity);
//        //若token即将过期（refresh:一天），则返回新的token
//        String newToken = getRefreshToken(claim);
//        if (StringUtils.isNotEmpty(newToken)) {
//            response.setHeader(HEADER_TOKEN_KEY, newToken);
//        }
//        return true;
//    }
//
//    /**
//     * 请求完成后的清理方法
//     * <p>
//     * 清除当前用户上下文，防止内存泄漏和信息泄露
//     *
//     * @param request  HTTP请求对象
//     * @param response HTTP响应对象
//     * @param handler  请求处理器
//     * @param ex       异常信息(如果有)
//     */
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) {
//        SystemUtil.clearCurrentUser();
//    }
//
//    /**
//     * 解析JWT token
//     *
//     * @param token JWT令牌
//     * @return 解析出的claim数据，解析失败则返回null
//     */
//    @org.jetbrains.annotations.Nullable
//    private Map<String, Object> parse(String token) {
//        if (isBlank(token)) {
//            return null;
//        }
//        try {
//            return Jwts.parserBuilder()
//                .setSigningKey(secret.getBytes())
//                .build()
//                .parseClaimsJws(token)
//                .getBody();
//        } catch (Exception ex) {
//            log.error("Token解析异常", ex);
//            return null;
//        }
//    }
//
//    /**
//     * 解析claim数据，提取用户信息
//     * @param claim JWT中的claim数据
//     * @return 解析得到的用户实体
//     */
//    private UserEntity parse(Map<String, Object> claim) {
//        return parse(null, claim);
//    }
//
//    /**
//     * 从claim提取用户信息，并加载用户权限
//     *
//     * @param request HTTP请求对象，用于获取请求头信息（暂不需要使用）
//     * @param claim   JWT中的claim数据
//     * @return 解析得到的用户实体，包含权限配置
//     */
//    @org.jetbrains.annotations.Nullable
//    private UserEntity parse(HttpServletRequest request, Map<String, Object> claim) {
//        try {
//            Object tenantId = claim.get(UserEntity.Constants.FIELD_TENANT_ID);
//            Object organizations = claim.get(UserEntity.Constants.FIELD_ORGANIZATION_LIST);
//            Object roleIds = claim.get(UserEntity.Constants.FIELD_ROLE_LIST);
//            List<String> roleIdList = JSON.parseArray(String.valueOf(roleIds), String.class);
//            UserEntity userEntity = UserEntity.builder()
//                .account(String.valueOf(claim.get(UserEntity.Constants.FIELD_ACCOUNT)))
//                .tenantId(tenantId == null ? null : String.valueOf(tenantId))
//                .userType(String.valueOf(claim.get(UserEntity.Constants.FIELD_USER_TYPE)))
//                .name(String.valueOf(claim.get(UserEntity.Constants.FIELD_NAME)))
////                .organizationList(organizations == null ? Lists.newArrayList() : JSON.parseArray(String.valueOf(organizations), String.class)
////                    .stream().map(s -> OrganizationEntity.builder().id(s).build()).collect(Collectors.toList()))
////                .roleList(roleIdList.stream()
////                    .map(s -> RoleEntity.builder().id(s).build()).collect(Collectors.toList()))
//                .build();
//            userEntity.setId(String.valueOf(claim.get(AbstractBaseBO.Constants.FIELD_ID)));
//
//            // 加载用户角色关联的权限
//            ResultDTO<RoleAssociationFormBean> roleAssociation = permissionsManagerControllerInterface.getRoleAssociation(roleIdList);
//            List<String> permissionsIds = roleAssociation.getData().getFunctionPermissionsIds().stream()
//                .filter(permission -> permission.contains(DATA_AUTH))
//                .collect(Collectors.toList());
//
//            // 加载数据权限规则配置
//            if (!CollectionUtils.isEmpty(permissionsIds)) {
//                List<DataDictionaryDTO> dataDictionaryDTOS = dictionaryControllerInterface.findDictionaryByKey(permissionsIds).getData();
//                List<JSONObject> ruleConfigs = dataDictionaryDTOS.stream()
//                    .map(dataDictionaryDTO -> cacheKeyPermissionInformation(claim, dataDictionaryDTO.getRemark()))
//                    .collect(Collectors.toList());
//                userEntity.setAuthConfigList(ruleConfigs);
//            }
//
//            // 加载用户是否为管理员
//            ResultDTO<UserRoleOrganizationDTO> userRoleAndOrganization = userManagerControllerInterface.getUserRoleAndOrganization(userEntity.getId());
//
//            return userEntity;
//        } catch (Exception ex) {
//            log.error("用户信息解析异常", ex);
//            return null;
//        }
//    }
//
//    /**
//     * 检查token是否需要刷新
//     * <p>
//     * 当token的剩余有效期小于刷新阈值时，生成新的token
//     *
//     * @param claim JWT中的claim数据
//     * @return 新token字符串，如不需要刷新则返回null
//     */
//    private String getRefreshToken(Map<String, Object> claim) {
//        Integer exp = (Integer) claim.get("exp");
//        long currentTimeMillis = System.currentTimeMillis() / 1000;
//        if (exp - currentTimeMillis < refresh) {
//            claim.remove("exp");
//            return getToken(claim);
//        }
//        return null;
//    }
//
//    /**
//     * 生成新的JWT token
//     *
//     * @param claim token中要包含的数据
//     * @return 生成的JWT token字符串
//     */
//    private String getToken(Map<String, Object> claim) {
//        // expire为token有效时长, 单位毫秒
//        Date date = new Date(System.currentTimeMillis() + (expire * 1000));
//        return Jwts.builder()
//            .setHeaderParam("typ", "JWT")
//            .setClaims(claim)
//            .setExpiration(date)
//            .signWith(SignatureAlgorithm.HS512, secret.getBytes())
//            .compact();
//    }
//
//    /**
//     * 替换规则配置中的请求头变量
//     * <p>
//     * 将规则配置中的${dataAuthKey}形式的占位符替换为实际的请求头值
//     *
//     * @param claim      JWT中的claim数据
//     * @param ruleConfig 规则配置字符串
//     * @return 处理后的规则配置JSON对象
//     */
//    private JSONObject cacheKeyPermissionInformation(Map<String, Object> claim, String ruleConfig) {
//        for (DataAuthEnum allDataAuthEnum : DataAuthEnum.getAllDataAuthEnums()) {
//            Object claimVal = claim.get(allDataAuthEnum.getClaimMapKey());
//            if (claimVal != null) {
//                ruleConfig = ruleConfig.replace("${" + allDataAuthEnum.getDataAuthKey() + "}", claimVal.toString());
//            }
//        }
//        return JSON.parseObject(ruleConfig);
//    }
//
//
//}
