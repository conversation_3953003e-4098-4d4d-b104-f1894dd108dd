package com.polarizon.gendo.sg.authority.enums;

import com.polarizon.common.exception.asserts.BusinessExceptionAssert;

/**
 * 权限响应枚举
 * 定义了权限验证结果的响应类型，实现BusinessExceptionAssert接口用于生成标准业务异常
 */
public enum AuthResponseEnum implements BusinessExceptionAssert {
    /**
     * 权限拒绝
     * 表示用户没有访问请求资源的权限
     * 对应HTTP 401状态码
     */
    DENIED(401, "无数据权限", null);

    /**
     * 响应状态码
     * 符合HTTP标准的状态码
     */
    private final int code;
    
    /**
     * 响应消息
     * 对应状态的详细描述信息
     */
    private final String message;
    
    /**
     * 额外的错误数据
     * 可用于提供更详细的错误信息
     */
    private final Object errorData;

    /**
     * 获取响应状态码
     * @return HTTP状态码
     */
    public int getCode() {
        return this.code;
    }

    /**
     * 获取响应消息
     * @return 详细描述信息
     */
    public String getMessage() {
        return this.message;
    }

    /**
     * 获取额外的错误数据
     * @return 错误的详细数据
     */
    public Object getErrorData() {
        return this.errorData;
    }

    /**
     * 构造函数
     * 
     * @param code HTTP状态码
     * @param message 响应消息
     * @param errorData 额外的错误数据
     */
    private AuthResponseEnum(final int code, final String message, final Object errorData) {
        this.code = code;
        this.message = message;
        this.errorData = errorData;
    }
}
