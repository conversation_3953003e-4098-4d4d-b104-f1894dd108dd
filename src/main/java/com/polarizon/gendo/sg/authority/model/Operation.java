package com.polarizon.gendo.sg.authority.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作定义类
 * <p>
 * 描述资源的操作及其对应的数据规则
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Operation {
    
    /**
     * 数据规则映射
     * <p>
     * 键：角色ID，值：该角色在该操作下的数据规则
     */
    private Map<String, Object> dataRules = new HashMap<>();
} 