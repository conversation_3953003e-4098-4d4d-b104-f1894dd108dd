package com.polarizon.gendo.sg.authority.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 权限配置属性类
 * <p>
 * 用于读取application.yml中的权限配置属性
 */
@Data
@ConfigurationProperties(prefix = "authority")
public class AuthorityProperties {
    
    /**
     * 是否启用权限控制
     */
    private boolean enabled = true;
    
    /**
     * 权限配置文件路径
     */
    private String configPath = "classpath:authority-config.json";
    
    /**
     * JWT配置
     */
    private JwtProperties jwt = new JwtProperties();
    
    /**
     * MongoDB配置
     */
    private MongoProperties mongo = new MongoProperties();
    
    /**
     * 排除路径
     */
    private String excludePaths = "/swagger-ui/**,/v3/api-docs/**,/swagger-resources/**,/webjars/**,/error";
    
    /**
     * JWT配置属性
     */
    @Data
    public static class JwtProperties {
        
        /**
         * JWT密钥
         */
        private String secret = "aHR0cHM6Ly9teS5vc2NoaW5hLm5ldc91LzM2ODE4Njg=";
        
        /**
         * 令牌过期时间(秒)
         */
        private long expire = 604800;
        
        /**
         * 令牌刷新阈值(秒)
         */
        private long refresh = 86400;
    }
    
    /**
     * MongoDB配置属性
     */
    @Data
    public static class MongoProperties {
        
        /**
         * 是否启用MongoDB权限控制
         */
        private boolean enabled = true;
    }
} 