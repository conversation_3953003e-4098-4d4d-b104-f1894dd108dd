package com.polarizon.gendo.sg.authority.bean;

import com.polarizon.common.bean.bo.AbstractBaseBO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户实体扩展类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("用户实体扩展类")
public class UserEntityExtend extends UserEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户常量类
     * 定义字段名和文档名常量
     */
    public interface Constants extends UserEntity.Constants, AbstractBaseBO.Constants {
    }
} 