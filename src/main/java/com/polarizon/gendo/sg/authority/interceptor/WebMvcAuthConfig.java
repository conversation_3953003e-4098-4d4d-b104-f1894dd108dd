package com.polarizon.gendo.sg.authority.interceptor;

import com.polarizon.gendo.constant.api.DataDictionaryControllerInterface;
import com.polarizon.gendo.sg.authority.api.PermissionsManagerControllerInterface;
import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
import com.polarizon.gendo.sg.authority.util.JwtUtil;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * Spring MVC配置类
 * <p>
 * 注册认证拦截器，配置路径匹配规则
 */
@Configuration
public class WebMvcAuthConfig implements WebMvcConfigurer {

    @Resource
    private JwtUtil jwtUtil;

    /**
     * 权限管理服务接口
     * 用于获取用户角色关联的权限信息
     */
    @Lazy
    @Resource
    private PermissionsManagerControllerInterface permissionsManagerControllerInterface;

    /**
     * 用户管理服务接口
     */
    @Lazy
    @Resource
    private UserManagerControllerInterface userManagerControllerInterface;

    /**
     * 数据字典服务接口
     * 用于获取权限规则配置
     */
    @Lazy
    @Resource
    private DataDictionaryControllerInterface dictionaryControllerInterface;

    @Value("${authority.exclude-paths:/swagger-ui/**,/v3/api-docs/**,/swagger-resources/**,/webjars/**,/error}")
    private String excludePathsString;

    @Override
    public void addInterceptors(@NotNull InterceptorRegistry registry) {
        List<String> excludePaths = Arrays.asList(excludePathsString.split(","));

        registry.addInterceptor(
                new AuthenticationInterceptor(jwtUtil,
                    permissionsManagerControllerInterface,
                    userManagerControllerInterface,
                    dictionaryControllerInterface))
            .addPathPatterns("/**")
            .excludePathPatterns(excludePaths);
    }
} 