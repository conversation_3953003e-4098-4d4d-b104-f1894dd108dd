package com.polarizon.common.exception.enums;

import com.polarizon.common.exception.asserts.BusinessExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonResponseEnum implements BusinessExceptionAssert {
    /**
     * 添加异常断言枚举
     */
    ADD_ERROR(-1, "新增失败", null),

    /**
     * 修改异常断言枚举
     */
    UPDATE_ERROR(-2, "修改失败", null),

    /**
     * 删除异常断言枚举
     */
    DELETE_ERROR(-3, "删除失败", null),

    /**
     * 查询异常断言枚举
     */
    GET_ERROR(-4, "查询失败", null),

    /**
     * 添加异常断言枚举
     */
    ADD_ERROR_REASON(-1, "新增失败，原因为：{0}", null),

    /**
     * 修改异常断言枚举
     */
    UPDATE_ERROR_REASON(-2, "修改失败，原因为：{0}", null),

    /**
     * 删除异常断言枚举
     */
    DELETE_ERROR_REASON(-3, "删除失败，原因为：{0}", null),

    /**
     * 查询异常断言枚举
     */
    GET_ERROR_REASON(-4, "查询失败，原因为：{0}", null),

    /**
     * 服务异常断言枚举
     */
    SERVER_ERROR(500, "服务异常", null),

    /**
     * 流媒体服务异常断言枚举
     */
    SM_SERVER_ERROR(501, "流媒体服务异常", null),


    /**
     * 注销失败异常断言枚举
     */
    DEACTIVATE_ERROR(-4, "注销失败{0}", null),

    /**
     * 注册异常断言枚举
     */
    REGISTER_ERROR_REASON(-5, "注册失败，原因为：{0}", null),

    ;
    /**
     * 响应码
     */
    private final int code;
    /**
     * 消息
     */
    private final String message;
    /**
     * 错误数据
     */
    private final Object errorData;

}