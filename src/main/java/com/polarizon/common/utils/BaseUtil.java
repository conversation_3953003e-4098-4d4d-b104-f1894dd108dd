package com.polarizon.common.utils;

import com.google.common.collect.Lists;
import com.polarizon.common.annotation.AllowSetEmpty;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class BaseUtil {
    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = null;
            try {
                value = field.get(obj);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            map.put(fieldName, value);
        }
        return map;
    }

    /**
     * 获取到对象中属性为null的属性名
     *
     * @param source 要拷贝的对象
     * @return
     */
    /*public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }*/

    /**
     * 获取到对象中属性为null的属性名
     *
     * @param source 要拷贝的对象
     * @return
     */
    public static String[] getNullPropertyNames(Object source) {
        //获取class类型和父类class类型
        Class<?> clazz = source.getClass();
        Class<?> superclass = clazz.getSuperclass();

        //解析属性
        List<Field> fieldList = Lists.newArrayList(clazz.getDeclaredFields());
        fieldList.addAll(Lists.newArrayList(superclass.getDeclaredFields()));
        Set<String> emptyNames = new HashSet<String>();
        for (Field field : fieldList) {
            try {
                field.setAccessible(true);
                //获取注解
                if (field.get(source) == null) {
                    emptyNames.add(field.getName());
                }
            } catch (IllegalAccessException e) {
                log.error("获取到对象中属性为null的属性名出现异常，message：{}", e.getMessage());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}
