<template>
  <div class="tabLists" v-if="documentList?.length">
    <!-- <span v-if="showScroll" class="chat-tags-scroll-prev mr8" :class="{ disabled: !left }" @click="onScroll(true)"><LeftOutlined /></span> -->
    <div :class="['chat-send-documents-tags flex', !isExpand ? 'none' : '']" ref="el">
      <div class="chat-tags-scroll flex mr10" ref="scrollEl">
        <a-card :bordered="false" :class="['mt14 mb14 ml8', isHover(item) ? 'isLight' : 'isNormal', customCardClass]" @mouseenter="handleMouseEnter(item)" @mouseleave="handleMouseLeave(item)" v-for="(item, index) in documentList" :key="item.id" closable>
          <div class="flex">
            <common-icon-svg :name="dynamicIcon(item)" class="tag-item-icon"></common-icon-svg>
            <div class="pl8">
              <div class="tag-item-name text-overflow-ellipsis" :title="item.name">{{ item.name || '--' }}</div>
              <div class="tag-item-type">
                <slot name="status" v-bind="{ item }"></slot>
              </div>
            </div>
          </div>
          <CloseIcon class="closeIcon" v-show="isDelete && isHover(item)" @click="emit('delete', index, item)"></CloseIcon>
        </a-card>
      </div>
    </div>
    <!-- <span v-if="showScroll" class="chat-tags-scroll-next ml8" :class="{ disabled: isRight }" @click="onScroll()"><RightOutlined /></span> -->
  </div>
</template>

<script setup>
import { LeftOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { useElementSize, useElementHover } from '@vueuse/core'
import { useScroll } from '@/hooks/web/useScroll'
import selectKnowledge from './selectKnowledge.vue'
import { markRaw, nextTick, watch } from 'vue'
import { storeToRefs } from 'pinia'
import _ from 'lodash'
import { useHoverList } from '@/hooks/web/useHoverList'
import { useChatKb } from '@/hooks/web/useChat'
import { fileTypes } from '@/consts/modules/chat.js'
/**props */
const props = defineProps({
  documentList: Array,
  isExpand: Boolean,
  isDelete: {
    type: Boolean,
    default: true
  },
  customCardClass:String
})
const el = ref(null)
const scrollEl = ref(null)
const emit = defineEmits(['delete'])
const { width } = useElementSize(scrollEl)
const showScroll = ref(false)

const { isHover, activeKey, handleMouseEnter, handleMouseLeave } = useHoverList({ data: props.documentList })

const { onScroll, left, isRight } = useScroll(scrollEl)

const dynamicIcon = (item) => {
  const curName = (item) => (item.name || item.s3file?.name || '')?.split('.')?.pop()
  return fileTypes.find((type) => (_.isArray(type.value) ? type.value.includes(curName(item)) : type.value === curName(item)))?.icon || 'knowledge'
}
// watch(
//   () => documentList.value,
//   (value) => {
//     nextTick(() => {
//       showScroll.value = !!scrollEl.value && scrollEl.value.scrollWidth > el.value.clientWidth
//     })
//   }
// )
</script>
<style scoped lang="less">
.tabLists {
  height: 100px;
  .chat-send-documents-tags {
    box-shadow: inset 0px -6px 6px -4px rgba(0, 26, 68, 0.05);
  }
}
.chat-tags-scroll {
  flex-grow: 1;
  overflow: scroll;
  transition: all 0.3s;
  white-space: nowrap;
  .tag-item {
    display: flex;
    align-items: center;
    &-icon {
      width: 24px;
      height: 24px;
      align-self: center;
    }
    &-name {
      color: rgba(0, 26, 68, 1);
      width: 120px;
    }
    &-type {
      color: rgba(0, 26, 68, 0.65);
    }
  }
}
::v-deep(.a-card) {
  padding: 8px 16px;
  border-radius: @border-radius-md;
  border: none;
}

::v-deep(.ant-card .ant-card-body) {
  background: rgba(240, 244, 255, 0.5);

  padding: 8px 16px;
  border-radius: @border-radius-md;
  border: none;
  .closeIcon {
    position: absolute;
    right: -4px;
    top: -4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    z-index: 2;
  }
}
</style>
