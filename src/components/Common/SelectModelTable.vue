<template>
  <g-modal-popover overlayClassName="model-popover" :destroyTooltipOnHide="true" v-bind="$attrs" title="选择算法模型" placement="bottomLeft" width="900px" @close="onClose">
    <template #content>
      <g-table @register="register" :scroll="{ y: 250 }" :rowSelection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange, getCheckboxProps: getCheckboxProps }"></g-table>
    </template>
    <slot></slot>
  </g-modal-popover>
</template>
<script setup>
import { useListPage } from 'gendo-ui-vue3'
import intelligentApi from '@/api/marker/intelligent'
import { annotationMethodList, annotationTypeList } from '@/consts/modules/data/database'
const props = defineProps({
  filter: Object //模型数据过滤参数,目前主要包含：type: String,//算法类型， appType: String,//版本类型， trainStatus: String,//训练状态
})
const emit = defineEmits(['change', 'update:open'])
const selectedRowKeys = ref([])
const onSelectChange = (_selectedRowKeys, selectedRows) => {
  selectedRowKeys.value = _selectedRowKeys
  emit('change', _selectedRowKeys, selectedRows)
}
const getCheckboxProps = (record) => {
  //  训练完成的模型或者上传成功的模型可选
  return {
    disabled: (!record.imported && record.trainStatus !== 'TRAIN_FINISH') || (record.imported && record.uploadStatus !== 'UPLOAD_SUCCESS') ? true : false
  }
}
const { tableContext } = useListPage({
  designScope: 'select-model-template',
  tableProps: {
    api: intelligentApi.modelList,
    beforeFetch: (data) => {
      if (props.filter) {
        Object.assign(data, props.filter)
      }
      data.orderBy = 'createTime,desc'
      data.name = data.name || undefined
      return data
    },
    formConfig: {
      showResetButton: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '',
          componentProps: {
            placeholder: '请输入模型名称'
          }
        }
      ]
    },
    columns: [
      {
        title: '算法类型',
        dataIndex: 'type',
        width: 180,
        customRender: ({ record }) => {
          return record.algorithmBO?.type ? annotationTypeList.find((m) => m.value == record.algorithmBO?.type)?.label : '-'
        }
      },
      {
        title: '模型名称',
        dataIndex: 'algorithmBO.name',
        width: 180,
        customRender: ({ record }) => {
          return record.algorithmBO?.name || '-'
        }
      },
      {
        title: '标注方式',
        dataIndex: 'labelMode',
        width: 140,
        customRender: ({ record }) => {
          return record.trainDatasetVersions && record.trainDatasetVersions.length > 0 ? annotationMethodList.find((m) => m.value == record.trainDatasetVersions[0].datasetVersionBO?.annotationMethod)?.label || '-' : '-'
        }
      },
      {
        title: '算法版本',
        dataIndex: 'versionFormat',
        width: 130,
        customRender: ({ text }) => {
          return text || '-'
        }
      },
      {
        title: '版本类型',
        dataIndex: 'appType',
        width: 180,
        customRender: ({ text }) => {
          return text ? (text == '1' ? 'GPU服务器' : text) : '-'
        }
      }
    ],
    showIndexColumn: true,
    showActionColumn: false,
    showTableSetting: false
  }
})
const [register] = tableContext
const onClose = () => {
  emit('update:open', false)
}
</script>
<style lang="less">
.model-popover .ant-popover-inner-content {
  // max-height: 400px;
  width: 960px;
  // overflow-y: auto;
}
</style>
