<!--  -->
<template>
  <a-carousel class="carousel-dot" arrows dots-class="slick-dots slick-thumb" :autoplay="false">
    <template #customPaging="props">
      <a> <span class="customPagingDot" :title="props.i"></span> </a>
    </template>
    <slot></slot>
  </a-carousel>
</template>

<script>
export default {
  name: 'GCarousel',
  data() {
    return {}
  },

  components: {},

  computed: {},

  mounted() {},

  methods: {}
}
</script>
<style lang="less">
.carousel-dot.ant-carousel {
  .slick-prev {
    display: none !important;
  }
  .slick-next {
    display: none !important;
  }
}
.carousel-dot {
  position: relative;
  height: 100%;
  .slick-initialized {
    height: calc(100% - 30px);
  }
  .slick-dots {
    bottom: 0px;
  }
}
</style>
