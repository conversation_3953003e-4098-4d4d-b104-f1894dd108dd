<template>
  <g-form v-if="!attrs.hideSearchForm && !attrs.useSettingSearchForm" class="bb1 mb24" @register="registerForm" @submit="emit('submit', $event)" @reset="emit('reset', $event)" />
  <div class="mb24 flex-space-between">
    <slot name="btn"></slot>
    <div class="flex item-center" v-if="attrs.useSettingSearchForm || $slots.search">
      <g-form v-if="attrs.useSettingSearchForm" class="setting-search-form" :style="`width:${attrs.settingFormWidth}${typeof attrs.settingFormWidth === 'number' ? 'px' : ''}`" @register="registerForm" @submit="emit('submit', $event)" @reset="emit('reset', $event)" />
      <slot name="search"></slot>
    </div>
  </div>
  <div>
    <table-collapse v-bind="$attrs" @change="emit('update:activeKey', $event.target.value)">
      <!-- <template v-slot:panelHeader="panelItem">
        <slot name="panelHeader" v-bind="panelItem"></slot>
      </template> -->
      <template #[slotName]="data" v-for="slotName in otherSlots" :key="slotName">
        <slot :name="slotName" v-bind="data || {}" />
      </template>
    </table-collapse>
  </div>
  <g-empty v-if="!attrs.panelLists?.length && !attrs.loading" />
  <page v-bind="$attrs" @change="onChange" @getList="emit('getList')"></page>
</template>
<script setup>
import { useForm } from 'gendo-ui-vue3'
import { useAttrs, useSlots } from 'vue'

const attrs = useAttrs()

const slots = useSlots()

// const otherSlots = Object.keys(slots).filter((key) => !['btn','panelHeader'].includes(key))
const otherSlots = Object.keys(slots).filter((key) => !['btn'].includes(key))

const emit = defineEmits(['update:query', 'getList', 'submit', 'reset'])

//表单配置
const [registerForm, methods] = useForm({
  autoSubmitOnEnter: true,
  labelWidth: attrs.labelWidth,
  labelAlign: attrs.labelAlign,
  baseColProps: attrs.baseColProps,
  schemas: attrs.formSchemas,
  autoSearch: attrs.autoSearch === false ? false : true,
  showActionButtonGroup: attrs.useSettingSearchForm ? false : true,
  showAdvancedButton: attrs.showAdvancedButton == false || attrs.useSettingSearchForm ? false : true,
  actionColOptions: {
    ...attrs.baseColProps,
    style: { textAlign: 'left', paddingLeft: '10px' }
  }
})
const onChange = (query) => {
  emit('update:query', query)
}
defineExpose({ methods })
</script>
<script>
export default {
  inheritAttrs: false
}
</script>
<style lang="less">
.g-basic-form.setting-search-form {
  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 0;
  }
  > .ant-row {
    justify-content: flex-end;
  }
}
</style>
