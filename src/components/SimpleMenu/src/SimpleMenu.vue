<template>
  <GendoMenu v-bind="getBindValues" :activeName="activeName" :openNames="getOpenKeys" :class="prefixCls" :activeSubMenuNames="activeSubMenuNames" @select="handleSelect">
    <template v-for="item in items" :key="item.path">
      <SimpleSubMenu :item="item" :parent="true" :collapsedShowTitle="collapsedShowTitle" :collapse="collapse" />
    </template>
  </GendoMenu>
</template>
<script>
// import _ from 'lodash'
import { defineComponent, computed, ref, unref, reactive, toRefs, watch } from 'vue'
import { isFunction, isUrl } from '@gendo/utils'
import { useDesign } from '@/hooks/web/useDesign'
import GendoMenu from './components/Menu.vue'
import SimpleSubMenu from './SimpleSubMenu.vue'
import { listenerRouteChange } from '@/utils/route/routeChange'
import { REDIRECT_NAME } from '@/router/constant'
import { useRouter } from 'vue-router'
import { findPath } from '@/utils/index.js'

import { useOpenKeys } from './useOpenKeys'
import { URL_HASH_TAB } from '@/consts/modules/app'

export default defineComponent({
  name: 'SimpleMenu',
  components: {
    GendoMenu,
    SimpleSubMenu
  },
  inheritAttrs: false,
  props: {
    items: {
      type: Array,
      default: () => []
    },
    collapse: Boolean,
    mixSider: Boolean,
    theme: String,
    accordion: {
      type: Boolean,
      default: true
    },
    collapsedShowTitle: Boolean,
    beforeClickFn: {
      type: Function
    },
    isSplitMenu: Boolean
  },
  emits: ['menuClick'],
  setup(props, { attrs, emit }) {
    const currentActiveMenu = ref('')
    const isClickGo = ref(false)

    const menuState = reactive({
      activeName: '',
      openNames: [],
      activeSubMenuNames: []
    })

    const { currentRoute } = useRouter()
    const { prefixCls } = useDesign('simple-menu')
    const { items, accordion, mixSider, collapse } = toRefs(props)

    const { setOpenKeys, getOpenKeys } = useOpenKeys(menuState, items, accordion, mixSider, collapse)

    const getBindValues = computed(() => ({ ...attrs, ...props }))

    watch(
      () => props.collapse,
      (collapse) => {
        if (collapse) {
          menuState.openNames = []
        } else {
          setOpenKeys(currentRoute.value.path)
        }
      },
      { immediate: true }
    )

    watch(
      () => props.items,
      () => {
        if (!props.isSplitMenu) {
          return
        }
        setOpenKeys(currentRoute.value.path)
      },
      { flush: 'post' }
    )

    //default active menu is currentActiveMenu，if not find, get the returnPath, if not find, get the current route path
    const getCurrentActiveMenu = (route) => {
      const menus = props.items
      const {
        path,
        query: { returnPath }
      } = route
      const currentMenuRoute = [returnPath, route?.meta?.currentActiveMenu, path]
      const parent = findPath(menus, (n) => currentMenuRoute.includes(n.path))
        ?.filter((item) => !item.hideMenu)
        ?.map((item) => item.path)
      return parent?.at(-1) || path
    }

    listenerRouteChange((route) => {
      if (route.name === REDIRECT_NAME) return
      //找到当前的菜单的可显示数据
      currentActiveMenu.value = getCurrentActiveMenu(route)

      handleMenuChange(route)

      if (unref(currentActiveMenu)) {
        menuState.activeName = unref(currentActiveMenu)
        setOpenKeys(unref(currentActiveMenu))
      }
    })

    async function handleMenuChange(route) {
      if (unref(isClickGo)) {
        isClickGo.value = false
        return
      }
      const path = (route || unref(currentRoute)).path

      menuState.activeName = path

      setOpenKeys(path)
    }

    async function handleSelect(key) {
      if (isUrl(key)) {
        // update-begin--author:sunjianlei---date:20220408---for: 【VUEN-656】配置外部网址打不开，原因是带了#号，需要替换一下
        let url = key.replace(URL_HASH_TAB, '#')
        window.open(url)
        //openWindow(url);
        // update-begin--author:sunjianlei---date:20220408---for: 【VUEN-656】配置外部网址打不开，原因是带了#号，需要替换一下
        return
      }
      const { beforeClickFn } = props
      if (beforeClickFn && isFunction(beforeClickFn)) {
        const flag = await beforeClickFn(key)
        if (!flag) return
      }

      emit('menuClick', key)

      isClickGo.value = true
      setOpenKeys(key)
      menuState.activeName = key
    }

    return {
      prefixCls,
      getBindValues,
      handleSelect,
      getOpenKeys,
      ...toRefs(menuState)
    }
  }
})
</script>
<style lang="less">
@import './index.less';
</style>
