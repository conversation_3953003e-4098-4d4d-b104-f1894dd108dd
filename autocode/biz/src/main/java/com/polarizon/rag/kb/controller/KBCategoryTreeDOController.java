
package com.polarizon.rag.kb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bisnode.opa.client.query.OpaQueryApi;
import com.bisnode.opa.client.query.QueryForDocumentRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.mongodb.client.AggregateIterable;
import com.polarizon.gendo.common.dto.MutationMessage;
import com.polarizon.gendo.common.utils.RegexUtil;
import com.polarizon.gendo.common.dto.OpaCheckResultDTO;
import com.polarizon.gendo.common.utils.Constants;
import com.polarizon.gendo.common.utils.QueryUtil;
import com.polarizon.rag.kb.KBCategoryTreeDO;
import com.polarizon.rag.kb.params.KBCategoryTreeDOParams;
import com.polarizon.rag.kb.repository.KBCategoryTreeDORepository;
import java.util.List;
import com.polarizon.rag.kb.BlockActionEnum;

import com.mongodb.BasicDBObject;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.api.AddResourceInterface;
import com.polarizon.gendo.common.api.CustomMongoRepositoryInterface;
import com.polarizon.gendo.common.api.BatchSetIdAfterInterface;
import com.polarizon.gendo.common.api.DeleteResourceInterface;
import com.polarizon.gendo.common.api.DelBeforeInterface;
import com.polarizon.gendo.common.api.ListResourceInterface;
import com.polarizon.gendo.common.api.SaveResourceInterface;
import com.polarizon.gendo.common.api.TreeResourceInterface;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.EditView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.ShowView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.service.MetaViewService;
import com.polarizon.gendo.common.service.MutationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.http.HttpStatus;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.util.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.polarizon.gendo.common.bo.ViewCount;
import com.polarizon.gendo.common.bo.MongoCountItem;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Streams.stream;
import static com.polarizon.gendo.common.dto.ResultDTO.Constants.SUCCESSFUL;
import static com.polarizon.gendo.common.dto.ResultDTO.error;
import static com.polarizon.gendo.common.dto.ResultDTO.ok;
import static com.polarizon.gendo.common.utils.BaseUtil.getNullPropertyNames;
import static com.polarizon.gendo.common.utils.BaseUtil.getContentPage;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static java.lang.Long.MAX_VALUE;
import static java.lang.Long.MIN_VALUE;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.endsWith;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.removeEnd;
import static org.apache.commons.lang3.StringUtils.startsWith;
import static org.springframework.beans.BeanUtils.copyProperties;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.support.PageableExecutionUtils.getPage;


@Slf4j
@Tag(name = "/kbcategorytree", description = "知识库分类树控制器")
@RestController("KBCategoryTreeDOController")
@RequestMapping("/kbcategorytree")
@Validated
public class KBCategoryTreeDOController implements
    ListResourceInterface<KBCategoryTreeDO, String>,
    TreeResourceInterface<KBCategoryTreeDO, String> {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier("KBCategoryTreeDORepository")
    protected KBCategoryTreeDORepository repository;

    @Autowired(required = false)
    @Qualifier("KBCategoryTreeDOBatchSetIdAfter")
    protected BatchSetIdAfterInterface<KBCategoryTreeDO> batchSetIdAfterService;

    @Autowired
    @Lazy
    private MetaViewService metaViewService;

    @Autowired
    private MutationService mutationService;


    @Autowired(required = false)
    private Map<String, DelBeforeInterface> delBeforeInterfaceMap;

    @Override
    public CustomMongoRepositoryInterface<KBCategoryTreeDO, String> repository() {
        return repository;
    }

    @Autowired
    private OpaQueryApi opa;

    public Function<List<KBCategoryTreeDO>, Pair<Boolean, String>> addChecker() {
        return (data) -> {
            return Pair.of(true, "");
        };
    }

    public Function<List<KBCategoryTreeDO>, Pair<Boolean, String>> deleteChecker() {
        //根据对象名获取对应的实现
        if (delBeforeInterfaceMap.containsKey("KBCategoryTreeDODelBefore")) {
            return delBeforeInterfaceMap.get("KBCategoryTreeDODelBefore").deleteChecker();
        }
        return (data) -> {
            return Pair.of(true, "");
        };
    }

    public Function<List<KBCategoryTreeDO>, Pair<Boolean, String>> saveChecker() {
        return (data) -> {
            return Pair.of(true, "");
        };
    }

    public Function<List<KBCategoryTreeDO>, Pair<Boolean, String>> batchSetIdAfter() {
        return (data) -> {
            if (Objects.isNull(batchSetIdAfterService)) {
                Pair.of(true, "未实现后置处理接口");
            }
            return batchSetIdAfterService.batchSetIdAfter(data);
        };
    }

    @DeleteMapping("/query")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件删除")
    public ResultDTO<List<KBCategoryTreeDO>> deleteByQuery(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false")
        boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 发送，false: 不发送", example = "false") 
        boolean notifyToSelf,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson
    ) {
        Query query = new BasicQuery(queryJson);
        if(query.getQueryObject().isEmpty()){
            return ok("删除成功");
        }return ok("删除成功", repository.deleteAll(query));
    }

    @GetMapping("/count/field")
    @JsonView(ShowView.class)
    @Operation(summary = "字段数据统计")
    public ResultDTO<Long> countByQuery(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
		@Parameter(description = "自定义数组参数")
        @RequestParam(required = false, value = "customField")
        String customField,
        @Parameter(description = "自定义数组值")
        @RequestParam(required = false, value = "customValue")
        List<String> customValue
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
		if (isNotBlank(customField)) {
            criteria.and(customField).in(customValue);
        }
        query.addCriteria(criteria);
        return ok("统计成功", repository.count(query));
    }

    @GetMapping("/query")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件查询")
    public ResultDTO<List<KBCategoryTreeDO>> query(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson
    ) {
        return ok("query no page!", repository.findAll(new BasicQuery(queryJson)));
    }

    @PostMapping("/query/aggregation")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义聚合查询")
    public ResultDTO<List<Map>> query(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
            String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
            String namespace,
        @RequestBody List<String> operations
    ) {
        if (isEmpty(operations)) {
            return error("conditions operations args is null!");
        }
        String collectionName = Optional.ofNullable(namespace).orElse("default") + "_com.polarizon.rag.kb.KBCategoryTreeDO";
        List<AggregationOperation> operationList = operations.stream().map(item -> {
            AggregationOperation operation = context -> context.getMappedObject(Document.parse(item));
            return operation;
        }).collect(toList());
        AggregationResults<Map> results = mongoTemplate.aggregate(newAggregation(operationList), collectionName, Map.class);
        return ok("query no page!", results.getMappedResults());
    }

    @GetMapping("/query/page")
    @JsonView(ShowView.class)
    @Operation(summary = "自定义条件查询")
    public ResultDTO<Page<KBCategoryTreeDO>> queryPage(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson")
        String queryJson,
        Pageable pageable
        ) {
        if (isNull(pageable) || !pageable.isPaged()) {
            return error("conditions pageable args is null!");
        }
        return ok("query with page!", repository.findAll(new BasicQuery(queryJson), pageable));
    }

    @GetMapping("/admin")
    @JsonView(ShowView.class)
    @Operation(summary = "admin查询")
    public ResultDTO<List<KBCategoryTreeDO>> admin(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "只包含命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = "includeNamespace")
        List<String> includeNamespace,
        @Parameter(description = "排除的命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = "excludeNamespace")
        List<String> excludeNamespace
    ) {
        List<Document> query = newArrayList();
        QueryUtil.setBetween(query, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(query, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
            query.add(new Document("createBy", new Document("$in", createBy)));
        }
        if (isNotEmpty(updateBy)) {
            query.add(new Document("updateBy", new Document("$in", updateBy)));
        }
        if (isNotEmpty(ids)) {
            List<ObjectId> oid = ids.stream().map(id -> new ObjectId(id)).collect(toList());
            query.add(new Document("_id", new Document("$in", oid)));
        }
        if (isNotEmpty(dataTag)) {
            query.add(new Document("dataTag", new Document("$in", dataTag)));
        }
        addLabelJsonDocument(query,labelJson);
        if (isNotEmpty(remark)) {
            query.add(new Document("remark", new Document("$in", remark)));
        }
        
        if (isNotEmpty(name)) {
            query.add(new Document("name", new Document("$in", name)));
        }
        
        if (isNotEmpty(description)) {
            query.add(new Document("description", new Document("$in", description)));
        }
        
        if (isNotEmpty(systemType)) {
            query.add(new Document("systemType", new Document("$in", systemType)));
        }
        
        if (isNotEmpty(blockActions)) {
            query.add(new Document("blockActions", new Document("$in", blockActions)));
        }
        String  suffix = "_com.polarizon.rag.kb.KBCategoryTreeDO";
        List<String> collect = mongoTemplate.getCollectionNames().stream()
            .filter(n -> endsWith(n, suffix))
            .filter(n -> !startsWith(n, "default_"))
            .filter(n -> isEmpty(includeNamespace) ? true : includeNamespace.contains(removeEnd(n, suffix)))
            .filter(n -> isEmpty(excludeNamespace) ? true : !excludeNamespace.contains(removeEnd(n, suffix)))
            .collect(toList());

        List<Document> stages = collect.stream()
            .map(collectName -> isNotEmpty(query) 
                ? new Document("$unionWith", new Document(Map.of(
                    "coll", collectName,
                    "pipeline", List.of(new Document("$match", new Document("$and", query)))
                  )))
                : new Document("$unionWith", new Document("coll", collectName))
            ).collect(toList());

        AggregateIterable<Document> allData = mongoTemplate
            .getCollection("default_com.polarizon.rag.kb.KBCategoryTreeDO")
            .aggregate(stages);

        List<KBCategoryTreeDO> result = stream(allData.iterator())
            .map(doc -> mongoTemplate.getConverter().read(KBCategoryTreeDO.class, doc))
            .collect(toList());

        return ok("admin no page!", result);
    }

    @GetMapping("/admin/page")
    @JsonView(ShowView.class)
    @Operation(summary = "admin分页查询")
    public ResultDTO<Page<KBCategoryTreeDO>> adminPage(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "只包含命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = "includeNamespace")
        List<String> includeNamespace,
        @Parameter(description = "排除的命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = "excludeNamespace")
        List<String> excludeNamespace,
        Pageable pageable
    ) {
        List<Document> query = newArrayList();
        QueryUtil.setBetween(query, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(query, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
            query.add(new Document("createBy", new Document("$in", createBy)));
        }
        if (isNotEmpty(updateBy)) {
            query.add(new Document("updateBy", new Document("$in", updateBy)));
        }
        if (isNotEmpty(ids)) {
            List<ObjectId> oid = ids.stream().map(id -> new ObjectId(id)).collect(toList());
            query.add(new Document("_id", new Document("$in", oid)));
        }
        if (isNotEmpty(dataTag)) {
            query.add(new Document("dataTag", new Document("$in", dataTag)));
        }
        addLabelJsonDocument(query,labelJson);
        if (isNotEmpty(remark)) {
            query.add(new Document("remark", new Document("$in", remark)));
        }
        
        if (isNotEmpty(name)) {
            query.add(new Document("name", new Document("$in", name)));
        }
        
        if (isNotEmpty(description)) {
            query.add(new Document("description", new Document("$in", description)));
        }
        
        if (isNotEmpty(systemType)) {
            query.add(new Document("systemType", new Document("$in", systemType)));
        }
        
        if (isNotEmpty(blockActions)) {
            query.add(new Document("blockActions", new Document("$in", blockActions)));
        }
        String  suffix = "_com.polarizon.rag.kb.KBCategoryTreeDO";
        List<String> collect = mongoTemplate.getCollectionNames().stream()
            .filter(n -> endsWith(n, suffix))
            .filter(n -> !startsWith(n, "default_"))
            .filter(n -> isEmpty(includeNamespace) ? true : includeNamespace.contains(removeEnd(n, suffix)))
            .filter(n -> isEmpty(excludeNamespace) ? true : !excludeNamespace.contains(removeEnd(n, suffix)))
            .collect(toList());

        List<Document> stages = collect.stream()
            .map(collectName -> isNotEmpty(query) 
                ? new Document("$unionWith", new Document(Map.of(
                    "coll", collectName,
                    "pipeline", List.of(new Document("$match", new Document("$and", query)))
                  )))
                : new Document("$unionWith", new Document("coll", collectName))
            ).collect(toList());

        AggregateIterable<Document> allData = mongoTemplate
            .getCollection("default_com.polarizon.rag.kb.KBCategoryTreeDO")
            .aggregate(stages);

        List<KBCategoryTreeDO> result = stream(allData.iterator())
            .map(doc -> mongoTemplate.getConverter().read(KBCategoryTreeDO.class, doc))
            .collect(toList());

        Page<KBCategoryTreeDO> pageData = getPage(result, pageable, () -> result.size());
        
        return ok("admin with page!", pageData);
    }

    @GetMapping("/conditions")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件查询")
    public ResultDTO<List<KBCategoryTreeDO>> conditions(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "排序参数")
        @RequestParam(required = false, value = "orderBy")
        String _sort
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);

        if (isNotBlank(_sort)) {
            Optional.of(Sort.by(Arrays.stream(_sort.split("&")).map(item -> {
                String[] sortStr = item.split(",");
                if (sortStr.length == 1) {
                    return Sort.Order.by(sortStr[0]);
                } else if (sortStr.length == 2) {
                    return new Sort.Order(sortStr[1].equalsIgnoreCase("desc") ? DESC : ASC, sortStr[0]);
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).collect(toList()))).ifPresent(orders -> query.with(orders));
        }

        return ok("conditions no page!", repository.findAll(query));
    }

    @PostMapping("/conditions")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件查询")
    public ResultDTO<List<KBCategoryTreeDO>> conditions(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @RequestBody KBCategoryTreeDOParams body,
        @Parameter(description = "排序参数")
        @RequestParam(required = false, value = "orderBy")
        String _sort,
        @Parameter(description = "自定义数组参数")
        @RequestParam(required = false, value = "customField")
        String customField,
        @Parameter(description = "自定义数组值")
        @RequestParam(required = false, value = "customValue")
        List<String> customValue
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (isNotEmpty(dataTag)) {
            criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotBlank(customField)) {
            criteria.and(customField).in(customValue);
        }
        body.andCriteria(criteria);
        query.addCriteria(criteria);

        if (isNotBlank(_sort)) {
            Optional.of(Sort.by(Arrays.stream(_sort.split("&")).map(item -> {
                String[] sortStr = item.split(",");
                if (sortStr.length == 1) {
                    return Sort.Order.by(sortStr[0]);
                } else if (sortStr.length == 2) {
                    return new Sort.Order(sortStr[1].equalsIgnoreCase("desc") ? DESC : ASC, sortStr[0]);
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).collect(toList()))).ifPresent(orders -> query.with(orders));
        }

        return ok("conditions no page!", repository.findAll(query));
    }

    @PostMapping("/conditions/page")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件分页查询")
    public ResultDTO<Page<KBCategoryTreeDO>> conditionsPage(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @RequestBody KBCategoryTreeDOParams body,
        Pageable pageable
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (isNotEmpty(dataTag)) {
            criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        body.andCriteria(criteria);
        query.addCriteria(criteria);
        return ok("conditions no page!", repository.findAll(query,pageable));
    }


    @GetMapping("/conditions/page")
    @JsonView(ShowView.class)
    @Operation(summary = "通用条件分页查询")
    public ResultDTO<Page<KBCategoryTreeDO>> conditionsPage(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        Pageable pageable
    ) {
        if (isNull(pageable) || !pageable.isPaged()) {
            return error("conditions pageable args is null!");
        }
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("conditions with page!", repository.findAll(query, pageable));
    }
    
    @GetMapping("/distinct/name")
    @JsonView(ShowView.class)
    @Operation(summary = "查询name字段的不同取值列表")
    public ResultDTO<List<String>> distinctName(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("distinct name", mongoTemplate.findDistinct(query, "name", KBCategoryTreeDO.class, String.class));
    }

    
    @GetMapping("/distinct/description")
    @JsonView(ShowView.class)
    @Operation(summary = "查询description字段的不同取值列表")
    public ResultDTO<List<String>> distinctDescription(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("distinct description", mongoTemplate.findDistinct(query, "description", KBCategoryTreeDO.class, String.class));
    }

    
    @GetMapping("/distinct/systemType")
    @JsonView(ShowView.class)
    @Operation(summary = "查询systemType字段的不同取值列表")
    public ResultDTO<List<String>> distinctSystemType(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("distinct systemType", mongoTemplate.findDistinct(query, "systemType", KBCategoryTreeDO.class, String.class));
    }

    
    @GetMapping("/distinct/blockActions")
    @JsonView(ShowView.class)
    @Operation(summary = "查询blockActions字段的不同取值列表")
    public ResultDTO<List<BlockActionEnum>> distinctBlockActions(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("distinct blockActions", mongoTemplate.findDistinct(query, "blockActions", KBCategoryTreeDO.class, BlockActionEnum.class));
    }
@GetMapping("/distinct/createBy")
    @JsonView(ShowView.class)
    @Operation(summary = "查询createBy字段的不同取值列表")
    public ResultDTO<List<String>> distinctCreateBy(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike
    ) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        query.addCriteria(criteria);
        return ok("distinct createBy", mongoTemplate.findDistinct(query, "createBy", KBCategoryTreeDO.class, String.class));
    }

    /**
     * 根据ID进行检测数据是否存在
     *
     * @param id ID 数据库ID
     * @return ResultDTO<BO>
     */
    @RequestMapping(method = RequestMethod.HEAD, path = "/{id}")
    @Operation(summary = "根据ID检查数据是否存在")
    @JsonView(ShowView.class)
    public void checkOneByID(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "对象ID", required = true, example="admin")
        @PathVariable(required = true, value = "id")
        String id
    ) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        if (isNotBlank(id) && ObjectId.isValid(id) && repository().existsById(id)) {
            response.setStatus(HttpStatus.OK.value());
        } else {
            response.setStatus(HttpStatus.NOT_FOUND.value());
        }
    }

    @PostMapping("/count/enum")
    @Operation(summary = "枚举字段数量统计")
    public ResultDTO<Map<Object, Object>> countPost(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @RequestBody KBCategoryTreeDOParams body,
        @Parameter(description = "统计的字段") @RequestParam(value = "fields") List<String> fields,
		@Parameter(description = "统计的属性值排序") @RequestParam(required = false, value = "_sort")  String _sort,
 		@Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson") String queryJson,
        @Parameter(description = "是否展开统计的字段") @RequestParam(required = false, value = "isUnwind") Boolean isUnwind
    ) {
        Map<Object, Object> countMap = new HashMap<>();
        Criteria criteria = new Criteria();
        if (isNotEmpty(dataTag)) {
            criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria, labelJson);
        body.andCriteria(criteria);
        if (isNotBlank(queryJson)) {
            JSONObject jsonObject = JSONObject.parseObject(queryJson);
            for (String k : jsonObject.keySet()) {
                criteria.and(k).is(jsonObject.getString(k));
            }
        }
        fields.forEach(field -> {
            Aggregation aggregation = newAggregation(
                Aggregation.match(criteria),
                Aggregation.match(Criteria.where(field).ne(null)),
                Aggregation.group(field).count().as("count")
            );
            if (Boolean.TRUE.equals(isUnwind)) {
                aggregation.getPipeline().add(Aggregation.unwind(field));
            }
            if (isNotBlank(_sort)) {
                aggregation.getPipeline().add(Aggregation.sort(Sort.Direction.fromString(_sort), "count"));
            }
            AggregationResults<MongoCountItem> results = mongoTemplate.aggregate(aggregation, KBCategoryTreeDO.class, MongoCountItem.class);
            countMap.put(field, results.getMappedResults().stream().map(MongoCountItem::toCountItem).toList());
        });
        return ok("count success", countMap);
    }
        

    @GetMapping("/count/enum")
    @Operation(summary = "枚举字段数量统计")
    public ResultDTO<Map<Object, Object>> count(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "统计的字段")@RequestParam(value = "fields") List<String> fields,
		@Parameter(description = "统计的属性值排序") @RequestParam(required = false, value = "_sort")  String _sort,
 		@Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson") String queryJson
   ) {
        String collectionName = Optional.ofNullable(namespace).orElse("default") + "_com.polarizon.rag.kb.KBCategoryTreeDO";
        Map<Object, Object> countMap = new HashMap<>();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        if (isNotBlank(queryJson)) {
            JSONObject jsonObject = JSONObject.parseObject(queryJson);
            for (String k : jsonObject.keySet()) {
                criteria.and(k).is(jsonObject.getString(k));
            }
        }
		// 统计的值默认倒序	
        if (isNull(_sort)) {
            _sort = "desc";
        }
        String finalDirection = _sort;
        fields.forEach(field -> {
            Aggregation aggregation = newAggregation(
				Aggregation.match(criteria),
				Aggregation.match(Criteria.where(field).exists(true).ne(null)),
                Aggregation.project().andExclude("_id").and(field).as(field),
				Aggregation.unwind(field),
                Aggregation.group(field).count().as("count"),
				Aggregation.sort(Sort.Direction.fromString(finalDirection), "count")
            );
            AggregationResults<MongoCountItem> results = mongoTemplate.aggregate(aggregation, collectionName, MongoCountItem.class);
            countMap.put(field, results.getMappedResults().stream().map(MongoCountItem::toCountItem).toList());
        });

        return ok("count success", countMap);
    }

    @GetMapping("/group/enum")
    @Operation(summary = "枚举字段数量统计")
    public ResultDTO<Map<Object, Object>> group(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "统计的字段")@RequestParam(value = "fields") List<String> fields,
		@Parameter(description = "统计的属性值排序") @RequestParam(required = false, value = "_sort")  String _sort,
 		@Parameter(description = "自定义mongo查询json")
        @RequestParam(required = false, value = "queryJson") String queryJson
   ) {
        String collectionName = Optional.ofNullable(namespace).orElse("default") + "_com.polarizon.rag.kb.KBCategoryTreeDO";
        Map<Object, Object> countMap = new HashMap<>();
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        if (isNotBlank(queryJson)) {
            JSONObject jsonObject = JSONObject.parseObject(queryJson);
            for (String k : jsonObject.keySet()) {
                criteria.and(k).is(jsonObject.getString(k));
            }
        }
		// 统计的值默认倒序	
        if (isNull(_sort)) {
            _sort = "desc";
        }
        String finalDirection = _sort;
        fields.forEach(field -> {
            Aggregation aggregation = newAggregation(
				Aggregation.match(criteria),
				Aggregation.match(Criteria.where(field).exists(true).ne(null)),
				Aggregation.unwind(field),
                Aggregation.group(field).max("createTime").as("createTime").push("$$ROOT").as("objects"),
                Aggregation.sort(DESC, "createTime")
            );
            AggregationResults<MongoCountItem> results = mongoTemplate.aggregate(aggregation, collectionName, MongoCountItem.class);
            countMap.put(field, results.getMappedResults().stream().map(MongoCountItem::toObjItem).toList());
        });

        return ok("count success", countMap);
    }

    @GetMapping("/count/page")
    @Operation(summary = "按照字段分组进行统计数据")
    public ResultDTO<Page<Map>> countByFieldsPage(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "统计的字段")
        @RequestParam(required = true, value = "countField") String countField,
        @Parameter(description = "分组字段")
        @RequestParam(required = true, value = "groupField") String groupField,
        @Parameter(description = "筛选条件")
        @RequestParam(required = false, value = "filter") String filter,
        Pageable pageable
    ) {
        String collectionName = Optional.ofNullable(namespace).orElse("default") + "_com.polarizon.rag.kb.KBCategoryTreeDO";
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        if (isNotBlank(filter)) {
            criteria.and(groupField).regex(filter);
        }

        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(criteria));
        operations.add(Aggregation.group(countField, groupField).count().as("count"));
        operations.add(Aggregation.group("_id." + groupField)
            .addToSet(new BasicDBObject("value", "$_id." + countField).append("count", "$count")).as("countBy"));
        operations.add(Aggregation.project().and("$_id").as("groupBy").and("countBy").as("countBy").andExclude("_id"));
        if (pageable.getSort().isSorted()) {
            operations.add(Aggregation.sort(pageable.getSort()));
        }
        operations.add(Aggregation.skip(pageable.getOffset()));
        operations.add(Aggregation.limit(pageable.getPageSize()));
        Aggregation aggregation = newAggregation(operations);

        // 执行聚合查询，统计记录数与总页数
        Aggregation countAgg = newAggregation(
            Aggregation.match(criteria),
            Aggregation.unwind(groupField),
            Aggregation.group(groupField).count().as("count"),
            Aggregation.count().as("totalCount")
        );
        AggregationResults<Map> countResult = mongoTemplate.aggregate(countAgg, collectionName, Map.class);
        Map uniqueMappedResult = countResult.getUniqueMappedResult();
        int totalCount = isNull(uniqueMappedResult) ? 0 : (int) uniqueMappedResult.get("totalCount");
        AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, collectionName, Map.class);
        List<Map> _data = results.getMappedResults();
        return ok("group with page!", new PageImpl<>(_data, pageable, totalCount));
    }

    @PostMapping("/copy")
    @JsonView(ShowView.class)
    @Operation(summary = "数据批量复制接口")
    public ResultDTO copyAndModify(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "ObjectID列表") 
        @RequestParam(required = false, value = "ids") 
        List<String> ids,
        
        @Parameter(description = "查询参数: 分类名称") 
        @RequestParam(required = false, value = "name") 
        List<String> name,
        @Parameter(description = "模糊查询参数: 分类名称") 
        @RequestParam(required = false, value = "nameLike") 
        String nameLike,
        
        @Parameter(description = "查询参数: 分类描述") 
        @RequestParam(required = false, value = "description") 
        List<String> description,
        @Parameter(description = "模糊查询参数: 分类描述") 
        @RequestParam(required = false, value = "descriptionLike") 
        String descriptionLike,
        
        @Parameter(description = "查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemType") 
        List<String> systemType,
        @Parameter(description = "模糊查询参数: 系统类型: ALL-所有, UNCATEGORIZED-未分类, 也可以自定义。这里不限制内容") 
        @RequestParam(required = false, value = "systemTypeLike") 
        String systemTypeLike,
        
        @Parameter(description = "查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActions") 
        List<BlockActionEnum> blockActions,
        @Parameter(description = "不为空查询参数: 禁用的操作，数组，禁止增加子类，禁止修改，禁止删除") 
        @RequestParam(required = false, value = "blockActionsNotEmpty") 
        Boolean blockActionsNotEmpty,
        @Parameter(description = "查询创建开始时间") 
        @RequestParam(required = false, value = "startCreateTime") 
        Long startCreateTime,
        @Parameter(description = "查询创建结束时间") 
        @RequestParam(required = false, value = "endCreateTime") 
        Long endCreateTime,
        @Parameter(description = "查询更新开始时间") 
        @RequestParam(required = false, value = "startUpdateTime") 
        Long startUpdateTime,
        @Parameter(description = "查询更新结束时间") 
        @RequestParam(required = false, value = "endUpdateTime") 
        Long endUpdateTime,
        @Parameter(description = "创建者") 
        @RequestParam(required = false, value = "createBy") 
        List<String> createBy,
        @Parameter(description = "模糊查询参数: 创建者") 
        @RequestParam(required = false, value = "createByLike") 
        String createByLike,
        @Parameter(description = "更新者") 
        @RequestParam(required = false, value = "updateBy") 
        List<String> updateBy,
        @Parameter(description = "模糊查询参数: 更新者") 
        @RequestParam(required = false, value = "updateByLike") 
        String updateByLike,
        @Parameter(description = "备注信息") 
        @RequestParam(required = false, value = "remark") 
        List<String> remark,
        @Parameter(description = "模糊查询参数: 备注信息") 
        @RequestParam(required = false, value = "remarkLike") 
        String remarkLike,
        @Parameter(description = "请求用户账号", example = "admin")
        @RequestHeader(required = true, value = Constants.HEADER_ACCOUNT_KEY)
        String userAccount,
        @Parameter(description = "忽略不需要复制的属性名称", example = "id,createTime,updateTime") 
        @RequestParam(required = false, value = "ignoreProperties") 
        List<String> ignoreProperties,
        @RequestBody()
        KBCategoryTreeDO modify
    ) {
        Criteria criteria = new Criteria();
        QueryUtil.setBetween(criteria, "createTime", startCreateTime, endCreateTime);
        QueryUtil.setBetween(criteria, "updateTime", startUpdateTime, endUpdateTime);
        if (isNotEmpty(createBy)) {
          criteria.and("createBy").in(createBy);
        }
        if (isNotBlank(createByLike)) {
            criteria.and("createBy").regex(".*?" + RegexUtil.escapeRegexCharacter(createByLike) + ".*");
        }
        if (isNotEmpty(updateBy)) {
            criteria.and("updateBy").in(updateBy);
        }
        if (isNotBlank(updateByLike)) {
            criteria.and("updateBy").regex(".*?" + RegexUtil.escapeRegexCharacter(updateByLike) + ".*");
        }
        if (isNotEmpty(remark)) {
          criteria.and("remark").in(remark);
        }
        if (isNotBlank(remarkLike)) {
            criteria.and("remark").regex(".*?" + RegexUtil.escapeRegexCharacter(remarkLike) + ".*");
        }
        if (isNotEmpty(ids)) {
          criteria.and("_id").in(ids);
        }
        if (isNotEmpty(dataTag)) {
          criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        if (isNotEmpty(name)) {
            criteria.and("name").in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and("name").regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }
        if (isNotEmpty(description)) {
            criteria.and("description").in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and("description").regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }
        if (isNotEmpty(systemType)) {
            criteria.and("systemType").in(systemType);
        }
        if (isNotBlank(systemTypeLike)) {
            criteria.and("systemType").regex(".*?" + RegexUtil.escapeRegexCharacter(systemTypeLike) + ".*");
        }
        if (isNotEmpty(blockActions) && Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").in(blockActions).exists(true).ne(List.of());
        } else if(isNotEmpty(blockActions)) {
            criteria.and("blockActions").in(blockActions);
        } else if (Objects.nonNull(blockActionsNotEmpty) && blockActionsNotEmpty) {
            criteria.and("blockActions").exists(true).ne(List.of());
        }
        // 忽略不需要复制的属性名称
        List<String> defaultIgnorePropertys = new ArrayList<>(List.of("id", "createBy", "updateBy", "createTime", "updateTime"));
        if (isNotEmpty(ignoreProperties)) {
            defaultIgnorePropertys.addAll(ignoreProperties);
        }

        // 创建临时集合
        String tempCollectionName = "tmp_copy_" + "KBCategoryTreeDO" + "_" + System.currentTimeMillis();
        String tempCollectionCounterName = tempCollectionName + ".Counters";
        mongoTemplate.createCollection(tempCollectionName);
        try {
            // 每页查询的数据量
            int size = 1000;
            int page = 0;
            List<KBCategoryTreeDO> copyDataSourceList;
            do {
                // 分页查询所有数据
                Query query = new Query().addCriteria(criteria).with(Sort.by(Sort.Direction.ASC, "_id")).skip(page * size).limit(size);
                copyDataSourceList = mongoTemplate.find(query, KBCategoryTreeDO.class);
                if (copyDataSourceList.isEmpty()) {
                    break;
                }

                // 复制并修改数据
                List<KBCategoryTreeDO> copiedList = new ArrayList<>();
                for (KBCategoryTreeDO fromCopyData : copyDataSourceList) {
                    KBCategoryTreeDO toCopiedData = new KBCategoryTreeDO();
                    toCopiedData.init(userAccount, true);
                    copyProperties(fromCopyData, toCopiedData,defaultIgnorePropertys.toArray(new String[defaultIgnorePropertys.size()]));
                    copyProperties(modify, toCopiedData, getNullPropertyNames(modify));
                    copiedList.add(toCopiedData);
                }

                // 将复制并修改后的数据批量插入到数据库中
                BulkOperations batchOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, tempCollectionName);
                batchOps.insert(copiedList);
                batchOps.execute();

                page++;
            } while (isNotEmpty(copyDataSourceList));

            // 通过临时表copy数据，直接在原表插入数据，如果查询匹配到新插入数据，会导致死循环
            int skip = 0;
            long count = mongoTemplate.count(new Query(), tempCollectionName);
            while (count > 0 && skip <= count) {
                List<KBCategoryTreeDO> sourceDataList = mongoTemplate.find(
                    new Query().skip(skip).limit(size).with(Sort.by("_id")), KBCategoryTreeDO.class, tempCollectionName);
                BulkOperations batchOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, KBCategoryTreeDO.class);
                batchOps.insert(sourceDataList);
                batchOps.execute();

                skip += size;
            }
        } finally {
            // 删除临时集合
            mongoTemplate.dropCollection(tempCollectionName);
            // 判断自增集合，是否存在
            if (mongoTemplate.collectionExists(tempCollectionCounterName)) {
                mongoTemplate.dropCollection(tempCollectionCounterName);
            }
        }

        return ok("复制成功");
    }

    @PostMapping("/delete/condition")
    @JsonView(ShowView.class)
    @Operation(summary = "按条件删除")
    public ResultDTO deleteByCondition(
        @Parameter(description = "租户ID")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(description = "数据标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) 
        List<String> dataTag,
        @Parameter(description = "领域标签") 
        @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) 
        String labelJson,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") boolean notNotify,
        @RequestBody KBCategoryTreeDOParams body
    ) {
        if(body.isParamEmpty()){
            return ok("no condition!");
        }
        Query query = buildQuery(dataTag, labelJson, body);
        if(query.getQueryObject().isEmpty()){
            return ok("delete");
        }
        return deleteBySlice(query, false, notNotify);
    }

    private Query buildQuery(List<String> dataTag, String labelJson, KBCategoryTreeDOParams body) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (isNotEmpty(dataTag)) {
            criteria.and("dataTag").in(dataTag);
        }
        addLabelJsonCriteria(criteria,labelJson);
        body.andCriteria(criteria);
        query.addCriteria(criteria);
        return query;
    }

    /**
     * 分批删除
     *
     * @param query 条件
     * @return
     */
    public ResultDTO deleteBySlice(Query query, boolean isSoft, boolean notNotify) {
        int max = 10000;
        Long count = repository.count(query);
        if (count < max) {
            return deleteByConditionActual(query, isSoft, notNotify);
        }
        Long slice = count % max > 0 ? count / max + 1 : count / max;
        query.with(Sort.by(Sort.Order.asc(AbstractBaseBO.Constants.FIELD_ID)));
        for (int i = 0; i < slice - 1; i++) {
            query.with(Pageable.ofSize(max).withPage(i));
            ResultDTO resultDTO = deleteByConditionActual(query, isSoft, notNotify);
            if (resultDTO.getStatus().equals(ResultDTO.Constants.FAILURE)) {
                return resultDTO;
            }
        }
        return ok("delete");
    }

    /**
     * 按条件删除
     *
     * @param query
     * @param isSoft
     * @return
     */
    private ResultDTO deleteByConditionActual(Query query, boolean isSoft, boolean notNotify) {
        //删除
        ResultDTO<List<KBCategoryTreeDO>> resultDTO = isSoft ? softDeleteByConditionActual(query) : reallyDeleteByConditionActual(query);
        if (!SUCCESSFUL.equals(resultDTO.getStatus()) || CollectionUtils.isEmpty(resultDTO.getData())) {
            return resultDTO;
        }
        //视图
        List<Object> objectList = resultDTO.getData().stream().map(user -> (Object) user).collect(Collectors.toList());
        metaViewService.updateMetaViewAspcet(objectList, objectList, MutationMessage.Action.DELETE);
        //mutation
        if (!notNotify) {
            mutationService.sendAll(objectList, null, MutationMessage.Action.DELETE,false);
        }
        //返回
        return resultDTO;
    }

    /**
     * 真删除
     *
     * @param query
     * @return
     */
    private ResultDTO<List<KBCategoryTreeDO>> reallyDeleteByConditionActual(Query query) {return ok("delete", repository.deleteAll(query));
    }

    /**
     * 软删除
     *
     * @param query
     * @return
     */
    private ResultDTO<List<KBCategoryTreeDO>> softDeleteByConditionActual(Query query) {
        return ok("delete");
    }


    

}
