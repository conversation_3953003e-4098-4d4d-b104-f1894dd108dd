<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.polarizon.rag</groupId>
        <artifactId>chenqi-helper-ms</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath> <!-- 可忽略 -->
    </parent>

    <name>chenqi-helper</name>
    <description>rag: chenqi-helper</description>
    <organization>
        <name>天海宸光 Polarizon</name>
        <url>https://www.polarizon.com/</url>
    </organization>

    <groupId>com.polarizon.rag</groupId>
    <artifactId>chenqi-helper-biz</artifactId>
    <packaging>jar</packaging>

    <properties>
        <biz.server.port>20504</biz.server.port>
        <start-class>com.polarizon.gendo.MainApplication</start-class>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <dockerfile-maven-version>1.4.13</dockerfile-maven-version>
        <spring-cloud.version>2022.0.4</spring-cloud.version>
        <spring-doc.version>2.0.2</spring-doc.version>
        <lang3.version>3.12.0</lang3.version>
        <collections4.version>4.4</collections4.version>
        <guava.version>31.1-jre</guava.version>
        <fastjson.version>1.2.80</fastjson.version>
        <okhttp.version>4.9.3</okhttp.version>
        <httpclient.version>4.5.13</httpclient.version>
        <hutool-version>5.6.5</hutool-version>
        <opa-java-client-version>0.4.0</opa-java-client-version>
        <docker.repository>hub.polarise.cn</docker.repository>
        <docker.registry>polarizon-rag</docker.registry>
        <mongock.version>5.3.4</mongock.version>
        <redisson.version>3.24.3</redisson.version>
        <lombok.version>1.18.34</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.mongock</groupId>
                <artifactId>mongock-bom</artifactId>
                <version>${mongock.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <!-- commons-collections4 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${collections4.version}</version>
        </dependency>

        <!-- commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${lang3.version}</version>
        </dependency>

        <!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <!-- HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${spring-doc.version}</version>
        </dependency>

        <!-- spring mvc -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- spring apo -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- spring feign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- feign -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <!-- opa -->
        <dependency>
            <groupId>com.bisnode.opa</groupId>
            <artifactId>opa-java-client</artifactId>
            <version>${opa-java-client-version}</version>
        </dependency>

        <!-- validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!--mongodb-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
            <version>3.12.10</version>
        </dependency>

         <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool-version}</version>
        </dependency>

        <!-- logback: Conditional processing in configuration files requires the Janino library -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.serioussam</groupId>
            <artifactId>syslogappender</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--kafka-->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- mongock -->
        <dependency>
            <groupId>io.mongock</groupId>
            <artifactId>mongodb-springdata-v4-driver</artifactId>
        </dependency>
        <dependency>
            <groupId>io.mongock</groupId>
            <artifactId>mongock-springboot</artifactId>
        </dependency>

        <!-- common-lib -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>common-lib</artifactId>
            <version>1.0.9</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>

        <!-- api引入 -->
        <dependency>
            <groupId>com.polarizon.rag</groupId>
            <artifactId>chenqi-helper-api</artifactId>
            <version>${project.version}</version>
        </dependency><!-- plugin引入 -->
        <dependency>
            <groupId>com.polarizon.rag</groupId>
            <artifactId>chenqi-helper-plugin</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- fass common-lib引入 todo 不能使用SNAPSHOT -->
        <dependency>
            <groupId>com.polarizon.gendo.faas</groupId>
            <artifactId>common-lib</artifactId>
            <version>4.8.0</version>
        </dependency>

        <!-- user-authority-lib -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>user-authority-lib</artifactId>
            <version>2.2.1-zhiku-beta.0</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>user-authority-api</artifactId> 
            <version>1.14.0-zhiku-auth-beta.5</version>
        </dependency>

    </dependencies>

    <!--指定使用maven打包-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <tagNameFormat>biz-v@{project.version}-${maven.build.timestamp}</tagNameFormat>
                    <updateDependencies>false</updateDependencies>
                    <pushChanges>false</pushChanges>
                    <arguments>-DskipTests</arguments>
                    <arguments>-Ddockerfile.skip</arguments>
                    <checkModificationExcludes>
                        <checkModificationExclude>**/pom.xml</checkModificationExclude>
                    </checkModificationExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-version}</version>
                <executions>
                    <execution>
                        <id>default</id>
                    </execution>
                </executions>
                <configuration>
                    <contextDirectory>${project.basedir}</contextDirectory>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <repository>${env.DOCKER_REPOSITORY}/${env.DOCKER_REGISTRY}/${project.name}</repository>
                    <tag>${env.PROJECT_TAG}</tag>
                    <buildArgs>
                        <project_name>${project.name}</project_name>
                        <work_port>${biz.server.port}</work_port>
                        <JAR_FILE>${project.build.finalName}.${project.packaging}</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>${start-class}</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!-- 可以把依赖的包都打包到生成的Jar包中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- 分发配置，必须与 settings.xml 的 id 一致 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://**************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://**************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
