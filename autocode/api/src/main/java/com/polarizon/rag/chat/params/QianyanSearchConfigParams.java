package com.polarizon.rag.chat.params;

import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.mongodb.core.query.Criteria;

import com.polarizon.gendo.common.utils.QueryUtil;

import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.DEAD;
import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.LIVE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.containsAny;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.util.List;
import java.util.Objects;


import com.polarizon.rag.chat.QianyanSearchConfig;
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
import com.polarizon.gendo.common.bo.BaseParams;
import com.polarizon.gendo.common.utils.RegexUtil;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
public class QianyanSearchConfigParams extends BaseParams {
	
    @Parameter(description = "软删除标志")
    List<StatusFlag> statusFlag;
    
    @Parameter(description = "是否要检索潜研平台") 
    public List<Boolean> isQianYanSearch;
    

    public void andCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCountCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCriteria(Criteria criteria, String prefix) {
        super.baseAndCriteria(criteria, prefix);

        if (isNotEmpty(isQianYanSearch)) {
            criteria.and(concatDot(prefix, "isQianYanSearch")).in(isQianYanSearch);
        }
        
        if (isNotBlank(prefix)){
            andElementCriteria(criteria, prefix);
        }
    }

    private void andElementCriteria(Criteria criteria, String prefix){ 
    }

    public Criteria getElementCountCriteria(String prefix) {
        Criteria criteria = new Criteria(); 
        return criteria;
    }

    public boolean filter(QianyanSearchConfig obj) {
        if (!super.filter(obj)){
            return false;
        } 
        if (isNotEmpty(isQianYanSearch) && !containsAny(isQianYanSearch, obj.isQianYanSearch)) {
            return false;
        }
        
        

        return true;
    }
}
