package com.polarizon.rag.application;

import static org.springframework.data.mongodb.core.mapping.FieldType.OBJECT_ID;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.annotation.AfterInit;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.ReadOnlyProperty;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


import com.polarizon.rag.application.ApplicationTypeEnum;
import com.polarizon.rag.chat.ConversationConfig;
import java.util.List;
import com.polarizon.rag.application.ApplicationEnableEnum;


@Schema(description = "应用记录")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document("#{@namespaceProvider.getNamespace()}_com.polarizon.rag.application.ApplicationDO")
@CompoundIndexes({

	@CompoundIndex(name = "createTime_index", def = "{'createTime':1}"),
	@CompoundIndex(name = "updateTime", def = "{'updateTime':1}"),
	@CompoundIndex(name = "_id", def = "{'_id':1}")
})
public class ApplicationDO extends AbstractBaseBO {

     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "应用名称")
    @JsonView({EditView.class})
    @JsonProperty("name")
    @Field("name")
    public String name;
     
    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "WPS:WPS插件智能审查;INSPECT:WPS插件智能写作")
    @JsonView({EditView.class})
    @JsonProperty("type")
    @Field("type")
    public ApplicationTypeEnum type;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "图标网址")
    @JsonView({EditView.class})
    @JsonProperty("logoUrlS3")
    @Field("logoUrlS3")
    public String logoUrlS3;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "描述")
    @JsonView({EditView.class})
    @JsonProperty("description")
    @Field("description")
    public String description;
     
    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "参数设置")
    @JsonView({EditView.class})
    @JsonProperty("conversationConfig")
    @Field("conversationConfig")
    public ConversationConfig conversationConfig;
     
    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "选择的知识库ID列表")
    @JsonView({EditView.class})
    @JsonProperty("kbIDList")
    @Field("kbIDList")
    public List<String> kbIDList;
    
     
    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "应用使能: ENABLE-启用, DISABLE-停用")
    @JsonView({EditView.class})
    @JsonProperty("enable")
    @Field("enable")
    public ApplicationEnableEnum enable;
     
    /**
     * avro type: *schema.BoolField
     */
    @Schema(description = "是否公开")
    @JsonView({EditView.class})
    @JsonProperty("isPublic")
    @Field("isPublic")
    public Boolean isPublic;
     
    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "指令列表")
    @JsonView({EditView.class})
    @JsonProperty("instructIDList")
    @Field("instructIDList")
    public List<String> instructIDList;
    
     
    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "可见度（用户id或public静态字符串）")
    @JsonView({EditView.class})
    @JsonProperty("visible")
    @Field("visible")
    public List<String> visible;


}
