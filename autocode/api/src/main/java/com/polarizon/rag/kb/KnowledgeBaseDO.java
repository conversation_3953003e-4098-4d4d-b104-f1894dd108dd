package com.polarizon.rag.kb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.annotation.AfterInit;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;
import com.polarizon.rag.kb.KbEnableEnum;


@Schema(description = "知识库")
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document("#{@namespaceProvider.getNamespace()}_com.polarizon.rag.kb.KnowledgeBaseDO")
@CompoundIndexes({

    @CompoundIndex(name = "createTime_index", def = "{'createTime':1}"),
    @CompoundIndex(name = "updateTime", def = "{'updateTime':1}"),
    @CompoundIndex(name = "_id", def = "{'_id':1}")
})
public class KnowledgeBaseDO extends AbstractBaseBO {


    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "知识库名称")
    @JsonView({EditView.class})
    @JsonProperty("name")
    @Field("name")
    @NotNull(message = "知识库名称不能为空")
    @Size(min = 1, max = 50, message = "知识库名称长度必须在1-50个字符之间")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9_-]{1,50}$", message = "请输入1-50长度的中文、英文、数字、下划线、中横线")
    public String name;

    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "知识库描述")
    @JsonView({EditView.class})
    @JsonProperty("description")
    @Field("description")
    public String description;

    /**
     * avro type: *schema.BoolField
     */
    @Schema(description = "是否公开-用作排序")
    @JsonView({EditView.class})
    @JsonProperty("isPublic")
    @Field("isPublic")
    public Boolean isPublic;

    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "可见度（用户id或public静态字符串）")
    @JsonView({EditView.class})
    @JsonProperty("visible")
    @Field("visible")
    public List<String> visible;

    /**
     * avro type: *schema.LongField
     */
    @Schema(description = "知识库内数据文件总数")
    @JsonView({EditView.class})
    @JsonProperty("totalDataFileNumber")
    @Field("totalDataFileNumber")
    public Long totalDataFileNumber;

    /**
     * avro type: *schema.LongField
     */
    @Schema(description = "知识库内数据文件总大小, 单位byte")
    @JsonView({EditView.class})
    @JsonProperty("totalDataFileBytes")
    @Field("totalDataFileBytes")
    public Long totalDataFileBytes;

    /**
     * avro type: *schema.Reference
     */
    @Schema(description = "知识库使能: ENABLE-启用, DISABLE-停用")
    @JsonView({EditView.class})
    @JsonProperty("enable")
    @Field("enable")
    public KbEnableEnum enable;

    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "内嵌模型名称")
    @JsonView({EditView.class})
    @JsonProperty("embeddingModelName")
    @Field("embeddingModelName")
    public String embeddingModelName;

    /**
     * avro type: *schema.ArrayField
     */
    @Schema(description = "分类IDs")
    @JsonView({EditView.class})
    @JsonProperty("categoryIDs")
    @Field("categoryIDs")
    public List<String> categoryIDs;




    @AfterInit
    public void totalDataFileNumberAfterInit(String userAccount, boolean isNew) {
        if (isNew && this.totalDataFileNumber == null) {
            this.totalDataFileNumber = 0L;
        }
    }


    @AfterInit
    public void totalDataFileBytesAfterInit(String userAccount, boolean isNew) {
        if (isNew && this.totalDataFileBytes == null) {
            this.totalDataFileBytes = 0L;
        }
    }

}

