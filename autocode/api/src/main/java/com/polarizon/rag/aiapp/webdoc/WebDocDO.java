package com.polarizon.rag.aiapp.webdoc;

import static org.springframework.data.mongodb.core.mapping.FieldType.OBJECT_ID;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.annotation.AfterInit;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.ReadOnlyProperty;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;




@Schema(description = "在线文档")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document("#{@namespaceProvider.getNamespace()}_com.polarizon.rag.aiapp.webdoc.WebDocDO")
@CompoundIndexes({

	@CompoundIndex(name = "createTime_index", def = "{'createTime':1}"),
	@CompoundIndex(name = "updateTime", def = "{'updateTime':1}"),
	@CompoundIndex(name = "_id", def = "{'_id':1}")
})
public class WebDocDO extends AbstractBaseBO {

     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "用户ID")
    @JsonView({EditView.class})
    @JsonProperty("ownerID")
    @Field("ownerID")
    public String ownerID;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "文档名称")
    @JsonView({EditView.class})
    @JsonProperty("name")
    @Field("name")
    public String name;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "文档内容-前端渲染所需json")
    @JsonView({EditView.class})
    @JsonProperty("content")
    @Field("content")
    public String content;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "文档内容-markdown")
    @JsonView({EditView.class})
    @JsonProperty("sourceContent")
    @Field("sourceContent")
    public String sourceContent;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "版本号，目前定义是时间戳")
    @JsonView({EditView.class})
    @JsonProperty("version")
    @Field("version")
    public String version;
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "会话ID")
    @JsonView({EditView.class})
    @JsonProperty("conversationID")
    @Field("conversationID")
    public String conversationID;
    
     
    /**
     * avro type: *schema.StringField
     */
    @Schema(description = "应用ID")
    @JsonView({EditView.class})
    @JsonProperty("applicationID")
    @Field("applicationID")
    public String applicationID;
    
     
    /**
     * avro type: *schema.BoolField
     */
    @Schema(description = "是否临时文档")
    @JsonView({EditView.class})
    @JsonProperty("isTmp")
    @Field("isTmp")
    public Boolean isTmp;



    @AfterInit
    public void isTmpAfterInit(String userAccount, boolean isNew) {
        if (isNew && this.isTmp == null) {
            this.isTmp = true;
        }
    } 

}
