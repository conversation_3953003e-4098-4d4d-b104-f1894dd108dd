package com.polarizon.rag.chat.params;

import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.mongodb.core.query.Criteria;

import com.polarizon.gendo.common.utils.QueryUtil;

import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.DEAD;
import static com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag.LIVE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.containsAny;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.util.List;
import java.util.Objects;

import com.polarizon.rag.chat.ModelTypeEnum;
import com.polarizon.rag.chat.ModelSourceTypeEnum;
import com.polarizon.rag.chat.ModelEnableEnum;

import com.polarizon.rag.chat.ModelInfoDO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
import com.polarizon.gendo.common.bo.BaseParams;
import com.polarizon.gendo.common.utils.RegexUtil;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
public class ModelInfoDOParams extends BaseParams {
	
    @Parameter(description = "软删除标志")
    List<StatusFlag> statusFlag;
    
    @Parameter(description = "图标网址") 
    public List<String> logoUrlS3;
    
    @Parameter(description = "模糊查询参数: 图标网址") 
    String logoUrlS3Like;
    
    @Parameter(description = "模型类型") 
    public List<ModelTypeEnum> type;
    
    
    @Parameter(description = "推理模型展示的名称") 
    public List<String> displayName;
    
    @Parameter(description = "模糊查询参数: 推理模型展示的名称") 
    String displayNameLike;
    
    @Parameter(description = "推理模型名称，新版，直接用于展示和模型调用") 
    public List<String> name;
    
    @Parameter(description = "模糊查询参数: 推理模型名称，新版，直接用于展示和模型调用") 
    String nameLike;
    
    @Parameter(description = "描述") 
    public List<String> description;
    
    @Parameter(description = "模糊查询参数: 描述") 
    String descriptionLike;
    
    @Parameter(description = "apiKey") 
    public List<String> apiKey;
    
    @Parameter(description = "模糊查询参数: apiKey") 
    String apiKeyLike;
    
    @Parameter(description = "endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/") 
    public List<String> endpointUrl;
    
    @Parameter(description = "模糊查询参数: endpoint_url，去掉'v1'的端点地址,如：https://dashscope.aliyuncs.com/compatible-mode/，http://**************:20000/") 
    String endpointUrlLike;
    
    @Parameter(description = "最大输出token数(过时的配置)") 
    public List<Integer> maxTokens;
    
    
    @Parameter(description = "最大输出token数，必填") 
    public List<Integer> maxOutputTokens;
    
    
    @Parameter(description = "模型最大上下文长度，必填") 
    public List<Integer> contextLength;
    
    
    @Parameter(description = "模型最大输入长度") 
    public List<Integer> maxInputLength;
    
    
    @Parameter(description = "模型来源的类型") 
    public List<ModelSourceTypeEnum> sourceType;
    
    
    @Parameter(description = "模型使能: ENABLE-启用, DISABLE-停用") 
    public List<ModelEnableEnum> enable;
    
    
    @Parameter(description = "是否是默认的") 
    public List<Boolean> isDefault;
    
    
    @Parameter(description = "否是推理模型") 
    public List<Boolean> isReasoning;
    

    public void andCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCountCriteria(Criteria criteria) {
        andCriteria(criteria, null);
        andElementCriteria(criteria, null);
    }

    public void andCriteria(Criteria criteria, String prefix) {
        super.baseAndCriteria(criteria, prefix);

        if (isNotEmpty(logoUrlS3)) {
            criteria.and(concatDot(prefix, "logoUrlS3")).in(logoUrlS3);
        }
        if (isNotBlank(logoUrlS3Like)) {
            criteria.and(concatDot(prefix, "logoUrlS3")).regex(".*?" + RegexUtil.escapeRegexCharacter(logoUrlS3Like) + ".*");
        }

        if (isNotEmpty(type)) {
            criteria.and(concatDot(prefix, "type")).in(type);
        }

        if (isNotEmpty(displayName)) {
            criteria.and(concatDot(prefix, "displayName")).in(displayName);
        }
        if (isNotBlank(displayNameLike)) {
            criteria.and(concatDot(prefix, "displayName")).regex(".*?" + RegexUtil.escapeRegexCharacter(displayNameLike) + ".*");
        }

        if (isNotEmpty(name)) {
            criteria.and(concatDot(prefix, "name")).in(name);
        }
        if (isNotBlank(nameLike)) {
            criteria.and(concatDot(prefix, "name")).regex(".*?" + RegexUtil.escapeRegexCharacter(nameLike) + ".*");
        }

        if (isNotEmpty(description)) {
            criteria.and(concatDot(prefix, "description")).in(description);
        }
        if (isNotBlank(descriptionLike)) {
            criteria.and(concatDot(prefix, "description")).regex(".*?" + RegexUtil.escapeRegexCharacter(descriptionLike) + ".*");
        }

        if (isNotEmpty(apiKey)) {
            criteria.and(concatDot(prefix, "apiKey")).in(apiKey);
        }
        if (isNotBlank(apiKeyLike)) {
            criteria.and(concatDot(prefix, "apiKey")).regex(".*?" + RegexUtil.escapeRegexCharacter(apiKeyLike) + ".*");
        }

        if (isNotEmpty(endpointUrl)) {
            criteria.and(concatDot(prefix, "endpointUrl")).in(endpointUrl);
        }
        if (isNotBlank(endpointUrlLike)) {
            criteria.and(concatDot(prefix, "endpointUrl")).regex(".*?" + RegexUtil.escapeRegexCharacter(endpointUrlLike) + ".*");
        }

        if (isNotEmpty(maxTokens)) {
            criteria.and(concatDot(prefix, "maxTokens")).in(maxTokens);
        }

        if (isNotEmpty(maxOutputTokens)) {
            criteria.and(concatDot(prefix, "maxOutputTokens")).in(maxOutputTokens);
        }

        if (isNotEmpty(contextLength)) {
            criteria.and(concatDot(prefix, "contextLength")).in(contextLength);
        }

        if (isNotEmpty(maxInputLength)) {
            criteria.and(concatDot(prefix, "maxInputLength")).in(maxInputLength);
        }

        if (isNotEmpty(sourceType)) {
            criteria.and(concatDot(prefix, "sourceType")).in(sourceType);
        }

        if (isNotEmpty(enable)) {
            criteria.and(concatDot(prefix, "enable")).in(enable);
        }

        if (isNotEmpty(isDefault)) {
            criteria.and(concatDot(prefix, "isDefault")).in(isDefault);
        }

        if (isNotEmpty(isReasoning)) {
            criteria.and(concatDot(prefix, "isReasoning")).in(isReasoning);
        }
        
        if (isNotBlank(prefix)){
            andElementCriteria(criteria, prefix);
        }
    }

    private void andElementCriteria(Criteria criteria, String prefix){               
    }

    public Criteria getElementCountCriteria(String prefix) {
        Criteria criteria = new Criteria();               
        return criteria;
    }

    public boolean filter(ModelInfoDO obj) {
        if (!super.filter(obj)){
            return false;
        } 
        if (isNotEmpty(logoUrlS3) && !containsAny(logoUrlS3, obj.logoUrlS3)) {
            return false;
        }
        if (isNotBlank(this. logoUrlS3Like) && !obj.logoUrlS3.contains(this.logoUrlS3Like)) {
            return false;
        }
         
        if (isNotEmpty(type) && !containsAny(type, obj.type)) {
            return false;
        }
        
         
        if (isNotEmpty(displayName) && !containsAny(displayName, obj.displayName)) {
            return false;
        }
        if (isNotBlank(this. displayNameLike) && !obj.displayName.contains(this.displayNameLike)) {
            return false;
        }
         
        if (isNotEmpty(name) && !containsAny(name, obj.name)) {
            return false;
        }
        if (isNotBlank(this. nameLike) && !obj.name.contains(this.nameLike)) {
            return false;
        }
         
        if (isNotEmpty(description) && !containsAny(description, obj.description)) {
            return false;
        }
        if (isNotBlank(this. descriptionLike) && !obj.description.contains(this.descriptionLike)) {
            return false;
        }
         
        if (isNotEmpty(apiKey) && !containsAny(apiKey, obj.apiKey)) {
            return false;
        }
        if (isNotBlank(this. apiKeyLike) && !obj.apiKey.contains(this.apiKeyLike)) {
            return false;
        }
         
        if (isNotEmpty(endpointUrl) && !containsAny(endpointUrl, obj.endpointUrl)) {
            return false;
        }
        if (isNotBlank(this. endpointUrlLike) && !obj.endpointUrl.contains(this.endpointUrlLike)) {
            return false;
        }
         
        if (isNotEmpty(maxTokens) && !containsAny(maxTokens, obj.maxTokens)) {
            return false;
        }
        
         
        if (isNotEmpty(maxOutputTokens) && !containsAny(maxOutputTokens, obj.maxOutputTokens)) {
            return false;
        }
        
         
        if (isNotEmpty(contextLength) && !containsAny(contextLength, obj.contextLength)) {
            return false;
        }
        
         
        if (isNotEmpty(maxInputLength) && !containsAny(maxInputLength, obj.maxInputLength)) {
            return false;
        }
        
         
        if (isNotEmpty(sourceType) && !containsAny(sourceType, obj.sourceType)) {
            return false;
        }
        
         
        if (isNotEmpty(enable) && !containsAny(enable, obj.enable)) {
            return false;
        }
        
         
        if (isNotEmpty(isDefault) && !containsAny(isDefault, obj.isDefault)) {
            return false;
        }
        
         
        if (isNotEmpty(isReasoning) && !containsAny(isReasoning, obj.isReasoning)) {
            return false;
        }
        
        

        return true;
    }
}
