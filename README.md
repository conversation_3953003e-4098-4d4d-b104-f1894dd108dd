# user-authority-lib

## 简介

`user-authority-lib` 专为Spring Boot应用程序提供全面的用户权限控制和数据访问安全机制。该库通过JSON配置文件实现灵活的权限规则定义，支持MongoDB数据库操作的细粒度权限控制。

核心特性：
- 基于角色的资源操作权限控制
- 细粒度的数据访问过滤规则
- 自动对MongoDB查询和聚合操作应用权限过滤
- 支持复杂的关联数据权限检查
- 基于JWT的用户身份验证和会话管理
- 多租户数据隔离

## 系统架构

该库采用分层设计，通过多种机制无缝集成到Spring应用中：

```
┌───────────────────────────────────────────────────────────┐
│                     应用服务层                             │
└─────────────────────────────┬─────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                    MVC请求拦截层                           │
│                                                           │
│   AuthenticationInterceptor        WebMvcAuthConfig        │
└─────────────────────────────┬─────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                    数据访问拦截层                          │
│                                                           │
│   MongoPermissionAspect                                   │
└─────────────────────────────┬─────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                    权限决策引擎                            │
│                                                           │
│   PermissionManager    RuleLoader     DataRuleEvaluator   │
└─────────────────────────────┬─────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────┐
│                    模型和工具层                            │
│                                                           │
│   AuthorityConfig   Role   Resource   UserContext         │
└───────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. 权限模型 (Model层)

**AuthorityConfig**：
- 存储完整的权限配置，包括角色和资源定义
- 通过JSON文件加载，支持动态更新
- 定义了角色和资源之间的权限映射关系

**Role**：
- 定义角色及其可访问的资源和操作
- 可分配给用户，控制用户对资源的访问权限

**Resource**：
- 定义资源及其操作规则
- 支持MongoDB集合与资源的映射
- 为每种操作定义最细粒度未表字段维度的数据访问规则

**UserContext**：
- 存储当前用户的上下文信息
- 包含用户ID、账号、租户ID和角色列表
- 通过ThreadLocal实现线程隔离

### 2. 权限决策引擎 (Core层)

**PermissionManager**：
- 核心权限决策引擎
- 判断用户是否有执行操作的权限
- 生成数据访问过滤条件
- 支持文档级别的权限检查

**RuleLoader**：
- 加载权限配置文件
- 支持配置热更新
- 解析JSON配置为权限模型对象

**DataRuleEvaluator**：
- 解析和评估数据规则
- 处理规则中的变量替换
- 将规则转换为MongoDB查询条件

### 3. 数据访问拦截 (Mongo层)

**MongoPermissionAspect**：
- 通过AOP拦截MongoDB操作
- 对查询、聚合、插入、更新和删除操作应用权限规则
- 支持多种MongoDB API方法的拦截
- 自动将文档转换为Document对象进行权限检查

### 4. 用户认证 (Interceptor层)

**AuthenticationInterceptor**：
- 拦截HTTP请求，验证JWT令牌
- 从令牌中提取用户信息，设置当前用户上下文
- 支持令牌刷新

**WebMvcAuthConfig**：
- 注册认证拦截器
- 配置路径匹配规则

## 权限模型设计

### 角色层次结构

本库支持以下预定义角色，每个角色具有不同的权限范围：

1. **超级管理员 (superAdmin)**
   - 可访问所有资源和执行所有操作
   - 不受数据过滤规则限制

2. **管理员 (admin)**
   - 可访问大部分资源和执行管理操作
   - 部分资源可能有轻微的数据访问限制

3. **内部用户 (internalUser)**
   - 访问权限受到限制，通常只能访问所属租户的数据
   - 可以执行特定资源的创建和更新操作

4. **外部用户 (externalUser)**
   - 权限最受限制，只能访问与自己直接相关的资源
   - 通常只能执行读取和有限的创建操作

### 数据过滤规则

数据过滤规则定义了用户可以访问的数据范围，支持以下类型的规则：

1. **NULL规则**：表示无限制访问
2. **简单条件**：基于字段值的简单过滤（如 `{"status": "active"}`）
3. **用户变量**：使用当前用户上下文中的值（如 `{"createdBy": "${x-user-id}"}`）
4. **租户限制**：基于租户ID限制数据访问（如 `{"tenantId": "${x-tenant-id}"}`）
5. **复合条件**：使用逻辑操作符（如 `$and`, `$or`）组合多个条件
6. **关联数据**：通过MongoDB聚合表达式实现关联数据的权限控制

## 使用指南

### 1. 添加依赖

将库添加到您的Maven项目中：

```xml
<dependency>
    <groupId>com.polarizon.gendo3</groupId>
    <artifactId>user-authority-lib</artifactId>
    <version>{最新版本}</version>
</dependency>
```

### 2. 配置应用

在application.yml中添加配置：

```yaml
authority:
  enabled: true
  config-path: classpath:authority-config.json
  exclude-paths: /swagger-ui/**,/v3/api-docs/**
  
  jwt:
    secret: your-jwt-secret
    expire: 604800
    refresh: 86400
  
  mongo:
    enabled: true
```

### 3. 定义权限规则

创建authority-config.json文件，定义角色和资源权限：

```json
{
  "roles": {
    "superAdmin": {
      "description": "超级管理员",
      "resourcePermissions": {
        "KnowledgeBase": ["read", "create", "update", "delete"],
        "KnowledgeFile": ["read", "create", "update", "delete"]
      }
    },
    "admin": {
      "description": "管理员",
      "resourcePermissions": {
        "KnowledgeBase": ["read", "create", "update", "delete"],
        "KnowledgeFile": ["read", "create", "update", "delete"]
      }
    },
    "internalUser": {
      "description": "内部用户",
      "resourcePermissions": {
        "KnowledgeBase": ["read", "create", "update", "delete"],
        "KnowledgeFile": ["read", "create", "update", "delete"]
      }
    },
    "externalUser": {
      "description": "外部用户",
      "resourcePermissions": {
        "KnowledgeBase": ["read", "create", "update", "delete"],
        "KnowledgeFile": ["read", "create", "update", "delete"]
      }
    }
  },
  
  "resources": {
    "KnowledgeBase": {
      "type": "MONGO",
      "collection": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
      "operations": {
        "read": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {
              "$or": [
                {"isPublic": true},
                {"$and": [{"isPublic": false}, {"createdBy": "${x-user-id}"}]}
              ]
            },
            "externalUser": {
              "$or": [
                {"isPublic": true},
                {"$and": [{"isPublic": false}, {"createdBy": "${x-user-id}"}]}
              ]
            },
            "default": null
          }
        },
        "create": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {"isPublic": false},
            "externalUser": {"isPublic": false},
            "default": null
          }
        },
        "update": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {"isPublic": false, "createdBy": "${x-user-id}"},
            "externalUser": {"isPublic": false, "createdBy": "${x-user-id}"},
            "default": null
          }
        },
        "delete": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {"isPublic": false, "createdBy": "${x-user-id}"},
            "externalUser": {"isPublic": false, "createdBy": "${x-user-id}"},
            "default": null
          }
        }
      }
    },
    
    "KnowledgeFile": {
      "type": "MONGO",
      "collection": "default_com.polarizon.rag.kb.KnowledgeFileDO",
      "operations": {
        "read": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$or": [
                    {"$and": [{"$eq": ["$$kb.isPublic", false]}, {"$eq": ["$$kb.createdBy", "${x-user-id}"]}]},
                    {"$eq": ["$$kb.isPublic", true]}
                  ]}
                }
              }
            },
            "externalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "default": null
          }
        },
        "create": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$or": [
                    {"$and": [
                      {"$eq": ["$$kb.isPublic", false]},
                      {"$eq": ["$$kb.createdBy", "${x-user-id}"]}
                    ]},
                    {"$and": [
                      {"$eq": ["$$kb.isPublic", true]},
                      {"$or": [
                        {"$eq": ["$role", "superAdmin"]},
                        {"$eq": ["$role", "admin"]}
                      ]}
                    ]}
                  ]}
                }
              }
            },
            "externalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "default": null
          }
        },
        "update": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "externalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]},
                    {"$eq": ["$createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "default": null
          }
        },
        "delete": {
          "dataRules": {
            "superAdmin": null,
            "admin": null,
            "internalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "externalUser": {
              "$expr": {
                "$let": {
                  "vars": {
                    "kb": {
                      "$arrayElemAt": [
                        {"$lookup": {
                          "from": "default_com.polarizon.rag.kb.KnowledgeBaseDO",
                          "localField": "knowledgeBaseId",
                          "foreignField": "_id",
                          "as": "kb"
                        }}, 0
                      ]
                    }
                  },
                  "in": {"$and": [
                    {"$eq": ["$$kb.isPublic", false]},
                    {"$eq": ["$$kb.createdBy", "${x-user-id}"]},
                    {"$eq": ["$createdBy", "${x-user-id}"]}
                  ]}
                }
              }
            },
            "default": null
          }
        }
      }
    }
  }
} 
```

### 4. 启用自动配置

确保自动配置已启用（默认已启用）：

```java
@SpringBootApplication
@EnableAutoConfiguration
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 高级使用

### 1. 关联数据权限控制

支持通过MongoDB聚合表达式实现复杂的关联数据权限控制：

```json
"read": {
  "dataRules": {
    "user": {
      "$expr": {
        "$let": {
          "vars": {
            "product": {
              "$arrayElemAt": [
                {"$lookup": {
                  "from": "products",
                  "localField": "productId",
                  "foreignField": "_id",
                  "as": "product"
                }}, 0
              ]
            }
          },
          "in": {"$eq": ["$$product.ownerId", "${x-user-id}"]}
        }
      }
    }
  }
}
```

### 2. 自定义认证机制

如果需要自定义认证逻辑，可以扩展AuthenticationInterceptor：

```java
@Component
public class CustomAuthInterceptor extends AuthenticationInterceptor {
    
    public CustomAuthInterceptor(JwtUtil jwtUtil) {
        super(jwtUtil);
    }
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 自定义认证逻辑
        boolean result = super.preHandle(request, response, handler);
        
        // 额外的处理
        if (result) {
            // ...
        }
        
        return result;
    }
}
```

### 3. 编程式权限检查

在业务代码中可以直接使用PermissionManager进行权限检查：

```java
@Service
public class UserService {
    
    @Autowired
    private PermissionManager permissionManager;
    
    public void someBusinessOperation() {
        // 检查操作权限
        if (!permissionManager.hasOperationPermission("KnowledgeBase", "create")) {
            throw new AccessDeniedException("无创建知识库权限");
        }
        
        // 获取数据过滤条件
        Criteria criteria = permissionManager.getDataFilter("KnowledgeBase", "read");
        
        // 应用到查询
        Query query = new Query();
        query.addCriteria(criteria);
        
        // 执行查询
        // ...
    }
}
```

## 单元测试

本库实现了全面的单元测试覆盖，确保各个组件的功能正确性和稳定性。测试分为以下几个主要方向：

### 1. 核心权限管理测试

**测试文件**:
- `PermissionManagerTest.java`：测试权限管理器的基本功能
- `PermissionManagerCheckDocumentTest.java`：测试文档级别的权限检查
- `IntegrationPermissionTest.java`：测试权限系统的集成功能
- `DataRuleEvaluatorTest.java`：测试数据规则评估
- `RuleLoaderTest.java`：测试规则加载和解析

**测试要点**:
- 验证不同角色（超级管理员、管理员、内部用户、外部用户）的资源操作权限
- 测试数据规则评估和变量替换功能
- 验证文档级别的权限检查
- 测试复杂规则和多条件组合

**测试结论**:
- 权限管理器能正确检查用户对资源的操作权限
- 数据规则评估器能正确解析规则并替换上下文变量
- 文档级别的权限控制准确有效
- 系统能处理复杂规则和边界情况

### 2. MongoDB权限集成测试

**测试文件**:
- `MongoPermissionAspectIntegrationTest.java`：测试MongoDB权限切面的集成功能
- `MongoPermissionAspectConvertTest.java`：测试文档转换和权限过滤
- `MongoIntegrationTest.java`：MongoDB集成测试的基类

**测试要点**:
- 验证MongoDB查询的权限过滤
- 测试不同角色用户的数据访问限制
- 验证文档操作（更新、删除）的权限检查
- 测试复杂查询和聚合操作的权限控制

**测试结论**:
- MongoDB权限切面能正确拦截和过滤查询
- 不同角色的用户只能访问其权限范围内的数据
- 文档操作的权限检查能准确限制用户行为
- 复杂查询和聚合操作的权限控制有效

### 3. 模型对象测试

**测试文件**:
- `UserContextTest.java`：测试用户上下文对象
- `AuthorityConfigTest.java`：测试权限配置对象
- `OperationTest.java`：测试操作对象
- `ResourceTest.java`：测试资源对象
- `RoleTest.java`：测试角色对象

**测试要点**:
- 验证模型对象的属性访问和修改
- 测试模型对象的序列化和反序列化
- 验证模型对象的行为和方法

**测试结论**:
- 模型对象能正确存储和提供权限相关信息
- 序列化和反序列化功能正常
- 模型对象的方法和行为符合预期

### 4. 上下文管理测试

**测试文件**:
- `ContextHolderTest.java`：测试用户上下文的存储和访问

**测试要点**:
- 验证用户上下文的存储和获取
- 测试多线程环境下的上下文隔离
- 验证上下文清理功能

**测试结论**:
- 用户上下文的存储和获取功能正常
- 多线程环境下的上下文隔离有效，确保线程安全
- 上下文清理功能正常工作，避免内存泄漏

## 性能考量

1. **查询性能**：权限过滤会在查询前添加条件，对于复杂的聚合表达式可能有一定性能影响，建议对关键字段建立索引。

2. **缓存策略**：考虑对频繁使用的权限检查结果进行缓存，减少重复计算。

3. **规则复杂度**：过于复杂的规则可能影响性能，建议适当简化规则设计。

4. **文档转换开销**：文档级别的权限检查需要将对象转换为Document，可能带来额外开销。

## 注意事项

1. **JWT安全**：确保JWT密钥的安全性，建议在生产环境中使用足够复杂的密钥。

2. **配置文件安全**：权限配置文件包含敏感信息，确保其安全性，考虑加密存储或使用安全的配置中心。

3. **异常处理**：权限验证失败会抛出SecurityException，确保适当处理这些异常。

4. **多租户数据隔离（智研项目中没有）**：确保正确配置租户相关的数据过滤规则，防止跨租户数据泄露。

5. **升级兼容性**：升级库版本时注意权限配置的兼容性，可能需要更新配置文件。

## 扩展开发

### 1. 添加新的资源类型

要支持新的资源类型，需要：

1. 在Resource类中添加新的资源类型
2. 实现相应的权限检查逻辑
3. 添加相应的AOP拦截

### 2. 自定义规则加载器

可以扩展RuleLoader以支持从不同来源加载规则：

```java
@Component
public class DatabaseRuleLoader extends RuleLoader {
    
    @Autowired
    private RuleRepository ruleRepository;
    
    @Override
    protected void loadConfig() throws IOException {
        // 从数据库加载规则
        String configJson = ruleRepository.findLatestConfig();
        authorityConfig = objectMapper.readValue(configJson, AuthorityConfig.class);
    }
}
```

### 3. 添加缓存支持

可以为权限检查添加缓存支持，提高性能：

```java
@Component
public class CachedPermissionManager extends PermissionManager {
    
    private final Cache<String, Boolean> operationPermissionCache;
    private final Cache<String, Criteria> dataFilterCache;
    
    public CachedPermissionManager(RuleLoader ruleLoader, DataRuleEvaluator ruleEvaluator) {
        super(ruleLoader, ruleEvaluator);
        
        this.operationPermissionCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();
            
        this.dataFilterCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();
    }
    
    @Override
    public boolean hasOperationPermission(String resourceId, String operation) {
        UserContext userContext = ContextHolder.getCurrentUser();
        if (userContext == null) {
            return false;
        }
        
        String cacheKey = userContext.getUserId() + ":" + resourceId + ":" + operation;
        return operationPermissionCache.get(cacheKey, k -> super.hasOperationPermission(resourceId, operation));
    }
    
    // ...其他方法缓存实现
}
```

### 4. 扩展用户上下文

如果需要在用户上下文中存储更多信息，可以扩展UserContext类：

```java
public class ExtendedUserContext extends UserContext {
    
    private List<String> permissions;
    private Map<String, String> attributes;
    
    // 构造函数、getter和setter
    
    public static class Builder extends UserContext.Builder {
        private List<String> permissions;
        private Map<String, String> attributes;
        
        public Builder permissions(List<String> permissions) {
            this.permissions = permissions;
            return this;
        }
        
        public Builder attributes(Map<String, String> attributes) {
            this.attributes = attributes;
            return this;
        }
        
        @Override
        public ExtendedUserContext build() {
            ExtendedUserContext context = new ExtendedUserContext();
            context.setUserId(this.userId);
            context.setAccount(this.account);
            context.setTenantId(this.tenantId);
            context.setRoleEntities(this.roleEntities);
            context.permissions = this.permissions;
            context.attributes = this.attributes;
            return context;
        }
    }
}
```

## 实际应用场景

### 1. 知识库管理系统

在知识库管理系统中，不同角色的用户对知识库和文件有不同的访问权限：

- 管理员可以查看和管理所有知识库
- 内部用户可以查看本租户的所有知识库，但只能修改自己创建的或公共知识库
- 外部用户只能查看和修改自己创建的知识库

### 2. 多租户SaaS平台

在多租户SaaS平台中，确保数据隔离和安全：

- 各租户的数据严格隔离
- 同一租户内不同角色的用户访问权限不同
- 支持跨租户授权访问特定资源

### 3. 协同办公系统

在协同办公系统中，实现灵活的文档共享和权限控制：

- 文档所有者可以完全控制文档
- 可以设置共享权限，允许特定用户或角色访问
- 支持组织结构层级的权限继承

---
