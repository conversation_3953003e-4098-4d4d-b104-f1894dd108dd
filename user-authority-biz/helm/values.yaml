
replicaCount: 1
image:
  repository: polarizon-rag/user-authority
  pullPolicy: IfNotPresent
  tag: 1.14.0-zhiku-beta.12-alpha
service:
  port: 20202
  debugPort: 20203
enableDebug: true

global:
  configMap:
    JAVA_OPTS: "-Xms1024M -Xmx1024M -Xss512K -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=256M -XX:+HeapDumpOnOutOfMemoryError"
    JAVA_DEBUG_OPTIONS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:20203"
    MINIO_ENDPOINT: "http://minio:9000"
    MINIO_ACCESSKEY: root
    MINIO_SECRETKEY: polarise
    DOCKER_API_URL: "unix:///var/run/docker.sock"
    REGISTRY_URL: "https://**************:30443/api/v2.0"
    REGISTRY_ADDRESS: "**************:30443"
    SPRING_RABBITMQ_HOST: "rabbitmq-headless"
    SPRING_RABBITMQ_PORT: 5672
    SPRING_RABBITMQ_USERNAME: "guest"
    SPRING_RABBITMQ_PASSWORD: "guest"
    SPRING_RABBITMQ_API_PORT: 15672
    SPRING_REDIS_LETTUCE_CLUSTER_REFRESH_PERIOD: 10s
    SPRING_REDIS_LETTUCE_CLUSTER_REFRESH_ADAPTIVE: true
    LOG_EXPIRE: 90
    SPRING_DATA_MONGODB_URI: "mongodb://root:<EMAIL>:27017,mongodb-1.mongodb-headless.middleware.svc.cluster.local:27017"
    timezone: Asia/Shanghai
    SPRING_DATA_REDIS_CLUSTER_NODES: redis-cluster-headless:6379
    MONGOCK_ENABLE: true
    SPRING_PROFILES_ACTIVE: prod,zhiyan
  image:
    registry: hub.polarise.cn
  # nodeSelector:
  #   node-role: gendo3-api-gateway
  traefik:
    ingressroute:
      enabled: true

