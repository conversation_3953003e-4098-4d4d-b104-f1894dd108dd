package com.polarizon.gendo.sg.authority.service;

import com.polarizon.gendo.sg.authority.bean.bo.GisLayoutBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class GisLayoutService {
    @Autowired
    private MongoTemplate mongoTemplate;

    public List<GisLayoutBO> updateGisLayout(GisLayoutBO gisLayoutBO) {
        Query query = Query.query(Criteria.where("account").is(gisLayoutBO.getAccount())
                .and("layoutIndex").is(gisLayoutBO.getLayoutIndex()));
        GisLayoutBO one = mongoTemplate.findOne(query, GisLayoutBO.class);
        if (Objects.nonNull(one)) {
            Update update = new Update();
            update.set("showFlag", gisLayoutBO.getShowFlag());
            mongoTemplate.findAndModify(query, update, GisLayoutBO.class);
        }else {
            mongoTemplate.save(gisLayoutBO);
        }
        return selectGisLayoutList(gisLayoutBO.getAccount());
    }

    public List<GisLayoutBO> selectGisLayoutList(String account) {
        return mongoTemplate.find(Query.query(Criteria.where("account").is(account)), GisLayoutBO.class);
    }
}
