package com.polarizon.gendo.sg.authority.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import com.polarizon.common.exception.BusinessException;
import com.polarizon.common.exception.enums.CommonResponseEnum;
import com.polarizon.gendo.sg.authority.bean.OrganizationEntity;
import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.bean.UserProcessEntity;
import com.polarizon.gendo.sg.authority.bean.form.UserProcessFormBean;
import com.polarizon.gendo.sg.authority.enums.ProcessStatus;
import com.polarizon.gendo.sg.authority.enums.UserResponseEnum;
import com.polarizon.gendo.sg.authority.enums.UserType;
import com.polarizon.gendo.sg.common.utils.query.criteria.LambdaCriteria;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.polarizon.common.bean.bo.AbstractBaseBO.Constants.*;
import static com.polarizon.gendo.sg.authority.bean.RoleEntity.Constants.FIELD_ENABLE;

/**
 * 用户审批
 */
@Slf4j
@Service
public class UserProcessService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private UserManagerService userManagerService;
    @Autowired
    private OrganizationService organizationService;

    public UserProcessEntity register(UserProcessEntity source) {
        //重复性校验
        String msg = userManagerService.registValid(null, source.getAccount(), source.getEmail(), source.getUserCode(), source.getPhone(), source.getUserIdentity(), UserType.TERMINAL.getValue());
        CommonResponseEnum.ADD_ERROR_REASON.assertTrue(StringUtils.isEmpty(msg), msg);
        //校验openID是否已绑定
        UserEntity userEntity = userManagerService.get(null, null, source.getOpenId());
        if (userEntity != null) {
            throw new RuntimeException("公众号已绑定账号[" + userEntity.getAccount() + "]，请先解绑！");
        }
        //查询该账号是否处于审批中
        Query query = Query.query(LambdaCriteria.<UserProcessEntity>builder()
            .eq(UserProcessEntity::getAccount, source.getAccount())
            .eq(UserProcessEntity::getProcess, ProcessStatus.charging)
            .eq(UserProcessEntity::getStatusFlag, AbstractBaseBO.StatusFlag.LIVE)
            .criteria());
        CommonResponseEnum.ADD_ERROR_REASON.assertTrue(!mongoTemplate.exists(query, UserProcessEntity.class), "审核中的账号不能再次注册");
        //添加审批记录
        source.init(null, true);
        source.setProcess(ProcessStatus.charging);
        UserProcessEntity result = mongoTemplate.insert(source);
        return result;
    }

    public Iterable<UserProcessEntity> page(Pageable pageable, UserProcessFormBean userProcessFormBean) {
        //组织隔离
        List<String> orgIds = Optional.ofNullable(userProcessFormBean.getOrganizationIds()).orElse(Lists.newArrayList()).stream().collect(Collectors.toList());
        List<String> childrenIds = organizationService.getChildrenIds(orgIds);
        orgIds = CollectionUtils.isEmpty(userProcessFormBean.getOrgs()) ? childrenIds : userProcessFormBean.getOrgs();
        Criteria criteria = LambdaCriteria.<UserProcessEntity>builder()
            .in(CollectionUtil.isNotEmpty(userProcessFormBean.getProcesses()), UserProcessEntity::getProcess, userProcessFormBean.getProcesses())
            .in(CollectionUtil.isNotEmpty(userProcessFormBean.getUserIdentities()), UserProcessEntity::getUserIdentity, userProcessFormBean.getUserIdentities())
            .in(CollectionUtil.isNotEmpty(userProcessFormBean.getAccounts()), UserProcessEntity::getAccount, userProcessFormBean.getAccounts())
            .in(CollectionUtil.isNotEmpty(userProcessFormBean.getNames()), UserProcessEntity::getName, userProcessFormBean.getNames())
            //.regex(StringUtils.isNotEmpty(userProcessFormBean.getName()), UserProcessEntity::getName, userProcessFormBean.getName())
            .regex(StringUtils.isNotEmpty(userProcessFormBean.getUserCode()), UserProcessEntity::getUserCode, userProcessFormBean.getUserCode())
            .regex(StringUtils.isNotEmpty(userProcessFormBean.getEmail()), UserProcessEntity::getEmail, userProcessFormBean.getEmail())
            .regex(StringUtils.isNotEmpty(userProcessFormBean.getPhone()), UserProcessEntity::getPhone, userProcessFormBean.getPhone())
            .between(userProcessFormBean.getStartCreateTime() != null && userProcessFormBean.getEndCreateTime() != null
                , UserProcessEntity::getCreateTime, userProcessFormBean.getStartCreateTime(), userProcessFormBean.getEndCreateTime())
            .ge(userProcessFormBean.getStartCreateTime() != null && userProcessFormBean.getEndCreateTime() == null
                , UserProcessEntity::getCreateTime, userProcessFormBean.getStartCreateTime())
            .le(userProcessFormBean.getStartCreateTime() == null && userProcessFormBean.getEndCreateTime() != null
                , UserProcessEntity::getCreateTime, userProcessFormBean.getEndCreateTime())
            .in(CollectionUtil.isNotEmpty(orgIds),
                UserProcessEntity::getOrganizationList,
                Stream.ofNullable(orgIds)
                    .flatMap(orgList -> orgList.stream())
                    .map(org -> new ObjectId(org)).collect(Collectors.toList()))
            .criteria();
        Query query = new Query();
        query.addCriteria(criteria);
        if (pageable != null) {
            long count = mongoTemplate.count(query, UserProcessEntity.class);
            query.with(pageable).allowDiskUse(true);
            List<UserProcessEntity> processEntityList = mongoTemplate.find(query, UserProcessEntity.class);
            return new PageImpl<>(processEntityList, pageable, count);
        } else {
            Sort sort = Sort.by("createTime").descending();
            query.with(sort);
            return mongoTemplate.find(query, UserProcessEntity.class);
        }
    }

    public Iterable<UserProcessEntity> getByOpenid(@SpringQueryMap Pageable pageable, String openId) {
        Criteria criteria = new LambdaCriteria<UserProcessEntity>().eq(UserProcessEntity::getOpenId, openId).criteria();
        Query query = new Query();
        query.addCriteria(criteria);
        List<UserProcessEntity> userProcessEntities;
        if (pageable.isPaged()) {
            long count = mongoTemplate.count(query, UserProcessEntity.class);
            query.with(pageable).allowDiskUse(true);
            userProcessEntities = mongoTemplate.find(query, UserProcessEntity.class);
            return new PageImpl<>(userProcessEntities, pageable, count);
        } else {
            Sort sort = Sort.by("createTime").descending();
            query.with(sort);
            userProcessEntities = mongoTemplate.find(query, UserProcessEntity.class);
            return userProcessEntities;
        }
    }

    public Map<ProcessStatus, Integer> count(List<String> organizationIds) {
        Map<ProcessStatus, Integer> result = Maps.newHashMap();
        List<UserProcessEntity> processEntities;
        if (CollectionUtils.isEmpty(organizationIds)) {
            processEntities = mongoTemplate.findAll(UserProcessEntity.class);
        } else {
            List<String> childrenIds = organizationService.getChildrenIds(organizationIds);
            Criteria criteria = LambdaCriteria.<UserProcessEntity>builder().in(!CollectionUtils.isEmpty(organizationIds),
                UserProcessEntity::getOrganizationList,
                Stream.ofNullable(childrenIds)
                    .flatMap(orgList -> orgList.stream())
                    .map(org -> new ObjectId(org))
                    .collect(Collectors.toList())).criteria();
            processEntities = mongoTemplate.find(Query.query(criteria), UserProcessEntity.class);
        }
        Map<ProcessStatus, List<UserProcessEntity>> statusListMap = processEntities.stream().collect(Collectors.groupingBy(UserProcessEntity::getProcess));
        for (ProcessStatus processStatus : ProcessStatus.values()) {
            int count = statusListMap.get(processStatus) == null ? 0 : statusListMap.get(processStatus).size();
            result.put(processStatus, count);
        }
        return result;
    }

    /**
     * 获取默认组织
     *
     * @return
     */
    public OrganizationEntity getDefaultOrg() {
        Query query = new Query();
        Criteria criteria = Criteria.where(OrganizationEntity.Constants.FIELD_PARENT_ID).is(null)
            .orOperator(Criteria.where(FIELD_STATUS_FLAG).is(AbstractBaseBO.StatusFlag.LIVE)
                , Criteria.where(FIELD_STATUS_FLAG).is(null));
        query.addCriteria(criteria);
        query.with(Sort.by(FIELD_CREATE_TIME).ascending());
        return mongoTemplate.findOne(query, OrganizationEntity.class);
    }

    /**
     * 获取默认角色
     *
     * @return
     */
    public RoleEntity getDefaultRole() {
        Query query = new Query();
        Criteria criteria = Criteria.where(FIELD_CREATE_BY).ne("init")
            .and(FIELD_STATUS_FLAG).is(AbstractBaseBO.StatusFlag.LIVE)
            .and(FIELD_ENABLE).is(true);
        query.addCriteria(criteria);
        query.with(Sort.by(FIELD_CREATE_TIME).ascending());
        return mongoTemplate.findOne(query, RoleEntity.class);
    }
}
