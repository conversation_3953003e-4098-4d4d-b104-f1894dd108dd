package com.polarizon.gendo.sg.authority.controller;

import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.api.SystemControllerInterface;
import com.polarizon.gendo.sg.authority.bean.TenantEntity;
import com.polarizon.gendo.sg.authority.bean.bo.SystemLogBO;
import com.polarizon.gendo.sg.authority.bean.bo.SystemLogoBO;
import com.polarizon.gendo.sg.authority.bean.form.SystemLogBean;
import com.polarizon.gendo.sg.authority.service.OrganizationService;
import com.polarizon.gendo.sg.authority.service.SystemService;
import com.polarizon.gendo.sg.authority.service.TenantManagerService;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

import static com.polarizon.common.utils.Constants.HEADER_ACCOUNT_KEY;
import static com.polarizon.common.utils.Constants.HEADER_TENANT_ID_KEY;

@Slf4j
@RestController
@RequestMapping("/system")
@CrossOrigin
public class SystemController implements SystemControllerInterface {
    @Autowired
    private SystemService systemLogService;
    @Autowired
    private TenantManagerService tenantManagerService;

    @Override
    public ResultDTO<SystemLogBO> insertLog(SystemLogBO systemLogBO) {
        return ResultDTO.ok("新增成功", systemLogService.insert(Arrays.asList(systemLogBO)).get(0));
    }

    @Override
    public ResultDTO<List<SystemLogBO>> insertLogs(List<SystemLogBO> systemLogBOs) {
        return ResultDTO.ok("新增成功", systemLogService.insert(systemLogBOs));
    }

    @Override
    public ResultDTO<Iterable<SystemLogBO>> findLogs(SystemLogBean systemLogBean, Pageable pageable) {
        return ResultDTO.ok("查询成功", systemLogService.pageLog(systemLogBean, pageable));
    }

    @Override
    public ResultDTO<SystemLogoBO> insertLogo(String tenantId, String account, SystemLogoBO systemLogoBO) {
        systemLogoBO.setTenantId(tenantId);
        return ResultDTO.ok("新增成功", systemLogService.replaceLogo(systemLogoBO));
    }

    @Override
    public ResultDTO<SystemLogoBO> findOne(String tenantId, String account) {
        SystemLogoBO result = null;
        if (StringUtils.isNotEmpty(tenantId)){
            result = systemLogService.findOne(tenantId);
            return ResultDTO.ok("查询成功", result);
        } 
        
        // 先判断是否为单组织
        List<TenantEntity> allTenant = tenantManagerService.list();
        if (allTenant!=null && allTenant.size()==1){
            result = systemLogService.findOne(allTenant.get(0).getId());
            return ResultDTO.ok("查询成功", result);
        }
        return ResultDTO.ok("查询失败，请使用平台内置数据", null);
    }


    @ApiOperation(value = "查询logo, 图片以base64返回", notes = "图片以base64返回")
    @GetMapping("/logo/base64")
    public ResultDTO<String> logoBase64(
            @RequestHeader(required = false, value = HEADER_TENANT_ID_KEY) @ApiParam(value = "租户id") String tenantId,
            @RequestHeader(required = false, value = HEADER_ACCOUNT_KEY) @ApiParam(value = "用户账号") String account) {
        return ResultDTO.ok("导出成功");
    }

    @ApiOperation(value = "日志导出", notes = "日志导出")
    @PostMapping("/log/export")
    public ResultDTO<String> exportLog(@RequestBody SystemLogBean systemLogBean, Pageable pageable) {
        try {
            systemLogService.exportLog(systemLogBean, pageable);
        } catch (Exception e) {
            log.error("导出日志失败", e);
            return ResultDTO.ok("导出失败");
        }
        return ResultDTO.ok("导出成功");
    }

}
