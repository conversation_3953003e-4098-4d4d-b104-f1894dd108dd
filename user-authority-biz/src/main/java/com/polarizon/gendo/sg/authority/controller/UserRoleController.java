package com.polarizon.gendo.sg.authority.controller;

import com.google.common.collect.Maps;
import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.api.UserRoleControllerInterface;
import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.enums.UserRoleResponseEnum;
import com.polarizon.gendo.sg.authority.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 用户角色关联控制器
 */
@Slf4j
@RestController
@RequestMapping("/userRole")
@CrossOrigin
public class UserRoleController implements UserRoleControllerInterface {
    @Autowired
    private UserRoleService userRoleService;

    @Override
    public ResultDTO<Object> association(String account, String userId, List<String> roleIds) {
        //判断入参
        UserRoleResponseEnum.ASSOCIATION_ERROR.assertNotEmpty(userId);
        UserRoleResponseEnum.ASSOCIATION_ERROR.assertNotEmpty(roleIds);

        //用户关联角色
        userRoleService.association(userId, roleIds, account);
        return ResultDTO.ok("用户关联角色成功");
    }

    @Override
    public ResultDTO<Map<String, List<UserEntity>>> listUser(List<String> roleIds) {
        Map<String, List<UserEntity>> result = userRoleService.listUser(roleIds);
        if (CollectionUtils.isEmpty(result)) {
            return ResultDTO.ok("没有查询到角色关联的用户", Maps.newHashMap());
        }
        return ResultDTO.ok("查询角色关联的用户成功", result);
    }

    @Override
    public ResultDTO<Map<String, List<RoleEntity>>> listRole(List<String> userIds) {
        Map<String, List<RoleEntity>> result = userRoleService.listRole(userIds);
        if (CollectionUtils.isEmpty(result)) {
            return ResultDTO.ok("没有查询到用户关联的角色", Maps.newHashMap());
        }
        return ResultDTO.ok("查询用户关联的角色成功", result);
    }

    @Override
    public ResultDTO<Object> removeRoleAssociation(List<String> userIds, List<String> roleIds) {
        userRoleService.removeRoleAssociation(userIds, roleIds);
        return ResultDTO.ok("移除角色关联成功");
    }
}
