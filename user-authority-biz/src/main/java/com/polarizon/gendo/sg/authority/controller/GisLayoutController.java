package com.polarizon.gendo.sg.authority.controller;

import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.api.GisLayoutControllerInterface;
import com.polarizon.gendo.sg.authority.bean.bo.GisLayoutBO;
import com.polarizon.gendo.sg.authority.service.GisLayoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/gisLayout")
@CrossOrigin
public class GisLayoutController implements GisLayoutControllerInterface {

    @Autowired
    private GisLayoutService gisLayoutService;

    @Override
    public ResultDTO<List<GisLayoutBO>> updateGisLayout(String tenantId, String account, GisLayoutBO gisLayoutBO) {
        gisLayoutBO.setAccount(account);
        List<GisLayoutBO> result = gisLayoutService.updateGisLayout(gisLayoutBO);
        return ResultDTO.ok("更新成功", result);
    }

    @Override
    public ResultDTO<List<GisLayoutBO>> selectGisLayoutList(String tenantId, String account) {
        List<GisLayoutBO> result = gisLayoutService.selectGisLayoutList(account);
        return ResultDTO.ok("查询成功", result);
    }


}
