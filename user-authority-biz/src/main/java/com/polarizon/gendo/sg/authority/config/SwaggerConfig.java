package com.polarizon.gendo.sg.authority.config;

import com.fasterxml.classmate.TypeResolver;
import com.polarizon.common.api.BasicSwaggerConfig;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.schema.AlternateTypeRuleConvention;
import springfox.documentation.schema.ScalarType;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.List;

import static com.polarizon.common.utils.Constants.HEADER_TOKEN_KEY;
import static java.util.Collections.singletonList;


@Slf4j
@Data
@Accessors(fluent = true)
@Configuration
@EnableOpenApi
@ConditionalOnProperty(prefix = "project.swagger", name = "enable", havingValue = "true")
public class SwaggerConfig extends BasicSwaggerConfig {

    @Value("${project.name}")
    private String title;

    @Value("${project.version}")
    private String version;

    @Value("${project.description}")
    private String description;

    @Value("${project.home}")
    private String url;

    @Value("${project.organization}")
    private String name;

    @Bean
    @ConditionalOnMissingBean(Docket.class)
    public Docket createRestApi() {
        List<RequestParameter> requestParameters = Arrays.asList(new RequestParameterBuilder()
                // 不能叫Authorization
                .name(HEADER_TOKEN_KEY)
                .description(HEADER_TOKEN_KEY)
                .in(ParameterType.HEADER)
                .required(true)
                .query(q -> q.model(m -> m.scalarModel(ScalarType.STRING)))
                .build()
            , new RequestParameterBuilder()
                .name("X-Organization-Id")
                .description("X-Organization-Id")
                .in(ParameterType.HEADER)
                .required(false)
                .query(q -> q.model(m -> m.scalarModel(ScalarType.STRING)))
                .build());
        return createDocket().globalRequestParameters(requestParameters);
    }

    @Bean
    public AlternateTypeRuleConvention pageableConvention(final TypeResolver resolver) {
        return createPageableConvention(resolver);
    }

}