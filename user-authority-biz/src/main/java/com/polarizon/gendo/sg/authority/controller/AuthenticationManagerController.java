package com.polarizon.gendo.sg.authority.controller;

import com.google.common.collect.Lists;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import com.polarizon.common.bean.dto.ResultDTO;
import com.polarizon.gendo.sg.authority.api.AuthenticationManagerControllerInterface;
import com.polarizon.gendo.sg.authority.api.UserManagerControllerInterface;
import com.polarizon.gendo.sg.authority.bean.OrganizationEntity;
import com.polarizon.gendo.sg.authority.bean.RoleEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.bean.dto.EmailPasswordResetDTO;
import com.polarizon.gendo.sg.authority.bean.dto.LoginResultDTO;
import com.polarizon.gendo.sg.authority.bean.dto.MultiFactorAuthDTO;
import com.polarizon.gendo.sg.authority.bean.dto.PermissionsTreeDTO;
import com.polarizon.gendo.sg.authority.bean.dto.PhonePasswordForgottenPreDTO;
import com.polarizon.gendo.sg.authority.bean.dto.PhonePasswordForgottenUpdateDTO;
import com.polarizon.gendo.sg.authority.bean.dto.PhonePasswordResetDTO;
import com.polarizon.gendo.sg.authority.bean.dto.UserInfoUpdateDTO;
import com.polarizon.gendo.sg.authority.bean.form.UserAuthEmailFormBean;
import com.polarizon.gendo.sg.authority.bean.form.UserAuthFormBean;
import com.polarizon.gendo.sg.authority.bean.form.UserQueryFormBean;
import com.polarizon.gendo.sg.authority.enums.PermissionsType;
import com.polarizon.gendo.sg.authority.mapper.UserMapper;
import com.polarizon.gendo.sg.authority.service.AuthenticationManagerService;
import com.polarizon.gendo.sg.authority.service.OrganizationService;
import com.polarizon.gendo.sg.authority.service.PermissionsManagerService;
import com.polarizon.gendo.sg.authority.service.UserManagerService;
import com.polarizon.gendo.sg.authority.utils.SystemUtil;
import com.polarizon.gendo.sg.common.utils.VerificationCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.polarizon.common.bean.dto.ResultDTO.Constants.FAILURE_DATA_DISABLE;
import static com.polarizon.common.bean.dto.ResultDTO.Constants.FAILURE_DATA_ERROR;
import static com.polarizon.common.bean.dto.ResultDTO.Constants.FAILURE_DATA_NOT_EXIST;
import static com.polarizon.common.bean.dto.ResultDTO.Constants.SUCCESSFUL_MSG;
import static com.polarizon.common.utils.Constants.RESULT_FLAG;
import static com.polarizon.gendo.sg.authority.constent.RedisConstants.REDIS_MAIL_VERIFY_CODE_PREFIX;
import static com.polarizon.gendo.sg.authority.constent.RedisConstants.REDIS_SMS_VERIFY_CODE_PREFIX;
import static com.polarizon.gendo.sg.authority.enums.UserResponseEnum.ACCOUNT_NONEXISTENT_ERROR;

/**
 * 授权管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/authentication/manager")
@CrossOrigin
public class AuthenticationManagerController implements AuthenticationManagerControllerInterface {
    @Resource
    private AuthenticationManagerService authenticationManagerService;
    @Autowired
    private UserManagerService userManagerService;
    @Autowired
    private PermissionsManagerService permissionsManagerService;
    @Autowired
    private OrganizationService organizationService;
    @Resource(name = "stringKeyValueRedisTemplateCustom")
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private UserManagerControllerInterface userManagerControllerInterface;

    @Override
    public ResultDTO<LoginResultDTO> login(UserAuthFormBean userAuthBO) {
        return authenticationManagerService.login(userAuthBO);
    }

    @Override
    public ResultDTO<Object> passwordUpdate(UserAuthFormBean userAuthBO) {
        long result = authenticationManagerService.passwordUpdate(userAuthBO);
        if (result == FAILURE_DATA_NOT_EXIST) {
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "密码修改失败，用户不存在");
        }
        if (result == FAILURE_DATA_ERROR) {
            return ResultDTO.error(FAILURE_DATA_ERROR, "密码修改失败，原密码错误");
        }
        if (result == FAILURE_DATA_DISABLE) {
            return ResultDTO.error(FAILURE_DATA_DISABLE, "密码修改失败，新密码不可与原来密码一样");
        }
        if (result == RESULT_FLAG) {
            return ResultDTO.error("密码修改失败");
        }
        return ResultDTO.ok("密码修改成功");
    }

    @Override
    public ResultDTO passwordUpdateByEmail(EmailPasswordResetDTO emailPasswordResetDTO) {
        // 验证用户是否存在
        UserEntity userEntity = userManagerService.get(null, emailPasswordResetDTO.getAccount(), null);
        if (userEntity == null || !userEntity.getEnable() || !AbstractBaseBO.StatusFlag.LIVE.equals(userEntity.getStatusFlag())) {
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "密码修改失败，用户不存在或已停用");
        }

        // 验证邮箱是否匹配
        if (!emailPasswordResetDTO.getEmail().equals(userEntity.getEmail())) {
            return ResultDTO.error(FAILURE_DATA_ERROR, "密码修改失败，邮箱不匹配");
        }

        // 验证邮箱验证码
        Object redisVerifyCode = redisTemplate.opsForValue().get(REDIS_MAIL_VERIFY_CODE_PREFIX + emailPasswordResetDTO.getEmail());
        if (redisVerifyCode == null || !VerificationCodeUtil.compare(emailPasswordResetDTO.getVerifyCode(), redisVerifyCode.toString())) {
            return ResultDTO.error("密码修改失败，验证码错误或已失效");
        }

        // 更新密码
        userEntity.setPassword(emailPasswordResetDTO.getPassword());
        UserEntity updatedUser = userManagerService.save(userEntity);
        if (updatedUser == null) {
            return ResultDTO.error("密码修改失败");
        }

        // 删除验证码
        redisTemplate.delete(REDIS_MAIL_VERIFY_CODE_PREFIX + emailPasswordResetDTO.getEmail());
        return ResultDTO.ok("密码修改成功");
    }

    @Override
    public ResultDTO<?> passwordUpdateByPhone(PhonePasswordResetDTO phonePasswordResetDTO) {
        // 验证用户是否存在
        UserEntity userEntity = userManagerService.getByPhone(null, phonePasswordResetDTO.getPhone());
        if (userEntity == null || !userEntity.getEnable() || !AbstractBaseBO.StatusFlag.LIVE.equals(userEntity.getStatusFlag())) {
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "密码修改失败，用户不存在或已停用");
        }
        // 验证账号是否匹配
        if (StringUtils.isNotBlank(phonePasswordResetDTO.getAccount())) {
            if (!StringUtils.equals(phonePasswordResetDTO.getAccount(), userEntity.getAccount())) {
                return ResultDTO.error(FAILURE_DATA_ERROR, "密码修改失败，账号不匹配");
            }
        }
        // 验证手机号是否匹配
        if (!StringUtils.equals(userEntity.getPhone(), phonePasswordResetDTO.getPhone())) {
            return ResultDTO.error(FAILURE_DATA_ERROR, "密码修改失败，手机号不匹配");
        }
        // 验证手机验证码
        Object redisVerifyCode = redisTemplate.opsForValue().get(REDIS_SMS_VERIFY_CODE_PREFIX + phonePasswordResetDTO.getPhone());
        if (redisVerifyCode == null || !VerificationCodeUtil.compare(phonePasswordResetDTO.getVerifyCode(), redisVerifyCode.toString())) {
            return ResultDTO.error("密码修改失败，验证码错误或已失效");
        }
        // 校验密码
        String newPassword = authenticationManagerService.decoded(phonePasswordResetDTO.getPassword());
        if (!authenticationManagerService.validPassword(newPassword)) {
            return ResultDTO.error("密码修改失败，新密码格式有误");
        }
        // 检查新密码是否与旧密码相同
        if (StringUtils.equals(userEntity.getPassword(), newPassword)) {
            // 删除验证码
            redisTemplate.delete(REDIS_SMS_VERIFY_CODE_PREFIX + phonePasswordResetDTO.getPhone());
            return ResultDTO.error(FAILURE_DATA_DISABLE, "密码修改失败，新密码不可与原来密码一样。如需修改，请重新获取验证码后重试");
        }
        long updateRes = authenticationManagerService.updatePasswordById(userEntity.getId(), newPassword);
        if (updateRes == 0) {
            return ResultDTO.error("密码修改失败");
        }

        // 删除验证码
        redisTemplate.delete(REDIS_SMS_VERIFY_CODE_PREFIX + phonePasswordResetDTO.getPhone());
        return ResultDTO.ok("密码修改成功");
    }

    @Override
    public ResultDTO<String> passwordForgottenPre(PhonePasswordForgottenPreDTO phonePasswordForgottenPreDTO) {
        // 验证用户是否存在
        UserEntity userEntity = userManagerService.getByPhone(null, phonePasswordForgottenPreDTO.getPhone());
        if (userEntity == null || !userEntity.getEnable() || !AbstractBaseBO.StatusFlag.LIVE.equals(userEntity.getStatusFlag())) {
            return ResultDTO.error(ACCOUNT_NONEXISTENT_ERROR.getCode(), ACCOUNT_NONEXISTENT_ERROR.getMessage());
        }
        // 验证手机验证码
        Object redisVerifyCode = redisTemplate.opsForValue().get(REDIS_SMS_VERIFY_CODE_PREFIX + phonePasswordForgottenPreDTO.getPhone());
        if (redisVerifyCode == null || !VerificationCodeUtil.compare(phonePasswordForgottenPreDTO.getVerifyCode(), redisVerifyCode.toString())) {
            return ResultDTO.error(FAILURE_DATA_ERROR, "验证码错误或已失效");
        }
        return ResultDTO.ok("忘记密码-前置校验成功", userEntity.getAccount());
    }

    @Override
    public ResultDTO<?> passwordForgottenUpdate(PhonePasswordForgottenUpdateDTO phonePasswordForgottenUpdateDTO) {
        // 获取用户信息
        UserEntity userEntity = userManagerService.findOne(UserQueryFormBean.builder().account(phonePasswordForgottenUpdateDTO.getAccount()).build());

        // 校验密码
        String newPassword = authenticationManagerService.decoded(phonePasswordForgottenUpdateDTO.getPassword());
        if (!authenticationManagerService.validPassword(newPassword)) {
            return ResultDTO.error("密码修改失败，新密码格式有误");
        }

        try {
            long updateRes = authenticationManagerService.updatePasswordById(userEntity.getId(), newPassword);
            // 如果密码一样，就直接返回修改成功
            if (StringUtils.equals(userEntity.getPassword(), newPassword)) {
                return ResultDTO.ok("密码修改成功");
            }
            if (updateRes == 0) {
                log.error("密码修改失败");
                return ResultDTO.error("密码修改失败");
            }
        } finally {
            // 删除验证码
            redisTemplate.delete(REDIS_SMS_VERIFY_CODE_PREFIX + userEntity.getPhone());
        }

        return ResultDTO.ok("密码修改成功");
    }

    @Override
    public ResultDTO passwordUpdateByEmail(UserAuthEmailFormBean userAuthBO) {
        //邮箱验证码校验
        if (StringUtils.isEmpty(userAuthBO.getEmail())) {
            return ResultDTO.error("密码修改失败，邮箱不能为空");
        }
        if (StringUtils.isEmpty(userAuthBO.getVerifyCode())) {
            return ResultDTO.error("密码修改失败，验证码不能为空");
        }
        Object redisVerifyCode = redisTemplate.opsForValue().get(REDIS_MAIL_VERIFY_CODE_PREFIX + userAuthBO.getEmail());
        if (redisVerifyCode == null || !VerificationCodeUtil.compare(userAuthBO.getVerifyCode(), redisVerifyCode.toString())) {
            return ResultDTO.error("操作失败，验证码错误或已失效");
        }
        return passwordUpdate(userAuthBO);
    }

    @Override
    public ResultDTO<Object> passwordCheck(String password) {
        String account = SystemUtil.getCurrentUser().getAccount();
        UserAuthFormBean userAuthBO = UserAuthFormBean.builder().account(account).password(password).build();
        Integer result = authenticationManagerService.passwordCheck(userAuthBO);
        if (result.equals(FAILURE_DATA_NOT_EXIST)) {
            log.info("密码校验失败，用户不存在，userAuthBO:{}", userAuthBO);
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "密码校验失败，用户不存在");
        }
        if (result.equals(FAILURE_DATA_DISABLE)) {
            log.info("密码校验失败，用户已停用，userAuthBO:{}", userAuthBO);
            return ResultDTO.error(FAILURE_DATA_DISABLE, "密码校验失败，用户已停用");
        }
        if (result.equals(FAILURE_DATA_ERROR)) {
            log.info("密码校验失败，密码错误，userAuthBO:{}", userAuthBO);
            return ResultDTO.error(FAILURE_DATA_ERROR, "密码校验失败，密码错误");
        }
        return ResultDTO.ok("密码校验成功");
    }

    @Override
    public ResultDTO<Object> permissionsCheck(String token, String targetUrl) {
        //校验权限
        UserEntity userBean = authenticationManagerService.permissionsCheck(token, targetUrl);
        return ResultDTO.ok("权限校验通过", userBean);
    }

    @Override
    public ResultDTO<List<PermissionsTreeDTO>> userPermissions(String token) {
        //查询功能权限树
        List<PermissionsTreeDTO> trees = authenticationManagerService.userPermissions();
        if (CollectionUtils.isEmpty(trees)) {
            return ResultDTO.ok("查询用户拥有的权限成功", Lists.newArrayList());
        }
        return ResultDTO.ok("查询用户拥有的权限成功", trees);
    }

    @Override
    public ResultDTO<UserEntity> tokenUser(String token) {
        return ResultDTO.ok("", authenticationManagerService.queryByToken(token));
    }

    @Override
    public ResultDTO<String> getSalt(String account) {
        if (StringUtils.isEmpty(account)) {
            return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.getRandomSalt());
        }
        UserEntity userEntity = userManagerService.get(null, account, null);
        if (userEntity == null) {
            return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.getRandomSalt());
        }
        try {
            return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.getRealSalt(userEntity.getPassword()));
        } catch (Exception ex) {
            log.error("获取加密算法失败，请联系管理员重置密码", ex);
            return ResultDTO.error("获取加密算法失败，请联系管理员重置密码");
        }
    }

    @Override
    public ResultDTO<String> getPwd(String account) {
        //获取当前用户
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser.isSystem()) {
            //用户组织校验
            UserEntity userEntity = userManagerService.get(null, account, null);
            if (userEntity == null) {
                return ResultDTO.error("操作失败，用户不存在或没有权限");
            }
            //返回密码
            return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.encode(userEntity.getPassword()));
        }

        //重置密码功能权限校验
        String permissionId = "com.polarise.permissions.function.system.user.reset-password";
        List<String> permissions = permissionsManagerService.listPermissionsIdByRoleId(currentUser.getRoleList().stream().map(RoleEntity::getId).collect(Collectors.toList()), PermissionsType.FUNCTION.getValue());
        if (!permissions.contains(permissionId)) {
            return ResultDTO.error("操作失败，用户不存在或没有权限");
        }

        //用户组织校验
        UserEntity userEntity = userManagerService.get(null, account, null);
        if (userEntity == null) {
            return ResultDTO.error("操作失败，用户不存在或没有权限");
        }
        //获取当前用户可见组织
        List<String> orgIds = organizationService.getChildrenIds(currentUser.getOrganizationList().stream().map(OrganizationEntity::getId).collect(Collectors.toList()));
        if (!userEntity.getOrganizationList().stream().anyMatch(item -> orgIds.contains(item.getId()))) {
            return ResultDTO.error("操作失败，用户不存在或没有权限");
        }

        //返回密码
        return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.encode(userEntity.getPassword()));
    }

    @Override
    public ResultDTO<String> getEmail(String account) {
        //查询用户信息
        UserEntity one = authenticationManagerService.queryByAccount(account);
        //判断校验结果
        if (one == null || !one.getEnable() || !AbstractBaseBO.StatusFlag.LIVE.equals(one.getStatusFlag())) {
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "账号不存在或已停用");
        }
        //返回邮箱
        return ResultDTO.ok(SUCCESSFUL_MSG, one.getEmail());
    }

    @Override
    public ResultDTO<LoginResultDTO> multiFactorLogin(MultiFactorAuthDTO multiFactorAuthDTO) {
        ResultDTO<LoginResultDTO> loginResultDTOResultDTO = authenticationManagerService.multiFactorLoginService(multiFactorAuthDTO);
        LoginResultDTO loginResultDTO;
        if (loginResultDTOResultDTO.getStatus() == 0) {
            loginResultDTO = loginResultDTOResultDTO.getData();
            Optional.ofNullable(userManagerControllerInterface.getUserRoleAndOrganization(loginResultDTO.getUserId())
                .getData()).ifPresent(loginResultDTO::setUserRoleOrganizationDTO);
        }
        return loginResultDTOResultDTO;
    }

    @Override
    public ResultDTO<UserEntity> updateUserInfo(UserInfoUpdateDTO userInfoUpdateDTO) {
        // 验证用户是否存在
        UserEntity userEntity = userManagerService.get(userInfoUpdateDTO.getId(), null, null);
        if (userEntity == null || !userEntity.getEnable() || !AbstractBaseBO.StatusFlag.LIVE.equals(userEntity.getStatusFlag())) {
            return ResultDTO.error(FAILURE_DATA_NOT_EXIST, "更新失败，用户不存在或已停用");
        }

        // 更新用户信息
        UserMapper.INSTANCE.updateEntityFromDto(userInfoUpdateDTO, userEntity);

        // 保存更新
        UserEntity updatedUser = userManagerService.save(userEntity);
        if (updatedUser == null) {
            return ResultDTO.error("用户信息更新失败");
        }

        return ResultDTO.ok("用户信息更新成功", updatedUser);
    }

    @Override
    public ResultDTO<String> getPwdSecret() {
        return ResultDTO.ok(SUCCESSFUL_MSG, authenticationManagerService.getPwdSecret());
    }

}
