<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.6</version>
    </parent>

    <name>user-authority</name>
    <description>Gendo3: 用户组织权限微服务</description>
    <organization>
        <name>天海宸光 Polarizon</name>
        <url>https://www.polarizon.com/</url>
    </organization>

    <groupId>com.polarizon.gendo3</groupId>
    <artifactId>user-authority-biz</artifactId>
    <version>1.14.0-zhiku-beta.12-alpha</version>
    <packaging>jar</packaging>

    <scm>
        <connection>
            scm:git:http://git.polarizon.com/scm/gendo3/user-authority-ms.git
        </connection>
        <developerConnection>
            scm:git:http://git.polarizon.com/scm/gendo3/user-authority-ms.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <biz.server.port>20202</biz.server.port>
        <start-class>com.polarizon.gendo.sg.authority.MainApplication</start-class>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mongock.version>4.1.19</mongock.version>
        <dockerfile-maven-version>1.4.13</dockerfile-maven-version>
        <spring-cloud.version>2021.0.1</spring-cloud.version>
        <shedlock.version>4.23.0</shedlock.version>
        <!--Harbor仓库的地址，ip:port-->
        <docker.repository>hub.polarise.cn</docker.repository>
        <!--上传的Docker镜像前缀，此前缀一定要和Harbor中的项目名称一致，表示打包后的Docker镜像会上传到Harbor的哪个项目中-->
        <docker.registry>polarizon-rag</docker.registry>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.github.cloudyrock.mongock</groupId>
                <artifactId>mongock-bom</artifactId>
                <version>${mongock.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>aliyun-spring-boot-dependencies</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- public-common-lib -->
        <dependency>
            <groupId>com.polarizon.public</groupId>
            <artifactId>common-lib</artifactId>
            <version>1.0.164-beta.0</version>
        </dependency>
        <!-- common-lib -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>common-lib</artifactId>
            <version>1.0.9</version>
            <optional>true</optional>
        </dependency>
        <!-- faas-common-lib-->
        <dependency>
            <groupId>com.polarizon.gendo.faas</groupId>
            <artifactId>common-lib</artifactId>
            <version>4.8.0</version>
        </dependency>
        <!-- api引入 -->
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>user-authority-api</artifactId>
            <version>1.14.0-zhiku-auth-beta.6-alpha</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>push-message-api</artifactId>
            <version>1.3.0-rag-training-beta.4</version>
        </dependency>
        <dependency>
            <groupId>com.polarizon.gendo3</groupId>
            <artifactId>device-configuration-api</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- mongock -->
        <dependency>
            <groupId>com.github.cloudyrock.mongock</groupId>
            <artifactId>mongock-spring-v5</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.cloudyrock.mongock</groupId>
            <artifactId>mongodb-springdata-v3-driver</artifactId>
        </dependency>

        <!-- openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- 高版本redis的lettuce需要commons-pool2 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!--SchedulerLock-->
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${shedlock.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-redis-spring</artifactId>
            <version>${shedlock.version}</version>
        </dependency>

        <!--JWT-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!-- freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- security -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>

        <!--mongodb-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!-- spring mvc -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- spring validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- hutool 工具包 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.0</version>
        </dependency>

        <!-- logback: Conditional processing in configuration files requires the Janino library -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.serioussam</groupId>
            <artifactId>syslogappender</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--解决升级jdk版本，Handler dispatch failed;nested exception is java.lang.NoClassDefFoundError: javax/xml/bind/DatatypeConverter-->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Apache Commons Collections -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>26.0.2</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

    <!--指定使用maven打包-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <tagNameFormat>biz-v@{project.version}-${maven.build.timestamp}</tagNameFormat>
                    <updateDependencies>false</updateDependencies>
                    <pushChanges>false</pushChanges>
                    <arguments>-DskipTests</arguments>
                    <arguments>-Ddockerfile.skip</arguments>
                    <checkModificationExcludes>
                        <checkModificationExclude>**/pom.xml</checkModificationExclude>
                    </checkModificationExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-version}</version>
                <executions>
                    <execution>
                        <id>default</id>
                    </execution>
                </executions>
                <configuration>
                    <contextDirectory>${project.basedir}</contextDirectory>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <repository>${docker.repository}/${docker.registry}/${project.name}</repository>
                    <tag>${env.PROJECT_TAG}</tag>
                    <buildArgs>
                        <project_name>${project.name}</project_name>
                        <work_port>${biz.server.port}</work_port>
                        <JAR_FILE>${project.build.finalName}.${project.packaging}</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit47</artifactId>
                        <version>3.0.0-M3</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <configuration>
                    <skip>${maven.test.skip}</skip>
                    <destFile>${basedir}/target/coverage-reports/jacoco-unit.exec</destFile>
                    <dataFile>${basedir}/target/coverage-reports/jacoco-unit.exec</dataFile>
                    <output>file</output>
                    <append>true</append>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <phase>test-compile</phase>
                    </execution>

                    <execution>
                        <id>jacoco-site</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- 分发配置，必须与 settings.xml 的 id 一致 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://**************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://**************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>

        <profile>
            <id>develop</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.coverage.jacoco.xmlReportPaths>${project.reporting.outputDirectory}/jacoco/jacoco.xml
                </sonar.coverage.jacoco.xmlReportPaths>
                <sonar.host.url>http://**************:9000/</sonar.host.url>
                <sonar.login>squ_3a3c1a36191f8807c3d07356ac6c0dcf0bbdf796</sonar.login>
                <sonar.projectKey>${env.DOCKER_REGISTRY}-${project.name}</sonar.projectKey>
                <sonar.projectName>${env.DOCKER_REGISTRY}-${project.name}</sonar.projectName>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                    <version>0.8.7</version>
                    <classifier>runtime</classifier>
                    <scope>runtime</scope>
                </dependency>
            </dependencies>

            <!--指定使用maven打包-->
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <mainClass>${start-class}</mainClass>
                            <layout>ZIP</layout>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal><!-- 可以把依赖的包都打包到生成的Jar包中 -->
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>

        </profile>

        <profile>
            <id>product</id>
            <!--指定使用maven打包-->
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <!-- 指定该Main Class为全局的唯一入口-->
                            <mainClass>${start-class}</mainClass>
                            <layout>ZIP</layout>
                            <!--重新打包的时候需要排除依赖-->
                            <excludeGroupIds>com.polarizon</excludeGroupIds>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal><!-- 可以把依赖的包都打包到生成的Jar包中 -->
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

    </profiles>

</project>
