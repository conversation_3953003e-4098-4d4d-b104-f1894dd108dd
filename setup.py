# -*- coding: UTF-8 -*-
from setuptools import setup
from setuptools import find_namespace_packages

# Package version
__version__ = '1.1.3'
print(__version__)

# Module dependencies
requirements = []
with open('requirements.txt', encoding='utf-8', errors='ignore') as f:
    for line in f.read().splitlines():
        requirements.append(line)

setup(
    version=__version__,
    install_requires=requirements,
    package_dir={"": "src"},
    packages=find_namespace_packages("src"),
    python_requires=">=3.6, <=3.10.5",
    entry_points={
        'console_scripts': [
            'task = task.main:run',
        ],
    }
)
