package com.polarizon.gendo.sg.device.configuration.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

@Data
@ApiModel("租户能力VO")
@NoArgsConstructor
@AllArgsConstructor
public class TenantAbilityVO {

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("租户名称")
    private String tenantName;

    @ApiModelProperty("任务配置")
    private Collection<EventTaskInfoVO> eventTaskInfo;


    @Data
    @ApiModel("任务信息VO")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventTaskInfoVO {

        @ApiModelProperty("算法类型")
        private String algorithmType;

        @ApiModelProperty("任务名称")
        private List<String> taskNames;

    }

}
