package com.polarizon.gendo.sg.device.configuration.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 公共
 */
public class CommonUtil {

    /**
     * 英文、数字、下划线匹配
     */
    private static final Pattern englishNumberUnderlinePattern = Pattern.compile("^[\\w]+$");

    /**
     * 中文、英文、数字、下划线匹配
     */
    private static final Pattern chineseEnglishNumberUnderlinePattern = Pattern.compile("^[\\w|\u4E00-\u9FA5]+$");

    /**
     * 中文、英文、数字、下划线、中划线匹配
     */
    private static final Pattern chineseEnglishNumberUnderlinePatternHyphen = Pattern.compile("^[\\w|\u4E00-\u9FA5|-]+$");

    /**
     * 转义正则特殊字符 （$()*+.[]?\^{},|）
     *
     * @param keyword
     * @return
     */
    public static String escapeExprSpecialWord(String keyword) {
        if (StringUtils.isEmpty(keyword))
            return keyword;
        String[] fbsArr = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"};
        for (String key : fbsArr) {
            if (keyword.contains(key)) {
                keyword = keyword.replace(key, "\\" + key);
            }
        }
        return keyword;
    }

    /**
     * 匹配英文、数字、下划线
     *
     * @param content 待正则字符
     * @return 匹配结果
     */
    public static boolean onlyContainEnglishNumberUnderline(String content) {
        return englishNumberUnderlinePattern.matcher(content).find();
    }

    /**
     * 匹配中文、英文、数字、下划线
     *
     * @param content 待正则字符
     * @return 匹配结果
     */
    public static boolean onlyContainerChineseEnglishNumberUnderline(String content) {
        return chineseEnglishNumberUnderlinePattern.matcher(content).find();
    }

    /**
     * 匹配中文、英文、数字、下划线、中划线（连字符）
     *
     * @param content 待正则字符
     * @return 匹配结果
     */
    public static boolean onlyContainerChineseEnglishNumberUnderlineHyphen(String content) {
        return chineseEnglishNumberUnderlinePatternHyphen.matcher(content).find();
    }
}
