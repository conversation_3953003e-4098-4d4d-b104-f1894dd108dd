package com.polarizon.gendo.sg.device.configuration.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@ApiModel("设备区域DTO")
public class DeviceAreaDTO {

    @ApiModelProperty("省/直辖市名称")
    private String provinceName;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区/县名称")
    private String countyName;

    @ApiModelProperty("街道/乡/镇名称")
    private String streetName;

    @ApiModelProperty("设备状态 1-在线 0-离线，在线/离线")
    private Integer deviceStatus;

    @ApiModelProperty("设备数量")
    private Integer deviceCount;

    /**
     * 常量
     */
    public interface Constant {
        //对应表中各个字段名
        String AREA_PROVINCE_NAME = "provinceName";
        String AREA_CITY_NAME = "cityName";
        String AREA_COUNTY_NAME = "countyName";
        String AREA_STREET_NAME = "streetName";
        String AREA_DEVICE_STATUS = "deviceStatus";
        String AREA_DEVICE_COUNT = "deviceCount";
        String PREVIOUS_PROVINCE_NAME = "_id".concat(".").concat(AREA_PROVINCE_NAME);
        String PREVIOUS_CITY_NAME = "_id".concat(".").concat(AREA_CITY_NAME);
        String PREVIOUS_COUNTY_NAME = "_id".concat(".").concat(AREA_COUNTY_NAME);
        String PREVIOUS_STREET_NAME = "_id".concat(".").concat(AREA_STREET_NAME);
        String PREVIOUS_DEVICE_STATUS = "_id".concat(".").concat(AREA_DEVICE_STATUS);
    }

}
