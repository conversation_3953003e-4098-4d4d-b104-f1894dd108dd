package com.polarizon.gendo.sg.push.message.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Configuration
public class JacksonMessageConverterConfig {

    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        return new FeignMappingJackson2HttpMessageConverter();
    }

    class FeignMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {

        public FeignMappingJackson2HttpMessageConverter() {
            List<MediaType> mediaTypeList = new ArrayList<>(getSupportedMediaTypes());
            // 在已有的 MediaType 基础上加入 ContentType: text/plain 返回类型转换支持，在原有基础上拓展不影响之前的功能
            mediaTypeList.add(new MediaType("text", "plain", StandardCharsets.UTF_8));
            // 设置新的 MediaType 支持列表
            setSupportedMediaTypes(mediaTypeList);
        }
    }

}
