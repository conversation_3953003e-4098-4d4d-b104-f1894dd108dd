package com.polarizon.gendo.sg.push.message.config;

import com.polarizon.gendo.sg.authority.interceptor.AuthenticatedInterceptor;
import com.polarizon.gendo.sg.common.spring.decode.StringDecoderForHeaderConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.Charset;


@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Value("${server.servlet.encoding.charset:UTF-8}")
    private String charset;

    /**
     * 设置登录信息拦截器
     *
     * @param registry InterceptorRegistry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(AuthenticatedInterceptor.build())
            .excludePathPatterns("/**/swagger-ui/**")
            .excludePathPatterns("/**/swagger-resources/**")
            .excludePathPatterns("/wechat/login")
            .excludePathPatterns("/dingtalk/login")
            .excludePathPatterns("/email/verify/send")
        ;
    }

    /**
     * 对于header中的中文字进行解码
     *
     * @return 转换结果
     */
    @Bean
    public StringDecoderForHeaderConverter stringHeaderConverter() {
        return new StringDecoderForHeaderConverter(StringUtils.isBlank(charset) ? null : Charset.forName(charset));
    }

}
