package com.polarizon.gendo.sg.push.message.service.handler;

import com.aliyun.dingtalkmicro_app_1_0.models.ListAllInnerAppsResponse;
import com.aliyun.dingtalkmicro_app_1_0.models.ListAllInnerAppsResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.CreateJsapiTicketHeaders;
import com.aliyun.dingtalkoauth2_1_0.models.CreateJsapiTicketResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationGetsendprogressRequest;
import com.dingtalk.api.request.OapiMessageCorpconversationGetsendresultRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiMessageCorpconversationGetsendprogressResponse;
import com.dingtalk.api.response.OapiMessageCorpconversationGetsendresultResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.polarizon.gendo.sg.push.message.bean.bo.AccessTokenBO;
import com.polarizon.gendo.sg.push.message.bean.bo.EventMessageBO;
import com.polarizon.gendo.sg.push.message.bean.bo.JsapiTicketBO;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.API_GET_SEND_PROGRESS;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.API_GET_SEND_RESULT;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.API_GET_USER_ID;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.API_GET_USER_INFO;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.API_PUSH_MESSAGE;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.DETAIL_URL;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.MESSAGE_MARKDOWN;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.MESSAGE_TITLE;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.MESSAGE_TYPE_ACTION_CARD;
import static com.polarizon.gendo.sg.push.message.utils.DingtalkConstants.REMARK_MARKDOWN;

/**
 * 钉钉对接处理
 */
@Slf4j
@Component
public class DingtalkHandler {
    @Value("${dingtalk.app_key}")
    private String appKey;

    @Value("${dingtalk.app_secret}")
    private String appSecret;

    @Value("${dingtalk.corp_id}")
    private String corpId;

    @Value("${dingtalk.agent_id}")
    private String agentId;

    /**
     * 主页地址，配置文件读取失败则从接口获取
     */
    @Value("${dingtalk.home_url:}")
    private String homeUrl;

    public AccessTokenBO getAccessToken() {
        try {
            com.aliyun.dingtalkoauth2_1_0.Client client = createAuthClient();
            com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest =
                new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest().setAppKey(appKey).setAppSecret(appSecret);
            GetAccessTokenResponse getAccessTokenResponse = client.getAccessToken(getAccessTokenRequest);
            AccessTokenBO accessTokenBO = new AccessTokenBO();
            accessTokenBO.setAccessToken(getAccessTokenResponse.body.accessToken);
            accessTokenBO.setExpireSin(getAccessTokenResponse.body.expireIn);
            return accessTokenBO;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取 AccessToken 失败：" + err.code + " " + err.message);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取 AccessToken 失败：" + err.code + " " + err.message);
            }
        }
        return null;
    }

    /**
     * 获取JsapiTicket
     *
     * @param accessToken AccessToken
     * @return
     */
    public JsapiTicketBO getJsapiTicket(String accessToken) {
        try {
            //TODO jsapiTicket的有效期为7200秒，可以考虑redis缓存，避免频繁获取
            com.aliyun.dingtalkoauth2_1_0.Client client = createAuthClient();
            RuntimeOptions runtime = new RuntimeOptions();
            CreateJsapiTicketHeaders headers = new CreateJsapiTicketHeaders();
            headers.xAcsDingtalkAccessToken = accessToken;
            CreateJsapiTicketResponse createJsapiTicketResponse = client.createJsapiTicketWithOptions(headers, runtime);
            return JsapiTicketBO.builder().ticket(createJsapiTicketResponse.body.getJsapiTicket()).expiresIn(createJsapiTicketResponse.body.getExpireIn()).build();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取 jsapiTicket 失败：" + err.code + " " + err.message);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取 jsapiTicket 失败：" + err.code + " " + err.message);
            }
        }
        return null;
    }

    /**
     * 发送actionCard消息
     *
     * @param eventMessageBO  事件消息
     * @param accessTokenBO   AccessToken
     * @param successCallback 成功回调
     * @return
     */
    public boolean pushActionCardMessage(EventMessageBO eventMessageBO, AccessTokenBO accessTokenBO,
                                         Consumer<DingtalkPushMsgPostHandler.PushMessageResponse> successCallback) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(API_PUSH_MESSAGE);
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(Long.parseLong(agentId));
            req.setUseridList(String.join(",", eventMessageBO.getHandlerOpenIdSet()));
            OapiMessageCorpconversationAsyncsendV2Request.Msg msg =
                new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMsgtype(MESSAGE_TYPE_ACTION_CARD);
            OapiMessageCorpconversationAsyncsendV2Request.ActionCard actionCard =
                new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
            actionCard.setSingleUrl(getMessageUrl(accessTokenBO, eventMessageBO.getId()));
            actionCard.setSingleTitle(MESSAGE_TITLE);
            String color = getColor(eventMessageBO.getEventLevel());
            //截取前100个字符的备注
            boolean remarkEmpty = StringUtils.isBlank(eventMessageBO.getRemark());
            String remark = !remarkEmpty ? eventMessageBO.getRemark().substring(0, Math.min(100,
                eventMessageBO.getRemark().length())) :
                Strings.EMPTY;
            String remarkMarkdown = !remarkEmpty ? String.format(REMARK_MARKDOWN, remark) : Strings.EMPTY;
            actionCard.setMarkdown(String.format(MESSAGE_MARKDOWN, color, eventMessageBO.getEventLevel(), remarkMarkdown,
                eventMessageBO.getEventName(), eventMessageBO.getEventLevel(), eventMessageBO.getDeviceAddress(),
                eventMessageBO.getEventHandleStatus(), eventMessageBO.getStartTime()));
            actionCard.setTitle("机场事件告警通知");
            msg.setActionCard(actionCard);
            req.setMsg(msg);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, accessTokenBO.getAccessToken());
            if (rsp.getErrcode() == 0) {
                Optional.ofNullable(successCallback).ifPresent(callback -> callback.accept(DingtalkPushMsgPostHandler.PushMessageResponse.builder().success(true).requestId(rsp.getRequestId()).taskId(rsp.getTaskId()).build()));
                log.info("消息推送接口调用成功,eventId：{},errorMsg: {},request_id: {},task_id: {},detailUrl: {}",
                    eventMessageBO.getId(), rsp.getErrmsg(), rsp.getRequestId(), rsp.getTaskId(),
                    actionCard.getSingleUrl());
                return true;
            }
            log.error("消息推送接口调用失败，eventId：{},errorCode: {},errorMsg: {}", eventMessageBO.getId(),
                rsp.getErrcode(), rsp.getErrmsg());
        } catch (ApiException e) {
            e.printStackTrace();
        }
        Optional.ofNullable(successCallback).ifPresent(callback -> callback.accept(DingtalkPushMsgPostHandler.PushMessageResponse.builder().success(false).build()));
        return false;
    }

    /**
     * 获取消息跳转地址
     *
     * @param accessTokenBO AccessToken
     * @param id            事件编码
     * @return
     */
    private String getMessageUrl(AccessTokenBO accessTokenBO, String id) {
        //调用钉钉api查询应用主页地址
        if (StringUtils.isBlank(homeUrl)) {
            //这里缓存homeUrl，避免频繁调用，该api调用有次数限制
            homeUrl = getHomeUrl(accessTokenBO);
        }
        if (StringUtils.isBlank(homeUrl)) {
            log.warn("没有获取到应用首页地址，拼接消息跳转地址失败");
            return Strings.EMPTY;
        }
        //拆分首页地址和请求参数
        String[] urlArr = homeUrl.split("\\?");
        return String.format(DETAIL_URL, urlArr[0], id, corpId);
    }

    /**
     * 获取发送进度
     *
     * @param accessTokenBO AccessToken
     * @param taskId        任务id
     * @return
     */
    public long getSendProgress(AccessTokenBO accessTokenBO, Long taskId) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(API_GET_SEND_PROGRESS);
            OapiMessageCorpconversationGetsendprogressRequest req =
                new OapiMessageCorpconversationGetsendprogressRequest();
            req.setAgentId(Long.parseLong(agentId));
            req.setTaskId(taskId);
            OapiMessageCorpconversationGetsendprogressResponse rsp = client.execute(req,
                accessTokenBO.getAccessToken());
            if (rsp.getErrcode() == 0) {
                return rsp.getProgress().getStatus();
            }
            log.error("获取发送进度失败，errorCode: {}，errorMsg: {}", rsp.getErrcode(), rsp.getErrmsg());
        } catch (Exception e) {
            log.error("获取发送进度失败：" + e.getMessage());
        }
        return -1;
    }

    /**
     * 获取发送结果
     *
     * @param accessTokenBO AccessToken
     * @param taskId        任务id
     * @return
     */
    public OapiMessageCorpconversationGetsendresultResponse getSendResult(AccessTokenBO accessTokenBO, Long taskId) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(API_GET_SEND_RESULT);
            OapiMessageCorpconversationGetsendresultRequest req = new OapiMessageCorpconversationGetsendresultRequest();
            req.setAgentId(Long.parseLong(agentId));
            req.setTaskId(taskId);
            return client.execute(req, accessTokenBO.getAccessToken());
        } catch (Exception e) {
            log.error("获取发送进度失败：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取应用主页地址
     *
     * @param accessTokenBO AccessToken
     * @return
     */
    private String getHomeUrl(AccessTokenBO accessTokenBO) {
        try {
            com.aliyun.dingtalkmicro_app_1_0.Client client = createMicroClient();
            com.aliyun.dingtalkmicro_app_1_0.models.ListAllInnerAppsHeaders listAllInnerAppsHeaders =
                new com.aliyun.dingtalkmicro_app_1_0.models.ListAllInnerAppsHeaders();
            listAllInnerAppsHeaders.xAcsDingtalkAccessToken = accessTokenBO.getAccessToken();
            ListAllInnerAppsResponse rsp = client.listAllInnerAppsWithOptions(listAllInnerAppsHeaders,
                new com.aliyun.teautil.models.RuntimeOptions());
            if (rsp.getStatusCode() != HttpStatus.SC_OK) {
                log.error("获取主页地址失败，statusCode: {}", rsp.getStatusCode());
                return null;
            }
            return rsp.body.getAppList().stream().filter(app -> Objects.equals(Long.parseLong(agentId),
                app.getAgentId())).findFirst().map(ListAllInnerAppsResponseBody.ListAllInnerAppsResponseBodyAppList::getHomepageLink).orElse(null);
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取主页地址失败：" + err.code + " " + err.message);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.error("获取主页地址失败：" + err.code + " " + err.message);
            }
        }
        return null;
    }

    /**
     * 获取用户id
     *
     * @param code        临时免登码
     * @param accessToken AccessToken
     * @return 用户id
     */
    public String getDingtalkUserId(String code, String accessToken) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(API_GET_USER_ID);
            OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
            req.setCode(code);
            OapiV2UserGetuserinfoResponse rsp = client.execute(req, accessToken);
            if (rsp.getErrcode() == 0) {
                return rsp.getResult().getUserid();
            }
            log.error("获取用户id失败，errorCode: {}，errorMsg: {}", rsp.getErrcode(), rsp.getErrmsg());
        } catch (Exception e) {
            log.error("获取用户id失败，errorMsg: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取用户姓名
     *
     * @param userId      用户id
     * @param accessToken AccessToken
     * @return 用户姓名
     */
    public String getDingtalkUserName(String userId, String accessToken) {
        try {
            DingTalkClient client = new DefaultDingTalkClient(API_GET_USER_INFO);
            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
            req.setUserid(userId);
            req.setLanguage("zh_CN");
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            if (rsp.getErrcode() == 0) {
                return rsp.getResult().getName();
            }
            log.error("获取用户信息失败，errorCode: {}，errorMsg: {}", rsp.getErrcode(), rsp.getErrmsg());
        } catch (Exception e) {
            log.error("获取用户信息失败，errorMsg: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取颜色
     *
     * @param eventLevel 事件等级
     * @return
     */
    private String getColor(String eventLevel) {
        switch (eventLevel) {
            case "提示":
                //绿色
                return "#00B899";
            case "预警":
                //蓝色
                return "#419AF2";
            case "告警":
                //橙色
                return "#FB9A09";
            case "危险":
                //红色
                return "#FF0000";
            default:
                return "#000000";
        }
    }

    /**
     * 计算dd.config的签名参数
     *
     * @param jsticket  通过微应用appKey获取的jsticket
     * @param nonceStr  自定义固定字符串
     * @param timeStamp 当前时间戳
     * @param url       调用dd.config的当前页面URL
     * @return 签名
     */
    public String sign(String jsticket, String nonceStr, long timeStamp, String url) {
        try {
            String plain =
                "jsapi_ticket=" + jsticket + "&noncestr=" + nonceStr + "&timestamp=" + String.valueOf(timeStamp) +
                    "&url=" + decodeUrl(url);
            MessageDigest sha1 = MessageDigest.getInstance("SHA-256");
            sha1.reset();
            sha1.update(plain.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(sha1.digest());
        } catch (Exception e) {
            log.error("签名失败：" + e.getMessage());
        }
        return null;
    }

    /**
     * 因为ios端上传递的url是encode过的，android是原始的url。开发者使用的也是原始url,
     * 所以需要把参数进行一般urlDecode
     *
     * @param url url
     * @return url
     * @throws Exception
     */
    private String decodeUrl(String url) throws Exception {
        URL urler = new URL(url);
        StringBuilder urlBuffer = new StringBuilder();
        urlBuffer.append(urler.getProtocol());
        urlBuffer.append(":");
        if (urler.getAuthority() != null && urler.getAuthority().length() > 0) {
            urlBuffer.append("//");
            urlBuffer.append(urler.getAuthority());
        }
        if (urler.getPath() != null) {
            urlBuffer.append(urler.getPath());
        }
        if (urler.getQuery() != null) {
            urlBuffer.append('?');
            urlBuffer.append(URLDecoder.decode(urler.getQuery(), StandardCharsets.UTF_8));
        }
        return urlBuffer.toString();
    }

    /**
     * 生成随机字符串
     *
     * @return
     */
    public String generateNonce() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] nonceBytes = new byte[16];
        secureRandom.nextBytes(nonceBytes);
        return bytesToHex(nonceBytes);
    }

    /**
     * 将字节数组转换为16进制字符串
     *
     * @param bytes 字节数组
     * @return
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte aByte : bytes) {
            hexString.append(String.format("%02x", aByte));
        }
        return hexString.toString();
    }

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    private com.aliyun.dingtalkoauth2_1_0.Client createAuthClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    private static com.aliyun.dingtalkmicro_app_1_0.Client createMicroClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkmicro_app_1_0.Client(config);
    }
}
