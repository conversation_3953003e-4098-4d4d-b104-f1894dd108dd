<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="user-authority-lib [install]-testSkip" type="MavenRunConfiguration" factoryName="Maven">
    <MavenSettings>
      <option name="myGeneralSettings" />
      <option name="myRunnerSettings">
        <MavenRunnerSettings>
          <option name="delegateBuildToMaven" value="false" />
          <option name="environmentProperties">
            <map />
          </option>
          <option name="jreName" value="#USE_PROJECT_JDK" />
          <option name="mavenProperties">
            <map />
          </option>
          <option name="passParentEnv" value="true" />
          <option name="runMavenInBackground" value="true" />
          <option name="skipTests" value="true" />
          <option name="vmOptions" value="-DarchetypeCatalog=internal" />
        </MavenRunnerSettings>
      </option>
      <option name="myRunnerParameters">
        <MavenRunnerParameters>
          <option name="cmdOptions" />
          <option name="profiles">
            <set />
          </option>
          <option name="goals">
            <list>
              <option value="install" />
            </list>
          </option>
          <option name="multimoduleDir" />
          <option name="pomFileName" value="pom.xml" />
          <option name="profilesMap">
            <map />
          </option>
          <option name="projectsCmdOptionValues">
            <list />
          </option>
          <option name="resolveToWorkspace" value="false" />
          <option name="workingDirPath" value="$PROJECT_DIR$" />
        </MavenRunnerParameters>
      </option>
    </MavenSettings>
    <method v="2" />
  </configuration>
</component>