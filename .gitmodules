[submodule "user-authority-ms"]
	path = user-authority-ms
	url = ssh://*********************:8022/gendo3/user-authority-ms.git
[submodule "frontend-web"]
	path = frontend-web
	url = ssh://*********************:8022/rag/frontend-web.git
[submodule "autocode-graphql-ms"]
	path = autocode-graphql-ms
	url = ssh://*********************:8022/rag/autocode-graphql-ms.git
[submodule "rag-api-gateway"]
	path = rag-api-gateway
	url = ssh://*********************:8022/rag/rag-api-gateway.git
[submodule "constant-collection-ms"]
	path = constant-collection-ms
	url = ssh://*********************:8022/rag/constant-collection-ms.git	
[submodule "wps-addon-polarinsight"]
	path = wps-addon-polarinsight
	url = ssh://*********************:8022/rag/wps-addon-polarinsight.git
[submodule "model-manage-ms"]
	path = model-manage-ms
	url = ssh://*********************:8022/rag/model-manage-ms.git
[submodule "autocode-kb-manage-ms"]
	path = autocode-kb-manage-ms
	url = ssh://*********************:8022/rag/autocode-kb-manage-ms.git
[submodule "autocode-ai-app-ms"]
	path = autocode-ai-app-ms
	url = ssh://*********************:8022/rag/autocode-ai-app-ms.git
[submodule "autocode-chenqi-helper-ms"]
	path = autocode-chenqi-helper-ms
	url = ssh://*********************:8022/rag/autocode-chenqi-helper-ms.git
[submodule "autocode-ai-agent-ms"]
	path = autocode-ai-agent-ms
	url = ssh://*********************:8022/rag/autocode-ai-agent-ms.git
[submodule "message-proxy"]
	path = message-proxy
	url = ssh://*********************:8022/gendo3/message-proxy.git
