#!/bin/bash


export OMP_NUM_THREADS=20

#vllm serve Qwen2.5-72B-Instruct \
vllm serve /models/Qwen2.5-32B-Instruct-GPTQ-Int4 \
        --served-model-name qwen2.5-32b-instruct-gptq \
        --host 0.0.0.0 --port 20000 \
        --uvicorn-log-level error \
        -tp 1 --swap-space 2 \
        --cpu-offload-gb 0 \
        --max-num-batched-tokens 16384 \
        --max-model-len 16384 \
        --disable-custom-all-reduce \
        --gpu-memory-utilization 0.80 \
        --enforce-eager \
        --api-key=sk-e305b56dad2137e75c97196088b469a2
