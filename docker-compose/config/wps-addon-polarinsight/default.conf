map $uri $custom_content_type {
    default                "application/octet-stream";

    ~(.*\.json)$           "application/json";

    ~(.*\.mp4)$             "video/mp4";
    ~(.*\.ts)$              "video/mp2t";
    ~(.*\.mpeg)$            "video/mpeg";
    ~(.*\.mov)$             "video/quicktime";
    ~(.*\.webm)$            "video/webm";
    ~(.*\.flv)$             "video/x-flv";
    ~(.*\.m4v)$             "video/x-m4v";
    ~(.*\.mng)$             "video/x-mng";
    ~(.*\.asf)$             "video/x-ms-asf";
    ~(.*\.wmv)$             "video/x-ms-wmv";
    ~(.*\.avi)$             "video/x-msvideo";

    ~(.*\.gif)$              "image/gif";
    ~(.*\.jpeg)$             "image/jpeg";
    ~(.*\.jpg)$              "image/jpeg";
    ~(.*\.png)$              "image/png";
    ~(.*\.tiff)$             "image/tiff";
    ~(.*\.wbmp)$             "image/vnd.wap.wbmp";
    ~(.*\.ico)$              "image/x-icon";
    ~(.*\.jng)$              "image/x-jng";
    ~(.*\.bmp)$              "image/x-ms-bmp";
    ~(.*\.svg)$              "image/svg+xml";
    ~(.*\.webp)$             "image/webp";
}

server {
    listen       3889;
    server_name  wps.polarizon.com;

    # 这里是dns的原因，如果用指定的域名，没有刷新的话，依赖的服务重启之后就访问不了了    
    resolver 127.0.0.11;

    
    #开启gzip功能
    gzip on;
    #Nginx做为反向代理的时候启用：off – 关闭所有的代理结果数据压缩
    gzip_proxied off;
    # 启用gzip压缩的最小文件，小于设置值的文件将不会压缩
    gzip_min_length 1k;
    #开启gzip静态压缩功能
    gzip_static on;
    #gzip缓存大小
    #gzip_buffers 4 16k;
    #gzip http版本
    gzip_http_version 1.0;
    #gzip 压缩级别 1-10
    gzip_comp_level 6;
    #gzip 压缩类型
    gzip_types application/javascript text/css text/javascript;
    # 是否在http header中添加Vary: Accept-Encoding，建议开启
    gzip_vary on;
    # 禁用IE 6 gzip
    gzip_disable "MSIE [1-6]\.";

    location /install {
        try_files $uri $uri/ /publish.html;
        alias  /usr/share/nginx/html/wps-addon-publish;
        index  publish.html publish.htm;
    }


     location /graphql {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection upgrade;
        proxy_pass http://rag-api-gateway:4000;
        client_max_body_size 100M;
    }
    

    location /gateway {
        proxy_buffering off;
        proxy_set_header Connection '';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Accel-Buffering no;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection upgrade;
        proxy_pass http://rag-api-gateway:4000;
        client_max_body_size 100M;
    }

    
    location ~ ^/gateway/kb-chat/v1/(chatStream|instructChat) {
        proxy_buffering off;
        proxy_set_header Connection '';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Accel-Buffering no;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection upgrade;
        proxy_pass http://rag-api-gateway:4000;
        client_max_body_size 100M;
        client_body_timeout 180;
        send_timeout 180;
        keepalive_timeout 180;
        proxy_read_timeout 180;
    }

    location /ceph {
        proxy_hide_header Content-Type;
        add_header Content-Type $custom_content_type;
        rewrite /ceph/(.*) /$1 break;
        proxy_pass http://minio:9000;
        sendfile on;
        tcp_nodelay on;
        client_max_body_size 51200M;
        client_body_timeout 1800;
        send_timeout 1800;
        keepalive_timeout 1800;
    }


    location / {
        root   /usr/share/nginx/html/wps-addon-build;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}