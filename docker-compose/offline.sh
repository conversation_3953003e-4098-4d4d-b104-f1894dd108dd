#!/bin/bash

config_offline_repo() {
    cp /etc/apt/sources.list /etc/apt/sources.list-backup
    tar -zxf packages.tgz -C /opt
    echo 'deb [trusted=yes] file:///opt/ packages/' >/etc/apt/sources.list
    apt-get update
    systemctl disable --now apt-daily-upgrade.service  unattended-upgrades.service apt-daily-upgrade.timer apt-daily-upgrade.timer apt-daily.timer
}

install_docker(){
    apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    apt-mark hold docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    systemctl enable --now docker
}

install_nvidia_driver(){
    apt-get install gcc g++ make
cat <<EOF | tee /etc/modprobe.d/blacklist-nouveau.conf
blacklist nouveau
options nouveau modeset=0
EOF
    update-initramfs -u
    rmmod nouveau
    init 3
    ./NVIDIA-Linux-x86_64-550.135.run -s
}

install_nvidia_docker(){
    apt-get install -y nvidia-container-toolkit
    apt-mark hold nvidia-container-toolkit
}

config_docker(){
cat > /etc/docker/daemon.json <<EOF
{
    "default-runtime": "nvidia",
    "runtimes": {
        "nvidia": {
            "path": "/usr/bin/nvidia-container-runtime",
            "runtimeArgs": []
        }
    }
}
EOF
    systemctl daemon-reload
    systemctl restart docker
}

function menu() {
    echo "1. ustc repo"
    echo "2. install docker"
    echo "3. install nvidia driver"
    echo "4. install nvidia docker"
    echo "5. exit"
    read -p "Input your choice: " num
    case $num in
        1) config_offline_repo ;;
        2) install_docker ;;
        3) install_nvidia_driver ;;
        4) install_nvidia_docker ;;
        5) exit ;;
        *) echo "Invalid input" ;;
    esac
}

while true
do
    menu
done
