{"name": "graphql-gateway", "version": "2.19.0-beta.1-alpha", "description": "graphql gateway for Gendo V3", "type": "module", "publishConfig": {"registry": "http://**************:8081/repository/npm/"}, "bin": {"graphql": "./dist/index.js"}, "scripts": {"clean": "rm -rf dist", "build": "tsc", "serve": "tsc && cross-env NODE_ENV=development node ./dist/index.js"}, "dependencies": {"@apollo/datasource-rest": "^5.0.1", "@apollo/server": "^4.3.0", "@types/node": "^18.11.18", "apollo-type-bigint": "^0.1.3", "axios": "^1.3.1", "cross-env": "^7.0.3", "dataloader": "^2.2.1", "dotenv": "^16.4.5", "graphql": "^16.6.0", "graphql-redis-subscriptions": "^2.6.1", "graphql-subscriptions": "^2.0.0", "graphql-type-json": "^0.3.2", "graphql-ws": "^5.16.0", "ioredis": "^5.4.1", "typescript": "^4.9.5", "ws": "^8.18.0"}}