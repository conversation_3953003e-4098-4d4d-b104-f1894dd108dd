package com.polarizon.gendo.sg.authority.bean.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import static com.polarizon.gendo.sg.authority.bean.bo.DocumentTypeBO.DocumentTypeInterface.FIELD_DOCUMENT_TYPE;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("运维小技巧文档类型BO")
@Document(DocumentTypeBO.DocumentTypeInterface.DOCUMENT_NAME_DOCUMENT_TYPE)
@JsonView(AbstractBaseBO.ShowView.class)
public class DocumentTypeBO extends AbstractBaseBO {

    @ApiModelProperty("文档类型")
    @Field(FIELD_DOCUMENT_TYPE)
    @JsonProperty(FIELD_DOCUMENT_TYPE)
    @JsonView({AbstractBaseBO.EditView.class})
    private String documentType;

    public interface DocumentTypeInterface {
        String DOCUMENT_NAME_DOCUMENT_TYPE = "com.polarizon.gendo.sg.document.type";
        String FIELD_DOCUMENT_TYPE = "documentType";
    }
}
