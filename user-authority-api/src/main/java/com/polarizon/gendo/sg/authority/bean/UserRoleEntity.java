package com.polarizon.gendo.sg.authority.bean;


import com.polarizon.common.bean.bo.AbstractBaseBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户角色关联实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(UserRoleEntity.Constants.DOCUMENT_NAME)
public class UserRoleEntity extends AbstractBaseBO {
    /**
     * 用户id
     */
    @Indexed
    private String userId;

    /**
     * 角色id
     */
    @Indexed
    private String roleId;

    /**
     * 用户角色关联常量类
     */
    public static interface Constants {
        String DOCUMENT_NAME = "com.polarizon.gendo.sg.userRole";
        String FIELD_USER_ID = "userId";
        String FIELD_ROLE_ID = "roleId";
    }

}
