package com.polarizon.gendo.sg.authority.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举类
 */
@Getter
@AllArgsConstructor
public enum UserType {
    /**
     * 系统用户，系统初始化创建
     */
    SYSTEM("system"),

    /**
     * 厂商用户，算法厂商
     */
    SUPPLIER("supplier"),

    /**
     * 终端用户，组织结构创建
     */
    TERMINAL("terminal");

    /**
     * 用户类型对应的值
     */
    private String value;

    @JsonValue
    public String getJsonValue() {
        // 反序列化成json时, 枚举类取value
        return this.value;
    }
}
