package com.polarizon.gendo.sg.authority.bean.dto;

import com.polarizon.gendo.sg.authority.validation.ValidationGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;

/**
 * 多因素认证数据传输对象
 */
@ApiModel("多因素认证DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiFactorAuthDTO {

    /**
     * 账号
     */
    @Nullable
    @ApiModelProperty(value = "账号", required = false)
    private String account;

    /**
     * 密码
     */
    @Nullable
    @ApiModelProperty(value = "密码", required = false)
    private String password;

    /**
     * 邮箱
     */
    @Nullable
    @ApiModelProperty(value = "邮箱", required = false)
    private String email;

    /**
     * 邮箱验证码
     */
    @Nullable
    @ApiModelProperty(value = "邮箱验证码")
    private String emailVerifyCode;

    /**
     * 手机号
     */
    @Nullable
    @ApiModelProperty(value = "手机号", required = false)
    private String phone;

    /**
     * 手机验证码
     */
    @Nullable
    @ApiModelProperty(value = "手机验证码", required = false)
    private String verifyCode;

    /**
     * 认证因素类型
     */
    @NotBlank(message = "认证因素类型不能为空", groups = {ValidationGroups.MultiFactorAuth.class})
    @ApiModelProperty(value = "认证因素类型: ACCOUNT-账号， SMS-短信验证码， EMAIL-邮箱验证码", required = true)
    private String factorType;

    /**
     * 账号、邮箱和手机号，不能同时为空
     *
     * @return boolean
     */
    @AssertTrue(message = "账号、邮箱和手机号，不能同时为空", groups = {ValidationGroups.MultiFactorAuth.class})
    private boolean isAccountOrEmailOrPhoneNotNull() {
        return StringUtils.isNotBlank(account) || StringUtils.isNotBlank(email) || StringUtils.isNotBlank(phone);
    }
}