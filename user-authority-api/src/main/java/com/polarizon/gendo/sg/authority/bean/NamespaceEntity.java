package com.polarizon.gendo.sg.authority.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import com.polarizon.gendo.sg.authority.bean.dto.OrganizationTreeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static com.polarizon.common.utils.ValidationConstants.NAME_SIZE_MAX;
import static com.polarizon.common.utils.ValidationConstants.NAME_SIZE_MIN;
import static com.polarizon.common.utils.ValidationConstants.PATTERN_NO_CONTAINS_SPECIAL_CHARACTERS;


/**
 * 名称空间实体类
 */
@Data
@ApiModel("命名空间")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(NamespaceEntity.Constants.DOCUMENT_NAME)
public class NamespaceEntity extends AbstractBaseBO {
    @JsonView(AbstractBaseBO.ShowView.class)
    @NotBlank(message = "租户id不能为空")
    @ApiModelProperty("租户id")
    private String tenantId;

    @JsonView(AbstractBaseBO.EditView.class)
    @Size(min = NAME_SIZE_MIN, max = NAME_SIZE_MAX, message = "命名空间名称字符长度不能小于：{min}，不能大于：{max}")
    @Pattern(regexp = PATTERN_NO_CONTAINS_SPECIAL_CHARACTERS, message = "命名空间名称只能包含中文、英文、数字、中划线、下划线")
    @NotBlank(message = "命名空间名称不能为空")
    @ApiModelProperty("命名空间名称")
    private String name;

    @JsonProperty("default")
    @JsonView(AbstractBaseBO.ShowView.class)
    @ApiModelProperty("默认命名空间标识")
    private boolean isDefault;

    /**
     * 树节点
     */
    @Transient
    @ApiModelProperty("树节点")
    @JsonView(ShowTreeView.class)
    private OrganizationTreeDTO tree;

    /**
     * JSON显示视图，显示树结构
     */
    public interface ShowTreeView extends ShowView {
    }

    /**
     * 命名空间常量类
     */
    public interface Constants {
        String DOCUMENT_NAME = "com.polarizon.gendo.sg.namespace";
        String FIELD_ID = "id";
        String FIELD_NAME = "name";
        String FIELD_TENANT_ID = "tenantId";
        String FIELD_DEFAULT = "isDefault";
    }
}
