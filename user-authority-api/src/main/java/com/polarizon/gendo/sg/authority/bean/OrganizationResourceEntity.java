package com.polarizon.gendo.sg.authority.bean;

import com.polarizon.common.bean.bo.AbstractBaseBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import static com.polarizon.gendo.sg.authority.bean.OrganizationResourceEntity.Constants.DOCUMENT_NAME;

/**
 * 组织资源关联表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(DOCUMENT_NAME)
public class OrganizationResourceEntity extends AbstractBaseBO {
    /**
     * 组织id
     */
    private String organizationId;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源类型，user、device
     */
    private String resourceType;

    /**
     * 预留字段
     */
    private Object data;

    /**
     * 常量类
     */
    public static interface Constants {
        String DOCUMENT_NAME = "com.polarizon.gendo.sg.organizationResource";
        String FIELD_ORGANIZATION_ID = "organizationId";
        String FIELD_RESOURCE_ID = "resourceId";
        String FIELD_RESOURCE_TYPE = "resourceType";
    }
}
