{"doc": "标注转换任务", "namespace": "com.polarizon.gendo.task", "type": "record", "logicalType": "controller", "name": "LabelConversionTaskDO", "fields": [{"name": "taskName", "type": "string", "doc": "任务名称"}, {"name": "importLabeledDataFormat", "type": "com.polarizon.gendo.enums.LabeledDataFormatEnum", "doc": "带标注导入，导入数据格式"}, {"name": "datasetVersionID", "type": "string", "doc": "关联数据集版本ID"}, {"name": "datasetVersionBO", "type": "null", "aliases": ["@ReferenceObject(datasetVersionID:DatasetVersion)"], "doc": "关联数据集版本BO"}, {"name": "importTaskID", "type": "string", "doc": "导入任务ID"}, {"name": "labelTaskID", "type": "string", "doc": "智能标注任务ID"}, {"name": "annotationFiles", "type": {"type": "array", "items": "string", "default": []}, "doc": "从外部指定标注文件列表，非必填"}, {"name": "progress", "type": "double", "doc": "转换进度"}, {"name": "importTaskBasePath", "type": "string", "doc": "导入的基础路径"}, {"name": "labelConversionStatus", "type": {"type": "enum", "name": "LabelConversionStatusEnum", "doc": "标注转换: CONVERSING-转换中、COMPLETED-清洗完成、FAILURE-清洗失败", "symbols": ["CONVERSING", "COMPLETED", "FAILURE"]}, "default": "LabelConversionStatusEnum.CONVERSING", "doc": "标注转换"}, {"name": "status", "type": "com.polarizon.gendo.enums.TaskSchedulingStatusEnum", "doc": "任务状态"}, {"type": "long", "name": "finishTime", "doc": "标注转换完成时间"}]}