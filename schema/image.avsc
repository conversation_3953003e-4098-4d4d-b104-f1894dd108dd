{"doc": "镜像信息", "namespace": "com.polarizon.gendo.model", "type": "record", "logicalType": "controller", "name": "ImageDO", "aliases": [], "fields": [{"name": "name", "type": "string", "doc": "算法名称，自定算法中使用"}, {"name": "image", "type": "string", "doc": "算法镜像"}, {"name": "agentType", "type": "com.polarizon.gendo.enums.TaskTypeEnum", "doc": "agent镜像类型"}, {"name": "imageID", "type": "string", "doc": "算法镜像id"}, {"name": "apiVersion", "type": "string", "doc": "算法仓API版本/弃用"}, {"name": "harborAddress", "type": "string", "doc": "算法仓docker仓库地址"}, {"name": "taskType", "type": "com.polarizon.gendo.enums.TaskTypeEnum", "doc": "任务类型"}, {"name": "appType", "type": "string", "doc": "应用类型"}, {"name": "dataType", "type": {"type": "enum", "name": "ImageTypeEnum", "doc": "system-系统内置；custom-自定义模型; custom_image-自定义镜像", "symbols": ["SYSTEM", "CUSTOM", "CUSTOM_IMAGE"]}, "doc": "数据类型，SYSTEM-系统内置;CUSTOM-用户自定义;custom_image-自定义镜像"}, {"name": "algorithmType", "type": "com.polarizon.gendo.enums.AlgorithmTypeEnum", "doc": "算法类型"}, {"name": "framework", "type": "string", "doc": "AI框架"}, {"name": "anns", "type": "string", "doc": "神经网络:Artificial Neural Networks，简写为ANNs"}, {"name": "imageS3", "type": "com.polarizon.gendo.model.S3File", "doc": "镜像前端上传S3地址"}, {"name": "imageBytes", "type": "long", "doc": "镜像文件大小"}, {"name": "scriptS3", "type": "com.polarizon.gendo.model.S3File", "doc": "脚本前端上传S3地址"}, {"name": "scriptBytes", "type": "long", "doc": "脚本文件大小"}, {"name": "dockerfileS3", "type": "com.polarizon.gendo.model.S3File", "doc": "dockerfile前端上传S3地址"}, {"name": "dockerfileBytes", "type": "long", "doc": "dockerfile文件大小"}, {"name": "command", "type": "string", "doc": "默认启动命令，前端输入"}, {"name": "hyperparams", "type": {"type": "array", "items": "com.polarizon.gendo.model.HyperparamBO", "default": []}, "doc": "超参"}, {"name": "sphyParams", "type": {"type": "array", "items": "com.polarizon.gendo.model.HyperparamBO", "default": []}, "doc": "速度优先超参"}, {"name": "prcParams", "type": {"type": "array", "items": "com.polarizon.gendo.model.HyperparamBO", "default": []}, "doc": "精度优先超参"}, {"name": "script", "type": "string", "aliases": ["@HiddenField"], "doc": "脚本文件路径"}, {"name": "loadStatus", "type": {"type": "enum", "name": "LoadStatusEnum", "doc": "loading-加载中；loaded-加载完成; error-加载出错", "symbols": ["LOADING", "LOADED", "ERROR"]}, "doc": "加载状态"}, {"name": "uploadStatus", "type": {"type": "enum", "name": "UploadStatusEnum", "doc": "uploadOnline-在线上传；uploadOffline-离线上传", "symbols": ["UPLOAD_ONLINE", "UPLOAD_OFFLINE"]}, "default": "UploadStatusEnum.UPLOAD_ONLINE", "doc": "上传状态"}, {"name": "uploadImageStatus", "type": "com.polarizon.gendo.enums.UploadImageStatusEnum", "doc": "镜像上传状态"}, {"name": "uploadScriptS3Status", "type": "com.polarizon.gendo.enums.UploadImageStatusEnum", "doc": "脚本上传状态"}, {"name": "uploadDockerfileStatus", "type": "com.polarizon.gendo.enums.UploadImageStatusEnum", "doc": "Dockerfile上传状态"}, {"name": "imageOrDockerfile", "type": {"type": "enum", "name": "ImageOrDockerfileEnum", "doc": "imageType-镜像；dockerFileType-Dockerfile", "symbols": ["IMAGE_TYPE", "DOCKERFILE_TYPE"]}, "doc": "镜像类型"}, {"name": "workbenchBORefList", "aliases": ["@DocumentReference(imageID:Workbench)"], "type": "null", "doc": "工作台关联"}, {"name": "baseImageID", "type": "string", "doc": "基础镜像ID"}, {"name": "baseImageDO", "type": "null", "aliases": ["@ReferenceObject(baseImageID:image)"], "doc": "关联基础镜像BO"}, {"name": "properties", "type": "json", "doc": "自定义配置"}, {"name": "visable", "aliases": ["@ReferenceParam(excludeVisable:string){排除可见度}", "@QueryNotIn(excludeVisable)"], "type": {"type": "array", "items": "string"}, "doc": "可见度（用户id或public静态字符串）"}]}