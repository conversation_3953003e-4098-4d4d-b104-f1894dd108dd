{"doc": "运维监控表", "namespace": "com.polarizon.gendo.app.operation", "type": "record", "logicalType": "controller", "name": "OperationDO", "aliases": ["OperationDO@CompoundIndexes(time_type#{'operationTimeStamp':-1, 'operationType':1})"], "fields": [{"name": "operationTimeStamp", "type": "long", "doc": "运维时间戳"}, {"name": "operationName", "type": "string", "doc": "名称"}, {"name": "taskIds", "type": {"type": "array", "items": "string", "default": []}, "doc": "任务编码"}, {"name": "operationType", "type": "string", "doc": "类型"}, {"name": "extractionRate", "type": "double", "doc": "总抽帧率（用于计算gpu使用占比）"}, {"name": "hostGpuNums", "type": "json", "doc": "使用的主机与gpu编号"}]}