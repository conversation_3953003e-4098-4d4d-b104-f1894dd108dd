{"doc": "乘客统计信息", "namespace": "com.polarizon.gendo.app.detection", "type": "record", "logicalType": "controller", "name": "SystemPassengerStatisticsDO", "aliases": ["SystemPassengerStatisticsDO@CompoundIndexes(collectTime_index#{'collectTime':1})"], "fields": [{"name": "systemId", "type": "string", "doc": "系统ID"}, {"name": "systemDO", "type": "null", "aliases": ["@ReferenceObject(systemId:SystemInfoDO)"], "doc": "关联系统信息BO"}, {"name": "collectTime", "type": "long", "doc": "采集日期"}, {"name": "boardNum", "type": "long", "doc": "登机人数"}, {"name": "verifyNum", "type": "long", "doc": "安检人数"}, {"name": "checkinNum", "type": "long", "doc": "值机人数"}, {"name": "checkinHallNum", "type": "long", "doc": "值机大厅人数"}, {"name": "terminalNum", "type": "long", "doc": "航站楼人流量数据"}, {"name": "inNum", "type": "long", "doc": "进航站楼人数"}, {"name": "outNum", "type": "long", "doc": "出航站楼人数"}, {"name": "verifyAreaNum", "type": "long", "doc": "安检区人数"}, {"name": "checkinAreaNum", "type": "long", "doc": "值机柜台人数"}, {"name": "passengerStatistics", "type": "json", "doc": "乘客统计信息"}]}