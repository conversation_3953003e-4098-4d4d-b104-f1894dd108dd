{"doc": "应用算法", "namespace": "com.polarizon.gendo.app.algo", "type": "record", "logicalType": "controller", "name": "ApplicationAlgorithmDO", "aliases": ["@MongoView", "ApplicationAlgorithm@SoftDelete"], "fields": [{"name": "serviceName", "type": "string", "doc": "业务名称"}, {"name": "config<PERSON><PERSON>us", "type": "boolean", "doc": "是否配置"}, {"name": "publishStatus", "type": {"type": "enum", "name": "ApplicationAlgorithmPublishStatusEnum", "doc": "发布状态: UNPUBLISH, PUBLISHED-已发布", "symbols": ["UNPUBLISH", "PUBLISHED"]}, "default": "ApplicationAlgorithmPublishStatusEnum.UNPUBLISH", "doc": "发布状态"}, {"name": "serviceType", "type": "string", "doc": "业务类型数据字典key-数据字典"}, {"name": "source", "type": {"type": "enum", "name": "ApplicationAlgorithmSourceEnum", "doc": "算法来源:ARRANGE_MODEL-算法编排, WAREHOUSE_ALGORITHM-算法仓算法", "symbols": ["ARRANGE_MODEL", "WAREHOUSE_ALGORITHM"]}, "doc": "算法来源"}, {"name": "sourceID", "type": "string", "doc": "来源ID"}, {"name": "algoModelBO", "type": "null", "aliases": ["@ReferenceObject(sourceID:ArrangeModelDO)"], "doc": "关联编排BO"}, {"name": "industrys", "type": {"type": "array", "items": "string", "default": []}, "doc": "所属区域-数据字典"}, {"name": "sceneClassification", "type": {"type": "array", "items": "string", "default": []}, "doc": "所场景分类-数据字典"}, {"name": "appliedToAirports", "type": {"type": "array", "items": "string", "default": []}, "doc": "应用于机场-数据字典"}, {"name": "cover", "type": "string", "doc": "算法封面图"}, {"name": "detailImg", "type": "string", "doc": "算法详情图"}, {"name": "keywords", "type": {"type": "array", "items": "string", "default": []}, "doc": "关键词"}, {"name": "desc", "type": "string", "doc": "描述"}, {"name": "authDataLimit", "type": "int", "doc": "授权摄像头数量"}, {"name": "authTimeLimit", "type": "long", "doc": "授权时长，单位毫秒"}, {"name": "eventTaskRefList", "aliases": ["@DocumentReference(applicationAlgorithmID:EventTaskDO)"], "type": "null", "doc": "任务引用列表"}]}