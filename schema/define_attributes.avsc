{"doc": "标签属性", "namespace": "com.polarizon.gendo.label", "type": "record", "logicalType": "controller", "name": "AttributeDefinitionDO", "aliases": ["AttributeDefinition@CascadeDelete(attributeID:AttributeValue)", "AttributeDefinition@OpaCheckDefine(NoDuplicatName:/label/define/attribute/same/name)", "AttributeDefinition@OpaCheckDefine(NoUsedName:/label/define/attribute/name/use)", "AttributeDefinition@WhenAddDoCheck(NoDuplicatName:/label/define/attribute/same/name)", "AttributeDefinition@WhenChgDoCheck(NoDuplicatName:/label/define/attribute/name/use)", "AttributeDefinition@WhenDelDoCheck(NoDuplicatName:/label/define/attribute/name/use)"], "fields": [{"name": "tagID", "type": "string", "doc": "标签ID"}, {"name": "tagBO", "type": "null", "aliases": ["@ReferenceObject(tagID:Tag)"], "doc": "父标签BO"}, {"name": "spec_id", "aliases": ["@AutoIncrement"], "type": "long", "doc": "属性自增ID"}, {"name": "name", "type": "string", "doc": "属性名"}, {"name": "default_value", "type": "string", "doc": "values默认值"}, {"name": "mutable", "type": "boolean", "doc": "可变的"}, {"name": "input_type", "type": {"type": "enum", "name": "LabelAttributeInputTypeEnum", "doc": "输入数据类型: 单选-radio, 多选-checkbox, 文本-text", "symbols": ["radio", "checkbox", "text"]}, "doc": "输入数据类型"}, {"name": "values", "type": "null", "aliases": ["@DocumentReference(attributeID:AttributeValue)"], "doc": "属性值集合"}]}