{"doc": "远端盒子规则配置", "namespace": "com.polarizon.gendo.detection.process.bo", "type": "record", "name": "EdgeBindBO", "fields": [{"name": "edgeCode", "type": "string", "doc": "远端盒子id"}, {"name": "edgeType", "type": {"type": "enum", "name": "EdgeTypeEnum", "doc": "box-盒子；camera-摄像头", "symbols": ["box", "camera"]}, "doc": "远端盒子id"}, {"name": "modelIDs", "type": {"type": "array", "items": "string"}, "doc": "下发模型IDs"}]}