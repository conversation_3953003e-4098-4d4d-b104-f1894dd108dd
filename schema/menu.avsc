{"doc": "权限", "namespace": "com.polarizon.gendo.user.authority", "type": "record", "logicalType": "controller", "name": "MenuDO", "fields": [{"name": "menuName", "type": "string", "doc": "权限名称"}, {"name": "parentId", "type": "string", "doc": "上级权限id"}, {"name": "perms", "type": "string", "doc": "权限字符"}, {"name": "orderNum", "type": "string", "doc": "排序"}, {"name": "path", "type": "string", "doc": "路由"}, {"name": "component", "type": "string", "doc": "组件"}, {"name": "icon", "type": "string", "doc": "图标"}, {"name": "menuType", "type": "string", "doc": "菜单类型"}, {"name": "children", "type": {"type": "array", "items": {"type": "com.polarizon.gendo.user.authority.MenuDO"}, "default": []}, "doc": "子部门"}]}