{"doc": "升级申请", "namespace": "com.polarizon.gendo.asset", "type": "record", "logicalType": "controller", "name": "AssetProcessDO", "aliases": ["AssetProcessDO@DomainIgnores(orgId)"], "fields": [{"name": "userId", "type": "string", "doc": "用户编号(id)"}, {"name": "processStatus", "type": "int", "default": "0", "doc": "审批状态"}, {"name": "currentGradeId", "type": "string", "doc": "当前等级编号"}, {"name": "targetGradeId", "type": "string", "doc": "目标等级编号"}, {"name": "reason", "type": "string", "doc": "申请理由"}, {"name": "domainAddress", "type": "string", "doc": "审批界面的地址"}]}