package com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp;

import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.primitives.Bytes;
import com.polarizon.gendo.sg.device.configuration.bean.DeviceBean;
import com.polarizon.gendo.sg.device.configuration.service.monitor.netty.RtmpClientHandler;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp.amf.AmfNumber;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp.amf.AmfObject;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp.amf.AmfString;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp.amf.AmfType;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * rtmp会话
 */
@Slf4j
@Data
public class RtmpSession implements RtmpClientHandler {
    private int transactionId = 0;
    private long lastTime;
    private String app;
    private String tcUrl;
    private int index;
    Pattern statusMsgPattern = Pattern.compile("[\\S\\s]*(onStatus.*[status|error])[\\S\\s]*");
    /**
     * 超时
     */
    private List<String> streams = Lists.newArrayList();
    private List<Byte> S_MSG = new ArrayList<>();
    List<DeviceBean> deviceBeans;

    /**
     * 构造
     *
     * @param deviceBeans
     */
    public RtmpSession(List<DeviceBean> deviceBeans) {
        String address = deviceBeans.get(0).getRealAddr();
        this.index = 0;
        this.tcUrl = address.substring(0, address.lastIndexOf("/"));
        this.app = this.tcUrl.substring(0, this.tcUrl.lastIndexOf("/"));
        this.deviceBeans = deviceBeans;
        for (DeviceBean deviceBean : deviceBeans) {
            String rtmpUrl = deviceBean.getRealAddr();
            streams.add(rtmpUrl.substring(address.lastIndexOf("/")));
        }
    }

    /**
     * 握手
     *
     * @param ctx
     * @return
     */
    public int handShake(ChannelHandlerContext ctx) {
        ByteBuf optionsC1 = ctx.alloc().buffer(handshakeSize + 1);
        // 发送C0消息
        optionsC1.writeByte(protocolVersion);
        byte[] c1 = C1(handshakeSize);
//        log.info("c1数据块：{}", c1);
        // 发送C1消息
        optionsC1.writeBytes(c1);
        ctx.writeAndFlush(optionsC1);
        return S_STATUS;
    }

    /**
     * 发送消息
     *
     * @param byteBuf
     * @param ctx
     * @return
     * @throws IOException
     */
    public int sMsg(ByteBuf byteBuf, ChannelHandlerContext ctx) throws IOException {
        // 接收S0消息
        byte[] byteArray = new byte[byteBuf.readableBytes()];
        byteBuf.getBytes(0, byteArray);
        S_MSG.addAll(Bytes.asList(byteArray));
        final ByteBuf buf = ctx.alloc().buffer();
        if (S_MSG.size() >= 1 + handshakeSize && S_MSG.size() < 1 + 2 * handshakeSize) {
            List<Byte> s1 = S_MSG.subList(1, 1 + handshakeSize);
            //发送C2消息
            buf.writeBytes(Bytes.toArray(s1));
            ctx.writeAndFlush(buf);
            return S_STATUS;
        }
        if (S_MSG.size() >= 1 + 2 * handshakeSize) {
            //发送connect消息
            RtmpHeader header = new RtmpHeader(RtmpHeader.ChunkType.TYPE_0_FULL, RTMP_COMMAND_CHANNEL, RtmpHeader.MessageType.COMMAND_AMF0);
            byte[] bodyBytes = buildConnectBody();
            header.setPacketLength(bodyBytes.length);
//            log.info("握手成功，开始发送connect消息-----------");
            // Write header for first chunk
            header.writeTo(buf);
            int remainingBytes = bodyBytes.length;
            int pos = 0;
            int chunkSize = 128;
            while (remainingBytes > chunkSize) {
                buf.writeBytes(bodyBytes, pos, chunkSize);
                remainingBytes -= chunkSize;
                pos += chunkSize;
                header.writeAggregateHeaderByte(buf);
            }
            buf.writeBytes(bodyBytes, pos, remainingBytes);
            ctx.writeAndFlush(buf);
            return CONNECT_STATUS;
        }
        return S_STATUS;
    }

    /**
     * 建立连接
     *
     * @param byteBuf
     * @param ctx
     * @return
     * @throws IOException
     */
    public int connect(ByteBuf byteBuf, ChannelHandlerContext ctx) throws IOException {
        byte[] byteArray = new byte[byteBuf.readableBytes()];
        byteBuf.getBytes(0, byteArray);
        String msg = new String(byteArray);
        if (msg.contains("onBWDone")) {
//            log.info("建立连接成功，开始发送createStream消息-----------");
            RtmpHeader header = new RtmpHeader(RtmpHeader.ChunkType.TYPE_1_RELATIVE_LARGE, RTMP_COMMAND_CHANNEL, RtmpHeader.MessageType.COMMAND_AMF0);
            byte[] bodyBytes = buildCreateStreamBody();
            header.setPacketLength(bodyBytes.length);
            final ByteBuf createStream = ctx.alloc().buffer();
            header.writeTo(createStream);
            int remainingBytes = bodyBytes.length;
            int pos = 0;
            int chunkSize = 128;
            while (remainingBytes > chunkSize) {
                createStream.writeBytes(bodyBytes, pos, chunkSize);
                remainingBytes -= chunkSize;
                pos += chunkSize;
                header.writeAggregateHeaderByte(createStream);
            }
            createStream.writeBytes(bodyBytes, pos, remainingBytes);
            ChannelFuture future = ctx.writeAndFlush(createStream);
            future.addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture channelFuture) throws Exception {
                    if (channelFuture.isSuccess()) {
//                        log.info("发送createStream消息成功----------");
                    }
                }
            });
            byteBuf.clear();
            return CREATE_STREAM_STATUS;
        }
        return CONNECT_STATUS;
    }

    /**
     * 创建流通道
     *
     * @param byteBuf
     * @param ctx
     * @return
     * @throws IOException
     */
    public int createStream(ByteBuf byteBuf, ChannelHandlerContext ctx) throws IOException {
        byte[] byteArray = new byte[byteBuf.readableBytes()];
        byteBuf.getBytes(0, byteArray);
        String msg = new String(byteArray);
        if (msg.contains("result")) {
//            log.info("创建流通道成功，开始发送play消息-----------");
//            log.info("stream：{}", deviceBeans.get(index).getRealAddr());
            RtmpHeader header = new RtmpHeader(RtmpHeader.ChunkType.TYPE_0_FULL, RTMP_STREAM_CHANNEL, RtmpHeader.MessageType.COMMAND_AMF0);
            byte[] bodyBytes = buildPlayBody();
            header.setPacketLength(bodyBytes.length);
            final ByteBuf createStream = ctx.alloc().buffer();
            header.writeTo(createStream);
            int remainingBytes = bodyBytes.length;
            int pos = 0;
            int chunkSize = 128;
            while (remainingBytes > chunkSize) {
                createStream.writeBytes(bodyBytes, pos, chunkSize);
                remainingBytes -= chunkSize;
                pos += chunkSize;
                header.writeAggregateHeaderByte(createStream);
            }
            createStream.writeBytes(bodyBytes, pos, remainingBytes);
            ChannelFuture future = ctx.writeAndFlush(createStream);
            future.addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture channelFuture) throws Exception {
                    if (channelFuture.isSuccess()) {
//                        log.info("{}发送play消息成功----------", tcUrl);
                    }
                    lastTime = System.currentTimeMillis();
                }
            });
            byteBuf.clear();
            return PLAY_STATUS;
        }
        return CREATE_STREAM_STATUS;
    }

    /**
     * 响应
     *
     * @param msg
     * @param ctx
     * @param deviceStatusMap
     * @return
     */
    public int onPlay(String msg, ChannelHandlerContext ctx, Multimap<Boolean, DeviceBean> deviceStatusMap) {
        return Optional.of(msg)
            .filter(m -> m.contains(ON_STATUS))
//            .map(m -> {
//                log.info("{}play响应消息：{}", deviceBeans.get(index).getRealAddr(), m);
//                return m;
//            })
            .map(m -> {
                Matcher matcher = statusMsgPattern.matcher(msg);
                if (matcher.find()) {
//                    log.info("play响应消息：{}", matcher.group(1));
                }
                if (m.contains(STREAM_ERROR)) {
                    deviceStatusMap.put(false, deviceBeans.get(index));
                    index++;
                    return ERROR_STATUS;
                } else {
                    deviceStatusMap.put(true, deviceBeans.get(index));
                }
                index++;
                if (index < streams.size()) {
//                    log.info("stream：{}", streams.get(index));
                    play(ctx);
                    return PLAY_STATUS;
                } else {
                    return END_STATUS;
                }
            }).orElseGet(() -> {
                //超时则校验下一个摄像头
                if (System.currentTimeMillis() - lastTime > TIMEOUT) {
                    deviceStatusMap.put(false, deviceBeans.get(index));
                    index++;
                    if (index < streams.size()) {
//                        log.info("超时stream：{}", deviceBeans.get(index).getRealAddr());
                        play(ctx);
                    } else {
                        return END_STATUS;
                    }
                }
                return PLAY_STATUS;
            });
    }

    /**
     * 响应
     *
     * @param ctx
     */
    private void play(ChannelHandlerContext ctx) {
        try {
            RtmpHeader header = new RtmpHeader(RtmpHeader.ChunkType.TYPE_0_FULL, RTMP_STREAM_CHANNEL, RtmpHeader.MessageType.COMMAND_AMF0);
            byte[] bodyBytes = buildPlayBody();
            header.setPacketLength(bodyBytes.length);
            final ByteBuf createStream = ctx.alloc().buffer();
            header.writeTo(createStream);
            int remainingBytes = bodyBytes.length;
            int pos = 0;
            int chunkSize = 128;
            while (remainingBytes > chunkSize) {
                createStream.writeBytes(bodyBytes, pos, chunkSize);
                remainingBytes -= chunkSize;
                pos += chunkSize;
                header.writeAggregateHeaderByte(createStream);
            }
            createStream.writeBytes(bodyBytes, pos, remainingBytes);
            ChannelFuture future = ctx.writeAndFlush(createStream);
            future.addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture channelFuture) throws Exception {
                    if (channelFuture.isSuccess()) {
//                        log.info("发送play消息成功----------");
                    }
                    lastTime = System.currentTimeMillis();
                }
            });
        } catch (Exception e) {
            log.info("play消息发送失败", e);
        }
    }

//    private void delStream(ChannelHandlerContext ctx) {
//        try {
//            RtmpHeader header = new RtmpHeader(RtmpHeader.ChunkType.TYPE_0_FULL, RTMP_STREAM_CHANNEL, RtmpHeader.MessageType.COMMAND_AMF0);
//            byte[] bodyBytes = buildDelBody();
//            header.setPacketLength(bodyBytes.length);
//            final ByteBuf createStream = ctx.alloc().buffer();
//            header.writeTo(createStream);
//            int remainingBytes = bodyBytes.length;
//            int pos = 0;
//            int chunkSize = 128;
//            while (remainingBytes > chunkSize) {
//                createStream.writeBytes(bodyBytes, pos, chunkSize);
//                remainingBytes -= chunkSize;
//                pos += chunkSize;
//                header.writeAggregateHeaderByte(createStream);
//            }
//            createStream.writeBytes(bodyBytes, pos, remainingBytes);
//            ChannelFuture future = ctx.writeAndFlush(createStream);
//            future.addListener(new ChannelFutureListener() {
//                @Override
//                public void operationComplete(ChannelFuture channelFuture) throws Exception {
//                    if (channelFuture.isSuccess()){
////                        log.info("发送play消息成功----------");
//                    }
//                    lastTime = System.currentTimeMillis();
//                }
//            });
//        } catch (Exception e){
//            log.info("play消息发送失败", e);
//        }
//    }

    /**
     * 构建连接内容
     *
     * @return
     * @throws IOException
     */
    private byte[] buildConnectBody() throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        AmfString.writeStringTo(out, "connect", false);
        AmfNumber.writeNumberTo(out, 0);
        AmfObject args = new AmfObject();
        args.setProperty("app", app);
        args.setProperty("flashVer", "LNX 9,0,124,2"); // Flash player OS: Linux, version: 11.2.202.233
        args.setProperty("tcUrl", tcUrl);
        args.setProperty("fpad", false);
        args.setProperty("capabilities", 15);
        args.setProperty("audioCodecs", 4071);
        args.setProperty("videoCodecs", 252);
        args.setProperty("videoFunction", 1);
        args.writeTo(out);
        return out.toByteArray();
    }

    /**
     * 构建流内容
     *
     * @return
     * @throws IOException
     */
    private byte[] buildCreateStreamBody() throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        AmfString.writeStringTo(out, "createStream", false);
        AmfNumber.writeNumberTo(out, 0);
        out.write(AmfType.NULL.getValue());
        return out.toByteArray();
    }

    /**
     * 构建响应内容
     *
     * @return
     * @throws IOException
     */
    private byte[] buildPlayBody() throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        AmfString.writeStringTo(out, "play", false);
        AmfNumber.writeNumberTo(out, 0);
        out.write(AmfType.NULL.getValue());
        AmfString.writeStringTo(out, streams.get(index), false);
        AmfNumber.writeNumberTo(out, -2000);
//        AmfBoolean.writeTo(out, true);
        return out.toByteArray();
    }

//    private byte[] buildDelBody() throws IOException {
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        AmfString.writeStringTo(out, "deleteStream", false);
//        AmfNumber.writeNumberTo(out, transactionId++);
//        out.write(AmfType.NULL.getValue());
//        AmfString.writeStringTo(out, streams.get(index), false);
//        return out.toByteArray();
//    }

    /**
     * 创建指定长度的字节数组
     *
     * @param length
     * @return
     */
    private static byte[] C1(int length) {
        byte[] data = new byte[length];
        new Random().nextBytes(data);
        return data;
    }
}
