package com.polarizon.gendo.sg.device.configuration.service.monitor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.polarizon.gendo.sg.authority.api.SystemControllerInterface;
import com.polarizon.gendo.sg.authority.bean.bo.SystemLogBO;
import com.polarizon.gendo.sg.authority.enums.SysLogType;
import com.polarizon.gendo.sg.common.utils.fault.constant.FaultConstants;
import com.polarizon.gendo.sg.common.utils.fault.enums.PromethusQuery;
import com.polarizon.gendo.sg.common.utils.fault.enums.RunFault;
import com.polarizon.gendo.sg.device.configuration.api.PromethusApiFeignClient;
import com.polarizon.gendo.sg.device.configuration.bean.dto.PromethusResultDTO;
import com.polarizon.gendo.sg.device.configuration.bean.vo.PromethusDataVO;
import com.polarizon.gendo.sg.device.configuration.utils.DeviceConstants;
import com.polarizon.gendo.sg.device.configuration.utils.RedisUtil;
import com.polarizon.gendo.sg.device.configuration.utils.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 普罗米修斯定时器
 */
@Component
@Slf4j
public class PromethusMonitor {

    @Resource
    private PromethusApiFeignClient promethusApiFeignClient;

    @Resource
    private SystemControllerInterface systemControllerInterface;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询时间步长
     */
    @Value("${polarise.promethus.fault-step}")
    private String faultStep;

    /**
     * 定时任务执行时间间隔
     */
    @Value("${polarise.promethus.fault-time}")
    private Long faultTime;

    /**
     * 故障指标key过期时间（半个小时内，不重复记录相同故障指标到日志库）
     */
    private String lastTimeKey = "polarise.fault:lasttime";

    /**
     * key结构：promethus:fault:server:{instanceid}:{faultCode}
     */
    private String faultInstanceKey = "polarise.fault:" + FaultConstants.FaultIndex.SERVER + ":{}:{}";

    private final long faultKeyExpireTime = 30 * 60L;

    /**
     * 监控硬件信息是否异常
     */
    @Scheduled(fixedDelayString = "${polarise.promethus.fault-time}")
    public void serverMonitor() {
        //设置上次查询信息时间
        if (!redisUtil.exists(lastTimeKey)) {
            redisUtil.set(lastTimeKey, DateUtil.currentSeconds() - (faultTime / 1000));
        }
        //上次查询信息时间
        long lastTime = Long.valueOf((Integer)redisUtil.get(lastTimeKey));

        //当前时间
        long nowTime = DateUtil.currentSeconds();

        //替换network查询参数
        PromethusQuery network = PromethusQuery.NETWORK;
        if (network.getUsage().contains("${device}")) {
            //network信息
            PromethusResultDTO promethusResultDTO = promethusApiFeignClient.query(FaultConstants.PromethusQuery.NETWORK_INFO, nowTime);
            PromethusDataVO data = promethusResultDTO.getData();
            PromethusDataVO.PromethusInstance instance = data.getResult().stream().findFirst().orElse(new PromethusDataVO.PromethusInstance());
            if (!ObjectUtils.isEmpty(instance.getMetric().getDevice())) {
                network.setUsage(network.getUsage().replace("${device}", instance.getMetric().getDevice()));
                network.setTotal(network.getTotal().replace("${device}", instance.getMetric().getDevice()));
            }
        }

        //cpu、gpu、内存、硬盘指标异常查询
        List<String> indexs = Arrays.asList(FaultConstants.FaultIndex.CPU, FaultConstants.FaultIndex.GPU, FaultConstants.FaultIndex.DISK, FaultConstants.FaultIndex.MEMORY, FaultConstants.FaultIndex.NETWORK);
        List<CompletableFuture> completableFutures = indexs.stream().map(index -> runQuery(index, lastTime, nowTime)).collect(Collectors.toList());
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));
        redisUtil.set(lastTimeKey, nowTime);
    }

    /**
     * 指标异常捕获
     *
     * @param index    指标
     * @param lastTime
     * @param nowTime
     * @return
     */
    private CompletableFuture runQuery(String index, long lastTime, long nowTime) {
        PromethusQuery promethusQuery = PromethusQuery.getPromethusQueryByIndex(index);
        CompletableFuture completableFuture = CompletableFuture.supplyAsync(() -> {
            //指标利用率
            PromethusResultDTO cpuUsageRes = promethusApiFeignClient.queryRange(promethusQuery.getUsage().concat(">=" + RunFault.getThresholdByIndex(index)), lastTime, nowTime, faultStep);
            return cpuUsageRes.getData();
        }, ThreadPoolUtils.COMMON_THREAD_POOL)
            .thenApply(cpuUsageData -> {
                //服务器实例对应的故障信息
                HashMap<String, Map<RunFault, List<Object>>> instanceFaultMap = Maps.newHashMap();
                ArrayList<SystemLogBO> systemLogBOS = Lists.newArrayList();
                try {
                    for (PromethusDataVO.PromethusInstance instance : cpuUsageData.getResult()) {
                        //获取不同等级故障的指标信息（相同等级故障不重复记录）
                        Map<RunFault, List<Object>> faultMap = instance.getValues().stream()
                            .collect(Collectors.toMap(val -> RunFault.getRunFaultByIndex(index, Double.valueOf((String) val.get(1))), Function.identity(), (x, y) -> x));
                        //过滤掉已经缓存的异常
                        faultMap = faultMap.entrySet().stream()
                            .filter(entry -> !redisUtil.exists(StrFormatter.format(faultInstanceKey, instance.getMetric().getInstance(), entry.getKey().getCode())))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (x, y) -> x));

                        if (!faultMap.isEmpty()) {
                            instanceFaultMap.put(instance.getMetric().getInstance(), faultMap);
                        }
                    }
                    //存在指标利用率异常则查询指标总量
                    if (!instanceFaultMap.isEmpty()) {
                        //cpu总量
                        PromethusResultDTO cpuTotalRes = promethusApiFeignClient.query(promethusQuery.getTotal(), lastTime);
                        Map<String, PromethusDataVO.PromethusInstance> instanceMap = cpuTotalRes.getData().getResult().stream()
                            .collect(Collectors.toMap(cpuTotal -> cpuTotal.getMetric().getInstance(), Function.identity(), (x, y) -> x));
                        //遍历故障服务器实例
                        instanceFaultMap.forEach((instance, faultMap) -> {
                            //服务器指标总量
                            PromethusDataVO.PromethusInstance promethusInstance = instanceMap.get(instance);
                            JSONObject logDetail = new JSONObject();
                            logDetail.put(FaultConstants.FaultDetailField.IP, promethusInstance.getMetric().getInstance());
                            logDetail.put(FaultConstants.FaultIndex.TOTAL(promethusQuery.getIndex()), promethusInstance.getValue().get(1));
                            faultMap.forEach((runFault, val) -> {
                                logDetail.put(FaultConstants.FaultIndex.USAGE(promethusQuery.getIndex()), (new BigDecimal((String) val.get(1)).setScale(2, RoundingMode.HALF_UP)).toString().concat("%"));
                                //平台服务器故障日志
                                SystemLogBO systemLogBO = SystemLogBO.builder()
                                    .sysLogType(SysLogType.RUN_ERROR)
                                    .tenantId(null)
                                    .faultType(DeviceConstants.PLATE_FAULT)
                                    .faultName(runFault.getDesc())
                                    .faultCode(runFault.getCode())
                                    .faultLevel(runFault.getLevel())
                                    .logContent(logDetail.toJSONString())
                                    .build();
                                systemLogBOS.add(systemLogBO);
                                //缓存当前异常，过期时间为半小时（半小时内不记录同一服务器的相同异常）
                                redisUtil.set(StrFormatter.format(faultInstanceKey, promethusInstance.getMetric().getInstance(), runFault.getCode()), 1, faultKeyExpireTime);
                            });
                        });
                        systemControllerInterface.insertLogs(systemLogBOS);
                    }
                } catch (Exception e) {
                    log.error("记录平台故障日志异常", e);
                }
                return this;
            });
        return completableFuture;
    }

}
