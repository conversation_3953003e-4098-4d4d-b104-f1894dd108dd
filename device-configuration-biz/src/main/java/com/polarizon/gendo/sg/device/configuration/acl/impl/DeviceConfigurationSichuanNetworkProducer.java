package com.polarizon.gendo.sg.device.configuration.acl.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.polarizon.gendo.sg.device.configuration.acl.DeviceConfigurationSichuanNetworkAcl;
import com.polarizon.gendo.sg.device.configuration.acl.bean.AreaBean;
import com.polarizon.gendo.sg.device.configuration.acl.bean.AreaCameraBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备配置生产者
 */
@Slf4j
public class DeviceConfigurationSichuanNetworkProducer implements Runnable {

    /**
     * 阻塞队列
     */
    private final BlockingQueue<AreaCameraBean> blockingQueue;

    /**
     * 完成标识符
     */
    private final AtomicBoolean atomicBoolean;

    /**
     * 定义线程池
     */
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 构造
     * @param blockingQueue
     * @param atomicBoolean
     */
    public DeviceConfigurationSichuanNetworkProducer(BlockingQueue<AreaCameraBean> blockingQueue, AtomicBoolean atomicBoolean) {
        this.blockingQueue = blockingQueue;
        this.atomicBoolean = atomicBoolean;
    }

    /**
     * 执行
     */
    @Override
    public void run() {
        // 获取川网防腐层 Bean
        DeviceConfigurationSichuanNetworkAcl deviceConfigurationSichuanNetworkAcl = SpringUtil.getBean(DeviceConfigurationSichuanNetworkAcl.class);

        // 获取川网鉴权
        String authentication = deviceConfigurationSichuanNetworkAcl.authentication();

        // 获取根节点区域数据
        AreaBean parentArea = deviceConfigurationSichuanNetworkAcl.findRootArea(authentication);

        // 添加根节点摄像头到阻塞队列
        threadPoolExecutor.execute(() -> addCameraToBlockingQueue(parentArea, authentication));

        // 获取子节点区域数据
        List<AreaBean> childDeviceAreas = deviceConfigurationSichuanNetworkAcl.findChildAreasAndUpdateName(parentArea.getId(), parentArea.getName(), authentication);

        // 遍历添加父节点下子节点（广度优先遍历）
        while (CollectionUtils.isNotEmpty(childDeviceAreas)) {
            List<AreaBean> descendantDeviceAreas = Lists.newArrayList();

            // 遍历子节点
            for (AreaBean childDeviceArea : childDeviceAreas) {
                // 1、添加子节点到阻塞队列
                threadPoolExecutor.execute(() -> addCameraToBlockingQueue(childDeviceArea, authentication));

                // 2、添加子孙节点到列表
                descendantDeviceAreas.addAll(deviceConfigurationSichuanNetworkAcl.findChildAreasAndUpdateName(childDeviceArea.getId(), childDeviceArea.getName(), authentication));
            }

            // 子节点层遍历完成，设置孙节点成为新的子节点
            childDeviceAreas = descendantDeviceAreas;
        }

        // 停止所有任务
        threadPoolExecutor.shutdown();

        // 节点遍历完成设置标识符
        atomicBoolean.compareAndSet(false, true);
    }

    /**
     * 添加摄像头信息到阻塞队列
     *
     * @param areaBean       区域 Bean
     * @param authentication 鉴权
     */
    private void addCameraToBlockingQueue(AreaBean areaBean, String authentication) {
        // 获取川网防腐层 Bean
        DeviceConfigurationSichuanNetworkAcl deviceConfigurationSichuanNetworkAcl = SpringUtil.getBean(DeviceConfigurationSichuanNetworkAcl.class);

        // 获取区域摄像头集合
        List<AreaCameraBean> areaCameraBeans = deviceConfigurationSichuanNetworkAcl.findAreaCameraBean(areaBean.getId(), areaBean.getName(), authentication);

        // 遍历摄像头信息
        for (AreaCameraBean areaCameraBean : areaCameraBeans) {
            if (Objects.isNull(areaCameraBean)) {
                continue;
            }

            try {
                // 添加数据到阻塞队列
                blockingQueue.put(areaCameraBean);
            } catch (InterruptedException e) {
                log.error("生产失败，出现中断异常，请联系管理员", e);
                Thread.currentThread().interrupt();
            }
        }
    }

}
