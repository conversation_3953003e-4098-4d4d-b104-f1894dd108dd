package com.polarizon.gendo.sg.device.configuration.service.monitor.netty;

import com.google.common.collect.Multimap;
import com.polarizon.gendo.sg.device.configuration.bean.DeviceBean;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtmp.RtmpSession;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.ReferenceCountUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

import static java.lang.Thread.sleep;

/**
 * Rtmp监听
 */
@Data
@Slf4j
public class ConnectRtmpClientHandler extends SimpleChannelInboundHandler<ByteBuf> implements RtmpClientHandler {
    private int status;
    private String tcUrl;
    public volatile boolean flag = true;
    private RtmpSession rtmpSession;
    Consumer<RtmpSession> completeConsumer;
    Multimap<Boolean, DeviceBean> deviceStatusMap;
    private volatile Long lastTime;

    /**
     * 构造
     *
     * @param deviceBeans
     * @param deviceStatusMap
     * @param completeConsumer
     */
    public ConnectRtmpClientHandler(List<DeviceBean> deviceBeans, Multimap<Boolean, DeviceBean> deviceStatusMap,
                                    Consumer<RtmpSession> completeConsumer) {
        String address = deviceBeans.get(0).getRealAddr();
        this.tcUrl = address.substring(0, address.lastIndexOf("/"));
        rtmpSession = new RtmpSession(deviceBeans);
        this.completeConsumer = completeConsumer;
        this.deviceStatusMap = deviceStatusMap;
    }

    /**
     * 接收
     *
     * @param ctx
     * @param message
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object message) {
        ByteBuf byteBuf = (ByteBuf) message;
        lastTime = System.currentTimeMillis();
        try {
            switch (status) {
                //接收S消息
                case S_STATUS:
                    status = rtmpSession.sMsg(byteBuf, ctx);
                    return;
                //接收connect消息
                case CONNECT_STATUS:
                    status = rtmpSession.connect(byteBuf, ctx);
                    return;
                //接收CREATE_STREAM消息
                case CREATE_STREAM_STATUS:
                    status = rtmpSession.createStream(byteBuf, ctx);
                    return;
                //接收play消息
                case PLAY_STATUS:
                    byte[] byteArray = new byte[byteBuf.readableBytes()];
                    byteBuf.getBytes(0, byteArray);
                    String msg = new String(byteArray);
                    status = rtmpSession.onPlay(msg, ctx, deviceStatusMap);
                    if (status == ERROR_STATUS || status == END_STATUS) {
                        ctx.disconnect();
                        ctx.close();
                        completeConsumer.accept(rtmpSession);
                    }
                    return;
                default:
                    return;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            error(ctx);
        } finally {
            //释放缓冲
            ReferenceCountUtil.release(byteBuf);
        }
    }

    /**
     * 超时检查
     *
     * @param ctx
     */
    private void checkTimeOut(ChannelHandlerContext ctx) {
        lastTime = System.currentTimeMillis();
        CompletableFuture.runAsync(() -> {
            while (true) {
                Long now = System.currentTimeMillis();
                if (now - lastTime > 2 * 1000) {
//                log.info("{}超时:{}", rtmpSession.getTcUrl(), status);
                    if (status != ERROR_STATUS && status != END_STATUS) {
                        error(ctx);
                    }
                    return;
                }
                try {
                    sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 出错
     *
     * @param ctx
     */
    private void error(ChannelHandlerContext ctx) {
        status = ERROR_STATUS;
        ctx.disconnect();
        ctx.close();
        int index = rtmpSession.getIndex();
        rtmpSession.setIndex(index + 1);
        deviceStatusMap.put(false, rtmpSession.getDeviceBeans().get(index));
        completeConsumer.accept(rtmpSession);
    }

    @Override
    protected void channelRead0(ChannelHandlerContext channelHandlerContext, ByteBuf byteBuf) throws Exception {

    }

    /***
     * 建立连接立即执行
     * @param ctx
     * @throws Exception
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
//        log.info("--------------RTMP握手开始-----------------");
        status = rtmpSession.handShake(ctx);
        checkTimeOut(ctx);
    }

    /**
     * 捕获的异常
     *
     * @param ctx
     * @param cause
     * @throws Exception
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.info(cause.getMessage());
        error(ctx);
    }


}
