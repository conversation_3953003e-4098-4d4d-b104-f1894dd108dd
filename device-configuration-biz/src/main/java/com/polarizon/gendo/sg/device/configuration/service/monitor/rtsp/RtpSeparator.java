package com.polarizon.gendo.sg.device.configuration.service.monitor.rtsp;


import com.polarizon.gendo.sg.device.configuration.service.monitor.rtsp.util.ByteArrayUtils;
import com.polarizon.gendo.sg.device.configuration.service.monitor.rtsp.vo.RTPPacket;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * rtp 解码
 */
public class RtpSeparator {

    /**
     * RTP 包标识
     */
    public byte RTPChannelNumber = 0x00;
    /**
     * RTCP 包标识
     */
    public byte RTCPChannelNumber = 0x01;
    /**
     * RTSP包中 RTCP包和 RTP包的标识
     */
    byte rtspHeaderMagic = 0x24; //36 $
    int rtspDataHeaderLength = 4;
    byte[] lastArray = new byte[0];


    /**********************************解码***********************************/

    public List<RTPPacket> readPackage(byte[] bytes) {

        byte[] allBuf = new byte[bytes.length + lastArray.length];
        System.arraycopy(lastArray, 0, allBuf, 0, lastArray.length);
        System.arraycopy(bytes, 0, allBuf, lastArray.length, bytes.length);
        lastArray = new byte[0];

        List<RTPPacket> result = new ArrayList<>();

        while (true) {
            //rtsp 协议解析
            // '$'符号开头，表明是RTP包或者RTCP包
            if (allBuf[0] == rtspHeaderMagic) {
                if (allBuf.length < rtspDataHeaderLength) {
                    lastArray = allBuf;
                    break;
                }

                //完整的rtp包长度
                int length = ((allBuf[2] & 0xff) << 8) + (allBuf[3] & 0xff);
                //rtp包不完整
                if (allBuf.length < (length + rtspDataHeaderLength)) {
                    lastArray = allBuf;
                    break;
                }

                //rtp 包
                if (allBuf[1] == RTPChannelNumber) {
                    addResult(result, ByteArrayUtils.subArry(allBuf, 0, length + rtspDataHeaderLength), "RTP");
                }
                if (allBuf[1] == RTCPChannelNumber) {
                    addResult(result, ByteArrayUtils.subArry(allBuf, 0, length + rtspDataHeaderLength), "RTCP");
                }

                // 包的长度不够，退出并等待下一次数据过来
                if (allBuf.length <= (length + rtspDataHeaderLength)) {
                    lastArray = ByteArrayUtils.subArry(allBuf, length + rtspDataHeaderLength);
                    break;
                }
                // 包的长度足够，处理下次数据
                allBuf = ByteArrayUtils.subArry(allBuf, length + rtspDataHeaderLength);
            } else {
                //rtsp 包
                int flag = 0;
                for (int j = 0; j < allBuf.length; j++) {
                    if ((allBuf[j] == rtspHeaderMagic && j > 0)) {
                        addResult(result, ByteArrayUtils.subArry(allBuf, 0, j), "RTSP");
                        flag = 1;
                        allBuf = ByteArrayUtils.subArry(allBuf, j);
                        break;
                    }
                }
                if (flag == 0 && allBuf.length != 0) {
                    addResult(result, allBuf, "RTSP");
                    break;
                }
            }
        }
        Collections.reverse(result);
        return result;
    }

    /**
     * 追加结果
     *
     * @param result
     * @param data
     * @param type
     */
    private void addResult(List<RTPPacket> result, byte[] data, String type) {
        RTPPacket rtpPacket = new RTPPacket();
        rtpPacket.type = type;
        rtpPacket.data = data;
        result.add(rtpPacket);
    }
}
