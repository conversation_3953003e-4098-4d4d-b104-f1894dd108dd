package com.polarizon.gendo.sg.device.configuration.acl.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MediaserverCameraBean {
    @ApiModelProperty(value = "摄像头 ID")
    private String id;

    @ApiModelProperty(value = "用户")
    private String user;

    @ApiModelProperty(value = "密码")
    private String passwd;

    @ApiModelProperty(value = "协议")
    private Protocol protocol;

    @Data
    @ApiModel(value = "协议")
    public static class Protocol {
        @ApiModelProperty(value = "协议名称")
        private String name;

        @ApiModelProperty(value = "协议详情")
        private Detail detail;

        @Data
        @ApiModel(value = "协议详情")
        public static class Detail {
            @ApiModelProperty(value = "URL")
            private String url;
        }
    }
    
    @ApiModelProperty(value = "播放地址-返回的时候给。MediaserverAcl提供")
    private String playUrl;
}

