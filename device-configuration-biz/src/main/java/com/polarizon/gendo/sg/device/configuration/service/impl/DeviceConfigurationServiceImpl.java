package com.polarizon.gendo.sg.device.configuration.service.impl;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.mongodb.client.result.UpdateResult;
import com.polarizon.common.bean.bo.AbstractBaseBO;
import com.polarizon.common.page.PageResult;
import com.polarizon.gendo.sg.authority.bean.TenantEntity;
import com.polarizon.gendo.sg.authority.bean.UserEntity;
import com.polarizon.gendo.sg.authority.utils.SystemUtil;
import com.polarizon.gendo.sg.common.utils.ValidateUtil;
import com.polarizon.gendo.sg.device.configuration.acl.TenantAcl;
import com.polarizon.gendo.sg.device.configuration.acl.bean.AreaCameraBean;
import com.polarizon.gendo.sg.device.configuration.bean.Areas;
import com.polarizon.gendo.sg.device.configuration.bean.DeviceBean;
import com.polarizon.gendo.sg.device.configuration.bean.DeviceQueryBean;
import com.polarizon.gendo.sg.device.configuration.bean.ManufacturersBean;
import com.polarizon.gendo.sg.device.configuration.bean.dto.DeviceAreaDTO;
import com.polarizon.gendo.sg.device.configuration.bean.query.DeviceOrganizationQuery;
import com.polarizon.gendo.sg.device.configuration.bean.query.DeviceSceneQuery;
import com.polarizon.gendo.sg.device.configuration.bean.vo.AreaTreeVO;
import com.polarizon.gendo.sg.device.configuration.bean.vo.DeviceAbilityGen;
import com.polarizon.gendo.sg.device.configuration.bean.vo.DeviceTypeVO;
import com.polarizon.gendo.sg.device.configuration.bean.vo.TenantAbilityVO;
import com.polarizon.gendo.sg.device.configuration.repository.DeviceBeanRepository;
import com.polarizon.gendo.sg.device.configuration.service.DeviceConfigurationService;
import com.polarizon.gendo.sg.device.configuration.service.DeviceRelationService;
import com.polarizon.gendo.sg.device.configuration.service.entity.dto.DeviceEventTaskDTO;
import com.polarizon.gendo.sg.device.configuration.service.entity.dto.DeviceEventTaskStatusDTO;
import com.polarizon.gendo.sg.device.configuration.service.entity.form.EventTaskQueryForm;
import com.polarizon.gendo.sg.device.configuration.service.monitor.netty.NettyClient;
import com.polarizon.gendo.sg.device.configuration.utils.CommonUtil;
import com.polarizon.gendo.sg.device.configuration.utils.DeviceConstants;
import com.polarizon.gendo.sg.device.configuration.utils.TcpScannerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.polarizon.common.utils.Constants.STATUS_FLAG_DELETED;
import static com.polarizon.common.utils.Constants.STATUS_FLAG_NORMAL;
import static com.polarizon.gendo.sg.device.configuration.bean.Areas.AreasConstant.ADDRESS_NAME;
import static com.polarizon.gendo.sg.device.configuration.bean.Areas.AreasConstant.PROVINCE_TEMP_NAME;
import static com.polarizon.gendo.sg.device.configuration.bean.DeviceBean.DeviceConstant.*;
import static com.polarizon.gendo.sg.device.configuration.bean.dto.DeviceAreaDTO.Constant.*;
import static java.lang.String.format;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * 设备信息服务层实现类
 */
@Slf4j
@Service
public class DeviceConfigurationServiceImpl implements DeviceConfigurationService {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DeviceBeanRepository deviceBeanRepository;
    @Autowired
    private DeviceRelationService deviceRelationService;
    @Autowired
    private TenantAcl tenantAcl;
    @Autowired
    private NettyClient nettyClient;

    /**
     * 超时时间（分钟）
     */
    private final long TIMEOUT = 10;

    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(10, 400,
        60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 新增
     *
     * @param deviceBean
     * @return
     */
    @Override
    public DeviceBean add(DeviceBean deviceBean) {
        // 初始化数据
        deviceBean.setDeviceCode(UUID.randomUUID().toString());
        deviceBean.setDeviceType(Objects.requireNonNullElse(deviceBean.getDeviceType(), "球机"));
        deviceBean.setManufacturersBean(Objects.requireNonNullElse(deviceBean.getManufacturersBean(), ManufacturersBean.defaultManufacturers()));
        deviceBean.setSubDeviceType(StringUtils.isBlank(deviceBean.getSubDeviceType()) ? "camera.ball" : deviceBean.getSubDeviceType());
        deviceBean.setDeviceStatus(DEVICE_STATUS_UP);

        // 增加设备数据
        return mongoTemplate.insert(deviceBean);
    }

    @Override
    public void upsertAreaCameraBean(AreaCameraBean areaCameraBean) {
        if (Objects.isNull(areaCameraBean.getLatitude()) || Objects.equals(areaCameraBean.getLatitude(), 0.0)) {
            return;
        }
        if (Objects.isNull(areaCameraBean.getLongitude()) || Objects.equals(areaCameraBean.getLongitude(), 0.0)) {
            return;
        }

        DeviceBean deviceBean = new DeviceBean();
        deviceBean.setDeviceCode(UUID.randomUUID().toString());
        deviceBean.setDeviceName(areaCameraBean.getCameraName());
        deviceBean.setAddress(areaCameraBean.getCameraUrl());
        deviceBean.setDeviceType("球机");
        deviceBean.setUpdateTime(System.currentTimeMillis());
//        deviceBean.setDeviceProperty();
//        deviceBean.setPort();
//        deviceBean.setDeviceUserName();
//        deviceBean.setDeviceUserPassword();
        deviceBean.setManufacturersBean(ManufacturersBean.defaultManufacturers());
        deviceBean.setSubDeviceType("camera.gun");
//        deviceBean.setNodeId("camera.ball");
        deviceBean.setDeviceStatus(1);
        deviceBean.setProtocolType("RTMP");

        Areas areas = Areas.addressAnalysis(areaCameraBean.getCameraAddress());
        areas = Objects.isNull(areas) ? new Areas() : areas;
        areas.setLongitude(Objects.requireNonNullElse(areaCameraBean.getLongitude(), 0.0));
        areas.setLatitude(Objects.requireNonNullElse(areaCameraBean.getLatitude(), 0.0));

        // 经纬度错误，进行反转
        if (areaCameraBean.getLatitude() > 90) {
            areas.setLongitude(areaCameraBean.getLatitude());
            areas.setLatitude(areaCameraBean.getLongitude());
        }

        // 跳过错误经纬度
        if (areas.getLongitude() < 90 || areas.getLongitude() > 150) {
            return;
        }
        if (areas.getLatitude() < 25 || areas.getLatitude() > 35) {
            return;
        }

        deviceBean.setAreas(areas);

        add(deviceBean);
    }

    @Override
    public int delete(List<String> deviceCode) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).in(deviceCode));
        Update update = new Update().set(FIELD_STATUS_FLAG, STATUS_FLAG_DELETED)
            .set(FIELD_UPDATE_TIME, System.currentTimeMillis())
            .set(FIELD_UPDATE_BY, SystemUtil.getCurrentUser() == null ? "del" : SystemUtil.getCurrentUser().getAccount());
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, DeviceBean.class);
        return (int) updateResult.getModifiedCount();
    }

    @Override
    public DeviceBean update(DeviceBean deviceBean) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).is(deviceBean.getDeviceCode()));

        Update update = new Update();
        update.set(FIELD_DEVICE_NAME, deviceBean.getDeviceName());
        update.set(FIELD_ADDRESS, deviceBean.getAddress());
        update.set(FILED_DEVICE_GB_CODE, deviceBean.getDeviceGBCode());
        update.set(FILED_DEVICE_MANUFACTURERS, deviceBean.getDeviceManufacturers());
        update.set(FILED_DEVICE_MODEL, deviceBean.getDeviceModel());
        update.set(FIELD_DEVICE_TYPE, deviceBean.getDeviceType());
        update.set(FIELD_DEVICE_PROPERTY, deviceBean.getDeviceProperty());
        update.set(FIELD_PORT, deviceBean.getPort());
        update.set(FIELD_DEVICE_USER_NAME, deviceBean.getDeviceUserName());
        update.set(FIELD_DEVICE_USER_PASSWORD, deviceBean.getDeviceUserPassword());
        update.set(FIELD_MANUFACTURERS_BEAN, deviceBean.getManufacturersBean());
        update.set(FIELD_SUB_DEVICE_TYPE, deviceBean.getSubDeviceType());
        update.set(FIELD_CHANNEL, deviceBean.getChannel());
        update.set(FIELD_USER_STATUS, deviceBean.getUserStatus());
        update.set(FIELD_AREAS, deviceBean.getAreas());
        update.set(FIELD_PROTOCOL_TYPE, deviceBean.getProtocolType());
        update.set(FIELD_MANAGEMENT_UNIT, deviceBean.getManagementUnit());
        update.set(FIELD_REMARKS, deviceBean.getRemarks());
        update.set(FIELD_UPDATE_TIME, deviceBean.getUpdateTime());
        update.set(FIELD_UPDATE_BY, deviceBean.getUpdateBy());
        update.set(FIELD_DEVICE_SUBADDRESS, deviceBean.getDeviceSubaddress());
        update.set(FIELD_ADDRESS_DESCRIBE, deviceBean.getAddressDescribe());
        List<Pair<Query, Update>> updates = Lists.newArrayList(Pair.of(query, update));

        List<DeviceBean.Relation> relations = Objects.requireNonNullElse(deviceBean.getRelations(), new ArrayList<>());
        if (CollectionUtils.isEmpty(relations)) {
            Update organizationUpdate = new Update();
            organizationUpdate.set("relations.$[].organizations", Lists.newArrayList());
            updates.add(Pair.of(query, organizationUpdate));
        }

        List<String> tenantIds = Lists.newArrayList();
        for (DeviceBean.Relation relation : relations) {
            String tenantId = relation.getTenantId();
            String tenantName = relation.getTenantName();

            // 租户更新
            Query tenantQuery = Query.of(query).addCriteria(Criteria.where("relations.tenantId").ne(tenantId));
            Update tenantUpdate = new Update().addToSet("relations", DeviceBean.Relation.builder().tenantId(tenantId).tenantName(tenantName).build());
            updates.add(Pair.of(tenantQuery, tenantUpdate));

            // 组织更新
            List<DeviceBean.OrganizationObj> organizationObjs = Objects.requireNonNullElse(relation.getOrganizations(), new ArrayList<>());
            Query organizationQuery = Query.of(query).addCriteria(Criteria.where("relations.tenantId").is(tenantId));
            Update organizationUpdate = new Update().set("relations.$[relation].organizations", organizationObjs).filterArray("relation.tenantId", tenantId);
            updates.add(Pair.of(organizationQuery, organizationUpdate));

            // 添加租户
            tenantIds.add(tenantId);
        }

        // 更新组织
        Query organizationQuery = Query.of(query);
        Update organizationUpdate = new Update().set("relations.$[relation].organizations", Lists.newArrayList()).filterArray(Criteria.where("relation.tenantId").nin(tenantIds));
        updates.add(Pair.of(organizationQuery, organizationUpdate));

        mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, DeviceBean.class).updateOne(updates).execute();
        return deviceBean;
    }

    @Override
    public DeviceBean get(String deviceCode) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).is(deviceCode));
        return mongoTemplate.findOne(query, DeviceBean.class);
    }

    @Override
    public List<String> getDeviceCodeByCreate(String createBy) {
        Query query = new Query(Criteria.where(FIELD_CREATE_BY).is(createBy).and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL));
        query.fields().include(FIELD_DEVICE_CODE).exclude(previousOperation());
        return mongoTemplate.find(query, DeviceBean.class).stream().map(DeviceBean::getDeviceCode).collect(Collectors.toList());
    }

    @Override
    public PageResult<DeviceBean> page(int pagenumber, int pagesize, DeviceQueryBean deviceQueryBean, String tenantId) {
        PageResult<DeviceBean> pageable = new PageResult<>();
        Query query = getPageQuery(deviceQueryBean, tenantId);

        // 开始页
        pageable.setPageNumber(pagenumber);

        // 每页条数
        pageable.setPageSize(pagesize);

        //排序
        List<Sort.Order> orders = new ArrayList<>();
        Sort sort = Sort.by(orders);
        pageable.setSort(sort);

        // 查询出一共的条数
        long count = mongoTemplate.count(query, DeviceBean.class);

        // 查询
        List<DeviceBean> list = mongoTemplate.find(query.with(pageable), DeviceBean.class);

        // 将集合与分页结果封装
        return PageResult.of(new PageImpl<>(list, pageable, count));
    }

    @Override
    public Page<? extends DeviceAbilityGen> pageByOrganization(DeviceOrganizationQuery queryParam, List<String> organizationIds, Class<? extends DeviceAbilityGen> resultClass, String... fields) {
        String tenantId = queryParam.getTenantId();
        Criteria criteria = Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);
        //region 权限过滤
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }
        //endregion

        //region 组织过滤
        Criteria criteria2 = new Criteria();
        if (CollectionUtils.isNotEmpty(organizationIds)) {
            criteria2.and(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).in(organizationIds);
        } else if (StringUtils.isNotEmpty(tenantId)) {
            criteria2.and(DeviceBean.Relation.Constant.FILED_TENANT_ID).is(tenantId).and(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).exists(true);
        } else {
            criteria2.and(format(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID))).exists(true);
        }
        criteria.and(FILED_RELATIONS).elemMatch(criteria2);
        //endregion

        //region 表头过滤
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceTypes())) {
            criteria.and(FIELD_DEVICE_TYPE).in(queryParam.getDeviceTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceStatuses())) {
            criteria.and(FIELD_DEVICE_STATUS).in(queryParam.getDeviceStatuses());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getProtocolTypes())) {
            criteria.and(FIELD_PROTOCOL_TYPE).in(queryParam.getProtocolTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceManufacturers())) {
            criteria.and(FILED_DEVICE_MANUFACTURERS).in(queryParam.getDeviceManufacturers());
        }
        //endregion

        //region 地图过滤
        Integer type = queryParam.getType();
        String key = queryParam.getKey();
        if (Objects.nonNull(type) && StringUtils.isNotEmpty(key)) {
            if (type == 0) {
                criteria.and(FILED_AREAS_ADDRESS_NAME).regex(key);
            } else if (type == 1) {
                criteria.and(FIELD_DEVICE_NAME).regex(key);
            }
        }
        //endregion

        //搜索框过滤
        if (StringUtils.isNotEmpty(queryParam.getContent())) {
            String content = CommonUtil.escapeExprSpecialWord(queryParam.getContent());
            List<Criteria> criteriaOrList = new ArrayList<>();
            criteriaOrList.add(Criteria.where(FIELD_DEVICE_NAME).regex(content));
            criteriaOrList.add(Criteria.where(FILED_DEVICE_GB_CODE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANUFACTURERS_BEAN.concat(".").concat(ManufacturersBean.Constant.MANUFACTURERS_NAME)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANUFACTURERS_BEAN.concat(".").concat(ManufacturersBean.Constant.PRODUCT_TYPE)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_AREAS.concat(".").concat(ADDRESS_NAME)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_DEVICE_TYPE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANAGEMENT_UNIT).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_PROTOCOL_TYPE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_ADDRESS).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_DEVICE_SUBADDRESS).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_ADDRESS_DESCRIBE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_REMARKS).regex(content));
            // 所属组织
            criteriaOrList.add(Criteria.where(FILED_RELATIONS).elemMatch(Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS_NAME).regex(content)));
            criteria.orOperator(criteriaOrList);
        }
        //设备查询
        return queryDevice(Query.query(criteria), tenantId, queryParam.getPage(), queryParam.getSize(), queryParam.getSort(), queryParam.getHiddenAbility(), currentUser, resultClass, fields);
    }

    @Override
    public Page<? extends DeviceAbilityGen> pageByScene(DeviceSceneQuery queryParam, List<String> organizationIds, Class<? extends DeviceAbilityGen> resultClass, String... fields) {
        String tenantId = queryParam.getTenantId();
        UserEntity currentUser = SystemUtil.getCurrentUser();
        Criteria criteria = Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);
        //region 设备权限过滤
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }
        //endregion

        //region 场景过滤
        Criteria criteria2 = new Criteria();
        criteria2.and(DeviceBean.Relation.Constant.FILED_TENANT_ID).is(StringUtils.isEmpty(tenantId) ? "default" : tenantId);
        if (StringUtils.isEmpty(queryParam.getSceneId())) {
            criteria2.and(DeviceBean.Relation.Constant.FILED_SCENE_Ids).exists(true);
        } else {
            criteria2.and(DeviceBean.Relation.Constant.FILED_SCENE_Ids).is(queryParam.getSceneId());
        }
        //组织过滤
        if (CollectionUtils.isNotEmpty(organizationIds)) {
            criteria2.and(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).in(organizationIds);
        }
        criteria.and(FILED_RELATIONS).elemMatch(criteria2);
        //endregion

        //region 表头过滤
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceTypes())) {
            criteria.and(FIELD_DEVICE_TYPE).in(queryParam.getDeviceTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceStatuses())) {
            criteria.and(FIELD_DEVICE_STATUS).in(queryParam.getDeviceStatuses());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getProtocolTypes())) {
            criteria.and(FIELD_PROTOCOL_TYPE).in(queryParam.getProtocolTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getDeviceManufacturers())) {
            criteria.and(FILED_DEVICE_MANUFACTURERS).in(queryParam.getDeviceManufacturers());
        }
        //endregion

        //region 地图过滤
        Integer type = queryParam.getType();
        String key = queryParam.getKey();
        if (Objects.nonNull(type) && StringUtils.isNotEmpty(key)) {
            if (type == 0) {
                criteria.and(FILED_AREAS_ADDRESS_NAME).regex(key);
            } else if (type == 1) {
                criteria.and(FIELD_DEVICE_NAME).regex(key);
            }
        }
        //endregion

        //搜索框过滤
        if (StringUtils.isNotEmpty(queryParam.getContent())) {
            String content = CommonUtil.escapeExprSpecialWord(queryParam.getContent());
            List<Criteria> criteriaOrList = new ArrayList<>();
            criteriaOrList.add(Criteria.where(FIELD_DEVICE_NAME).regex(content));
            criteriaOrList.add(Criteria.where(FILED_DEVICE_GB_CODE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANUFACTURERS_BEAN.concat(".").concat(ManufacturersBean.Constant.MANUFACTURERS_NAME)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANUFACTURERS_BEAN.concat(".").concat(ManufacturersBean.Constant.PRODUCT_TYPE)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_AREAS.concat(".").concat(ADDRESS_NAME)).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_DEVICE_TYPE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_MANAGEMENT_UNIT).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_PROTOCOL_TYPE).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_ADDRESS).regex(content));
            criteriaOrList.add(Criteria.where(FIELD_REMARKS).regex(content));
            // 所属组织
            criteriaOrList.add(Criteria.where(FILED_RELATIONS).elemMatch(Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS_NAME).regex(content)));
            criteria.orOperator(criteriaOrList);
        }
        //设备查询
        return queryDevice(Query.query(criteria), tenantId, queryParam.getPage(), queryParam.getSize(), queryParam.getSort(), queryParam.getHiddenAbility(), currentUser, resultClass, fields);
    }

    @Override
    public List<DeviceBean> list(DeviceQueryBean deviceQueryBean, String tenantId) {
        Query query = getPageQuery(deviceQueryBean, tenantId);
        if (deviceQueryBean.getSize() != null) {
            query.limit(deviceQueryBean.getSize());
        }
        if (StringUtils.isNotEmpty(deviceQueryBean.getOrder())) {
            Sort sort = Sort.by(deviceQueryBean.getOrder()).descending();
            query.with(sort);
        }
        List<DeviceBean> deviceBeans = mongoTemplate.find(query, DeviceBean.class);
        return deviceBeans;
    }

    @Override
    public List<DeviceBean> getDeviceListLikeNodeId(String subDeviceType, String deviceName, String tenantId) {
        Query query = new Query();
        Criteria criteria = new Criteria();

        if (StringUtils.isNotBlank(subDeviceType)) {
            query.addCriteria(Criteria.where(FIELD_SUB_DEVICE_TYPE).regex(subDeviceType));
        }
        if (StringUtils.isNotBlank(deviceName)) {
            criteria.orOperator(Criteria.where(FIELD_DEVICE_NAME).regex(deviceName));
        }
        if (StringUtils.isNotBlank(tenantId)) {
            query.addCriteria(Criteria.where(FILED_RELATIONS_TENANT_ID).is(tenantId));
        }
        query.addCriteria(Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL));

        return mongoTemplate.find(query, DeviceBean.class);
    }


    @Override
    public Map<String, String> getDeviceNameByDeviceCode(Set<String> deviceCodeList) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FIELD_DEVICE_CODE).in(deviceCodeList));
        query.fields().include(FIELD_DEVICE_CODE).include(FIELD_DEVICE_NAME);
        List<DeviceBean> deviceBeans = mongoTemplate.find(query, DeviceBean.class);
        if (CollectionUtils.isEmpty(deviceBeans)) {
            return Maps.newHashMap();
        }
        return deviceBeans.stream().collect(Collectors.toMap(DeviceBean::getDeviceCode, DeviceBean::getDeviceName));
    }


    @Override
    public List<DeviceTypeVO> getDeviceTypeVOS(String nodeIdRegex, Boolean showDevice, String tenantId) {
        Criteria criteria = new Criteria();
        criteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);

        // 动态查询
        if (StringUtils.isNotBlank(tenantId)) {
            criteria.and(FILED_RELATIONS_TENANT_ID).is(tenantId);
        }
        if (StringUtils.isNotBlank(nodeIdRegex)) {
            criteria.and(FIELD_SUB_DEVICE_TYPE).regex(Pattern.compile("^".concat(nodeIdRegex).concat(".*$")));
        }

        // 1、匹配操作
        MatchOperation match = match(criteria);

        // 2、分组操作
        GroupOperation group = (Objects.nonNull(showDevice) && showDevice) ? group(FIELD_SUB_DEVICE_TYPE, FIELD_DEVICE_TYPE).count().as(DeviceTypeVO.Constant.FILED_TOTAL).sum(FIELD_DEVICE_STATUS).as(DeviceTypeVO.Constant.FILED_ONLINE).push(FIELD_DEVICE_CODE).as(DeviceTypeVO.Constant.FILED_DEVICE_CODES) : group(FIELD_SUB_DEVICE_TYPE, FIELD_DEVICE_TYPE).count().as(DeviceTypeVO.Constant.FILED_TOTAL).sum(FIELD_DEVICE_STATUS).as(DeviceTypeVO.Constant.FILED_ONLINE);

        // 3、取值操作
        ProjectionOperation project = (Objects.nonNull(showDevice) && showDevice) ? project(DeviceTypeVO.Constant.FILED_TOTAL, DeviceTypeVO.Constant.FILED_ONLINE).and(DeviceTypeVO.Constant.ID_SUB_DEVICE_TYPE).as(DeviceTypeVO.Constant.FILED_NODE_ID).and(DeviceTypeVO.Constant.ID_DEVICE_TYPE).as(DeviceTypeVO.Constant.FILED_NODE_NAME).and(DeviceTypeVO.Constant.FILED_DEVICE_CODES).as(DeviceTypeVO.Constant.FILED_DEVICE_CODES) : project(DeviceTypeVO.Constant.FILED_TOTAL, DeviceTypeVO.Constant.FILED_ONLINE).and(DeviceTypeVO.Constant.ID_SUB_DEVICE_TYPE).as(DeviceTypeVO.Constant.FILED_NODE_ID).and(DeviceTypeVO.Constant.ID_DEVICE_TYPE).as(DeviceTypeVO.Constant.FILED_NODE_NAME);

        // 4、组合管道
        TypedAggregation<DeviceBean> aggregation = newAggregation(DeviceBean.class, match, group, project);

        // 5、执行管道
        return mongoTemplate.aggregate(aggregation, DeviceTypeVO.class).getMappedResults();
    }

    @Override
    public List<DeviceBean> limitDeviceBeanByNameRegex(String deviceNameRegex, Integer number, String tenantId) {
        Criteria criteria = new Criteria();
        criteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);
        //region 权限过滤
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }
        //endregion
        // 动态查询
        if (StringUtils.isNotBlank(deviceNameRegex)) {
            criteria.and(FIELD_DEVICE_NAME).regex(deviceNameRegex);
        }
        if (StringUtils.isNotBlank(tenantId)) {
            criteria.and(FILED_RELATIONS_TENANT_ID).is(tenantId);
        }

        // 获取结果
        return mongoTemplate.find(Query.query(criteria).limit(Objects.requireNonNullElse(number, 500)), DeviceBean.class);
    }

    @Override
    public List<DeviceBean> getAllCameraList(String key, String tenantId) {
        Query query = new Query(Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL));

        if (StringUtils.isNotBlank(key)) {
            query.addCriteria(Criteria.where(FIELD_DEVICE_NAME).regex(key));
        }
        if (StringUtils.isNotBlank(tenantId)) {
            query.addCriteria(Criteria.where(FILED_RELATIONS_TENANT_ID).is(tenantId));
        }

        return mongoTemplate.find(query, DeviceBean.class);
    }

    @Override
    public Collection<AreaTreeVO> getAreaTree(String tenantId, String content, String organizationId) {
        // 获取租户对应区域信息
        List<DeviceAreaDTO> deviceAreaDTOS = getDeviceAreaByTenantId(tenantId, organizationId);
        //得到区域树
        List<AreaTreeVO> areaTree = getAreaTree(deviceAreaDTOS);
        //过滤市/县/镇为空的数据
        areaTree.forEach(province -> filterNullArea(province.getChildren()));
        //将省为空的数据，用“其它”代替，并放到末尾
        List<AreaTreeVO> collect = areaTree.stream().filter(item -> StringUtils.isEmpty(item.getAreaName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            return areaTree;
        }
        areaTree.removeIf(item -> StringUtils.isEmpty(item.getAreaName()));
        collect.forEach(item -> item.setAreaName(PROVINCE_TEMP_NAME));
        areaTree.addAll(collect);
        return areaTree;
    }

    private List<AreaTreeVO> getAreaTree(List<DeviceAreaDTO> deviceAreas) {
        deviceAreas.forEach(item -> {
            if (item.getProvinceName() == null) {
                item.setProvinceName("");
            }
            if (item.getCityName() == null) {
                item.setCityName("");
            }
            if (item.getCountyName() == null) {
                item.setCountyName("");
            }
            if (item.getStreetName() == null) {
                item.setStreetName("");
            }
        });
        List<AreaTreeVO> collect = deviceAreas.stream()
            .collect(Collectors.collectingAndThen(
                Collectors.groupingBy(item -> item.getProvinceName(),
                    Collectors.collectingAndThen(
                        Collectors.groupingBy(item -> item.getCityName(),
                            Collectors.collectingAndThen(
                                Collectors.groupingBy(item -> item.getCountyName(),
                                    Collectors.collectingAndThen(
                                        Collectors.groupingBy(item -> item.getStreetName(),
                                            Collectors.mapping(t -> t, Collectors.toList()))
                                        , e -> e.entrySet().stream().map(ie -> getVoByDTO(ie)).collect(Collectors.toList())))
                                , e -> e.entrySet().stream().map(ie -> getVoByVO(ie)).collect(Collectors.toList()))
                        ), e -> e.entrySet().stream().map(ie -> getVoByVO(ie)).collect(Collectors.toList())))
                , e -> e.entrySet().stream().map(ie -> getVoByVO(ie)).collect(Collectors.toList())));
        return collect;
    }

    private void filterNullArea(List<AreaTreeVO> areaTree) {
        if (CollectionUtils.isEmpty(areaTree)) {
            return;
        }
        areaTree.removeIf(item -> StringUtils.isEmpty(item.getAreaName()));
        areaTree.forEach(item -> filterNullArea(item.getChildren()));
    }

    private AreaTreeVO getVoByVO(Map.Entry<String, List<AreaTreeVO>> ie) {
        List<AreaTreeVO> list = ie.getValue();
        String key = ie.getKey();
        String nodeId = DigestUtils.md5Hex(key);
        int deviceCount = list.stream().mapToInt(item -> item.getDeviceCount()).sum();
        int onlineDeviceCount = list.stream().mapToInt(item -> item.getOnlineDeviceCount()).sum();
        AreaTreeVO vo = new AreaTreeVO();
        vo.setAreaName(key);
        vo.setNodeId(nodeId);
        vo.setDeviceCount(deviceCount);
        vo.setOnlineDeviceCount(onlineDeviceCount);
        list.forEach(item -> item.setParentId(nodeId));
        vo.setChildren(list);
        return vo;
    }

    private AreaTreeVO getVoByDTO(Map.Entry<String, List<DeviceAreaDTO>> ie) {
        int deviceCount = ie.getValue().stream().mapToInt(item -> item.getDeviceCount()).sum();
        int onlineDeviceCount = ie.getValue().stream().filter(item -> Objects.equals(item.getDeviceStatus(), DEVICE_STATUS_UP)).mapToInt(item -> item.getDeviceCount()).sum();
        AreaTreeVO vo = new AreaTreeVO();
        vo.setAreaName(ie.getKey());
        vo.setNodeId(DigestUtils.md5Hex(ie.getKey()));
        vo.setDeviceCount(deviceCount);
        vo.setOnlineDeviceCount(onlineDeviceCount);
        return vo;
    }

    @Override
    public List<DeviceAreaDTO> getDeviceAreaByTenantId(String tenantId, String organizationId) {
        Criteria criteria = new Criteria();
        MatchOperation matchOperation = match(criteria);

        // 匹配条件
        if (StringUtils.isNotBlank(tenantId)) {
            criteria.and(FILED_RELATIONS_TENANT_ID).is(tenantId);
        }
        criteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);
        //region 组织或角色过滤
        if (StringUtils.isNotEmpty(organizationId)) {
            List<String> organizationIds = deviceRelationService.getOrganizationIds(organizationId);
            if (CollectionUtils.isNotEmpty(organizationIds)) {
                criteria.and(FILED_RELATIONS).elemMatch(Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).in(organizationIds));
            }
        }
        //endregion
        //region 权限过滤
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }
        //endregion

        // 分组统计
        GroupOperation groupOperation = group(FILED_AREAS_PROVINCE_NAME, FILED_AREAS_CITY_NAME, FILED_AREAS_COUNTY_NAME, FILED_AREAS_STREET_NAME, FIELD_DEVICE_STATUS).count().as(AREA_DEVICE_COUNT);

        // 获取字段
        ProjectionOperation projectionOperation = project(AREA_DEVICE_COUNT).and(PREVIOUS_PROVINCE_NAME).as(AREA_PROVINCE_NAME).and(PREVIOUS_CITY_NAME).as(AREA_CITY_NAME).and(PREVIOUS_COUNTY_NAME).as(AREA_COUNTY_NAME).and(PREVIOUS_STREET_NAME).as(AREA_STREET_NAME).and(PREVIOUS_DEVICE_STATUS).as(AREA_DEVICE_STATUS);

        // 排序字段
        SortOperation sort = sort(Sort.Direction.DESC, AREA_PROVINCE_NAME, AREA_CITY_NAME, AREA_COUNTY_NAME, AREA_STREET_NAME);

        // 聚合查询
        TypedAggregation<DeviceBean> aggregation = new TypedAggregation<>(DeviceBean.class, matchOperation, groupOperation, projectionOperation, sort);

        // 获取结果
        return mongoTemplate.aggregate(aggregation, DeviceAreaDTO.class).getMappedResults();
    }

    @Override
    public <T> Page<T> getDeviceByAreaAndRegexp(String tenantId, String provinceName, String cityName, String countyName, String streetName, String deviceNameMatch, String positionNameMatch, String contentMatch, List<String> deviceManufacturers, List<String> deviceTypes, List<String> protocolTypes, List<Integer> deviceStatusS, String organizationId, Pageable pageable, Class<T> classes, String... fields) {
        Criteria criteria = new Criteria();
        //endregion
        // 匹配条件
        Criteria criteria2 = null;
        if (StringUtils.isNotBlank(tenantId)) {
            criteria2 = Criteria.where(DeviceBean.Relation.Constant.FILED_TENANT_ID).is(tenantId);
        }

        //region 权限过滤
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }

        //region 组织过滤
        if (StringUtils.isNotEmpty(organizationId)) {
            List<String> organizationIds = deviceRelationService.getOrganizationIds(organizationId);
            if (CollectionUtils.isNotEmpty(organizationIds)) {
                if (criteria2 == null) {
                    criteria2 = Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).in(organizationIds);
                } else {
                    criteria2.and(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS.concat(".").concat(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID)).in(organizationIds);
                }
            }
        }
        if (criteria2 != null) {
            criteria.and(FILED_RELATIONS).elemMatch(criteria2);
        }
        //endregion
        if (StringUtils.isNotBlank(provinceName)) {
            if (provinceName.equals(PROVINCE_TEMP_NAME)) {
                provinceName = null;
            }
            criteria.and(FILED_AREAS_PROVINCE_NAME).is(provinceName);
        }
        if (StringUtils.isNotBlank(cityName)) {
            criteria.and(FILED_AREAS_CITY_NAME).is(cityName);
        }
        if (StringUtils.isNotBlank(countyName)) {
            criteria.and(FILED_AREAS_COUNTY_NAME).is(countyName);
        }
        if (StringUtils.isNotBlank(streetName)) {
            criteria.and(FILED_AREAS_STREET_NAME).is(streetName);
        }
        if (StringUtils.isNotBlank(deviceNameMatch)) {
            criteria.and(FIELD_DEVICE_NAME).regex(deviceNameMatch);
        }
        if (StringUtils.isNotBlank(positionNameMatch)) {
            criteria.and(FILED_AREAS_ADDRESS_NAME).regex(positionNameMatch);
        }
        if (CollectionUtils.isNotEmpty(deviceManufacturers)) {
            criteria.and(FILED_DEVICE_MANUFACTURERS).in(deviceManufacturers);
        }
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            criteria.and(FIELD_DEVICE_TYPE).in(deviceTypes);
        }
        if (CollectionUtils.isNotEmpty(protocolTypes)) {
            criteria.and(FIELD_PROTOCOL_TYPE).in(protocolTypes);
        }
        if (CollectionUtils.isNotEmpty(deviceStatusS)) {
            criteria.and(FIELD_DEVICE_STATUS).in(deviceStatusS);
        }
        if (StringUtils.isNotBlank(contentMatch)) {
            criteria.orOperator(
                // 设备名称
                Criteria.where(FIELD_DEVICE_NAME).regex(contentMatch),
                // 设备标识
                Criteria.where(FILED_DEVICE_GB_CODE).regex(contentMatch),
                // 设备厂商
                Criteria.where(FILED_DEVICE_MANUFACTURERS).regex(contentMatch),
                // 设备型号
                Criteria.where(FILED_DEVICE_MODEL).regex(contentMatch),
                // 设备类型
                Criteria.where(FIELD_DEVICE_TYPE).regex(contentMatch),
                // 安装地点
                Criteria.where(FILED_AREAS_ADDRESS_NAME).regex(contentMatch),
                // 管辖单位
                Criteria.where(FIELD_MANAGEMENT_UNIT).regex(contentMatch),
                // 协议类型
                Criteria.where(FIELD_PROTOCOL_TYPE).regex(contentMatch),
                // 码流地址
                Criteria.where(FIELD_ADDRESS).regex(contentMatch),
                // 子码流地址
                Criteria.where(FIELD_DEVICE_SUBADDRESS).regex(contentMatch),
                // 安装位置
                Criteria.where(FIELD_ADDRESS_DESCRIBE).regex(contentMatch),
                // 所属组织
                Criteria.where(FILED_RELATIONS).elemMatch(Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS_NAME).regex(contentMatch)),
                // 备注
                Criteria.where(FIELD_REMARKS).regex(contentMatch));
        }
        criteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);

        // 地图业务不分页（地图业务）
        pageable = pageable == null ? Pageable.unpaged() : pageable;

        // 查询条件
        Query query = Query.query(criteria).with(pageable);
        if (ArrayUtils.isNotEmpty(fields)) {
            query.fields().include(fields);
        }

        // 查询结果
        List<T> result = mongoTemplate.find(query, classes, DeviceBean.DeviceConstant.DOCUMENT_NAME);
        long count = pageable.isUnpaged() ? result.size() : mongoTemplate.count(Query.query(criteria), DeviceBean.DeviceConstant.DOCUMENT_NAME);
        return new PageImpl<>(result, pageable, count);
    }


    @Override
    public long updateTenantName(String tenantId, String tenantName) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FILED_RELATIONS_TENANT_ID).is(tenantId));

        Update update = new Update();
        update.set(FILED_RELATIONS_PLACEHOLDER_TENANT_NAME, tenantName);

        return mongoTemplate.updateMulti(query, update, DeviceBean.class).getModifiedCount();
    }

    @Override
    public long updateOrganizationName(String organizationId, String organizationName) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FILED_RELATIONS_ORGANIZATION_ID).is(organizationId));

        Update update = new Update();
        update.set(FILED_SET_RELATIONS_ARRAY_ORGANIZATIONS_ARRAY_ORGANIZATION_NAME, organizationName);
        update.filterArray(FILED_ARRAY_FILTERS_RELATION_ORGANIZATIONS_ORGANIZATION_ID, organizationId);
        update.filterArray(FILED_ARRAY_FILTERS_ORGANIZATION_ORGANIZATION_ID, organizationId);

        return mongoTemplate.updateMulti(query, update, DeviceBean.class).getModifiedCount();
    }

    @Override
    public void updateDeviceStatus(Collection<String> deviceCodes, Integer deviceStatus) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).in(deviceCodes));

        Update update = new Update();
        update.set(FIELD_DEVICE_STATUS, deviceStatus);

        mongoTemplate.updateMulti(query, update, DeviceBean.class);
    }

    /**
     * 修改流媒体地址
     */
    @Override
    public void updateMsAddr(String deviceCodes, String msAddr, String msSubAddr) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).is(deviceCodes));
        Update update = new Update();
        if (Objects.nonNull(msAddr)) {
            update.set(FIELD_MS_ADDRESS, msAddr);
        }
        if (Objects.nonNull(msSubAddr)) {
            update.set(FIELD_MS_DEVICE_SUBADDRESS, msSubAddr);
        }
        mongoTemplate.updateFirst(query, update, DeviceBean.class);
    }

    @Override
    public void deleteMsAddr(String deviceCodes, Boolean msAddr, Boolean msSubAddr) {
        Query query = new Query(Criteria.where(FIELD_DEVICE_CODE).is(deviceCodes));
        Update update = new Update();
        if (BooleanUtils.isTrue(msAddr)) {
            update.unset(FIELD_MS_ADDRESS);
        }
        if (BooleanUtils.isTrue(msSubAddr)) {
            update.unset(FIELD_MS_DEVICE_SUBADDRESS);
        }
        mongoTemplate.updateFirst(query, update, DeviceBean.class);
    }

    @Override
    public void deleteAllMsAddr() {
        Query query = new Query();
        Update update = new Update();
        update.unset(FIELD_MS_ADDRESS);
        update.unset(FIELD_MS_DEVICE_SUBADDRESS);
        mongoTemplate.updateMulti(query, update, DeviceBean.class);
    }

    /**
     * @param tenantId
     * @return
     */
    @Override
    public long deleteDeviceTenant(String tenantId) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FILED_RELATIONS_TENANT_ID).is(tenantId));

        Update update = new Update();
        update.pull(FILED_RELATIONS, Query.query(Criteria.where(DeviceBean.Relation.Constant.FILED_TENANT_ID).is(tenantId)));

        return mongoTemplate.updateMulti(query, update, DeviceBean.class).getModifiedCount();
    }

    @Override
    public long deleteDeviceOrganization(Collection<String> organizationIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FILED_RELATIONS_ORGANIZATION_ID).in(organizationIds));

        Update update = new Update();
        update.pull(FILED_RELATIONS_PLACEHOLDER_ORGANIZATIONS, Query.query(Criteria.where(DeviceBean.OrganizationObj.Constant.FILED_ORGANIZATION_ID).in(organizationIds)));

        return mongoTemplate.updateMulti(query, update, DeviceBean.class).getModifiedCount();
    }

    /**
     * 删除场景
     *
     * @param scenesIds
     * @return
     */
    @Override
    public long deleteDeviceScenes(Collection<String> scenesIds) {
        Query query = new Query();
        query.addCriteria(Criteria.where(FILED_RELATIONS_SCENES_ID).in(scenesIds));

        Update update = new Update();
        update.pull(FILED_RELATIONS_PLACEHOLDER_SCENES, Query.query(Criteria.where(FILED_RELATIONS_SCENES_ID).in(scenesIds)));

        return mongoTemplate.updateMulti(query, update, DeviceBean.class).getModifiedCount();
    }

    @Override
    public void deviceAbility(List<? extends DeviceAbilityGen> deviceAbilities, String tenantId, List<String> organizationIds, Boolean justDisposition) {
        // 获取设备编码
        List<String> deviceCodes = deviceAbilities.stream().map(DeviceAbilityGen::getDeviceCode).distinct().collect(Collectors.toList());

        // 获取租户集合
        List<TenantEntity> tenantEntities = StringUtils.isBlank(tenantId) ? tenantAcl.findAllTenant() : Lists.newArrayList(tenantAcl.findTenantEntity(tenantId));
        // TODO:v3这边各租户的数据，都是放在null租户库中的，并没有根据租户分库分表
        tenantEntities.add(TenantEntity.builder().build());

        // 记录到达状态
        CountDownLatch countDownLatch = new CountDownLatch(tenantEntities.size());

        // 遍历租户设置任务配置
        for (TenantEntity tenantEntity : tenantEntities) {
            // 租户存在过滤
            if (tenantEntity == null) {
                countDownLatch.countDown();
                continue;
            }

            // 线程任务提交
            THREAD_POOL_EXECUTOR.execute(() -> {
                try {
                    if (justDisposition) {
                        // 只获取设备的布控状态
                        deviceDisposition(tenantEntity, deviceCodes, deviceAbilities);
                    } else {
                        // 获取设备布控状态与能力
                        deviceAbility(tenantEntity, deviceCodes, deviceAbilities, organizationIds);
                    }
                } catch (Exception e) {
                    log.error("device ability throw exception, please check params and code", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        // 等待任务完成
        try {
            countDownLatch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("device ability interrupted error, please check count: {}", countDownLatch.getCount());
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void deviceAbility(TenantEntity tenantEntity, List<String> deviceCodes, List<? extends DeviceAbilityGen> deviceAbilities, List<String> organizationIds) {
        // 获取绑定任务配置、布控中设备编码、任务配置数据
        Optional<DeviceEventTaskStatusDTO> optional = Optional.ofNullable(deviceRelationService.deviceEventTaskInfo(tenantEntity.getId()
            , EventTaskQueryForm.builder().deviceCodes(deviceCodes).organizationIds(organizationIds).build()));
        List<String> dispositionDeviceCodes = optional.map(DeviceEventTaskStatusDTO::getDispositionDeviceCodes).orElse(Lists.newArrayList());
        List<DeviceEventTaskDTO> deviceEventTasks = optional.map(DeviceEventTaskStatusDTO::getDeviceEventTaskDTOS).orElse(Lists.newArrayList());

        // 获取设备编码已有能力信息
        HashMultimap<String, TenantAbilityVO.EventTaskInfoVO> deviceCodeEventTaskInfoMap = HashMultimap.create();
        deviceEventTasks.forEach(task -> deviceCodeEventTaskInfoMap.put(task.getDeviceCode(), new TenantAbilityVO.EventTaskInfoVO(task.getAlgorithmType(), task.getTaskNames())));

        // 设置任务配置信息
        for (DeviceAbilityGen device : deviceAbilities) {
            // 设置设备布控状态
            if (!device.isDisposition() && dispositionDeviceCodes.contains(device.getDeviceCode())) {
                device.setDisposition(true);
            }

            // 获取已有能力信息
            Set<TenantAbilityVO.EventTaskInfoVO> eventTasks = deviceCodeEventTaskInfoMap.get(device.getDeviceCode());
            if (CollectionUtils.isEmpty(eventTasks)) {
                continue;
            }

            // 添加已有能力信息
            device.getAbility().add(new TenantAbilityVO(tenantEntity.getId(), tenantEntity.getName(), eventTasks));
        }
    }

    @Override
    public List<TenantAbilityVO> getAbilityByDeviceCode(String tenantId, String deviceCode, List<String> organizationIds) {
        // 获取租户集合
        List<TenantEntity> tenantEntities = StringUtils.isBlank(tenantId) ? tenantAcl.findAllTenant()
            : Lists.newArrayList(tenantAcl.findTenantEntity(tenantId));

        return tenantEntities.parallelStream()
            .map(tenantEntity -> getDeviceAbility(tenantEntity, deviceCode, organizationIds))
            .filter(tenantAbilityVO -> CollectionUtils.isNotEmpty(tenantAbilityVO.getEventTaskInfo()))
            .collect(Collectors.toList());
    }

    private TenantAbilityVO getDeviceAbility(TenantEntity tenantEntity, String deviceCode, List<String> organizationIds) {
        // 获取绑定任务配置、布控中设备编码、任务配置数据
        List<DeviceEventTaskDTO> deviceEventTasks = Optional.ofNullable(deviceRelationService.deviceEventTaskInfo(tenantEntity.getId()
                , EventTaskQueryForm.builder()
                    .organizationIds(organizationIds)
                    .deviceCodes(Collections.singletonList(deviceCode))
                    .includeDeviceCode(false).build()))
            .map(DeviceEventTaskStatusDTO::getDeviceEventTaskDTOS).orElseGet(Collections::emptyList);

        // 获取设备编码已有能力信息
        Set<TenantAbilityVO.EventTaskInfoVO> eventTasks = deviceEventTasks.stream()
            .filter(task -> task.getDeviceCode().equals(deviceCode))
            .collect(Collectors.collectingAndThen(Collectors.toSet(), taskSet ->
                taskSet.stream()
                    .map(task -> new TenantAbilityVO.EventTaskInfoVO(task.getAlgorithmType(), task.getTaskNames()))
                    .collect(Collectors.toSet())));

        return new TenantAbilityVO(tenantEntity.getId(), tenantEntity.getName(), eventTasks);

    }

    private void deviceDisposition(TenantEntity tenantEntity, List<String> deviceCodes, List<? extends DeviceAbilityGen> deviceAbilities) {
        // 只获取布控中设备编码，减少查询。
        Optional<DeviceEventTaskStatusDTO> optional = Optional.ofNullable(deviceRelationService.deviceEventTaskInfo(tenantEntity.getId()
            , EventTaskQueryForm.builder().deviceCodes(deviceCodes).includeDeviceEventTask(false).build()));
        List<String> dispositionDeviceCodes = optional.map(DeviceEventTaskStatusDTO::getDispositionDeviceCodes).orElse(Collections.emptyList());
        // 设置任务配置信息
        for (DeviceAbilityGen device : deviceAbilities) {
            // 设置设备布控状态
            if (!device.isDisposition() && dispositionDeviceCodes.contains(device.getDeviceCode())) {
                device.setDisposition(true);
            }
        }
    }

    @Override
    public boolean isExists(String deviceCode, String deviceName, String deviceAddress, String deviceSubaddress) {
        Criteria criteria = new Criteria();
        Query query = new Query(criteria);

        if (StringUtils.isNotBlank(deviceCode)) {
            criteria.and(FIELD_DEVICE_CODE).ne(deviceCode);
        }

        List<Criteria> orCriteriaS = Lists.newArrayList();
        if (StringUtils.isNotBlank(deviceAddress)) {
            orCriteriaS.add(Criteria.where(FIELD_ADDRESS).is(deviceAddress));
        }

        if (StringUtils.isNotBlank(deviceSubaddress)) {
            orCriteriaS.add(Criteria.where(FIELD_DEVICE_SUBADDRESS).is(deviceSubaddress));
        }

        if (StringUtils.isNotBlank(deviceName)) {
            orCriteriaS.add(Criteria.where(FIELD_DEVICE_NAME).is(deviceName));
        }

        criteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);
        if (CollectionUtils.isNotEmpty(orCriteriaS)) {
            criteria.orOperator(orCriteriaS);
        }
        return mongoTemplate.count(query, DeviceBean.class) > 0;
    }

    @Override
    public Multimap<Boolean, DeviceBean> isOnline(Collection<DeviceBean> deviceBeans) {
        Multimap<Boolean, DeviceBean> deviceStatusMap = HashMultimap.create();
        // 遍历设备获取设备状态
        Map<String, List<DeviceBean>> typeMap = deviceBeans.stream()
            .filter(deviceBean -> StringUtils.isNotBlank(deviceBean.getProtocolType()))
            .collect(Collectors.groupingBy(DeviceBean::getProtocolType));
        typeMap.entrySet().forEach(entry -> {
            switch (entry.getKey()) {
                case DeviceConstants.RTMP:
                    rtmpCheck(deviceStatusMap, entry.getValue());
                    break;
                case DeviceConstants.RTSP:
                    rtspCheck(deviceStatusMap, entry.getValue());
                    break;
                default:
                    return;
            }
        });
        log.info("离线设备数量=>{}", deviceStatusMap.get(false).size());

//        for (DeviceBean deviceBean : deviceBeans) {
//            // 获取主机、端口 Pair
//            Pair<String, Integer> pair = TcpScannerUtil.hostNamePortAnalysis(deviceBean);
//            if (Objects.isNull(pair) || StringUtils.isBlank(pair.getFirst()) || Objects.isNull(pair.getSecond())) {
//                deviceStatusCodeMap.put(false, deviceBean.getDeviceCode());
//                continue;
//            }
//
//            // 获取设备信息
//            String deviceHostPort = pair.getFirst().concat("_").concat(String.valueOf(pair.getSecond()));
//            boolean status = deviceHostPortStatusMap.containsKey(deviceHostPort) ? deviceHostPortStatusMap.get(deviceHostPort) : TcpScannerUtil.isSocketAliveSurvey(pair.getFirst(), pair.getSecond());
//
//            // 存储探测信息
//            deviceHostPortStatusMap.put(deviceHostPort, status);
//            deviceStatusCodeMap.put(status, deviceBean.getDeviceCode());
//        }

        return deviceStatusMap;
    }

    private void rtmpCheck(Multimap<Boolean, DeviceBean> deviceStatusMap, Collection<DeviceBean> deviceBeans) {
        Map<Pair<String, Integer>, List<DeviceBean>> deviceMap = deviceBeans.stream().collect(Collectors.groupingBy(decive -> TcpScannerUtil.hostNamePortAnalysis(decive)));
        CountDownLatch downLatch = new CountDownLatch(deviceMap.size());
        deviceMap.forEach((addr, deviceList) -> {
            if (Objects.isNull(addr) || StringUtils.isBlank(addr.getFirst()) || Objects.isNull(addr.getSecond())) {
                deviceStatusMap.putAll(false, deviceList);
                downLatch.countDown();
                return;
            }
            log.info("开始校验{}，设备数量{}", addr.toString(), deviceList.size());
            if (nettyClient.connectRtmp(downLatch, addr, deviceList, deviceStatusMap) == null) {
                //log.info("{}流媒体异常", addr.toString());
                downLatch.countDown();
                deviceStatusMap.putAll(false, deviceList);
                return;
            }
        });
        try {
            downLatch.await(2, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void rtspCheck(Multimap<Boolean, DeviceBean> deviceStatusMap, Collection<DeviceBean> deviceBeans) {
        Map<Pair<String, Integer>, List<DeviceBean>> deviceMap = deviceBeans.stream()
            .collect(Collectors.groupingBy(device -> TcpScannerUtil.hostNamePortAnalysis(device)));
        deviceMap.forEach((addr, deviceList) -> {
            if (Objects.isNull(addr) || StringUtils.isBlank(addr.getFirst()) || Objects.isNull(addr.getSecond())) {
                deviceStatusMap.putAll(false, deviceList);
                return;
            }
            for (DeviceBean device : deviceList) {
                Boolean result = nettyClient.connectRtsp(addr, device.getRealAddr());
                if (result == null) {
                    //log.info("{}流媒体异常", addr);
                    deviceStatusMap.putAll(false, deviceList);
                    return;
                }
                deviceStatusMap.put(result, device);
            }
        });
    }

//    private void check(NettyClient client, Iterator<Map.Entry<Pair<String, Integer>, List<String>>> iterator){
//        if(!iterator.hasNext()){
//            return;
//        }
//        Map.Entry<Pair<String, Integer>, List<String>> next = iterator.next();
//        log.info("开始校验{}", next.getKey().toString());
//        if(next.getValue().get(0).startsWith(DeviceConstants.RTMP.toLowerCase())){
//                try {
//                    RtmpSession rtmpSession = new RtmpSession(next.getValue());
//                    client.connectRtmp(next.getKey(), rtmpSession);
//                } catch (Exception e){
//                    e.printStackTrace();
//                    log.info("{}流媒体异常", next.getKey().toString());
//                };
//        }
//    }

    @Override
    public Map<String, Long> totalDeviceByStatus(String tenantId) {
        Map<String, Long> result = Maps.newHashMap();

        // 基础过滤条件
        Criteria baseCriteria = new Criteria();
        if (StringUtils.isNotBlank(tenantId)) {
            baseCriteria.and(FILED_RELATIONS_TENANT_ID).is(tenantId);
        }
        baseCriteria.and(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL);

        //region 权限过滤
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            baseCriteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }

        // 获取正常状态
        long abnormalCount = mongoTemplate.count(Query.query(baseCriteria).addCriteria(Criteria.where(FIELD_USER_STATUS).is(USER_STATUS_ABNORMAL)), DeviceBean.class);

        // 获取在线状态
        long onlineCount = mongoTemplate.count(Query.query(baseCriteria).addCriteria(Criteria.where(FIELD_DEVICE_STATUS).is(DeviceBean.DeviceConstant.DEVICE_STATUS_UP)), DeviceBean.class);

        // 获取不在线状态
        long offlineCount = mongoTemplate.count(Query.query(baseCriteria).addCriteria(Criteria.where(FIELD_DEVICE_STATUS).is(DeviceBean.DeviceConstant.DEVICE_STATUS_DOWN)), DeviceBean.class);

        result.put("abnormal", abnormalCount);
        result.put("online", onlineCount);
        result.put("offline", offlineCount);
        result.put("count", onlineCount + offlineCount);
        return result;
    }

    private AreaTreeVO childrenAddGet(String areaName, String absAreaName, String nodeId, Collection<AreaTreeVO> childrens, Map<String, Integer> deviceAreaCountMap, Map<String, Integer> deviceOnlineCountMap, String parentId) {
        AreaTreeVO childrenAreaDTO = getChildrenAreaDTO(childrens, areaName);
        if (Objects.nonNull(childrenAreaDTO)) {
            return childrenAreaDTO;
        }

        AreaTreeVO areaTreeVO = new AreaTreeVO(areaName, deviceOnlineCountMap.get(absAreaName), deviceAreaCountMap.get(absAreaName), nodeId, parentId, Lists.newArrayList());
        childrens.add(areaTreeVO);
        return areaTreeVO;
    }

    private AreaTreeVO getChildrenAreaDTO(Collection<AreaTreeVO> childrens, String areaName) {
        for (AreaTreeVO children : childrens) {
            if (StringUtils.equals(children.getAreaName(), areaName)) {
                return children;
            }
        }

        return null;
    }

    /**
     * 获取分页查询对象
     *
     * @param deviceQueryBean
     * @param tenantId
     * @return Query
     */
    private Query getPageQuery(DeviceQueryBean deviceQueryBean, String tenantId) {
        Criteria criteria = new Criteria();
        if (Objects.nonNull(tenantId)) {
            criteria.and(FILED_RELATIONS_TENANT_ID).is(tenantId);
        }
        UserEntity currentUser = SystemUtil.getCurrentUser();
        if (currentUser != null && !currentUser.isSystem()) {
            criteria.and(FIELD_DEVICE_CODE).in(deviceRelationService.getDevicePermissions(currentUser));
        }

        if (deviceQueryBean == null) {
            return Query.query(criteria);
        }

        List<Criteria> criteriaAndList = new ArrayList<>();
        String nodeId = deviceQueryBean.getNodeId();
        String content = deviceQueryBean.getContent();
        Integer deviceStatus = deviceQueryBean.getDeviceStatus();
        Integer userStatus = deviceQueryBean.getUserStatus();
        List<String> deviceCodeList = deviceQueryBean.getDeviceCodeList();
        Boolean queryAll = deviceQueryBean.getQueryAll();
        String name = deviceQueryBean.getName();
        List<String> organizationIds = deviceQueryBean.getOrganizationIds();
        List<String> protocolTypes = deviceQueryBean.getProtocolTypes();

        if (StringUtils.isNotBlank(nodeId)) {
            criteria.and(FIELD_SUB_DEVICE_TYPE).regex(Pattern.compile("^".concat(nodeId).concat(".*$")));
        }

        if (Objects.nonNull(deviceStatus)) {
            criteriaAndList.add(Criteria.where(FIELD_DEVICE_STATUS).is(deviceStatus));
        }

        if (Objects.nonNull(userStatus)) {
            criteriaAndList.add(Criteria.where(FIELD_USER_STATUS).is(userStatus));
        }

        if (CollectionUtils.isNotEmpty(deviceCodeList)) {
            criteriaAndList.add(Criteria.where(FIELD_DEVICE_CODE).in(deviceCodeList));
        }

        if (Objects.isNull(queryAll) || !queryAll) {
            criteriaAndList.add(Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL));
        }

        if (StringUtils.isNotBlank(name)) {
            criteria.and(FIELD_DEVICE_NAME).regex(name);
        }

        if (CollectionUtils.isNotEmpty(organizationIds)) {
            organizationIds = deviceRelationService.getOrganizationIdsEx(organizationIds);
            criteria.and(FILED_RELATIONS_ORGANIZATION_ID).in(organizationIds);
        }
        if (CollectionUtils.isNotEmpty(protocolTypes)) {
            criteriaAndList.add(Criteria.where(FIELD_PROTOCOL_TYPE).in(protocolTypes));
        }

        if (StringUtils.isNotBlank(content)) {
            criteria.orOperator(
                // 设备名称
                Criteria.where(FIELD_DEVICE_NAME).regex(content),
                // 设备标识
                Criteria.where(FILED_DEVICE_GB_CODE).regex(content),
                // 设备厂商
                Criteria.where(FILED_DEVICE_MANUFACTURERS).regex(content),
                // 设备型号
                Criteria.where(FILED_DEVICE_MODEL).regex(content),
                // 设备类型
                Criteria.where(FIELD_DEVICE_TYPE).regex(content),
                // 安装地点
                Criteria.where(FILED_AREAS_ADDRESS_NAME).regex(content),
                // 管辖单位
                Criteria.where(FIELD_MANAGEMENT_UNIT).regex(content),
                // 协议类型
                Criteria.where(FIELD_PROTOCOL_TYPE).regex(content),
                // 码流地址
                Criteria.where(FIELD_ADDRESS).regex(content),
                // 子码流地址
                Criteria.where(FIELD_DEVICE_SUBADDRESS).regex(content),
                // 安装位置
                Criteria.where(FIELD_ADDRESS_DESCRIBE).regex(content),
                // 所属组织
                Criteria.where(FILED_RELATIONS).elemMatch(Criteria.where(DeviceBean.Relation.Constant.FILED_ORGANIZATIONS_NAME).regex(content)),
                // 备注
                Criteria.where(FIELD_REMARKS).regex(content));
        }

        if (CollectionUtils.isNotEmpty(criteriaAndList)) {
            criteria.andOperator(criteriaAndList.toArray(new Criteria[0]));
        }

        return Query.query(criteria);
    }

    /**
     * 设备查询
     *
     * @param query
     * @param tenantId
     * @param pageNum
     * @param size
     * @param sort
     * @param hiddenAbility
     * @param currentUser
     * @return
     */
    private Page<? extends DeviceAbilityGen> queryDevice(Query query, String tenantId, Integer pageNum, Integer size, List<String> sort, Boolean hiddenAbility, UserEntity currentUser, Class<? extends DeviceAbilityGen> resultClass, String... fields) {
        // 查询数量（地图业务）
        Pageable pageRequest = (size == null || size == -1) ? Pageable.unpaged() : PageRequest.of(pageNum, size, Sort.by(sortOrder(sort)));

        //先查条数
        long count = 0;
        if (!pageRequest.isUnpaged()) {
            count = mongoTemplate.count(query, DeviceBean.DeviceConstant.DOCUMENT_NAME);
        }

        // 查询分页
        query.with(pageRequest);
        // 获取字段
        if (ArrayUtils.isNotEmpty(fields)) {
            query.fields().include(fields);
        }
        // 查询分页
        List<? extends DeviceAbilityGen> result = mongoTemplate.find(query, resultClass, DeviceBean.DeviceConstant.DOCUMENT_NAME);
        if (pageRequest.isUnpaged()) {
            count = result.size();
        }

        //补充使能
        if ((Objects.isNull(hiddenAbility) || !hiddenAbility)) {
            List<String> organizationIds = currentUser.getOrganizationList().stream().map(AbstractBaseBO::getId).collect(Collectors.toList());
            deviceAbility(result, tenantId, organizationIds, false);
        }

        // 返回结果
        return new PageImpl<>(result, pageRequest, count);
    }

    private List<Sort.Order> sortOrder(List<String> sort) {
        return Objects.requireNonNullElse(sort, new ArrayList<String>()).stream().map(order -> {
            String[] split = order.split(",");
            boolean isDesc = false;
            String property = order;
            if (split.length == 2) {
                isDesc = split[1].trim().equalsIgnoreCase("desc");
                property = split[0].trim();
            }
            return isDesc ? Sort.Order.desc(property) : Sort.Order.asc(property);
        }).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        NettyClient nettyClient = new NettyClient();
        Map<String, String> hostAndPort = ValidateUtil.getHostAndPort("rtsp://**************:3554/live/2c2a9bcd-98c3-4b0d-a28b-6000c99862d61/channel1/stream1");
        Pair<String, Integer> pair = Pair.of(hostAndPort.get(ValidateUtil.HOST), Integer.parseInt(hostAndPort.get(ValidateUtil.PORT)));
        System.out.println(nettyClient.connectRtsp(pair, "rtsp://**************:3554/live/2c2a9bcd-98c3-4b0d-a28b-6000c99862d61/channel1/stream1"));
    }
}
