package com.polarizon.gendo.sg.device.configuration.config;

import lombok.Getter;
import org.springframework.context.ApplicationListener;
import org.springframework.data.mapping.PersistentEntity;
import org.springframework.data.mapping.context.MappingContextEvent;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * mongo索引创建者
 */
@Getter
@Component
public class MyMongoIndexCreator2 implements ApplicationListener<MappingContextEvent<?, ?>> {

    private final Map<Class<?>, MongoPersistentEntity> classesSeen = new ConcurrentHashMap<>();

    @Override
    public void onApplicationEvent(MappingContextEvent<?, ?> event) {

        PersistentEntity<?, ?> entity = event.getPersistentEntity();

        // Double check type as Spring infrastructure does not consider nested generics
        if (entity instanceof MongoPersistentEntity) {
            Class<?> type = entity.getType();
            if (!classesSeen.containsKey(type)) {
                classesSeen.put(type, (MongoPersistentEntity) entity);
            }
        }
    }
}
