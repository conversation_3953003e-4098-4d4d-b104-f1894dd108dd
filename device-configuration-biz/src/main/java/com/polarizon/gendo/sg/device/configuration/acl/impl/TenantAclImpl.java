package com.polarizon.gendo.sg.device.configuration.acl.impl;

import com.polarizon.gendo.sg.authority.api.TenantManagerControllerInterface;
import com.polarizon.gendo.sg.authority.bean.TenantEntity;
import com.polarizon.gendo.sg.device.configuration.acl.TenantAcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户信息访问控制
 */
@Component
public class TenantAclImpl implements TenantAcl {
    /**
     * feign接口
     */
    @Autowired
    private TenantManagerControllerInterface tenantManagerControllerInterface;

    /**
     * 获取所有租户ID
     *
     * @return
     */
    @Override
    public List<String> findAllTenantId() {
        List<String> tenantIds = tenantManagerControllerInterface.list().getData().stream().map(TenantEntity::getId).collect(Collectors.toList());
        tenantIds.add("default");
        return tenantIds;
    }

    /**
     * 获取所有租户
     *
     * @return
     */
    @Override
    public List<TenantEntity> findAllTenant() {
        return tenantManagerControllerInterface.list().getData();
    }

    /**
     * 获取单个租户
     *
     * @param tenantId
     * @return
     */
    @Override
    public TenantEntity findTenantEntity(String tenantId) {
        return tenantManagerControllerInterface.get(tenantId).getData();
    }

}
