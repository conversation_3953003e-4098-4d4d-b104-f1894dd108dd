//package com.polarizon.gendo.sg.device.configuration.changelogs.initializer;
//
//import com.github.cloudyrock.mongock.ChangeLog;
//import com.github.cloudyrock.mongock.ChangeSet;
//import com.github.cloudyrock.mongock.driver.mongodb.springdata.v3.decorator.impl.MongockTemplate;
//import com.google.common.collect.Lists;
//import com.polarizon.gendo.sg.device.configuration.bean.BusinessViewBean;
//import com.polarizon.gendo.sg.device.configuration.bean.DeviceBean;
//import com.polarizon.gendo.sg.device.configuration.bean.vo.MoveDeviceVO;
//import com.polarizon.gendo.sg.device.configuration.dao.BusinessViewBeanDao;
//import com.polarizon.gendo.sg.device.configuration.utils.DeviceConstants;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.data.mongodb.core.query.Update;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Stream;
//
//import static com.polarizon.common.bean.bo.AbstractBaseBO.Constants.FIELD_STATUS_FLAG;
//import static com.polarizon.common.utils.Constants.STATUS_FLAG_NORMAL;
//import static com.polarizon.gendo.sg.device.configuration.bean.DeviceBean.DeviceConstant.FIELD_DEVICE_PROPERTY;
//import static com.polarizon.gendo.sg.device.configuration.constant.device.camera.CameraProperty.MASTER_DEVICE;
//import static java.util.stream.Collectors.toList;
//
//@ChangeLog(order = "1")
//public class DeviceTypeInitializerChangeLog {
//
//
//    /**
//     * 设备组初始化
//     *
//     * @param businessViewBeanDao
//     */
//    @ChangeSet(id = "data-initializer-with-repository", order = "001", author = "polarise")
//    public void deviceTypeInitializer(BusinessViewBeanDao businessViewBeanDao,
//                                      MongockTemplate template) {
//        businessViewBeanDao.deleteAll();
//        List<BusinessViewBean> list = getInitBusinessViewGroup();
//        businessViewBeanDao.saveAll(list);
//
//        // 将原有设备的设备类型写入到新的设备组里。
//        List<DeviceBean> allDevice = getAll(template);
//        for (DeviceBean device : allDevice) {
//            MoveDeviceVO moveDeviceVO = new MoveDeviceVO();
//            moveDeviceVO.setDeviceCodes(Lists.newArrayList(device.getDeviceCode()));
//            moveDeviceVO.setIsDeviceGroup(false);
//            moveDeviceVO.setFromNodeIds(Lists.newArrayList("-1"));
//            moveDeviceVO.setIsCopy(false);
//            moveDeviceVO.setToNodeId(device.getSubDeviceType());
//            moveDeviceToOtherGroup(template, moveDeviceVO);
//            device.setSubDeviceType(device.getSubDeviceType());
//            update(template, device);
//        }
//    }
//
//    /**
//     * 子码流处理
//     *
//     * @param businessViewBeanDao
//     */
//    @ChangeSet(id = "data-initializer-with-repository-sub-device", order = "002", author = "polarise")
//    public void subDeviceInitializer(BusinessViewBeanDao businessViewBeanDao,
//                                     MongockTemplate template) {
//        // 将原有设备的设备类型写入到新的设备组里。
//        List<DeviceBean> subDevices = getSubDevice(template);
//        for (DeviceBean subDevice : subDevices) {
//            update(template, subDevice);
//        }
//    }
//
//    /**
//     * 修改摄像头和NVR的分组名称
//     *
//     * @param template
//     */
//    @ChangeSet(id = "data-initializer-update-device-type-node-name", order = "003", author = "polarise")
//    public void updateDeviceTypeNodeName(MongockTemplate template) {
//        Query query = new Query(Criteria.where(BusinessViewBean.BusinessViewConstant.FIELD_NODE_ID).is(DeviceConstants.CAMERA));
//        Update update = new Update().set(BusinessViewBean.BusinessViewConstant.FIELD_NODE_NAME, DeviceConstants.CAMERA_NAME);
//        template.updateFirst(query, update, BusinessViewBean.class);
//        Query query1 = new Query(Criteria.where(BusinessViewBean.BusinessViewConstant.FIELD_NODE_ID).is(DeviceConstants.NVR));
//        Update update1 = new Update().set(BusinessViewBean.BusinessViewConstant.FIELD_NODE_NAME, DeviceConstants.STORAGE_DEVICE);
//        template.updateFirst(query1, update1, BusinessViewBean.class);
//    }
//
//    private List<DeviceBean> getSubDevice(MongockTemplate template) {
//        Query query = new Query(Criteria.where(FIELD_DEVICE_PROPERTY + "." + MASTER_DEVICE).exists(true));
//        return template.find(query, DeviceBean.class);
//    }
//
//    List<DeviceBean> getAll(MongockTemplate template) {
//        Query query = new Query(Criteria.where(FIELD_STATUS_FLAG).is(STATUS_FLAG_NORMAL));
//        return template.find(query, DeviceBean.class);
//    }
//
//    private void moveDeviceToOtherGroup(MongockTemplate template, MoveDeviceVO moveDeviceVO) {
//        Query query = new Query(Criteria.where(BusinessViewBean.BusinessViewConstant.FIELD_NODE_ID).is(moveDeviceVO.getToNodeId()));
//        //获取移动到其他设备组信息
//        BusinessViewBean viewBean = selectDeviceGroupByNodeId(template, moveDeviceVO.getToNodeId());
//        Update update = new Update();
//        updateGroup(template, moveDeviceVO, update, query, viewBean, moveDeviceVO.getDeviceCodes());
//    }
//
//    private void updateGroup(MongockTemplate template, MoveDeviceVO moveDeviceVO, Update update, Query query, BusinessViewBean viewBean, List<String> deviceCodes) {
//        if (Objects.nonNull(viewBean) && CollectionUtils.isNotEmpty(viewBean.getDeviceCodes())) {
//            //取并集，更新code
//            List<String> collect = Stream.of(viewBean.getDeviceCodes(), moveDeviceVO.getDeviceCodes()).flatMap(Collection::stream).distinct().collect(toList());
//            update.set(BusinessViewBean.BusinessViewConstant.FIELID_DEVICECODES, collect);
//        } else {
//            assert viewBean != null;
//            update.set(BusinessViewBean.BusinessViewConstant.FIELID_DEVICECODES, deviceCodes);
//        }
//        template.updateFirst(query, update, BusinessViewBean.class);
//    }
//
//    private BusinessViewBean selectDeviceGroupByNodeId(MongockTemplate template, String nodeId) {
//        Query query = new Query(Criteria.where(BusinessViewBean.BusinessViewConstant.FIELD_NODE_ID).is(nodeId));
//        return template.findOne(query, BusinessViewBean.class);
//    }
//
//    private void update(MongockTemplate template, DeviceBean deviceBean) {
//        deviceBean.setSubDeviceType(deviceBean.getDeviceType());
//        Query query = new Query(Criteria.where(DeviceBean.DeviceConstant.FIELD_DEVICE_CODE).is(deviceBean.getDeviceCode()));
//        template.findAndReplace(query, deviceBean);
//    }
//
//    /**
//     * 初始化设备组代码
//     *
//     * @return
//     */
//    List<BusinessViewBean> getInitBusinessViewGroup() {
//        List<BusinessViewBean> list = new ArrayList<>();
//        // 设备组代码
//        BusinessViewBean device = new BusinessViewBean();
//        device.setNodeId(DeviceConstants.DEVICE_NODE_ID);
//        device.setNodeName(DeviceConstants.DEVICE_NODE_NAME);
//        device.setLayer(0);
//        device.setParentNodeId("-1");
//        device.setIsDefault(true);
//        list.add(device);
//
//        BusinessViewBean camera = new BusinessViewBean();
//        camera.setNodeId(DeviceConstants.CAMERA);
//        camera.setNodeName(DeviceConstants.CAMERA_NODE_NAME);
//        camera.setLayer(1);
//        camera.setParentNodeId(DeviceConstants.DEVICE_NODE_ID);
//        camera.setIsDefault(true);
//        list.add(camera);
//
//        BusinessViewBean cameraBall = new BusinessViewBean();
//        cameraBall.setNodeId(DeviceConstants.CAMERA_BALL);
//        cameraBall.setNodeName(DeviceConstants.CAMERA_BALL_NODE);
//        cameraBall.setLayer(2);
//        cameraBall.setParentNodeId(DeviceConstants.CAMERA);
//        cameraBall.setIsDefault(true);
//        list.add(cameraBall);
//
//        BusinessViewBean cameraGun = new BusinessViewBean();
//        cameraGun.setNodeId(DeviceConstants.CAMERA_GUN);
//        cameraGun.setNodeName(DeviceConstants.CAMERA_GUN_NODE);
//        cameraGun.setLayer(2);
//        cameraGun.setParentNodeId(DeviceConstants.CAMERA);
//        cameraGun.setIsDefault(true);
//        list.add(cameraGun);
//
//        BusinessViewBean cameraPanoramic = new BusinessViewBean();
//        cameraPanoramic.setNodeId(DeviceConstants.CAMERA_PANORAMIC);
//        cameraPanoramic.setNodeName(DeviceConstants.PANORAMIC_NODE);
//        cameraPanoramic.setLayer(2);
//        cameraPanoramic.setParentNodeId(DeviceConstants.CAMERA);
//        cameraPanoramic.setIsDefault(true);
//        list.add(cameraPanoramic);
//
//        BusinessViewBean nvr = new BusinessViewBean();
//        nvr.setNodeId(DeviceConstants.NVR);
//        nvr.setNodeName("NVR");
//        nvr.setLayer(1);
//        nvr.setParentNodeId(DeviceConstants.DEVICE_NODE_ID);
//        nvr.setIsDefault(true);
//        list.add(nvr);
//
//        BusinessViewBean nvrNvr = new BusinessViewBean();
//        nvrNvr.setNodeId(DeviceConstants.NVR_NVR);
//        nvrNvr.setNodeName("NVR");
//        nvrNvr.setLayer(2);
//        nvrNvr.setParentNodeId(DeviceConstants.NVR);
//        nvrNvr.setIsDefault(true);
//        list.add(nvrNvr);
//
//        BusinessViewBean nvrCvr = new BusinessViewBean();
//        nvrCvr.setNodeId(DeviceConstants.NVR_CVR);
//        nvrCvr.setNodeName("CVR");
//        nvrCvr.setLayer(2);
//        nvrCvr.setParentNodeId(DeviceConstants.NVR);
//        nvrCvr.setIsDefault(true);
//        list.add(nvrCvr);
//
//        BusinessViewBean nvrMediaStorage = new BusinessViewBean();
//        nvrMediaStorage.setNodeId(DeviceConstants.NVR_MEDIA_STORAGE);
//        nvrMediaStorage.setNodeName(DeviceConstants.NVR_MEDIA_STORAGE_NODE);
//        nvrMediaStorage.setLayer(2);
//        nvrMediaStorage.setParentNodeId(DeviceConstants.NVR);
//        nvrMediaStorage.setIsDefault(true);
//        list.add(nvrMediaStorage);
//
//        BusinessViewBean server = new BusinessViewBean();
//        server.setNodeId(DeviceConstants.SERVER);
//        server.setNodeName(DeviceConstants.SERVER_NODE);
//        server.setLayer(1);
//        server.setParentNodeId(DeviceConstants.DEVICE_NODE_ID);
//        server.setIsDefault(true);
//        list.add(server);
//
//        BusinessViewBean serverAlgorithmServer = new BusinessViewBean();
//        serverAlgorithmServer.setNodeId(DeviceConstants.SERVER_ALGORITHM_SERVER);
//        serverAlgorithmServer.setNodeName(DeviceConstants.ALGORITHM_SERVER_NODE);
//        serverAlgorithmServer.setLayer(2);
//        serverAlgorithmServer.setParentNodeId(DeviceConstants.SERVER);
//        serverAlgorithmServer.setIsDefault(true);
//        list.add(serverAlgorithmServer);
//
//        BusinessViewBean serverAlgorithmBox = new BusinessViewBean();
//        serverAlgorithmBox.setNodeId(DeviceConstants.SERVER_ALGORITHM_BOX);
//        serverAlgorithmBox.setNodeName(DeviceConstants.ALGORITHM_BOX_NODE);
//        serverAlgorithmBox.setLayer(2);
//        serverAlgorithmBox.setParentNodeId(DeviceConstants.SERVER);
//        serverAlgorithmBox.setIsDefault(true);
//        list.add(serverAlgorithmBox);
//
//        BusinessViewBean serverBackServer = new BusinessViewBean();
//        serverBackServer.setNodeId(DeviceConstants.SERVER_BACK_SERVER);
//        serverBackServer.setNodeName(DeviceConstants.BACK_SERVER_NODE);
//        serverBackServer.setLayer(2);
//        serverBackServer.setParentNodeId(DeviceConstants.SERVER);
//        serverBackServer.setIsDefault(true);
//        list.add(serverBackServer);
//
//        BusinessViewBean serverMediaServer = new BusinessViewBean();
//        serverMediaServer.setNodeId(DeviceConstants.SERVER_MEDIA_SERVER);
//        serverMediaServer.setNodeName(DeviceConstants.MEDIA_SERVER_NODE);
//        serverMediaServer.setLayer(2);
//        serverMediaServer.setParentNodeId(DeviceConstants.SERVER);
//        serverMediaServer.setIsDefault(true);
//        list.add(serverMediaServer);
//
//        BusinessViewBean sensor = new BusinessViewBean();
//        sensor.setNodeId(DeviceConstants.SENSOR);
//        sensor.setNodeName(DeviceConstants.SENSOR_NODE);
//        sensor.setLayer(1);
//        sensor.setParentNodeId(DeviceConstants.DEVICE_NODE_ID);
//        sensor.setIsDefault(true);
//        list.add(sensor);
//
//        BusinessViewBean sensorTemperature = new BusinessViewBean();
//        sensorTemperature.setNodeId(DeviceConstants.SENSOR_TEMPERATURE);
//        sensorTemperature.setNodeName(DeviceConstants.TEMPERATURE_SENSOR_NODE);
//        sensorTemperature.setLayer(2);
//        sensorTemperature.setParentNodeId(DeviceConstants.SENSOR);
//        sensorTemperature.setIsDefault(true);
//        list.add(sensorTemperature);
//
//        BusinessViewBean sensorHumidity = new BusinessViewBean();
//        sensorHumidity.setNodeId(DeviceConstants.SENSOR_HUMIDITY);
//        sensorHumidity.setNodeName(DeviceConstants.HUMIDITY_SENSOR_NODE);
//        sensorHumidity.setLayer(2);
//        sensorHumidity.setParentNodeId(DeviceConstants.SENSOR);
//        sensorHumidity.setIsDefault(true);
//        list.add(sensorHumidity);
//
//        BusinessViewBean sensorWindSpeedDirection = new BusinessViewBean();
//        sensorWindSpeedDirection.setNodeId(DeviceConstants.SENSOR_WIND_SPEED_DIRECTION);
//        sensorWindSpeedDirection.setNodeName(DeviceConstants.WIND_SPEED_DIRECTION_SENSOR_NODE);
//        sensorWindSpeedDirection.setLayer(2);
//        sensorWindSpeedDirection.setParentNodeId(DeviceConstants.SENSOR);
//        sensorWindSpeedDirection.setIsDefault(true);
//        list.add(sensorWindSpeedDirection);
//
//        BusinessViewBean sensorSpeed = new BusinessViewBean();
//        sensorSpeed.setNodeId(DeviceConstants.SENSOR_SPEED);
//        sensorSpeed.setNodeName(DeviceConstants.SPEED_SENSOR_NODE);
//        sensorSpeed.setLayer(2);
//        sensorSpeed.setParentNodeId(DeviceConstants.SENSOR);
//        sensorSpeed.setIsDefault(true);
//        list.add(sensorSpeed);
//        //场景类型初始化
//        BusinessViewBean scene = new BusinessViewBean();
//        scene.setNodeId(DeviceConstants.SCENE_NODE_ID);
//        scene.setNodeName(DeviceConstants.SCENE_NODE_NAME);
//        scene.setLayer(0);
//        scene.setParentNodeId("-1");
//        scene.setIsDefault(true);
//        list.add(scene);
//
//        BusinessViewBean ungrouped = new BusinessViewBean();
//        ungrouped.setNodeId(DeviceConstants.UNGROUPED_NODE_ID);
//        ungrouped.setNodeName(DeviceConstants.UNGROUPED_NODE_NAME);
//        ungrouped.setLayer(0);
//        ungrouped.setParentNodeId("-1");
//        ungrouped.setIsDefault(true);
//        list.add(ungrouped);
//        return list;
//    }
//
//}
