package com.polarizon.gendo.sg.device.configuration.acl.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.polarizon.gendo.sg.device.configuration.acl.bean.AreaCameraBean;
import com.polarizon.gendo.sg.device.configuration.service.DeviceConfigurationService;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备配置消费者
 */
@Slf4j
public class DeviceConfigurationSichuanNetworkConsumer implements Runnable {

    /**
     * 阻塞队列
     */
    private final BlockingQueue<AreaCameraBean> blockingQueue;

    /**
     * 完成标识符
     */
    private final AtomicBoolean atomicBoolean;

    /**
     * 构造
     *
     * @param blockingQueue
     * @param atomicBoolean
     */
    public DeviceConfigurationSichuanNetworkConsumer(BlockingQueue<AreaCameraBean> blockingQueue, AtomicBoolean atomicBoolean) {
        this.blockingQueue = blockingQueue;
        this.atomicBoolean = atomicBoolean;
    }

    /**
     * 执行
     */
    @Override
    public void run() {
        while (!atomicBoolean.get()) {
            try {
                // 获取摄像头信息
                AreaCameraBean take = blockingQueue.poll(5, TimeUnit.SECONDS);
                if (Objects.isNull(take)) {
                    continue;
                }

                // 添加摄像头到数据库
                addAreaCameraBeanToDB(take);
            } catch (InterruptedException e) {
                log.error("消费失败，出现中断异常，请联系管理员", e);
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 添加摄像头信息到数据库
     *
     * @param areaCameraBean 区域摄像头
     */
    public void addAreaCameraBeanToDB(AreaCameraBean areaCameraBean) {
        DeviceConfigurationService deviceConfigurationService = SpringUtil.getBean(DeviceConfigurationService.class);
        deviceConfigurationService.upsertAreaCameraBean(areaCameraBean);
    }

}
