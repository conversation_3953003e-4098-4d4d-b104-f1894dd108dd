package com.polarizon.gendo.sg.device.configuration.service.monitor.rtsp.util;

/**
 * byte字节工具类
 */
public class ByteUtil {
    /**
     * 字节数组转字符串
     *
     * @param buffer
     * @return
     */
    public static String byteToStr(byte[] buffer) {
        try {
            int length = 0;
            for (int i = 0; i < buffer.length; ++i) {
                if (buffer[i] == 0) {
                    length = i;
                    break;
                }
            }
            return new String(buffer, 0, length, "UTF-8");
        } catch (Exception e) {
            return "";
        }
    }
}
