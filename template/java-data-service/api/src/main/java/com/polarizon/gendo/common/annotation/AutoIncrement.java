package com.polarizon.gendo.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD })
public @interface AutoIncrement {
    /**
     * 开始值
     */
    int start() default 1;

    /**
     * 步长
     */
    int step() default 1;

    /**
     * 排序，当多个字段都有自增注解时，并且自增字段之前有依赖关系时，需要指定排序
     */
    int sort() default 0;

    /**
     * 自增分组字段
     */
    String[] groupBy() default {};
}