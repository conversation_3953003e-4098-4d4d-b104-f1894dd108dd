package com.polarizon.gendo.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "算法识别出结构化数据")
public class AlgorithmResultBox {
    /**
     * {"xmin": "0","ymin": "0","xmax": "100","ymax": "100","track_id": "100","class": "1",
     * "gpsx": "123.111","gpsy": "124.3434","tags": [[1,2,3,4],[5,6,7,8],[2,0,"粤A13455"]]}
     */
    @Schema(description = "左上角x")
    @JSONField(name = "xmin")
    private Double xMin;

    @Schema(description = "左上角y")
    @JSONField(name = "ymin")
    private Double yMin;

    @Schema(description = "右下角x")
    @JSONField(name = "xmax")
    private Double xMax;

    @Schema(description = "右下角y")
    @JSONField(name = "ymax")
    private Double yMax;

    @Schema(description = "跟踪id")
    @JSONField(name = "track_id")
    private Integer trackId;

    @Schema(description = "类别/人/车/物体")
    @JSONField(name = "class")
    private Integer category;

    @Schema(description = "经度")
    @JSONField(name = "longitude")
    private double longitude;

    @Schema(description = "纬度")
    @JSONField(name = "latitude")
    private double latitude;

    @Schema(description = "识别数据标签")
    @JSONField(name = "tags")
    private List<List<String>> tags;

    @Schema(description = "事件等级")
    @JSONField(name = "event_max_level")
    private Integer eventMaxLevel = 0;

    @Schema(description = "分数")
    @JSONField(name = "score")
    private Float score;

    @Schema(description = "事件描述")
    @JSONField(name = "event_label")
    private List<String> eventLabel = Lists.newArrayList();
}
