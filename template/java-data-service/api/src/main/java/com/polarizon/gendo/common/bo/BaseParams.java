package com.polarizon.gendo.common.bo;

import com.polarizon.gendo.common.utils.RegexUtil;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@SuperBuilder
@Data
@NoArgsConstructor
public class BaseParams {
    @Parameter(description = "ObjectID列表")
    public List<String> ids;
    @Parameter(description = "查询创建开始时间")
    public Long startCreateTime;
    @Parameter(description = "查询创建结束时间")
    public Long endCreateTime;
    @Parameter(description = "查询更新开始时间")
    public Long startUpdateTime;
    @Parameter(description = "查询更新结束时间")
    public Long endUpdateTime;
    @Parameter(description = "创建者")
    public List<String> createBy;
    @Parameter(description = "模糊查询参数: 创建者")
    public String createByLike;
    @Parameter(description = "更新者")
    public List<String> updateBy;
    @Parameter(description = "模糊查询参数: 更新者")
    public String updateByLike;
    @Parameter(description = "备注信息")
    public List<String> remark;
    @Parameter(description = "模糊查询参数: 备注信息")
    public String remarkLike;

    /**
     * 判断查询参数是否为空
     *
     * @return
     */
    public boolean isParamEmpty() {
        // 获取所有字段
        Class<?> objClass = this.getClass();
        Field[] fields = objClass.getDeclaredFields();
        Field[] superFields = objClass.getSuperclass().getDeclaredFields();
        List<Field> allFields = List.of(fields, superFields).stream().flatMap(arr -> Stream.of(arr)).toList();

        //判断字段是否为空
        for (Field field : allFields) {
            field.setAccessible(true);
            try {
                //这里有点问题，@Slf4j会增加log字段，这里把它排除掉
                if (Objects.equals("log", field.getName())) {
                    continue;
                }
                Object fieldValue = field.get(this);
                if (fieldValue != null) {
                    if (fieldValue instanceof String) {
                        if (StringUtils.isNotBlank((String) fieldValue)) {
                            // 字符串字段不为空
                            return false;
                        }
                    } else if (fieldValue instanceof List) {
                        if (CollectionUtils.isNotEmpty((List<?>) fieldValue)) {
                            // 列表字段不为空
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("获取字段值失败", e);
            }
        }
        // 所有字段都为空
        return true;
    }

    public void baseAndCriteria(Criteria criteria) {
        baseAndCriteria(criteria, null);
    }

    public void baseAndCriteria(Criteria criteria, String prefix) {
        setBetween(criteria, "createTime", this.startCreateTime, this.endCreateTime);
        setBetween(criteria, "updateTime", this.startUpdateTime, this.endUpdateTime);
        if (isNotEmpty(this.createBy)) {
            criteria.and(concatDot(prefix, "createBy")).in(this.createBy);
        }
        if (isNotBlank(this.createByLike)) {
            criteria.and(concatDot(prefix, "createBy")).regex(".*?" + RegexUtil.escapeRegexCharacter(this.createByLike) + ".*");
        }
        if (isNotEmpty(this.updateBy)) {
            criteria.and(concatDot(prefix, "updateBy")).in(this.updateBy);
        }
        if (isNotBlank(this.updateByLike)) {
            criteria.and(concatDot(prefix, "updateBy")).regex(".*?" + RegexUtil.escapeRegexCharacter(this.updateByLike) + ".*");
        }
        if (isNotEmpty(this.remark)) {
            criteria.and(concatDot(prefix, "remark")).in(this.remark);
        }
        if (isNotBlank(this.remarkLike)) {
            criteria.and(concatDot(prefix, "remark")).regex(".*?" + RegexUtil.escapeRegexCharacter(this.remarkLike) + ".*");
        }
        if (isNotEmpty(this.ids)) {
            criteria.and(concatDot(prefix, "_id")).in(this.ids.stream().map(ObjectId::new).collect(Collectors.toList()));
        }
    }

    public String concatDot(String prefix, String key) {
        return prefix == null ? key : prefix + "." + key;
    }

    public <T extends AbstractBaseBO> boolean filter(T t) {
        if (nonNull(this.startCreateTime) && t.createTime < this.startCreateTime) {
            return false;
        }
        if (nonNull(this.endCreateTime) && t.createTime > this.endCreateTime) {
            return false;
        }
        if (nonNull(this.startUpdateTime) && t.updateTime < this.startUpdateTime) {
            return false;
        }
        if (nonNull(this.endUpdateTime) && t.updateTime > this.endUpdateTime) {
            return false;
        }
        if (isNotEmpty(this.createBy) && !this.createBy.contains(t.createBy)) {
            return false;
        }
        if (isNotBlank(this.createByLike) && !t.createBy.contains(this.createByLike)) {
            return false;
        }
        if (isNotEmpty(this.updateBy) && !this.updateBy.contains(t.updateBy)) {
            return false;
        }
        if (isNotBlank(this.updateByLike) && !t.updateBy.contains(this.updateByLike)) {
            return false;
        }
        if (isNotEmpty(this.remark) && !this.remark.contains(t.remark)) {
            return false;
        }
        if (isNotBlank(this.remarkLike) && !t.remark.contains(this.remarkLike)) {
            return false;
        }
        if (isNotEmpty(this.ids) && !this.ids.contains(t.getId())) {
            return false;
        }
        return true;
    }

    /**
     * 通用条件查询，设置区间条件
     */
    private void setBetween(Criteria criteria, String fieldName, Long startVal, Long endVal) {
        if (isNull(startVal) && isNull(endVal)) {
            return;
        }
        if (nonNull(startVal) && nonNull(endVal)) {
            criteria.and(fieldName).gte(startVal).lte(endVal);
        } else if (nonNull(startVal)) {
            criteria.and(fieldName).gte(startVal);
        } else {
            criteria.and(fieldName).lte(endVal);
        }
    }
}
