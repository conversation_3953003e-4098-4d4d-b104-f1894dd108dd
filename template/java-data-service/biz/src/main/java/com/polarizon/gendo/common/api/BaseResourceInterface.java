package com.polarizon.gendo.common.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.gendo.common.dto.ValidatedList;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.google.common.collect.Lists.newArrayList;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public interface BaseResourceInterface<BO extends AbstractBaseBO, ID> {
    /**
     * 获取DAO
     *
     * @return MongoRepository
     */
    CustomMongoRepositoryInterface<BO, ID> repository();

    default void setTagDomain(List<String> dataTag, String domainLabel, BO bean) {
        // 如果从header里获取到了dataTag，就把dataTag设置到bean里
        Optional.ofNullable(dataTag).ifPresent(bean::setDataTag);
        // 如果从header里获取到了domainLabel，就把domainLabel设置到bean里
        Optional.ofNullable(domainLabel).ifPresent(s -> bean.setDomainLabel(getDomainLabelMap(domainLabel)));
    }

    default Map getDomainLabelMap(String domainLabel) {
        try {
            return JSON.parseObject(domainLabel, Map.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("domainLabel is not a json string!");
        }
    }

    default void batchSetTagDomain(List<String> dataTag, String domainLabel, ValidatedList<BO> beans) {
        // 如果从header里获取到了dataTag，就把dataTag设置到每个bean里
        Optional.ofNullable(dataTag).ifPresent(s -> {
            beans.stream().forEach(a -> a.setDataTag(s));
        });
        // 如果从header里获取到了domainLabel，就把domainLabel设置到每个bean里
        Optional.ofNullable(domainLabel).ifPresent(s -> {
            final Map<String, String> domainLabelMap = getDomainLabelMap(domainLabel);
            beans.forEach(a -> a.setDomainLabel(domainLabelMap));
        });
    }

    default void addLabelJsonCriteria(Criteria criteria, String labelJson) {
        if (StringUtils.isNotBlank(labelJson)) {
            Map<String, Object> domainLabel = getDomainLabelMap(labelJson);
            if (nonNull(domainLabel) && isNotEmpty(domainLabel.keySet())) {
                List<Criteria> orCriterias = Lists.newArrayList(Criteria.where("domainLabel").isNull());
                for (Map.Entry<String, Object> entry : domainLabel.entrySet()) {
                    if (entry.getValue() instanceof String s) {
                        orCriterias.add(Criteria.where("domainLabel." + entry.getKey()).is(s));
                    } else if (entry.getValue() instanceof JSONArray s) {
                        orCriterias.add(Criteria.where("domainLabel." + entry.getKey()).in(s.toArray()));
                    }
                }
                criteria.orOperator(orCriterias);
            }
        }
    }

    default void addLabelJsonDocument(List<Document> query, String labelJson) {
        if (isNotBlank(labelJson)) {
            Map<String, Object> domainLabel = getDomainLabelMap(labelJson);
            if (nonNull(domainLabel) && isNotEmpty(domainLabel.keySet())) {
                List<Document> orDocuments = newArrayList(new Document("domainLabel", null));
                for (Map.Entry<String, Object> entry : domainLabel.entrySet()) {
                    if (entry.getValue() instanceof String s) {
                        orDocuments.add(new Document("domainLabel." + entry.getKey(), s));
                    } else if (entry.getValue() instanceof JSONArray s) {
                        orDocuments.add(new Document("domainLabel." + entry.getKey(), new Document("$in", s)));
                    }
                }
                // 构建查询条件
                query.add(new Document("$or", orDocuments));
            }
        }
    }


}
