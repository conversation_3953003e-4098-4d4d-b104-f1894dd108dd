package com.polarizon.gendo.common.api;

import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.EditView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.ShowView;
import com.polarizon.gendo.common.bo.AbstractBaseBO.UpdateAction;
import com.polarizon.gendo.common.config.MutationHolder;
import com.polarizon.gendo.common.dto.MutationMessage.Action;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.dto.ValidatedList;
import com.polarizon.gendo.common.utils.Constants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static com.google.common.collect.Lists.newArrayList;
import static com.polarizon.gendo.common.dto.ResultDTO.error;
import static com.polarizon.gendo.common.dto.ResultDTO.ok;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

public interface SaveResourceInterface<BO extends AbstractBaseBO, ID>
    extends SaveInterface<BO, ID>, BaseResourceInterface<BO, ID> {

    default MutationHolder mutationHolder() {
        return MutationHolder.getSingleMutation();
    }

    /**
     * BO修改合法检查
     *
     * @return MongoRepository
     */
    default Function<List<BO>, Pair<Boolean, String>> saveChecker() {
        return (bo) -> Pair.of(true, "");
    }

    /**
     * 修改BO
     *
     * @param userAccount String 用户账号
     * @param source      BO BO对象
     * @return ResultDTO
     */
    @Override
    @PutMapping
    @Operation(summary = "单独修改，幂等操作，需要传入ID")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    default ResultDTO<BO> update(
        @Parameter(description = "租户ID", example = "tenant-A") @RequestHeader(required = false, value =
            Constants.HEADER_TENANT_ID_KEY) String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon") @RequestHeader(required = false, value =
            Constants.HEADER_NAMESPACE_KEY) String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "数据标签") @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) List<String> dataTag,
        @Parameter(description = "领域标签") @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) String domainLabel,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name = "notNotify",
            required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @RequestBody() @JsonView(EditView.class) @Valid BO source) {
        if (Optional.ofNullable(source).isPresent() && !allFieldIsNULL(source)) {
            setTagDomain(dataTag, domainLabel, source);
            Pair<Boolean, String> checkResult = saveChecker().apply(newArrayList(source));
            if (!checkResult.getFirst()) {
                return ResultDTO.error(checkResult.getSecond());
            }
            BO preData = repository().findById((ID) source.getId()).get();
            if (StringUtils.isBlank(source.getId()) || Objects.isNull(preData)) {
                return ResultDTO.error("Data " + source.getId() + " not find in DB, save error!");
            }
            source.init(userAccount, false);
            BO result = repository().save(source, unsetFields);
            return ok("save", result, preData);
        }
        return error("required request body is missing");
    }

    /**
     * 全部BO—批量修改
     *
     * @param userAccount String 用户账号
     * @param beans       Iterable<BO> BO对象列表
     * @return ResultDTO
     */
    @Override
    @PutMapping("/all")
    @Operation(summary = "批量修改，幂等操作，需要传入ID")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    default ResultDTO<Iterable<BO>> updateAll(
        @Parameter(description = "租户ID", example = "tenant-A") @RequestHeader(required = false, value =
            Constants.HEADER_TENANT_ID_KEY) String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon") @RequestHeader(required = false, value =
            Constants.HEADER_NAMESPACE_KEY) String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "数据标签") @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) List<String> dataTag,
        @Parameter(description = "领域标签") @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) String domainLabel,
        @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name = "notNotify",
            required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @Parameter(description = "空字段数据是否更新，true：更新，false：不更新(不校验以前是否存在)", required = false) @RequestParam(value =
            "updateNull", required = false, defaultValue = "false") boolean updateNull,
        @Parameter(description = "返回更新结果", required = false) @RequestParam(value = "returnRes", required = false,
            defaultValue = "true") boolean returnRes,
        @RequestBody() @JsonView(EditView.class) @Valid ValidatedList<BO> beans) {
        ResultDTO<Iterable<BO>> batchSaveCheck = batchSaveCheck(beans);
        if (Objects.nonNull(batchSaveCheck)) {
            return batchSaveCheck;
        }
        batchSetTagDomain(dataTag, domainLabel, beans);
        Pair<List<BO>, Pair<List<BO>, List<BO>>> updateAndInsertList = getUpdateAndInsertList(beans, userAccount);
        List<BO> preDatas = updateAndInsertList.getFirst();
        List<BO> updateList = updateAndInsertList.getSecond().getFirst();
        List<BO> insertList = updateAndInsertList.getSecond().getSecond();
        List<BO> result = newArrayList();
        if (isNotEmpty(insertList)) {
            return error("some id not find in DB", insertList);
        }
        if (isNotEmpty(updateList)) {
            result.addAll(innerSaveAll(updateList, returnRes, unsetFields));
        }
        if (returnRes) {
            return ok("update", result, preDatas);
        } else {
            return ok("update", null);
        }
    }


    /**
     * 全部BO—批量修改
     *
     * @param userAccount String 用户账号
     * @param beans       Iterable<BO> BO对象列表
     * @return ResultDTO
     */
    @Override
    @PutMapping(value = "/all", params = "updateNull=true")
    @Operation(summary = "批量修改，幂等操作，需要传入ID，如果没传的字段会设置为空")
    @JsonView(ShowView.class)
    @Validated(UpdateAction.class)
    default ResultDTO<Iterable<BO>> updateAllWithNull(
        @Parameter(description = "租户ID", example = "tenant-A") @RequestHeader(required = false, value =
            Constants.HEADER_TENANT_ID_KEY) String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon") @RequestHeader(required = false, value =
            Constants.HEADER_NAMESPACE_KEY) String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "数据标签") @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) List<String> dataTag,
        @Parameter(description = "领域标签") @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) String domainLabel,
        @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(required = false,
            value = "notNotify") boolean notNotify,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @Parameter(description = "返回更新结果", required = false) @RequestParam(value = "returnRes", required = false,
            defaultValue = "true") boolean returnRes,
        @RequestBody() @Valid ValidatedList<BO> beans) {
        ResultDTO<Iterable<BO>> batchSaveCheck = batchSaveCheck(beans);
        if (Objects.nonNull(batchSaveCheck)) {
            return batchSaveCheck;
        }
        batchSetTagDomain(dataTag, domainLabel, beans);
        // 更新空字段，不需要再查以前的数据。直接更新
        beans.forEach(bo -> bo.init(userAccount, StringUtils.isBlank(bo.getId())));
        mutationHolder().setMutation(
            MutationHolder.MutationVO.builder().data(newArrayList(beans)).preData(null).action(Action.UPDATE)
                                     .objClz(beans.get(0).getClass()).build());
        Iterable<BO> result = innerSaveAll(beans, returnRes, unsetFields);
        if (returnRes) {
            return ok("update", result);
        } else {
            return ok("update", null);
        }
    }


    /**
     * 更新BO
     *
     * @param userAccount String 用户账号
     * @param source      BO 更新BO对象
     * @return ResultDTO
     */
    @Override
    @PatchMapping
    @Operation(summary = "单独更新，非幂等操作，传入ID则修改，无ID则新建")
    @JsonView(ShowView.class)
    default ResultDTO<BO> save(
        @Parameter(description = "租户ID", example = "tenant-A") @RequestHeader(required = false, value =
            Constants.HEADER_TENANT_ID_KEY) String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon") @RequestHeader(required = false, value =
            Constants.HEADER_NAMESPACE_KEY) String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "数据标签") @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) List<String> dataTag,
        @Parameter(description = "领域标签") @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) String domainLabel,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name = "notNotify",
            required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @RequestBody() @JsonView(EditView.class) @Valid BO source) {
        if (Optional.ofNullable(source).isPresent() && !allFieldIsNULL(source)) {
            Pair<Boolean, String> checkResult = saveChecker().apply(newArrayList(source));
            if (!checkResult.getFirst()) {
                return ResultDTO.error(checkResult.getSecond());
            }
            setTagDomain(dataTag, domainLabel, source);
            if (StringUtils.isBlank(source.getId()) || !repository().existsById((ID) source.getId())) {
                source.init(userAccount, true);
                BO result = repository().insert(source);
                return ok("new", repository().findById((ID) result.getId()).get());
            }
            BO preData = repository().findById((ID) source.getId()).get();
            source.init(userAccount, false);
            BO result = repository().save(source, unsetFields);
            return ok("save", result, preData);
        }
        return error("required request body is missing");
    }

    /**
     * 更新全部BO—批量
     *
     * @param userAccount String 用户账号
     * @param beans       Iterable<BO> 更新BO对象
     * @return ResultDTO
     */
    @Override
    @PatchMapping("/all")
    @Operation(summary = "批量更新，非幂等操作，传入ID则修改，无ID则新建")
    @JsonView(ShowView.class)
    default ResultDTO<Iterable<BO>> saveAll(
        @Parameter(description = "租户ID", example = "tenant-A") @RequestHeader(required = false, value =
            Constants.HEADER_TENANT_ID_KEY) String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon") @RequestHeader(required = false, value =
            Constants.HEADER_NAMESPACE_KEY) String namespace,
        @Parameter(description = "请求用户账号", example = "admin") @RequestHeader(required = true, value =
            Constants.HEADER_ACCOUNT_KEY) @NotBlank String userAccount,
        @Parameter(description = "数据标签") @RequestHeader(required = false, value = Constants.HEADER_DATA_TAG_KEY) List<String> dataTag,
        @Parameter(description = "领域标签") @RequestHeader(required = false, value = Constants.HEADER_DOMAIN_LABEL_KEY) String domainLabel,
        @Parameter(description = "记录是否必须存在", required = false) @RequestParam(value = "prevExist", required = false) boolean prevExist,
        @Parameter(description = "是否不发送通知，true：不发送，false：发送", example = "false") @RequestParam(name = "notNotify",
            required = false) boolean notNotify,
        @Parameter(description = "是否发送变更通知给自己，true: 不发送，false: 发送", example = "false") @RequestParam(name =
            "notNotifyToSelf", required = false) boolean notNotifyToSelf,
        @Parameter(description = "需要删除的字段", example = "['field1', 'field2']") @RequestParam(name = "unsetFields",
            required = false) List<String> unsetFields,
        @RequestBody() @JsonView(EditView.class) @Valid ValidatedList<BO> beans) {
        ResultDTO<Iterable<BO>> batchSaveCheck = batchSaveCheck(beans);
        if (Objects.nonNull(batchSaveCheck)) {
            return batchSaveCheck;
        }
        batchSetTagDomain(dataTag, domainLabel, beans);
        Pair<List<BO>, Pair<List<BO>, List<BO>>> updateAndInsertList = getUpdateAndInsertList(beans, userAccount);
        List<BO> preDatas = updateAndInsertList.getFirst();
        List<BO> updateList = updateAndInsertList.getSecond().getFirst();
        List<BO> insertList = updateAndInsertList.getSecond().getSecond();
        List<BO> searchList = newArrayList();
        if (isNotEmpty(updateList)) {
            searchList.addAll(innerSaveAll(updateList, true, unsetFields));
        }
        if (isNotEmpty(insertList)) {
            searchList.addAll(repository().insert(insertList));
        }
        return ok("save", searchList, preDatas);
    }

    /**
     * 批量保存
     *
     * @param beans       BO对象列表
     * @param returnNew   是否返回新数据
     * @param unsetFields 需要删除的字段
     * @return
     */
    default List<BO> innerSaveAll(List<BO> beans, boolean returnNew, List<String> unsetFields) {
        List<BO> result = repository().saveAll(beans, unsetFields);
        if (!returnNew) {
            return result;
        }
        List<String> jobIdList = beans.stream().map(AbstractBaseBO::getId).collect(toList());
        return repository().findAllById((Iterable<ID>) jobIdList);
    }

    /**
     * 获取更新和插入列表,<preDatas,<<updateList><insertList>>>
     *
     * @param beans       BO对象列表
     * @param userAccount 用户账号
     * @return
     */
    private Pair<List<BO>, Pair<List<BO>, List<BO>>> getUpdateAndInsertList(List<BO> beans, String userAccount) {
        List<String> jobIdList = beans.stream().map(AbstractBaseBO::getId).collect(toList());
        Iterable<BO> existList = repository().findAllById((Iterable<ID>) jobIdList);
        List<BO> preDatas = newArrayList();
        List<BO> updateList = newArrayList();
        List<BO> insertList = newArrayList();
        Map<String, BO> existIndex = newArrayList(existList).stream().collect(toMap(bo -> bo.getId(), bo -> bo));
        List<Pair<Optional<BO>, Optional<BO>>> list = beans.stream().map(
            bo -> Pair.of(ofNullable(bo), ofNullable(existIndex.get(bo.getId())))).collect(toList());
        list.forEach(pair -> {
            BO source = pair.getFirst().orElse(null);
            BO target = pair.getSecond().orElse(null);
            if (nonNull(target)) {
                preDatas.add(target);
                source.init(userAccount, false);
                updateList.add(source);
            } else {
                source.init(userAccount, true);
                insertList.add(source);
            }
        });
        return Pair.of(preDatas, Pair.of(updateList, insertList));
    }

    /**
     * 保存数据校验
     *
     * @param beans BO对象列表
     * @return
     */
    private ResultDTO<Iterable<BO>> batchSaveCheck(ValidatedList<BO> beans) {
        if (isEmpty(beans)) {
            return ok("add data num is: 0!");
        }
        Pair<Boolean, String> checkResult = saveChecker().apply(beans);
        if (!checkResult.getFirst()) {
            return ResultDTO.error(checkResult.getSecond());
        }
        // 检查通过
        return null;
    }

    /**
     * 判断对象属性是否全为空
     *
     * @param o 对象
     * @return booleann
     */
    public static boolean allFieldIsNULL(Object o) {
        try {
            Class<?> clazz = o.getClass();
            Class<?> superclass = clazz.getSuperclass();
            List<Field> fieldList = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
            fieldList.addAll(Arrays.asList(superclass.getDeclaredFields()));
            for (Field field : fieldList) {
                field.setAccessible(true);
                Object object = field.get(o);
                if (object instanceof CharSequence) {
                    return false;
                } else {
                    if (null != object) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("判断对象属性为空异常");
        }
        return true;
    }

}
