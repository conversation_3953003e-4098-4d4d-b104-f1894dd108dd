{{- $SoftDelete := false -}}
{{- range $_, $alias := .Aliases }}
    {{- if hasSuffix "@SoftDelete" $alias.Name }}
        {{- $SoftDelete = true -}}
        {{- break -}}
    {{- end -}}
{{- end -}}
package {{ ToPackagePath .Name }}.repository;

import {{ ToPackagePath .Name }}.{{ PublicSimpleName .Name }};

import java.lang.String;
import java.util.List;
import java.util.Optional;

import com.polarizon.gendo.common.api.CustomMongoRepositoryInterface;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
{{- if $SoftDelete }}
import com.polarizon.gendo.common.bo.AbstractBaseBO.StatusFlag;
{{- end -}} {{/* end SoftDelete */}}

@Repository("{{ RepositoryName .Name }}")
public interface {{ RepositoryName .Name }} extends CustomMongoRepositoryInterface<{{ PublicSimpleName .Name }}, String> {
{{- if $SoftDelete }}
  Optional<{{ PublicSimpleName .Name }}> findOneByIdAndStatusFlag(String id, StatusFlag statusFlag);

  List<{{ PublicSimpleName .Name }}> findAllByStatusFlag(StatusFlag statusFlag);

  List<{{ PublicSimpleName .Name }}> findByIdInAndStatusFlag(Iterable<String> ids, StatusFlag statusFlag);
{{- end -}} {{/* end SoftDelete */}}

}
