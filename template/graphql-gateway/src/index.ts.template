#!/usr/bin/env node

import cors from 'cors';
import express from 'express';
import { createServer } from 'http';
import { ApolloServer } from '@apollo/server';
import { WebSocketServer } from 'ws';
import { useServer } from 'graphql-ws/lib/use/ws';
import bodyParser from 'body-parser';
import { expressMiddleware } from '@apollo/server/express4';
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer';
import { ApolloServerPluginLandingPageLocalDefault } from '@apollo/server/plugin/landingPage/default';
import { makeExecutableSchema } from '@graphql-tools/schema';
import typeDefs from './core/TypeDefs.js';
import { resolvers } from './core/Resolvers.js';
import { OpaClientAPI } from './opa/OpaClient.js';
import { TenantAPI } from './custom/TenantApi.js';
import { OrganizationAPI } from './custom/OrganizationApi.js';
import { UserAPI } from './custom/UserApi.js';
import { DeviceAPI } from './custom/DeviceApi.js';
import logger from './utils/logger.js'
import { mutationHook } from './core/Subscription.js'
import {register} from './msgproxy/index.js'
{{ range $i, $node := . }}
    {{- if and (eq (typeOf $node) "*schema.RecordDefinition") ( or ( eq $node.LogicalType "controller" ) ( eq $node.LogicalType "treeController" ) ) }}
        {{- println "import {" (cat (ObjectName $node.Name) "API" | nospace ) "} from" (cat "'./api/" (PublicSimpleName $node.Name) ".js';" | nospace) -}}
    {{- end -}}
{{- end }}

interface ContextValue {
  dataSources: {
    opaClientAPI: OpaClientAPI;
    tenantAPI: TenantAPI;
    organizationAPI: OrganizationAPI;
    userAPI: UserAPI;
    deviceAPI: DeviceAPI;
{{- range $i, $node := . }}
    {{- if and (eq (typeOf $node) "*schema.RecordDefinition") (eq $node.LogicalType "controller") }}
    {{ $node.Name | ObjectName | lower }}API: {{ ObjectName $node.Name }}API;
    {{- end -}}
{{- end }}
  };
}

// Set up the port on which the server will run.
export const PORT = process.env.PORT || '{{ GetConfig "project.port" }}';

// Set up the port on which the server will run.
const GRAPHQL_PATH = '';

// Create schema, which will be used separately by ApolloServer and
// the WebSocket server.
const schema = makeExecutableSchema({ typeDefs, resolvers });

// Create an Express app and HTTP server; we will attach the WebSocket
// server and the ApolloServer to this HTTP server.
const app = express();
app.use(express.json({ limit: '15mb' }))
app.use(express.urlencoded({ extended: true }))
const httpServer = createServer(app);

// Set up WebSocket server.
const wsServer = new WebSocketServer({
  server: httpServer,
  path: GRAPHQL_PATH,
});
const serverCleanup = useServer({
  schema,
}, wsServer);


// Set up ApolloServer.
const server = new ApolloServer<ContextValue>({
  schema,
  plugins: [
    // Proper shutdown for the HTTP server.
    ApolloServerPluginDrainHttpServer({ httpServer }),
    ApolloServerPluginLandingPageLocalDefault({
      embed: {
        endpointIsEditable: true,
      },
    }),
    // Proper shutdown for the WebSocket server.
    {
      async serverWillStart() {
        return {
          async drainServer() {
            await serverCleanup.dispose();
          },
        };
      },
    },
  ],
});

await server.start();

//先处理自定义的请求
app.post('/mutation', bodyParser.json({ limit: '10mb' }), (req, res) => {
  mutationHook(req.body);
  res.json({ status: '0', msg: "success", data: null });
});

//其余请求路由到apollo server
app.use(cors<cors.CorsRequest>(), bodyParser.json(), expressMiddleware(server, {
  context: async ({ req, res }) => {
    const { cache } = server;
    return {
      request: req,
      response: res,
      dataSources: {
        opaClientAPI: new OpaClientAPI({ cache }, req.headers),
        tenantAPI: new TenantAPI({ cache }, req.headers),
        organizationAPI: new OrganizationAPI({ cache }, req.headers),
        userAPI: new UserAPI({ cache }, req.headers),
        deviceAPI: new DeviceAPI({ cache }, req.headers),
{{- range $i, $node := . }}
    {{- if and (eq (typeOf $node) "*schema.RecordDefinition") ( or ( eq $node.LogicalType "controller" ) ( eq $node.LogicalType "treeController" ) ) }}
        {{ $node.Name | ObjectName | lower }}API: new {{ ObjectName $node.Name }}API({ cache }, req.headers),
    {{- end -}}
{{- end }}
      },
    };
  },
}));

// Now that our HTTP server is fully set up, actually listen.
httpServer.listen(parseInt(PORT), "0.0.0.0", () => {
  logger.info(`🚀 Query endpoint ready at http://localhost:${PORT}`);
  logger.info(`🚀 Subscription endpoint ready at ws://localhost:${PORT}${GRAPHQL_PATH}`);
});

//定时注册消息代理
register();