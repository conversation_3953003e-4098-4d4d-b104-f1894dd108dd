{{- define "FormTypeName" -}}
    {{- if typeIs "*schema.ArrayField" .Type -}}
    {{ FormName .Type.ItemType.Name }}
    {{- else -}}
    {{ FormName .Type.Name }}
    {{- end -}}
{{- end -}}
{{- define "ViewFormName" -}}
    {{- if typeIs "*schema.ArrayField" .Type -}}
    {{ ViewFormName .Type.ItemType.Name }}
    {{- else -}}
    {{ ViewFormName .Type.Name }}
    {{- end -}}
{{- end -}}


{{/* 如果avro定义有List则添加import语句 */}}
{{- $myList := list "null" -}}
{{ range $i, $field := . }}
    {{- if or (eq (typeOf $field) "*schema.RecordDefinition") (eq (typeOf $field) "*schema.EnumDefinition") -}}
		{{ $importName := (ObjectName $field.Name | lower) }}
        {{- if not (has $importName $myList) -}}
        {{- if and (eq (typeOf $field) "*schema.RecordDefinition") (eq $field.LogicalType "controller") }}
            {{- println "import {" $importName ", " (cat $importName "Query, " $importName "Mutation } from '../schema/" (PublicSimpleName $field.Name) ".js';" | nospace) -}}
        {{- else if and (eq (typeOf $field) "*schema.RecordDefinition") (eq $field.LogicalType "treeController") }}
            {{- println "import {" $importName ", " (cat $importName "Query, " $importName "Mutation } from '../schema/" (PublicSimpleName $field.Name) ".js';" | nospace) -}}
        {{ else }}
            {{- println "import {" $importName "} from " (cat "'../schema/" (PublicSimpleName $field.Name) ".js';" | nospace) -}}
        {{ end }}
		{{- $myList = append $myList $importName -}}
		{{- end -}}
	{{- end -}}
{{- end }}

//对象名称可选值
export const objectNames = [
    'com.polarizon.gendo.custom.message.notify',
    {{ range $i, $node := . -}}
    {{- if eq (typeOf $node) "*schema.RecordDefinition" -}}
    '{{ $node.Name }}',
    {{ end -}}
    {{- end -}}
  ];

export const schemas = {{ b64dec "YA==" }}#gateway
# Comments in GraphQL strings (such as this one) start with the hash (#) symbol.

    scalar JSON
    scalar Long

    """ 通用返回类型 """
    scalar AnyValue

    """ 对象名称"""
    scalar ObjectName

    """ 使能, 软删除标志: LIVE (存活),DEAD (死亡) """
    enum StatusFlag {
        LIVE    # "存活"
        DEAD    # "死亡"
    }

    """ 分页参数 """
    input Pageable {
        page: Int!
        size: Int!
        sort: [String]
    }

	""" 统计计数类型 """	
	type CountItem {
		value: String
		count: Int
	}

    """ 视图统计计数类型 """	
	type ViewCountItem {
		field: String
		countItem: [CountItem]
	}

	""" 分组统计数据类型 """
   	type GroupCountItem {
		groupBy: String
        groupByBO: UserVO
		countBy: [CountItem]
	}

	""" 分组统计数据类型:分页 """
	type GroupCountPage {
		""" 是否有前一页？ """
		hasNext: Boolean
		""" 是否有后一页？ """
		hasPrevious: Boolean
		""" 当前页序号 """
		pageNumber: Int
		""" 页大小 """
		pageSize: Int
		""" 总页数 """
		pages: Int
		""" 总数据量 """
		total: Int
		""" 返回结果列表 """
		result: [GroupCountItem]
	}

	""" 统计计数类型 """	
	type CheckResult {
        """ 返回校验结果: true-通过, false-失败 """
		allow: Boolean
        """ 返回异常错误或失败原因 """
		error: [String]
	}

    """ 订阅对象 """
    input SubscribeObject{
        objectName: ObjectName!
        actionEnum: MutationActionEnum
        subscribeFields: [String],
        subscribeCond: String
    }
    
    """ 订阅表单 """
    input MutationSubscribeForm{
        globalActionEnum: MutationActionEnum
        subscribeObjects: [SubscribeObject]
    }
    
    """ 订阅动作 """
    enum MutationActionEnum{
        ADD
        DELETE
        UPDATE
    }

    """ 订阅返回 """
    type MutationSubscribeVO{
        action: MutationActionEnum
        objectName: ObjectName
        mutationData : String
    }


# The "Query" type is special: it lists all of the available queries that
# clients can execute, along with the return type for each. In this
# case, the "books" query returns an array of zero or more Books (defined above).
type Query {
{{ range $i, $node := . -}}
{{- if and (eq (typeOf $node) "*schema.RecordDefinition") (eq $node.LogicalType "controller") -}}
    ${ {{ (ObjectName $node.Name | lower) }}Query }
{{- else if and (eq (typeOf $node) "*schema.RecordDefinition") (eq $node.LogicalType "treeController") }}
    ${ {{ (ObjectName $node.Name | lower) }}Query }
{{ end -}}
{{- end }}
}

type Mutation {
{{ range $i, $node := . -}}
{{- if and (eq (typeOf $node) "*schema.RecordDefinition") (eq $node.LogicalType "controller") -}}
    ${ {{ (ObjectName $node.Name | lower) }}Mutation }
{{- else if and (eq (typeOf $node) "*schema.RecordDefinition") (eq $node.LogicalType "treeController") }}
    ${ {{ (ObjectName $node.Name | lower) }}Mutation }
{{ end -}}
{{- end -}}
}

type Subscription {
    mutation_subscribe(bean: MutationSubscribeForm!)  : MutationSubscribeVO
}
{{ b64dec "YA==" }};

export default [
{{- $iLen := 0 -}}
{{- range $i, $field := . -}}
    {{- if or (eq (typeOf $field) "*schema.RecordDefinition") (eq (typeOf $field) "*schema.EnumDefinition") -}}
        {{- if eq $iLen 0 }}schemas, {{else}}, {{ end }}
        {{- ObjectName $field.Name | lower }}
        {{ $iLen = add1 $iLen -}}
    {{- end -}}
{{- end -}}
]

