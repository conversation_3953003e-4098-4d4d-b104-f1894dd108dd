#!/bin/sh

# 兼容 macOS 和 Linux 的 sed -i 参数
sed_inplace() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' "$@"
    else
        sed -i "$@"
    fi
}

update_helm_files() {
    mkdir -p "${targetBaseDir}/helm"
    
    sed_inplace "s/^version: .*/version: ${version}/g" "${targetBaseDir}/helm/Chart.yaml"
    sed_inplace "s/^appVersion: .*/appVersion: ${version}/g" "${targetBaseDir}/helm/Chart.yaml"
    sed_inplace "s/^  tag: .*/  tag: ${version}/g" "${targetBaseDir}/helm/values.yaml"
}

update_package_version() {
    sed_inplace "s/\"version\": \".*\"/\"version\": \"${version}\"/" "${targetBaseDir}/package.json"
}

rm_script_files() {
    rm -rv "${targetBaseDir}/scripts"
}

main() {
    update_helm_files 
    update_package_version
    rm_script_files
}

main "$@"