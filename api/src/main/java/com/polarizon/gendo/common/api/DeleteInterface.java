package com.polarizon.gendo.common.api;

import com.fasterxml.jackson.annotation.JsonView;
import com.polarizon.gendo.common.bo.AbstractBaseBO;
import com.polarizon.gendo.common.bo.AbstractBaseBO.EditView;
import com.polarizon.gendo.common.dto.ResultDTO;
import com.polarizon.gendo.common.utils.Constants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface DeleteInterface<BO extends AbstractBaseBO, ID> {
    /**
     * 根据ID删除BO
     *
     * @param id String 待删除ID
     * @return
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除")
    @JsonView(AbstractBaseBO.EditView.class)
    ResultDTO<BO> delete(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(required=true, description="删除对象ID", example="admin")
        @PathVariable(value = "id")
        ID id
    );

    /**
     * 删除BO—批量
     *
     * @param ids String[] 待删除ID
     * @return
     */
    @DeleteMapping("/all")
    @Operation(summary = "批量删除")
    @JsonView(EditView.class)
    ResultDTO<List<BO>> deleteAll(
        @Parameter(description = "租户ID", example = "tenant-A")
        @RequestHeader(required = false, value = Constants.HEADER_TENANT_ID_KEY)
        String tenantID,
        @Parameter(description = "命名空间", example = "com.polarizon")
        @RequestHeader(required = false, value = Constants.HEADER_NAMESPACE_KEY)
        String namespace,
        @Parameter(required=true, description="删除对象ID列表")
        @RequestParam(value = "ids")
        List<ID> ids
    );

}
