{"type": "record", "name": "ModelConfig", "namespace": "com.polarizon.rag.chat", "doc": "推理模型信息", "fields": [{"name": "code", "type": "string", "doc": "推理模型唯一标识", "default": "\"QWEN_2_5_14_B_INSTRUCT_GPTQ_INT_4\""}, {"name": "modelProviderName", "type": "string", "doc": "推理提供方名称。目前支持：qwen2.5-14b-instruct-gptq-int4 和 openai-api ; 用于调用 langchain-chatchat 的 中的modelName api 。", "default": "\"qwen2.5-14b-instruct-gptq-int4\""}, {"name": "apiParamModelName", "type": "string", "doc": "用于api调用，chatCompletion的api 中的 model_name 参数；例如使用'openai-api'的api时，值为gpt-3.5-turbo;推理模型名称", "default": "\"qwen2.5-14b-instruct-gptq-int4\""}, {"name": "displayModelName", "type": "string", "doc": "推理模型名称；前端展示模型名称", "default": "\"qwen2.5-14b\""}, {"name": "maxToken", "type": "int", "doc": "最大token", "default": 8192}]}