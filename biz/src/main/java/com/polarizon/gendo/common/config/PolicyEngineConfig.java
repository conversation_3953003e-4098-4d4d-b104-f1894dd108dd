package com.polarizon.gendo.common.config;

import com.bisnode.opa.client.OpaClient;
import com.bisnode.opa.client.query.OpaQueryApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PolicyEngineConfig {
    @Value("${gendo3.policy-engine.url}")
    private String url;

    @Bean
    public OpaQueryApi newClient() {
        return OpaClient.builder().opaConfiguration(url).build();
    }
}
