apiVersion: v1
data:
    BOOTSTRAP_SERVER: "kafka-0.kafka-headless.{{ .Release.Namespace }}.svc.cluster.local:9092,kafka-1.kafka-headless.{{ .Release.Namespace }}.svc.cluster.local:9092,kafka-2.kafka-headless.{{ .Release.Namespace }}.svc.cluster.local:9092"
    SPRING_DATA_MONGODB_URI: "mongodb://root:<EMAIL>:27017,mongodb-1.mongodb-headless.middleware.svc.cluster.local:27017"
kind: ConfigMap
metadata:
    name: global
