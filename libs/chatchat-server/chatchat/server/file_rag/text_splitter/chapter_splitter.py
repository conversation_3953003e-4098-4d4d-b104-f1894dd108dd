import re
import os
import copy
from dataclasses import dataclass
from typing import List, Iterable
import numpy as np
from io import BytesIO
from langchain.text_splitter import CharacterTextSplitter
from langchain_core.documents import Document
from langid import classify

class ChapterSplitter(CharacterTextSplitter):

    def __init__(self, type_GB=True, **kwargs):
        super().__init__(**kwargs)
        self._set_chapter_format(type_GB)
        self.chunk_size=256

    def _set_chapter_format(self, type_GB):
        self.MAX_LVL = 12
        if type_GB:
            self.proj_patt = [
                (r"[0-9 ]{1,2}[\. ]{0,2}(项目|工程)", 1),
                (r"[0-9 ]{1,2}[\. ]{0,2}(特殊|特别)?事项说明", 1),
                (r"[0-9 ]{0,2}[\. ]{0,2}附录", 1),

                # (r"[0-9 ]{1,2}[(\u4e00-\u9fa5)(，、\.,\-)]{1,15}$", 1),
                (r"前言$|(附录[. A-Za-z]?)", 1), # 实验性功能
                (r"[0-9]{1,2}+\.[0-9 ]{1,2}+[(\u4e00-\u9fa5)(，（）、\(\),\-)]{1,15}$", 2),
            ]
        else:
            self.proj_patt = [
                (r"[0-9 ]{1,2}[\. ]{0,2}项目", 1)
                # (r"2[\. ]{0,2}项目的必要性", 1)
                # (r"3[\. ]{0,2}项目技术方案", 1)
            ]            
        self.lines = []

    def _garbage(self, txt):
        patt = [
            r"(在此保证|不得以任何形式翻版|请勿传阅|仅供内部使用|未经事先书面授权)",
            r"(版权(归本公司)*所有|免责声明|保留一切权力|承担全部责任|特别声明|报告中涉及)",
            r"(不承担任何责任|投资者的通知事项：|任何机构和个人|本报告仅为|不构成投资)",
            r"(不构成对任何个人或机构投资建议|联系其所在国家|本报告由从事证券交易)",
            r"(本研究报告由|「认可投资者」|所有研究报告均以|请发邮件至)",
            r"(本报告仅供|市场有风险，投资需谨慎|本报告中提及的)",
            r"(本报告反映|此信息仅供|证券分析师承诺|具备证券投资咨询业务资格)",
            r"^(时间|签字|签章)[:：]",
            r"(参考文献|目录索引|图表索引)",
            r"[ ]*年[ ]+月[ ]+日",
            r"^(中国证券业协会|[0-9]+年[0-9]+月[0-9]+日)$",
            # r"\.{10,}",
            r"(———————END|帮我转发|欢迎收藏|快来关注我吧)"
        ]
        return any([re.search(p, txt) for p in patt])

    def _proj_match(self, line):
        for p, j in self.proj_patt:
            if re.match(p, line):
                return j
        return

    def _does_proj_match(self):
        mat = [None for _ in range(len(self.lines))]
        for i in range(len(self.lines)):
            mat[i] = self._proj_match(self.lines[i])
            # if mat[i] is not None and mat[i]< 8:
            #     print(f"\tLEVEL {mat[i]} : {self.lines[i]}")
        return mat

    def update_titles(self):
        primary_title = []
        secondary_title = []
        pop_list = []
        for n, line in enumerate(self.lines):
            prim = re.match(r"([0-9]{1,2})[\. ．]{0,2}([^\.0-9 ][^\.]+)[ ]?[\.．]+[ ]?\d+$", line) #  r"([0-9]{1,2})[\. ．]{0,2}([\u4e00-\u9fa5A-z]+)[ ]?[\.．]+[ ]?\d+$"
            if prim is not None:
                primary_title.append((prim.groups()[0], prim.groups()[1]))
                pop_list.append(n)
                continue
            scnd = re.match(r"([0-9]{1,2}[\.．][0-9]{1,2})[\. ．]{0,2}([^\.0-9 ][^\.]+)[ ]?[\.．]+[ ]?\d+$", line)
            if scnd is not None:
                secondary_title.append((scnd.groups()[0], scnd.groups()[1]))
                pop_list.append(n)
                continue
            menu = re.search(r"[\.]{8,}", line)
            if menu is not None:
                pop_list.append(n)
        print(f"Primary Title:\n{primary_title}")
        print(f"Secondary Title:\n{secondary_title}")
        self.proj_patt = []
        primary_format = [prim[0]+"[\. ．]{0,2}"+prim[1].strip() for prim in primary_title]
        secondary_format = [scnd[0]+"[\. ．]{0,2}"+scnd[1].strip() for scnd in secondary_title]
        self.proj_patt += [(p, 1) for p in primary_format]
        self.proj_patt += [(p, 2) for p in secondary_format]
        pop_list.sort(reverse=True)
        for i in pop_list:
            self.lines.pop(i) 
            
        
    def _merge(self):
        # merge continuous same level text
        lines = [self.lines[0]] if self.lines else []
        for i in range(1, len(self.lines)):
            # if self.mat[i-1] is not None and self.mat[i-1] < 5: print(f"\tLEVEL {self.mat[i-1]} : {self.lines[i-1]}")
            if self.mat[i] == self.mat[i - 1] \
                and len(lines[-1]) < self.chunk_size \
                and len(self.lines[i]) < self.chunk_size:
                # and len(lines[-1]) + len(self.lines[i]) < 384:
                if self.mat[i-1] is None or self.mat[i] > 5:
                        lines[-1] += "\n" + self.lines[i]
                        continue
                else:
                    # print(f"Skipping merging of {lines[-1]} and {self.lines[i]} because title level too high: {self.mat[i]}.")
                    pass
            lines.append(self.lines[i])
        self.lines = lines
        self.mat = self._does_proj_match()
        return self.mat

    def split_text_metadata(self, text):
        """
        分割文本元数据。

        此方法对给定的文本进行处理，首先通过一系列规则整理文本格式，然后识别章节标题并根据标题层级
        将文本分割成多个元数据段。最后，生成包含所有分割后文本的列表以及对应的元数据列表。

        :param text: 需要处理的原始文本。
        :return: 元数据和文本内容的列表，每个元数据代表一个章节或段落。
        """
        # meta_datas = []
        if text:
            # 整理格式，去除全角空格等有影响的字符
            self.lines = [l.strip().replace(u'\u3000', u' ')
                          .replace(u'\xa0', u'')
                          for l in re.split(r"[\r\n]", text)]
            self.lines = [l for l in self.lines if not self._garbage(l)]
            self.lines = [l for l in self.lines if l]
        # with open("text_splitter/lines.json", 'w') as f:
        #     import json
        #     json.dump(self.lines, f, ensure_ascii=False)
        # 识别目录，更新章节标题
        self.update_titles()
        
        # 查找章节标题，然后对章节段落进行合并
        self.mat = self._does_proj_match()
        mat = self._merge()

        tree = []
        for i in range(len(self.lines)):
            tree.append({"proj": mat[i],"children": [],"read": False})
        # find all children
        for i in range(len(self.lines) - 1):
            if tree[i]["proj"] is None: # 非标题的可以排除
                continue
            ed = i + 1
            while ed < len(tree) and (tree[ed]["proj"] is None or
                                      tree[ed]["proj"] > tree[i]["proj"]): # 直到找到下一个同级或上级标题
                ed += 1

            nxt = tree[i]["proj"] + 1
            st = set([p["proj"] for p in tree[i + 1: ed] if p["proj"]]) # 中间的全部内容，只要具有标题
            while nxt not in st: # 找到第二高的标题层级
                nxt += 1
                if nxt > self.MAX_LVL:
                    break
            if nxt <= self.MAX_LVL:
                for j in range(i + 1, ed):    # None, 也就是正文被看做children
                    if tree[j]["proj"] is not None:
                        break # 找到满足标题条件的文本，可能是子章节
                    tree[i]["children"].append(j) # 标题后面可能有补充文本，而补充文本不满足标题条件
                for j in range(i + 1, ed): # 次级标题nxt
                    if tree[j]["proj"] != nxt:
                        continue # 只添加直接子章节，无视子章节的子章节
                    tree[i]["children"].append(j)
            else: # 没有找到第二高的标题层级，意味着单标题成一章，因此后面的None全部是children
                for j in range(i + 1, ed):
                    tree[i]["children"].append(j)

        # get DFS combinations, find all the paths to leaf
        paths = []

        def dfs(i, path):
            nonlocal tree, paths
            path.append(i)
            tree[i]["read"] = True
            if len(self.lines[i]) > self.chunk_size: # 对应文本段超长时，直接返回
                paths.append(path)
                return
            if not tree[i]["children"]: # 到达叶节点，且段落有实际意义（文本长度或子章节）
                if len(path) > 1 or len(self.lines[i]) >= 32:
                    paths.append(path)
                return
            for j in tree[i]["children"]:
                dfs(j, copy.deepcopy(path))

        for i, t in enumerate(tree):
            if t["read"]:
                continue
            dfs(i, [])
            
        # concat txt on the path for all paths
        res = []
        lines = np.array(self.lines)
        for p in paths:
            if len(p) < 2:
                tree[p[0]]["read"] = False
                continue
            metadata = {}
            lvls = [tree[p_n]['proj'] if tree[p_n]['proj'] is not None else 0 for p_n in p] 
            lvl = np.argmax(lvls) # 寻找parent前的level
            metadata['chapterName'] = lines[p[lvl]][:20]
            metadata['chapterID'] = p[lvl] # 章节ID
            metadata['parentChapterName'] = '--'.join([l[:20] for l in lines[p[:lvl]]]) if lvl != 0 else lines[p[lvl]][:20]
            metadata['parentChapterID'] = str(p[max(0,lvl-1)]) # 防止索引越界 -1
            metadata['fileName'] = self.filename
            # metadata['test_id'] = p # 仅测试参考用
            txt = " ".join(lines[p[lvl:]])
            doc = Document(page_content=txt, metadata=metadata)
            res.append(doc)
        # concat continuous orphans 将连续的文档碎片进行合并
        assert len(tree) == len(lines)
        ii = 0
        while ii < len(tree):
            if tree[ii]["read"]:
                ii += 1
                continue
            txt = lines[ii]
            e = ii + 1
            while e < len(tree) and not tree[e]["read"] and len(txt) < self.chunk_size:
                txt += "\n" + lines[e]
                e += 1
            metadata = {}
            metadata['chapterName'] = txt[:20]
            metadata['chapterID'] = ii 
            metadata['parentChapterName'] = self.filename
            metadata['parentChapterID'] = str(-1)
            metadata['fileName'] = self.filename
            doc = Document(page_content=txt, metadata=metadata)
            res.append(doc)
            ii = e

        # if the node has not been read, find its daddy
        def find_daddy(st):
            nonlocal lines, tree
            proj = tree[st]["proj"]
            if len(self.lines[st]) > 2*self.chunk_size:
                return [st]
            if proj is None:
                proj = self.MAX_LVL + 1
            for i in range(st - 1, -1, -1):
                if tree[i]["proj"] and tree[i]["proj"] < proj:
                    a = [st] + find_daddy(i)
                    return a
            return []
        
        return res
    
    # 去除metadata的split_text
    def split_text(self, text) -> List[str]:
        meta_data, res = self.split_text_metadata(text)
        return res

    # 重写split_documents来实现metadata的加入
    def split_documents(self, documents: Iterable[Document]) -> List[Document]:
        texts, metadatas = [],[]
        for doc in documents:
            # print(f"M{doc.metadata}")
            texts.append(doc.page_content)
            metadatas.append(doc.metadata)  
        documents = []
        for i, text in enumerate(texts):
            lang_detection = classify(text)
            # print(lang_detection)
            # if lang_detection[0] == 'en':
            #     self.chunk_size = 81920
            self.chunk_size = 81920
            self.filename = metadatas[i].get('filename', 'default')
            splitted_docs = self.split_text_metadata(text)
            splitted_docs = sorted(splitted_docs, key=lambda x: x.metadata['chapterID'])
            for n, doc in enumerate(splitted_docs):
                # doc.metadata['chapterID'] = str(n)
                for k,v in metadatas[i].items():
                    doc.metadata[k] = v
                documents.append(doc)
        return documents

def main():
    # 测试分割效果
    import json
    import pickle
    import time
    from document_loaders.pdfplumberloader import PDFPlumberLoader
    t0 = time.time()
    loader = PDFPlumberLoader(file_path="document_loaders/test6.pdf")
    docs = loader.load()
    # with open("document_loaders/test.pkl", "wb") as f:
    #     pickle.dump(docs, f)
    # with open("document_loaders/test.pkl", "rb") as f:
    #     docs = pickle.load(f)
    # print(docs)
    text_splitter = ChapterSplitter()
    splitted_docs = text_splitter.split_documents(docs)
    page_contents = [d.metadata for d in splitted_docs]
    for n, page in enumerate(page_contents):
        page['content'] = splitted_docs[n].page_content
    with open("text_splitter/test.json", "w") as f:
        json.dump(page_contents, f, ensure_ascii=False)

    print(f"用时{time.time()-t0:.2f}s")
    pass

if __name__ == "__main__":
    main()