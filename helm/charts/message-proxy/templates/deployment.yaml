apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  labels:
    app: {{ .Chart.Name }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}
    spec:
      {{- if or .Values.nodeSelector .Values.global.nodeSelector }}
      nodeSelector: 
        {{- if .Values.nodeSelector }}
          {{- toYaml .Values.nodeSelector | nindent 8 }}
        {{- else }}
          {{- toYaml .Values.global.nodeSelector | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- if or .Values.tolerations .Values.global.tolerations }}
      tolerations:
        {{- if .Values.tolerations }}
          {{- toYaml .Values.tolerations | nindent 8 }}
        {{- else }}
          {{- toYaml .Values.global.tolerations | nindent 8 }}
        {{- end }}
      {{- end }}
      containers:
        - image: {{ template  "imageRepository" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.port }}
              protocol: TCP
          name: {{ .Chart.Name }}
          env:
            - name: "TZ"
              value: Asia/Shanghai
            - name: KUBE_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: "SERVER_IP"
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          envFrom:
            - configMapRef:
                name: {{ .Chart.Name }}
            - configMapRef:
                name: global
          volumeMounts:
            - mountPath: /etc/localtime
              name: host-time
            #- configMapRef:
            #    name: s3cfg
          livenessProbe:
            tcpSocket:
              port: {{ .Values.service.port }}
            initialDelaySeconds: 15
            periodSeconds: 20
            failureThreshold: 3
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            tcpSocket:
              port: {{ .Values.service.port }}
            initialDelaySeconds: 15
            periodSeconds: 20
            failureThreshold: 3
            successThreshold: 1
            timeoutSeconds: 1
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime