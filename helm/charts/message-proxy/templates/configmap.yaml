apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ .Release.Namespace }}
data:
  {{- if hasKey .Values "configMap" }}
  {{- range $key, $value := mergeOverwrite .Values.global.configMap .Values.configMap }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- else }}
  {{- range $key, $value := .Values.global.configMap }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
    MQ_ACTIVE_NAME: "kafka"
    QUARKUS_MONGODB_DATABASE: {{ .Chart.Name }}-{{ .Release.Namespace }}-db
