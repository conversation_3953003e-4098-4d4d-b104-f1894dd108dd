{{- if .Values.global.traefik.ingressroute.enabled -}}
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: {{ .Chart.Name | trimSuffix "-ms" }}
  namespace: {{ .Release.Namespace }}
spec:
  entryPoints:
    - web
  routes:
  - match: PathPrefix(`/{{ .Release.Namespace }}/{{ .Chart.Name | trimSuffix "-ms" }}-mock`)
    kind: Rule
    services:
    - name: {{ .Chart.Name | trimSuffix "-ms" }}-mock
      port: {{ .Values.service.port }}
    middlewares: 
    - name: {{ .Chart.Name | trimSuffix "-ms" }}-mock-stripprefix
      namespace: {{ .Release.Namespace }}
    {{- if .Values.global.traefik.middlewares }}
    {{- toYaml .Values.global.traefik.middlewares | nindent 4 }}
    {{- end }}

{{- end -}}