{{- if .Values.global.traefik.ingressroute.enabled -}}
{{- $ingressRouteSripprefix := (cat .Release.Namespace "-stripprefix" | nospace) -}}
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ .Release.Namespace }}
spec:
  entryPoints:
    - web
  routes:
  - match: PathPrefix(`/{{ .Release.Namespace }}/{{ .Values.service.name }}`)
    kind: Rule
    services:
    - name: {{ .Chart.Name }}
      port: {{ .Values.service.hostPort.port }}
    middlewares: 
    - name: {{ $ingressRouteSripprefix }}
      namespace: {{ .Release.Namespace }}
    {{- if .Values.global.traefik.middlewares }}
    {{- toYaml .Values.global.traefik.middlewares | nindent 4 }}
    {{- end }}

{{- end -}}