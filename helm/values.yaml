replicaCount: 1
image:
  repository: polarizon-rag/rag_query_rewriter
  pullPolicy: IfNotPresent
  tag: 1.3.0-beta.10-alpha
service:
  port: 20524
  name: rag-queryrewriter
traefik:
  # 集成CI为true，单独跑CI为false
  install: false
  ingressroute:
    enabled: true
nodeSelector:
  milvus: milvus

global:
  configMap:
    DOCKER_API_URL: "unix:///var/run/docker.sock"
  traefik:
    # 集成CI为true，单独跑CI为false
    install: false
    ingressroute:
      enabled: true