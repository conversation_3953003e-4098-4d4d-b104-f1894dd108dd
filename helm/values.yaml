replicaCount: 1
image:
  repository: polarizon-rag/graphql-gateway
  pullPolicy: IfNotPresent
  tag: 2.19.0-beta.1-alpha
service:
  port: 20230
global:
  configMap:
    # NODE_ENV: 'production' production 变量进不去编辑页面：https://www.apollographql.com/docs/apollo-server/workflow/build-run-queries
    NODE_ENV: "230"
    PROXY_UPLOAD: "true"
    DEBUG_LEVEL: "info"
    HTTP_DEBUG: "0"
    timezone: Asia/Shanghai
    # 需要提供harbor的地址给前端作为算法镜像离线上传的依据
    REGISTRY_ADDRESS: "**************:30443"
    DOCKER_REGISTRY_PROJECT: "polarizon-rag"
  image:
    registry: hub.polarise.cn
  # nodeSelector:
  #   node-role: gendo3-api-gateway
  traefik:
    ingressroute:
      enabled: true