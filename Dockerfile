## 249 /raid0/work/yws/patch/embedding_cuda121/Dockerfile2

# # Base Image
# FROM hub.polarise.cn/polarizon-gendo-v3/python:3.10.3-slim

# # 安装工具包
# RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
#     sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
#     apt-get update && \
#     apt-get install -y --no-install-recommends \
#         curl \
#         procps \
#         net-tools \
#         tcpdump \
#         telnet \
#         unzip \
#         vim \
#         htop \
#     && rm -rf /var/lib/apt/lists/* \
#     && apt-get clean


# # libreOffice
# COPY libreOffice /workspace/libreOffice
# RUN dpkg -i /workspace/libreOffice/DEBS/*.deb
# COPY font /usr/share/fonts
# ARG DEBIAN_FRONTEND=noninteractive
# ENV TZ=Asia/Shanghai
# RUN apt-get update && \
#   DEBIAN_FRONTEND=noninteractive apt-get install libxinerama1 libdbus-1-3 libnss3 libcairo2 libcups2 libgio-cil -y && \
#   apt-get clean && \
#   rm -rf /var/lib/apt/lists/* && \
#   rm -rf /var/cache/apt

# # nltk_data
# COPY nltk_data /root/nltk_data



FROM hub.polarise.cn/polarizon-gendo-v3/rag-python-base:3.10.3-slim-v1.0.0

# 设置版本
ARG version

# 设置非交互模式
ARG DEBIAN_FRONTEND=noninteractive

# 设置时区
ENV TZ=Asia/Shanghai

# 设置工作目录
ENV WORKSPACE=/workspace

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install -r requirements.txt -i http://**************:8081/repository/pypi-group/simple --trusted-host **************

# 设置工作目录
WORKDIR $WORKSPACE

# 复制应用代码
COPY src $WORKSPACE
COPY start.sh $WORKSPACE

# 设置入口点
ENTRYPOINT ["/bin/bash", "start.sh"]
