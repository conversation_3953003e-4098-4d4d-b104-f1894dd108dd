FROM hub.polarise.cn/polarizon-rag/cudnn-cu12-torch-251-python310-base:v1.0

COPY model		/model/

ARG version
ARG DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

ENV WORKSPACE=/workspace
WORKDIR $WORKSPACE

COPY requirements.txt 	$WORKSPACE
RUN  pip3 install -r requirements.txt \
          -i http://**************:8081/repository/pypi-group/simple \
         --trusted-host **************

COPY src 		$WORKSPACE

ENTRYPOINT ["python3", "main.py"]


## Dockerfile for cudnn-cu12-torch-251-python310-base:v1.0

#ARG   CUDA_VERSION=12.1.1
#FROM nvidia/cuda:${CUDA_VERSION}-devel-ubuntu20.04
#
#ARG PYTHON_VERSION=3.10
#ARG   CUDA_VERSION=12.1.1
#
#ENV DEBIAN_FRONTEND=noninteractive
#
#WORKDIR /work
#
#RUN    echo 'tzdata tzdata/Areas select Asia'          | debconf-set-selections \
#    && echo 'tzdata tzdata/Zones/Asia select Shanghai' | debconf-set-selections \
#    && sed -i 's#http://.*\.ubuntu.com#https://mirrors.ustc.edu.cn#g' /etc/apt/sources.list \
#    && apt-get update -y \
#    && apt-get install -y ccache software-properties-common \
#    && add-apt-repository ppa:deadsnakes/ppa \
#    && apt-get update -y \
#    && apt-get install -y python${PYTHON_VERSION} python${PYTHON_VERSION}-dev python${PYTHON_VERSION}-distutils \
#    && if [ "${PYTHON_VERSION}" != "3" ]; then update-alternatives --install /usr/bin/python3 python3 /usr/bin/python${PYTHON_VERSION} 1; fi \
#    && python3 --version \
#    && apt-get install -y --no-install-recommends python3-pip git vim curl libibverbs-dev \
#    && apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt
#
# Install pip s.t. it will be compatible with our PYTHON_VERSION
#RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python${PYTHON_VERSION}
#RUN python3 -m pip --version
#RUN ldconfig /usr/local/cuda-$(echo $CUDA_VERSION | cut -d. -f1,2)/compat/
#
#ADD  requirements.txt  ./
#RUN  pip3  install -r requirements.txt \
#           --no-cache-dir \
#           -i http://**************:8081/repository/pypi-group/simple \
#           --trusted-host **************
#
#ENTRYPOINT []
#CMD /bin/bash

